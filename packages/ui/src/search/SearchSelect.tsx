"use client";

import type { VariantProps } from "class-variance-authority";
import type { ComponentPropsWithoutRef } from "react";

import { cva } from "class-variance-authority";
import { Loader2Icon, XIcon } from "lucide-react";

import { cn } from "../lib";
import { <PERSON><PERSON> } from "../primitives/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../primitives/select";
import { Separator } from "../primitives/separator";
import { SEARCH_SELECT_NAME, useSearchSelect } from "./select";

// Symbol to represent empty/no value options
const EMPTY_VALUE_SYMBOL = Symbol("empty-value");
const EMPTY_VALUE_STRING = EMPTY_VALUE_SYMBOL.toString();

const i18n = {
  en: {
    actions: {
      select: "Select",
      clear: "Clear selection",
    },
  },
};

// Updated size variants to match selector-container system
const searchSelectVariants = cva(
  "group flex items-center rounded-md border border-input focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
  {
    variants: {
      size: {
        sm: "h-10 p-0.5",
        md: "h-14 p-0.5",
        lg: "h-16 p-1",
        xl: "h-24 p-2",
      },
      variant: {
        default: "",
        compact:
          "border-0 bg-transparent focus-within:ring-0 focus-within:ring-offset-0",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  },
);

const searchSelectContentVariants = cva("flex h-full flex-1 items-center", {
  variants: {
    size: {
      sm: "p-0",
      md: "p-0",
      lg: "p-0",
      xl: "p-0",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

const searchSelectTriggerVariants = cva(
  "h-full border-0 focus:ring-0 focus:ring-offset-0",
  {
    variants: {
      size: {
        sm: "min-h-6",
        md: "min-h-9",
        lg: "min-h-11",
        xl: "min-h-16",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const searchSelectClearVariants = cva("flex h-full items-center gap-1", {
  variants: {
    size: {
      sm: "px-1",
      md: "px-1",
      lg: "px-1",
      xl: "px-2",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

// Updated separator variants to match selector-container
const searchSelectSeparatorVariants = cva("", {
  variants: {
    size: {
      sm: "h-6", // 60% of h-10 (40px)
      md: "h-7", // ~60% of h-14 (56px)
      lg: "h-10", // ~60% of h-16 (64px)
      xl: "h-12", // 60% of h-24 (96px)
    },
  },
  defaultVariants: {
    size: "md",
  },
});

// Updated button variants to match selector-container
const searchSelectButtonVariants = cva(
  "hover:bg-muted focus:ring-0 focus:ring-offset-0",
  {
    variants: {
      size: {
        sm: "size-7 rounded-sm", // ~75% of h-10 (40px)
        md: "size-9 rounded-md", // ~75% of h-14 (56px)
        lg: "size-12 rounded-lg", // 75% of h-16 (64px)
        xl: "size-14 rounded-lg", // ~75% of h-24 (96px)
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

type SelectContentProps = ComponentPropsWithoutRef<typeof SelectContent>;
export interface SearchSelectProps<T extends { value: string; label: string }>
  extends VariantProps<typeof searchSelectVariants> {
  loading?: boolean;
  name?: string;
  group?: string;
  placeholder?: string;
  className?: string;
  options: T[];
  defaultValue?: string;
  onChange?: (value: string) => void;
  position?: SelectContentProps["position"];
  align?: SelectContentProps["align"];
  side?: SelectContentProps["side"];
  sideOffset?: SelectContentProps["sideOffset"];
}

export function SearchSelect<T extends { value: string; label: string }>({
  loading,
  size = "md",
  variant = "default",
  name = SEARCH_SELECT_NAME,
  group,
  placeholder = i18n.en.actions.select,
  className,
  options,
  defaultValue,
  onChange,
  position = "item-aligned",
  align = "start",
  side = "bottom",
  sideOffset = 0,
}: SearchSelectProps<T>) {
  // Transform options to handle empty string values
  const transformedOptions = options.map((option) => ({
    ...option,
    value: option.value === "" ? EMPTY_VALUE_STRING : option.value,
  }));

  // Transform defaultValue if it's an empty string
  const transformedDefaultValue =
    defaultValue === "" ? EMPTY_VALUE_STRING : defaultValue;

  // Custom onChange handler to convert symbol back to empty string
  const handleChange = (value?: string | null) => {
    if (value === null || value === undefined) {
      onChange?.("");
      return;
    }
    const actualValue = value === EMPTY_VALUE_STRING ? "" : value;
    onChange?.(actualValue);
  };

  const { value, onValueChange, onClear } = useSearchSelect({
    group,
    name,
    defaultValue: transformedDefaultValue,
    defaultSymbol: EMPTY_VALUE_STRING,
    onChange: handleChange,
  });

  // Transform external value (empty string) to internal value (symbol) for the Select component
  const internalValue = value === "" ? EMPTY_VALUE_STRING : value;

  // Check if we have a meaningful value (not the empty symbol)
  const hasValue = internalValue && internalValue !== EMPTY_VALUE_STRING;

  return (
    <div className={cn(searchSelectVariants({ size, variant }), className)}>
      <div className={searchSelectContentVariants({ size })}>
        <Select name={name} value={internalValue} onValueChange={onValueChange}>
          <SelectTrigger className={searchSelectTriggerVariants({ size })}>
            {loading ? (
              <Loader2Icon
                className={cn("animate-spin", {
                  "size-4": size === "sm",
                  "size-5": size === "md",
                  "size-6": size === "lg",
                  "size-7": size === "xl",
                })}
              />
            ) : (
              <SelectValue placeholder={placeholder} />
            )}
          </SelectTrigger>
          <SelectContent
            position={position}
            align={align}
            side={side}
            sideOffset={sideOffset}
          >
            {transformedOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {hasValue && !loading && (
        <div className={searchSelectClearVariants({ size })}>
          <Separator
            orientation="vertical"
            className={searchSelectSeparatorVariants({ size })}
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={onClear}
            className={searchSelectButtonVariants({ size })}
            title={i18n.en.actions.clear}
            aria-label={i18n.en.actions.clear}
          >
            <XIcon
              className={cn({
                "size-4": size === "sm",
                "size-5": size === "md",
                "size-6": size === "lg",
                "size-7": size === "xl",
              })}
            />
          </Button>
        </div>
      )}
    </div>
  );
}
