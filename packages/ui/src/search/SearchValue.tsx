import { useCallback } from "react";

import type { GenericNode, SelectorProps } from "@/ui/selectors/Selector";

import { Selector } from "@/ui/selectors/Selector";

import type { SearchValueOption } from "./value";

import { SEARCH_VALUE_NAME, useSearchValue } from "./value";

// Symbol to represent empty/no value options
const EMPTY_VALUE_SYMBOL = Symbol("empty-value");
const EMPTY_VALUE_STRING = EMPTY_VALUE_SYMBOL.toString();

export interface SearchValueProps<
  T extends string = string,
  V extends GenericNode = SearchValueOption<T>,
> extends SelectorProps<V> {
  loading?: boolean;
  name: string;
  group?: string;
  placeholder?: string;
  defaultValue?: T;
  onChange?: (value?: string | null) => void;
  onSelect?: (value?: V | null) => void;
  debounce?: number;
  className?: string;
}

export function SearchValue<
  T extends string = string,
  V extends SearchValueOption<T> = SearchValueOption<T>,
>({
  loading,
  size = "md",
  variant = "outline",
  align = "center",
  useDialog = false,
  disabled = false,
  name = SEARCH_VALUE_NAME,
  group,
  placeholder,
  defaultValue,
  onChange,
  onSelect,
  debounce = 500,
  ...props
}: SearchValueProps<T, V>) {
  // Transform defaultValue if it's an empty string
  const transformedDefaultValue =
    defaultValue === "" ? EMPTY_VALUE_STRING : defaultValue;

  // Custom onChange handler to convert symbol back to empty string
  const handleChange = useCallback(
    (value?: string | null) => {
      const actualValue = value === EMPTY_VALUE_STRING ? null : value;
      onChange?.(actualValue);
    },
    [onChange],
  );

  // Custom onSelect handler to handle empty values
  const handleSelect = useCallback(
    (value?: V | null) => {
      if (value && value.value === EMPTY_VALUE_STRING) {
        // Convert symbol back to empty string for external consumers
        const transformedValue = { ...value, value: "" as T } as V;
        onSelect?.(transformedValue);
      } else {
        onSelect?.(value ?? undefined);
      }
    },
    [onSelect],
  );

  const { value, selection, onSelectionChange, onValueChange, onClear } =
    useSearchValue<V>({
      name,
      group,
      defaultValue: transformedDefaultValue,
      defaultSymbol: EMPTY_VALUE_STRING, // Pass the symbol for clearing
      data: props.data, // Pass data to help with default selection initialization
      onChange: handleChange,
      onSelect: handleSelect,
      debounce,
    });

  return (
    <Selector<V>
      renderId={useCallback((value: V) => String(value.value), [])}
      renderValue={useCallback((value: V) => String(value.value), [])}
      renderItem={useCallback((value: V) => value.label, [])}
      {...props}
      clearable
      loading={loading}
      disabled={disabled}
      size={size}
      variant={variant}
      align={align}
      useDialog={useDialog}
      label={placeholder}
      value={value}
      selection={selection}
      defaultValue={transformedDefaultValue}
      onValueChange={onValueChange}
      onSelect={onSelectionChange}
      onClear={onClear}
    />
  );
}
