"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import { useDebounceValue } from "../hooks/useDebounceValue";
import { useSearchParamsContext } from "./SearchParams";

export const SEARCH_SELECT_NAME = "select";

export function useSearchSelectValue<T extends string | number | null = string>(
  group?: string,
  name = SEARCH_SELECT_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  const key = [group, name].filter(Boolean).join("-");
  return searchParams[key] as T | undefined;
}

export interface UseSearchSelectOptions<
  T extends string | number | null = string,
> {
  name?: string;
  group?: string;
  defaultValue?: T;
  defaultSymbol?: string;
  data?: { id: string; value: T; label?: string }[];
  onChange?: (value?: T | null) => void | Promise<void>;
  onSelect?: (
    item?: { id: string; value: T; label?: string } | null,
  ) => void | Promise<void>;
  debounce?: number;
}

export function useSearchSelect<T extends string | number | null = string>({
  name = SEARCH_SELECT_NAME,
  group,
  defaultValue,
  defaultSymbol,
  data = [],
  onChange,
  onSelect,
  debounce = 500,
}: UseSearchSelectOptions<T>) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  // Get the current value from search params
  const currentValue = Array.isArray(searchParams[key])
    ? searchParams[key][0]
    : searchParams[key];

  // Compute the initial selection once - used only for initialization
  const initialSelection = useMemo(() => {
    if (!defaultValue || data.length === 0) return undefined;
    return data.find((item) => item.value === defaultValue) ?? undefined;
  }, [defaultValue, data]);

  const [value, setValue] = useDebounceValue<T>(
    (currentValue as T) ?? (defaultValue as T),
    debounce,
  );

  // Initialize selection state with the computed initial selection
  const [selection, setSelection] = useState<
    { id: string; value: T; label?: string } | null | undefined
  >(initialSelection);

  const getNextValue = useCallback(
    (item?: { id: string; value: T; label?: string }) => {
      if (item && defaultSymbol) {
        if (item.value === defaultSymbol) {
          // Create a new value with null for special symbol handling
          return { ...item, value: null as T };
        }
      }
      return item;
    },
    [defaultSymbol],
  );

  const onValueChange = useCallback(
    async (value?: T) => {
      setValue(value as T);
      await onChange?.(value);
    },
    [onChange, setValue],
  );

  const onSelectionChange = useCallback(
    async (item?: { id: string; value: T; label?: string } | null) => {
      const nextValue = item ? getNextValue(item) : item;

      const valueToSet = nextValue?.value?.toString() ?? "";
      setSearchParams([[key, valueToSet]]);
      setSelection(nextValue);
      await onSelect?.(nextValue);
      await onValueChange(nextValue?.value);
    },
    [onSelect, setSearchParams, key, onValueChange, getNextValue],
  );

  const onClear = useCallback(async () => {
    await onSelectionChange(undefined);
    await onChange?.(null);
  }, [onSelectionChange, onChange]);

  // Sync debounced value with URL changes (handles browser navigation)
  useEffect(() => {
    const urlValue = (currentValue as T) ?? (defaultValue as T);
    if (value !== urlValue) {
      setValue(urlValue);
    }
  }, [currentValue, defaultValue, value, setValue]);

  // Sync selection with current value from URL (handles browser navigation)
  useEffect(() => {
    if (currentValue && data.length > 0) {
      const matchingItem = data.find(
        (item) => item.value?.toString() === currentValue,
      );
      if (matchingItem !== selection) {
        setSelection(matchingItem);
      }
    } else if (!currentValue) {
      // Clear selection if no current value (handles back button to empty state)
      if (selection !== null) {
        setSelection(null);
      }
    }
  }, [currentValue, data, selection]);

  return {
    value: value ?? (null as T),
    selection: selection === undefined ? null : selection,
    onSelectionChange,
    onValueChange,
    onClear,
  };
}
