"use client";

import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";
import { CalendarIcon, XIcon } from "lucide-react";

import { cn } from "../lib";
import { But<PERSON>, buttonVariants } from "../primitives/button";
import { Calendar } from "../primitives/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../primitives/popover";
import { Separator } from "../primitives/separator";
import {
  SEARCH_DATE_GROUP,
  SEARCH_DATE_NAME,
  useSearchDate,
  useSearchDateRange,
} from "./date";

const i18n = {
  en: {
    placeholder: "Search date range...",
    clear: "Clear date range",
  },
};

// Size variants matching selector-container
const searchDateContainerVariants = cva(
  "flex items-center justify-between rounded-md shadow-sm focus-within:ring-2 focus-within:ring-ring",
  {
    variants: {
      size: {
        sm: "h-10",
        md: "h-14",
        lg: "h-16",
        xl: "h-24",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const searchDateSeparatorVariants = cva("", {
  variants: {
    size: {
      sm: "h-6", // 60% of h-10 (40px)
      md: "h-7", // ~60% of h-12 (48px)
      lg: "h-10", // ~60% of h-16 (64px)
      xl: "h-12", // 60% of h-20 (80px)
    },
  },
  defaultVariants: {
    size: "md",
  },
});

const searchDateButtonVariants = cva(
  "hover:bg-muted focus:ring-0 focus:ring-offset-0",
  {
    variants: {
      size: {
        sm: "m-0.5 size-7 rounded-sm", // ~75% of h-10 (40px)
        md: "m-0.5 size-9 rounded-md", // ~75% of h-12 (48px)
        lg: "m-1 size-12 rounded-lg", // 75% of h-16 (64px)
        xl: "m-2 size-14 rounded-lg", // ~75% of h-20 (80px)
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface SearchDateRangeProps
  extends VariantProps<typeof searchDateContainerVariants> {
  loading?: boolean;
  name?: string;
  group?: string;
  placeholder?: string;
  className?: string;
}

export function SearchDateRange({
  loading,
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
  placeholder = i18n.en.placeholder,
  size = "md",
  className,
}: SearchDateRangeProps) {
  const { selection, onSelect, onClear, values, hasValue, text } =
    useSearchDateRange({
      name,
      group,
    });

  return (
    <Popover>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: size === "xl" ? "lg" : size,
            className: cn(searchDateContainerVariants({ size }), "px-0"),
          }),
          className,
        )}
      >
        <PopoverTrigger asChild className="flex-1">
          <Button
            disabled={loading}
            type="button"
            variant="ghost"
            size={size === "xl" ? "lg" : size}
            className={cn("h-full space-x-2 focus:z-10", {
              "rounded-e-none": hasValue,
            })}
          >
            <CalendarIcon
              className={cn("mr-auto opacity-50", {
                "size-4": size === "sm",
                "size-5": size === "md",
                "size-6": size === "lg",
                "size-7": size === "xl",
              })}
            />
            <span
              className={
                hasValue ? "truncate" : "truncate text-muted-foreground"
              }
            >
              {text || placeholder}
            </span>
          </Button>
        </PopoverTrigger>
        {hasValue && (
          <>
            <Separator
              orientation="vertical"
              className={searchDateSeparatorVariants({ size })}
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onClear}
              className={searchDateButtonVariants({ size })}
              title={i18n.en.clear}
              aria-label={i18n.en.clear}
            >
              <XIcon
                className={cn({
                  "size-4": size === "sm",
                  "size-5": size === "md",
                  "size-6": size === "lg",
                  "size-7": size === "xl",
                })}
              />
            </Button>
          </>
        )}
      </div>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          selected={selection || undefined}
          onSelect={onSelect}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  );
}

export interface SearchDateProps
  extends VariantProps<typeof searchDateContainerVariants> {
  loading?: boolean;
  name?: string;
  group?: string;
  placeholder?: string;
  className?: string;
}

export function SearchDate({
  loading,
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
  placeholder = "Search date...",
  size = "md",
  className,
}: SearchDateProps) {
  const { selection, onSelect, onClear, hasValue, text } = useSearchDate({
    name,
    group,
  });

  return (
    <Popover>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: size === "xl" ? "lg" : size,
            className: cn(searchDateContainerVariants({ size }), "px-0"),
          }),
          className,
        )}
      >
        <PopoverTrigger asChild className="flex-1">
          <Button
            disabled={loading}
            type="button"
            variant="ghost"
            size={size === "xl" ? "lg" : size}
            className={cn("h-full space-x-2 focus:z-10", {
              "rounded-e-none": hasValue,
            })}
          >
            <CalendarIcon
              className={cn("mr-auto opacity-50", {
                "size-4": size === "sm",
                "size-5": size === "md",
                "size-6": size === "lg",
                "size-7": size === "xl",
              })}
            />
            <span
              className={
                hasValue ? "truncate" : "truncate text-muted-foreground"
              }
            >
              {text || placeholder}
            </span>
          </Button>
        </PopoverTrigger>
        {hasValue && (
          <>
            <Separator
              orientation="vertical"
              className={searchDateSeparatorVariants({ size })}
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onClear}
              className={searchDateButtonVariants({ size })}
              title="Clear date"
              aria-label="Clear date"
            >
              <XIcon
                className={cn({
                  "size-4": size === "sm",
                  "size-5": size === "md",
                  "size-6": size === "lg",
                  "size-7": size === "xl",
                })}
              />
            </Button>
          </>
        )}
      </div>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={selection || undefined}
          onSelect={onSelect}
        />
      </PopoverContent>
    </Popover>
  );
}
