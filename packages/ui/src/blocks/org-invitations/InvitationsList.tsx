"use client";

import React from "react";
import { Mail, Plus } from "lucide-react";

import { Button } from "@/ui/primitives/button";
import { SearchFilter, SearchText } from "@/ui/search";
import {
  PaginatedCardContent,
  PaginatedCardFooter,
  PaginatedCardHeader,
  PaginatedCardRoot,
} from "@/ui/shared/PaginatedCard";

import type {
  GenericInvitation,
  OrganizationInvitationsListProps,
} from "./types";

import { EmptyInvitationsState } from "./EmptyState";
import { InvitationCard } from "./InvitationCard";

export function InvitationsList<
  T extends GenericInvitation = GenericInvitation,
>({
  invitations,
  total,
  loading = false,
  error,
  title = "User Invitations",
  description = "Manage pending user invitations and access requests",
  emptyMessage = "No pending invitations",
  itemNoun = { singular: "invitation", plural: "invitations" },
  renderInvitation,
  renderInvitationMenu,
  onInviteClick,
  inviteButtonLabel = "Invite User",
  filters,
  searchPlaceholder = "Search invitations...",
  searchNamespace = "invitations",
  pagination,
  onPaginationChange,
  onInvitationClick,
  className = "",
  gridCols = 2,
}: OrganizationInvitationsListProps<T>) {
  const defaultRenderInvitation = (invitation: T) => (
    <InvitationCard
      key={invitation.id}
      invitation={invitation}
      renderMenu={renderInvitationMenu}
      onClick={onInvitationClick}
    />
  );

  const renderActions = () => {
    if (!onInviteClick) return null;

    return (
      <Button onClick={onInviteClick} className="gap-2">
        <Plus className="size-4" />
        {inviteButtonLabel}
      </Button>
    );
  };

  const renderFilters = () => {
    if (!filters || filters.length === 0) return null;

    return (
      <div className="flex items-center gap-2">
        <SearchFilter
          name={searchNamespace}
          groups={filters}
          loading={loading}
        />
        <SearchText
          group={searchNamespace}
          placeholder={searchPlaceholder}
          loading={loading}
          className="flex-1"
        />
      </div>
    );
  };

  return (
    <PaginatedCardRoot loading={loading} className={className}>
      <PaginatedCardHeader
        icon={<Mail className="size-5 text-muted-foreground" />}
        title={title}
        count={total}
        description={description}
        loading={loading}
        actions={renderActions()}
        // filters={renderFilters()}
      />

      <PaginatedCardContent
        isLoading={loading}
        error={error}
        isEmpty={invitations.length === 0}
        emptyComponent={
          <EmptyInvitationsState
            message={`${emptyMessage}. You have not sent any invitations yet.`}
          />
        }
      >
        <div className="flex items-center gap-2">
          <SearchFilter
            name={searchNamespace}
            groups={filters || []}
            loading={loading}
          />
          <SearchText
            group={searchNamespace}
            placeholder={searchPlaceholder}
            loading={loading}
            className="flex-1"
          />
        </div>
        <div
          className={`grid grid-cols-1 gap-4 ${
            gridCols === 2
              ? "sm:grid-cols-2"
              : gridCols === 3
                ? "sm:grid-cols-3"
                : gridCols === 4
                  ? "sm:grid-cols-4"
                  : ""
          }`}
        >
          {invitations.map(renderInvitation || defaultRenderInvitation)}
        </div>
      </PaginatedCardContent>

      {pagination && onPaginationChange && (
        <PaginatedCardFooter
          pagination={pagination}
          setPagination={(updaterOrValue) => {
            if (typeof updaterOrValue === "function") {
              onPaginationChange(updaterOrValue(pagination));
            } else {
              onPaginationChange(updaterOrValue);
            }
          }}
          totalItems={total}
          itemNoun={itemNoun}
          loading={loading}
        />
      )}
    </PaginatedCardRoot>
  );
}
