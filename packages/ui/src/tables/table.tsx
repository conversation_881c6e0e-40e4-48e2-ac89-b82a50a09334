"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren, ReactNode } from "react";

import { useMemo } from "react";
import { CircleIcon, MenuIcon, Settings2Icon } from "lucide-react";

import { <PERSON><PERSON> } from "@/ui/primitives/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/ui/primitives/dropdown-menu";
import { SearchFilter, SearchText, useSearchPagination } from "@/ui/search";
import EmptyList from "@/ui/shared/EmptyList";

import { dataTableColumns, selectColumn } from "./columns";
import { DataTable, useDataTable } from "./data-table";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "./helpers";

// Generic paginated response interface - cleaner than union types
export interface PaginatedResponse<TData> {
  items: TData[];
  total: number;
}

export interface FilterGroup {
  id: string;
  label: string;
  options: {
    value: string | null;
    label: string;
  }[];
}

// Action Types
export const ActionType = {
  SELECTION: "selection",
  SETTINGS: "settings",
  ROW: "row",
} as const;

export type ActionType = (typeof ActionType)[keyof typeof ActionType];

// Action Context varies by action type
export type ActionContext<TData> =
  | {
      type: "selection";
      selectedRows: TData[];
      table: ReturnType<typeof useDataTable<TData, TData[]>>["table"];
    }
  | {
      type: "settings";
      table: ReturnType<typeof useDataTable<TData, TData[]>>["table"];
    }
  | {
      type: "row";
      row: TData;
      table: ReturnType<typeof useDataTable<TData, TData[]>>["table"];
    };

export interface TableConfig {
  groupName: string;
  enableSelection?: boolean;
  manualPagination?: boolean;
  i18n: {
    noData: string;
    selection: string;
    actions: {
      tableSettings: string;
      tableActions: string;
      search: string;
    };
  };
}

export interface TableAction<TData = unknown> {
  type: ActionType;
  label: string;
  icon?: React.ComponentType<{ size?: string | number }>;
  variant?: "default" | "destructive" | "ghost";
  disabled?: (context: ActionContext<TData>) => boolean;
  onClick?: (context: ActionContext<TData>) => void;
  render?: (context: ActionContext<TData>) => React.ReactNode;
}

export interface RowAction<TData = unknown> {
  label: string;
  onClick: (row: TData) => void;
  disabled?: (row: TData) => boolean;
  variant?: "default" | "destructive";
}

// TODO: design API declarative <TableAction {...props} />
// declarative approach that updates internal context state with mount/unmount
// better consumer API, less boilerplate and can pass in a component (or child function)

export function TableActions<TData>({
  table,
  actions = [],
  i18n,
}: {
  table: ReturnType<typeof useDataTable<TData, TData[]>>["table"];
  actions?: TableAction<TData>[];
  i18n: TableConfig["i18n"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;
  const selectedData = selection.rows.map((row) => row.original);

  // Filter for selection actions only
  const selectionActions = actions.filter(
    (action) => action.type === ActionType.SELECTION,
  );

  const handleAction = (action: TableAction<TData>) => {
    const context: ActionContext<TData> = {
      type: "selection",
      selectedRows: selectedData,
      table,
    };
    action.onClick?.(context);
  };

  if (selectionActions.length === 0) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.selection} ({selectionCount})
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {selectionActions.map((action) => {
            const context: ActionContext<TData> = {
              type: "selection",
              selectedRows: selectedData,
              table,
            };
            return (
              <DropdownMenuItem asChild key={action.label}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  disabled={action.disabled?.(context) ?? false}
                  onClick={() => handleAction(action)}
                >
                  {action.icon && <action.icon size="16" />}
                  {action.label}
                </Button>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface TableProps<TData> extends PropsWithChildren {
  loading?: boolean;
  data?: PaginatedResponse<TData>;
  columns: ColumnDef<TData, TData[]>[];
  config: TableConfig;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: FilterGroup[];
  header?: ReactNode | ReactNode[];
  actions?: TableAction<TData>[];
}

export default function Table<TData>({
  loading = false,
  data,
  columns,
  config,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  actions = [],
  header,
  children,
}: TableProps<TData>) {
  const { pagination, setPagination } = useSearchPagination({
    group: config.groupName,
    defaultPageSize,
    defaultPageIndex,
  });

  // Extract configuration values with defaults
  const enableSelection = config.enableSelection ?? false;
  const manualPagination = config.manualPagination ?? true;

  // Filter actions by type
  const actionsByType = useMemo(() => {
    const selection = actions.filter(
      (action) => action.type === ActionType.SELECTION,
    );
    const settings = actions.filter(
      (action) => action.type === ActionType.SETTINGS,
    );
    const row = actions.filter((action) => action.type === ActionType.ROW);

    return { selection, settings, row };
  }, [actions]);

  const tableColumns = useMemo(() => {
    const cols = [...columns];

    // Add selection column if enabled
    if (enableSelection) {
      cols.unshift(selectColumn as ColumnDef<TData, TData[]>);
    }

    // Add actions column if we have selection actions or row actions
    if (actionsByType.selection.length > 0 || actionsByType.row.length > 0) {
      // Check if there's already an actions column
      const hasActionsColumn = cols.some((col) => col.id === "actions");

      if (!hasActionsColumn) {
        cols.push({
          id: "actions",
          meta: {
            className: "w-[32px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              {actionsByType.selection.length > 0 && (
                <TableActions
                  table={table}
                  actions={actionsByType.selection}
                  i18n={config.i18n}
                />
              )}
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex size-full items-center justify-end">
              {actionsByType.row.length > 0 &&
                (() => {
                  const firstAction = actionsByType.row[0];
                  // If there's only one row action and it has a render function, render directly
                  if (actionsByType.row.length === 1 && firstAction?.render) {
                    const context: ActionContext<TData> = {
                      type: "row",
                      row: row.original,
                      table,
                    };
                    return firstAction.render(context);
                  }

                  // Otherwise use dropdown menu
                  return (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MenuIcon size="16" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuGroup>
                          {actionsByType.row.map((action) => {
                            const context: ActionContext<TData> = {
                              type: "row",
                              row: row.original,
                              table,
                            };

                            // Use render escape hatch if provided
                            if (action.render) {
                              return (
                                <DropdownMenuItem asChild key={action.label}>
                                  {action.render(context)}
                                </DropdownMenuItem>
                              );
                            }

                            // Standard button rendering
                            return (
                              <DropdownMenuItem asChild key={action.label}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="w-full justify-start"
                                  disabled={action.disabled?.(context) ?? false}
                                  onClick={() => action.onClick?.(context)}
                                >
                                  {action.icon && <action.icon size="16" />}
                                  {action.label}
                                </Button>
                              </DropdownMenuItem>
                            );
                          })}
                        </DropdownMenuGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  );
                })()}
            </div>
          ),
        } as ColumnDef<TData, TData[]>);
      }
    }

    return dataTableColumns<TData, TData[]>(cols);
  }, [columns, enableSelection, actionsByType, config.i18n]);

  const { table } = useDataTable<TData, TData[]>({
    data: data?.items ?? [],
    rowCount: data?.total,
    manualPagination,
    pagination,
    setPagination,
    columns: tableColumns,
  });

  return (
    <Card>
      <CardHeader className="border-b p-2">
        <div className="flex items-center justify-between">
          {filters && filters.length > 0 && (
            <div className="border-r pr-2">
              <SearchFilter name={config.groupName} groups={filters} />
            </div>
          )}

          <div className="flex h-fit flex-1 items-center gap-2 overflow-scroll p-1 px-2">
            {useMemo(() => {
              if (header === undefined) {
                return (
                  <SearchText
                    group={config.groupName}
                    loading={loading}
                    placeholder={config.i18n.actions.search}
                  />
                );
              }

              return header;
            }, [header, config.groupName, loading, config.i18n.actions.search])}
          </div>

          <div className="border-l pl-2">
            <DataTableSettings
              table={table}
              variant="ghost"
              size="icon"
              className="ml-auto"
              aria-label={config.i18n.actions.tableSettings}
            >
              <Settings2Icon size="20" color="currentColor" />
            </DataTableSettings>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <DataTable loading={loading} table={table}>
          <EmptyList title={config.i18n.noData}>{children}</EmptyList>
        </DataTable>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 border-t pt-6">
        <DataTableSimplePagination table={table} />
      </CardFooter>
    </Card>
  );
}

// Export helper for creating column headers
export { DataTableColumnHeader };
