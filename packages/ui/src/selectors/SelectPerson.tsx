import { useCallback } from "react";
import { UserIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@/ui/selectors/Selector";

import PreviewPerson from "@/ui/common/PreviewPerson";
import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { useSearchValue } from "@/ui/search/value";
import { Selector } from "@/ui/selectors/Selector";

const i18n = {
  en: {
    addPerson: "Add Person",
    label: "Select a Person",
    description: "The person to contact",
    placeholder: "Select a person to contact",
    searchPerson: "Search person...",
    avatarFor: "avatar image for ",
  },
};

export interface PersonPartial extends GenericNode {
  id: string;
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  avatar: string | null; // Fix null type issue for avatar property
}

export interface SelectPersonProps<Person extends PersonPartial>
  extends SelectorProps<Person> {
  placeholder?: string;
}

export function SelectPerson<Person extends PersonPartial>({
  children,
  useDialog = false,
  loading = false,
  label,
  placeholder = i18n.en.searchPerson,
  className,
  size = "md",
  variant = "outline",
  ...props
}: SelectPersonProps<Person>) {
  return (
    <Selector<Person>
      renderLoading={() => (
        <div className="flex w-full flex-col items-center justify-center gap-1">
          {Array.from({ length: 5 }).map((_, index) => (
            <PreviewPerson key={index} loading={true} size={size} />
          ))}
        </div>
      )}
      renderValue={useCallback(
        (person: Person) => `${person.firstName} ${person.lastName}`,
        [],
      )}
      renderItem={useCallback(
        (person: Person) => {
          const name = `${person.firstName} ${person.lastName}`.trim();
          return (
            <PreviewPerson
              loading={loading}
              size={size}
              person={{
                id: person.id,
                name,
                avatar: person.avatar ?? undefined,
              }}
              description={person.email ?? undefined}
            />
          );
        },
        [loading, size],
      )}
      label={
        label || (
          <span className="flex items-center gap-1 text-sm text-muted-foreground hover:text-accent-foreground">
            <UserIcon className="m-2 me-0 size-4" />
            <span>{placeholder}</span>
          </span>
        )
      }
      {...props}
      loading={loading}
      placeholder={placeholder}
      variant={variant}
      useDialog={useDialog}
      size={size}
      className={className}
    >
      {children}
    </Selector>
  );
}

export default SelectPerson;

export interface SelectPersonFieldProps<DataType extends PersonPartial>
  extends SelectPersonProps<DataType> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
  onQueryChange?: (value: string) => void;
}

export function SelectPersonField<DataType extends PersonPartial>({
  loading = false,
  name = "person.id",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  onQueryChange,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectPersonFieldProps<DataType>) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectPerson
              {...props}
              loading={loading}
              placeholder={placeholder}
              data={data ?? []}
              useDialog={false}
              onValueChange={(value) => {
                onQueryChange?.(value);
              }}
              selection={data?.find((person) => person.id === field.value)}
              onSelect={async (person) => {
                await onSelect?.(person);
                field.onChange(person.id);
                form.setValue("personId", person.id, {
                  shouldDirty: true,
                });
                form.setValue("person", person, {
                  shouldDirty: true,
                });
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchPersonProps<DataType extends PersonPartial>
  extends SelectorProps<DataType> {
  placeholder?: string;
  name?: string;
  group?: string;
  defaultValue?: string;
}

export function SearchPerson<DataType extends PersonPartial>({
  loading = false,
  placeholder = i18n.en.searchPerson,
  name = "person",
  group,
  data,
  defaultValue,
  ...props
}: SearchPersonProps<DataType>) {
  const { selection, onClear, onSelectionChange } = useSearchValue<DataType>({
    name,
    group,
    defaultValue,
    data,
  });

  return (
    <SelectPerson
      {...props}
      loading={loading}
      placeholder={placeholder}
      data={data}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
