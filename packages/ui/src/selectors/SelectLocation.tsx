"use client";

import { useCallback } from "react";
import { MapPinIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@/ui/selectors/Selector";

import PreviewLocation from "@/ui/common/PreviewLocation";
import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { useSearchValue } from "@/ui/search/value";
import { Selector } from "@/ui/selectors/Selector";

const i18n = {
  en: {
    label: "Location",
    description: "The location",
    placeholder: "Select a location",
  },
};

export interface PartialLocation extends GenericNode {
  name: string;
  address: {
    formatted: string | null;
    street?: string | null;
    city?: string | null;
    state?: string | null;
    postal?: string | null;
    country?: string | null;
  };
}

export interface SelectLocationProps<DataType extends PartialLocation>
  extends SelectorProps<DataType> {
  placeholder?: string;
}

export function SelectLocation<DataType extends PartialLocation>({
  size = "md",
  loading = false,
  variant = "outline",
  align = "center",
  className,
  disabled,
  useDialog,
  placeholder = i18n.en.placeholder,
  data,
  value,
  onSelect,
  ...props
}: SelectLocationProps<DataType>) {
  return (
    <Selector<DataType>
      className={className}
      renderValue={useCallback(
        (location: DataType) => location.address.formatted ?? "",
        [],
      )}
      renderSelection={useCallback(
        (location?: DataType) =>
          location ? (
            <PreviewLocation
              size={size}
              loading={loading}
              link={false}
              showCopyButton={false}
              location={{
                id: location.id,
                name: location.name,
                address: {
                  formatted: location.address.formatted ?? "",
                },
              }}
            />
          ) : null,
        [loading, size],
      )}
      renderLoading={useCallback(
        () => (
          <div className="flex w-fit flex-col gap-1">
            <PreviewLocation loading size={size} />
            <PreviewLocation loading size={size} />
            <PreviewLocation loading size={size} />
            <PreviewLocation loading size={size} />
            <PreviewLocation loading size={size} />
          </div>
        ),
        [size],
      )}
      renderItem={useCallback(
        (location: DataType) => (
          <PreviewLocation
            size={size}
            loading={loading}
            link={false}
            showCopyButton={false}
            location={{
              id: location.id,
              name: location.name,
              address: {
                formatted: location.address.formatted ?? "",
              },
            }}
          />
        ),
        [loading, size],
      )}
      label={
        <span className="flex items-center gap-1 text-sm text-muted-foreground hover:text-accent-foreground">
          <MapPinIcon className="m-2 me-0 size-4" />
          <span>{placeholder}</span>
        </span>
      }
      {...props}
      size={size}
      loading={loading}
      disabled={disabled}
      variant={variant}
      align={align}
      useDialog={useDialog ?? false}
      // because we use the address.formatted as the value and name, it omits valid search results
      shouldFilter={false}
      data={data}
      value={value}
      onSelect={onSelect}
    />
  );
}

export default SelectLocation;

export interface SelectLocationFieldProps<DataType extends PartialLocation>
  extends SelectLocationProps<DataType> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectLocationField<DataType extends PartialLocation>({
  loading = false,
  name = "locationId",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectLocationFieldProps<DataType>) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectLocation
              {...props}
              loading={loading}
              placeholder={placeholder}
              data={data}
              selection={data?.find((location) => location.id === field.value)}
              onSelect={async (location: DataType): Promise<void> => {
                if (onSelect) {
                  await onSelect(location);
                }

                field.onChange(location.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchLocationProps<DataType extends PartialLocation>
  extends SelectorProps<DataType> {
  placeholder?: string;
  name?: string;
  group?: string;
  defaultValue?: string;
}

export function SearchLocation<DataType extends PartialLocation>({
  loading = false,
  placeholder = i18n.en.placeholder,
  name = "location",
  group,
  data,
  defaultValue,
  ...props
}: SearchLocationProps<DataType>) {
  const { selection, onClear, onSelectionChange } = useSearchValue<DataType>({
    name,
    group,
    defaultValue,
    data,
  });

  return (
    <SelectLocation
      {...props}
      loading={loading}
      placeholder={placeholder}
      data={data}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
