import type { VariantProps } from "class-variance-authority";
import type { ReactNode } from "react";

import { cva } from "class-variance-authority";
import { XIcon } from "lucide-react";

import { cn } from "@/ui/lib";
import { Button, buttonVariants } from "@/ui/primitives/button";
import { Separator } from "@/ui/primitives/separator";

const i18n = {
  en: {
    actions: {
      clear: "Clear selection",
    },
  },
};

// Copy all the variant styles from SearchValue
const selectorContainerVariants = cva(
  "group flex w-full items-center justify-between rounded-md shadow-sm focus-within:ring-2 focus-within:ring-ring",
  {
    variants: {
      size: {
        sm: "h-10",
        md: "h-14",
        lg: "h-16",
        xl: "h-24",
      },
      variant: {
        link: "",
        ghost: "border-transparent shadow-none",
        secondary: "border border-secondary",
        primary: "border border-primary",
        destructive: "border border-destructive",
        accent: "border border-accent",
        muted: "border border-muted",
        outline: "border border-input",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "ghost",
    },
  },
);

const selectorContainerContentVariants = cva(
  "flex size-full flex-1 items-center",
  {
    variants: {
      size: {
        sm: "p-0.5",
        md: "p-0.5",
        lg: "p-1",
        xl: "p-2",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const selectorContainerSeparatorVariants = cva("", {
  variants: {
    size: {
      sm: "h-6", // 60% of h-10 (40px)
      md: "h-7", // ~60% of h-12 (48px)
      lg: "h-10", // ~60% of h-16 (64px)
      xl: "h-12", // 60% of h-20 (80px)
    },
  },
  defaultVariants: {
    size: "md",
  },
});

const selectorContainerButtonVariants = cva(
  "hover:bg-muted focus:ring-0 focus:ring-offset-0",
  {
    variants: {
      size: {
        sm: "m-0.5 size-7 rounded-sm", // ~75% of h-10 (40px)
        md: "m-0.5 size-9 rounded-md", // ~75% of h-12 (48px)
        lg: "m-1 size-12 rounded-lg", // 75% of h-16 (64px)
        xl: "m-2 size-14 rounded-lg", // ~75% of h-20 (80px)
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface SelectorContainerProps
  extends VariantProps<typeof selectorContainerVariants> {
  /** The selector component to wrap */
  children: ReactNode;
  /** Whether there is a selected value (controls clear button visibility) */
  hasValue?: boolean;
  /** Whether the component is in a loading state */
  loading?: boolean;
  /** Callback when clear button is clicked */
  onClear?: () => void;
  /** Additional className for the wrapper */
  className?: string;
}

export function SelectorContainer({
  children,
  hasValue = false,
  loading = false,
  onClear,
  size = "md",
  variant = "ghost",
  className,
}: SelectorContainerProps) {
  return (
    <div
      className={cn(
        buttonVariants({
          variant,
          size: size === "xl" ? "lg" : size,
          className: cn(selectorContainerVariants({ size, variant }), "px-0"),
        }),
        className,
      )}
    >
      <div className={selectorContainerContentVariants({ size })}>
        {children}
      </div>

      {hasValue && !loading && onClear && (
        <>
          <Separator
            orientation="vertical"
            className={selectorContainerSeparatorVariants({ size })}
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onClear}
            className={selectorContainerButtonVariants({ size })}
            title={i18n.en.actions.clear}
            aria-label={i18n.en.actions.clear}
          >
            <XIcon
              className={cn({
                "size-4": size === "sm",
                "size-5": size === "md",
                "size-6": size === "lg",
                "size-7": size === "xl",
              })}
            />
          </Button>
        </>
      )}
    </div>
  );
}
