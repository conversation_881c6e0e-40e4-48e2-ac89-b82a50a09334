"use client";

import { useCallback } from "react";
import { ListTreeIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@/ui/selectors/Selector";

import PreviewOrganization from "@/ui/common/PreviewOrganization";
import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Selector } from "@/ui/selectors/Selector";

import { useSearchValue } from "../search";

const i18n = {
  en: {
    label: "Organization",
    description: "The organization",
    placeholder: "Select an organization",
  },
};

export interface PartialOrganization extends GenericNode {
  id: string;
  name: string;
  avatar?: string | null;
}

export interface SelectOrganizationProps<DataType extends PartialOrganization>
  extends SelectorProps<DataType> {
  placeholder?: string;
}

export function SelectOrganization<DataType extends PartialOrganization>({
  size = "md",
  loading = false,
  variant = "outline",
  align = "center",
  className,
  disabled,
  useDialog,
  placeholder = i18n.en.placeholder,
  data,
  value,
  onSelect,
  ...props
}: SelectOrganizationProps<DataType>) {
  return (
    <Selector<DataType>
      className={className}
      renderValue={useCallback((org: DataType) => org.name, [])}
      renderLoading={useCallback(
        () => (
          <div className="flex w-fit min-w-full flex-col gap-1">
            {new Array(5).fill(0).map((_, index) => (
              <PreviewOrganization
                key={index}
                loading
                size={size}
                className="w-fit min-w-full"
              />
            ))}
          </div>
        ),
        [size],
      )}
      renderItem={useCallback(
        (org: DataType) =>
          org.id !== "null" ? (
            <PreviewOrganization
              size={size}
              loading={loading}
              organization={{
                id: org.id,
                name: org.name,
                avatar: org.avatar,
              }}
            />
          ) : (
            <div className="flex min-h-6 w-full items-center justify-center">
              <span className="text-sm font-semibold text-muted-foreground">
                None
              </span>
            </div>
          ),
        [loading, size],
      )}
      label={
        <span className="flex items-center gap-1 text-sm text-muted-foreground hover:text-accent-foreground">
          <ListTreeIcon className="m-2 me-0 size-4" />
          <span>{placeholder}</span>
        </span>
      }
      {...props}
      size={size}
      loading={loading}
      disabled={disabled}
      variant={variant}
      align={align}
      useDialog={useDialog ?? false}
      data={data}
      value={value}
      onSelect={onSelect}
    />
  );
}

export default SelectOrganization;

export interface SelectOrganizationFieldProps<
  DataType extends PartialOrganization,
> extends SelectOrganizationProps<DataType> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectOrganizationField<DataType extends PartialOrganization>({
  loading = false,
  name = "organizationId",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  data,
  onSelect,
  ...props
}: SelectOrganizationFieldProps<DataType>) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectOrganization
              {...props}
              loading={loading}
              placeholder={placeholder}
              data={data}
              selection={data?.find((org) => org.id === field.value)}
              onSelect={async (org: DataType): Promise<void> => {
                if (onSelect) {
                  await onSelect(org);
                }

                field.onChange(org.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchOrganizationProps<DataType extends PartialOrganization>
  extends SelectorProps<DataType> {
  placeholder?: string;
  name?: string;
  group?: string;
  defaultValue?: string;
}

export function SearchOrganization<DataType extends PartialOrganization>({
  loading = false,
  placeholder = i18n.en.placeholder,
  name = "organization",
  group,
  data,
  defaultValue,
  ...props
}: SearchOrganizationProps<DataType>) {
  const { selection, onClear, onSelectionChange } = useSearchValue<DataType>({
    name,
    group,
    defaultValue,
    data,
  });
  return (
    <SelectOrganization
      {...props}
      loading={loading}
      placeholder={placeholder}
      data={data}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
