import type { ChangeE<PERSON>, DragEvent } from "react";

import { forwardRef, useCallback, useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { Upload, X } from "lucide-react";

import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Button } from "@/ui/primitives/button";
import { Progress } from "@/ui/primitives/progress";

const i18n = {
  en: {
    label: "Image Upload",
    description: "Upload an image file",
    placeholder: "Choose an image file or drag and drop",
    dragActive: "Drop the image file here",
    uploading: "Uploading...",
    preview: "Image Preview",
  },
};

export type AspectRatio = "square" | "portrait" | "landscape" | "wide" | "circle" | number;

export interface ImageControlProps {
  value?: File | string | null;
  onChange?: (event: ChangeEvent<HTMLInputElement> & { target: { value: File | null } }) => void;
  onFileSelect?: (file: File | null) => void;
  accept?: string;
  maxSize?: number; // in bytes
  disabled?: boolean;
  showPreview?: boolean;
  aspectRatio?: AspectRatio;
  progress?: number; // 0-100 for upload progress
  className?: string;
  placeholder?: string;
}

export const ImageControl = forwardRef<HTMLInputElement, ImageControlProps>(
  function ImageControl(
    {
      value,
      onChange,
      onFileSelect,
      accept = "image/*",
      maxSize,
      disabled = false,
      showPreview = true,
      aspectRatio = "square",
      progress,
      className,
      placeholder = i18n.en.placeholder,
    },
    ref,
  ) {
    const [isDragActive, setIsDragActive] = useState(false);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    const getAspectRatioClass = useCallback((ratio: AspectRatio): string => {
      if (typeof ratio === "number") {
        return "";
      }
      
      switch (ratio) {
        case "square":
          return "aspect-square";
        case "portrait":
          return "aspect-[3/4]";
        case "landscape":
          return "aspect-[4/3]";
        case "wide":
          return "aspect-[16/9]";
        case "circle":
          return "aspect-square rounded-full";
        default:
          return "aspect-square";
      }
    }, []);

    const getAspectRatioStyle = useCallback((ratio: AspectRatio) => {
      if (typeof ratio === "number") {
        return { aspectRatio: ratio.toString() };
      }
      return {};
    }, []);

    const handleFileChange = useCallback(
      (file: File | null) => {
        // Clean up previous preview URL
        if (previewUrl?.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
        }

        if (file) {
          // Validate file size if maxSize is provided
          if (maxSize !== undefined && file.size > maxSize) {
            // File too large, don't proceed
            return;
          }

          // Create preview URL for image files
          const url = URL.createObjectURL(file);
          setPreviewUrl(url);
        } else {
          setPreviewUrl(null);
        }

        // Call callbacks
        onFileSelect?.(file);
        onChange?.({
          target: { value: file },
        } as ChangeEvent<HTMLInputElement> & { target: { value: File | null } });
      },
      [maxSize, onChange, onFileSelect, previewUrl],
    );

    const handleInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] ?? null;
        handleFileChange(file);
      },
      [handleFileChange],
    );

    const handleDragEnter = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(true);
    }, []);

    const handleDragLeave = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(false);
    }, []);

    const handleDragOver = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
    }, []);

    const handleDrop = useCallback(
      (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragActive(false);

        const files = event.dataTransfer.files;
        if (files.length > 0) {
          const file = files[0];
          if (file && file.type.startsWith("image/")) {
            handleFileChange(file);
          }
        }
      },
      [handleFileChange],
    );

    const handleRemove = useCallback(() => {
      handleFileChange(null);
    }, [handleFileChange]);

    const currentFile = value instanceof File ? value : null;
    const currentUrl = typeof value === "string" ? value : previewUrl;
    const hasImage = currentFile ?? currentUrl;

    // Clean up on unmount
    useEffect(() => {
      return () => {
        if (previewUrl?.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
        }
      };
    }, [previewUrl]);

    return (
      <div className={cn("space-y-4", className)}>
        {/* Upload Area */}
        <div
          className={cn(
            "relative rounded-lg border-2 border-dashed p-6 transition-colors",
            "hover:bg-muted/50 focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
            isDragActive && "border-primary bg-primary/5",
            disabled && "cursor-not-allowed opacity-50",
            !isDragActive && "border-border",
          )}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <div className="text-center">
            <input
              ref={ref}
              type="file"
              accept={accept}
              onChange={handleInputChange}
              disabled={disabled}
              className="absolute inset-0 size-full cursor-pointer opacity-0 disabled:cursor-not-allowed"
            />
            
            <Upload className="mx-auto mb-2 size-8 text-muted-foreground" />
            
            <p className="mb-1 text-sm text-muted-foreground">
              {isDragActive ? i18n.en.dragActive : placeholder}
            </p>
            
            {maxSize && (
              <p className="text-xs text-muted-foreground">
                Max size: {(maxSize / (1024 * 1024)).toFixed(1)}MB
              </p>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {typeof progress === "number" && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">{i18n.en.uploading}</span>
              <span className="text-muted-foreground">{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* Image Preview */}
        {showPreview && hasImage && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">{i18n.en.preview}</h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleRemove}
                disabled={disabled}
                className="h-7 px-2"
              >
                <X className="mr-1 size-3" />
                Remove
              </Button>
            </div>
            
            <div className="relative overflow-hidden rounded-lg border bg-muted/10">
              <div 
                className={cn(
                  "relative mx-auto w-full max-w-xs",
                  getAspectRatioClass(aspectRatio)
                )}
                style={getAspectRatioStyle(aspectRatio)}
              >
                <img
                  src={currentUrl ?? ""}
                  alt="Preview"
                  className="absolute inset-0 size-full object-cover"
                  onError={() => {
                    // Handle image loading errors
                    setPreviewUrl(null);
                  }}
                />
              </div>
            </div>
            
            {/* File Information */}
            {currentFile && (
              <div className="space-y-1 text-xs text-muted-foreground">
                <div className="flex justify-between">
                  <span>Name:</span>
                  <span className="font-mono">{currentFile.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Size:</span>
                  <span className="font-mono">
                    {(currentFile.size / 1024).toFixed(1)} KB
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Type:</span>
                  <span className="font-mono">{currentFile.type}</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  },
);

export interface ImageFieldProps {
  name?: string;
  label?: string;
  description?: string;
  accept?: string;
  maxSize?: number;
  disabled?: boolean;
  showPreview?: boolean;
  aspectRatio?: AspectRatio;
  progress?: number;
  placeholder?: string;
  className?: string;
}

export function ImageField({
  name = "image",
  label = i18n.en.label,
  description = i18n.en.description,
  ...props
}: ImageFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ImageControl {...props} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
