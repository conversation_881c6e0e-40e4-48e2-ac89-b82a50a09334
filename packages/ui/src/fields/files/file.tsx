import type { <PERSON><PERSON><PERSON>, Drag<PERSON><PERSON>, ReactNode } from "react";

import { forwardRef, useCallback, useEffect, useState } from "react";
import {
  Archive,
  File,
  FileText,
  Image,
  Music,
  Upload,
  Video,
  X,
} from "lucide-react";
import { useFormContext } from "react-hook-form";

import { cn } from "@/ui/lib";
import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Progress } from "@/ui/primitives/progress";

const i18n = {
  en: {
    label: "File Upload",
    description: "Upload a file",
    placeholder: "Choose a file or drag and drop",
    dragActive: "Drop the file here",
    uploading: "Uploading...",
    preview: "File Preview",
    remove: "Remove",
    fileInfo: "File Information",
  },
};

export type FileType =
  | "image"
  | "video"
  | "audio"
  | "document"
  | "archive"
  | "any";

export interface FilePreviewProps {
  file: File;
  url?: string;
  className?: string;
  onError?: () => void;
}

export interface FileControlProps {
  value?: File | string | null;
  onChange?: (
    event: ChangeEvent<HTMLInputElement> & { target: { value: File | null } },
  ) => void;
  onFileSelect?: (file: File | null) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  disabled?: boolean;
  showPreview?: boolean;
  _fileType?: FileType;
  progress?: number; // 0-100 for upload progress
  className?: string;
  placeholder?: string;
  dragActiveText?: string;
  allowedTypes?: string[];
  renderPreview?: (props: FilePreviewProps) => ReactNode;
  renderFileInfo?: (file: File) => ReactNode;
  showFileIcon?: boolean;
  compact?: boolean;
}

// File type detection utilities
const getFileIcon = (file: File) => {
  const type = file.type.toLowerCase();

  if (type.startsWith("image/")) return Image;
  if (type.startsWith("video/")) return Video;
  if (type.startsWith("audio/")) return Music;
  if (
    type.includes("pdf") ||
    type.includes("document") ||
    type.includes("text")
  )
    return FileText;
  if (type.includes("zip") || type.includes("rar") || type.includes("tar"))
    return Archive;

  return File;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Default preview component for different file types
const DefaultFilePreview = ({
  file,
  url,
  className,
  onError,
}: FilePreviewProps) => {
  const type = file.type.toLowerCase();

  if (type.startsWith("image/")) {
    return (
      <img
        src={url}
        alt={file.name}
        className={cn("max-h-48 max-w-full rounded object-contain", className)}
        onError={onError}
      />
    );
  }

  if (type.startsWith("video/")) {
    return (
      <video
        src={url}
        controls
        className={cn("max-h-48 max-w-full rounded", className)}
        onError={onError}
      >
        Your browser does not support the video element.
      </video>
    );
  }

  if (type.startsWith("audio/")) {
    return (
      <audio
        src={url}
        controls
        className={cn("w-full max-w-sm", className)}
        onError={onError}
      >
        Your browser does not support the audio element.
      </audio>
    );
  }

  // For other file types, show file icon and info
  const IconComponent = getFileIcon(file);
  return (
    <div
      className={cn(
        "flex items-center space-x-3 rounded-lg border bg-muted/20 p-4",
        className,
      )}
    >
      <IconComponent className="size-8 text-muted-foreground" />
      <div className="min-w-0 flex-1">
        <p className="truncate text-sm font-medium">{file.name}</p>
        <p className="text-xs text-muted-foreground">
          {formatFileSize(file.size)}
        </p>
      </div>
    </div>
  );
};

// Default file info component
const DefaultFileInfo = ({ file }: { file: File }) => (
  <div className="space-y-1 text-xs text-muted-foreground">
    <div className="flex justify-between">
      <span>Name:</span>
      <span className="ml-2 max-w-[200px] truncate font-mono" title={file.name}>
        {file.name}
      </span>
    </div>
    <div className="flex justify-between">
      <span>Size:</span>
      <span className="font-mono">{formatFileSize(file.size)}</span>
    </div>
    <div className="flex justify-between">
      <span>Type:</span>
      <span className="font-mono">{file.type || "Unknown"}</span>
    </div>
    {file.lastModified && (
      <div className="flex justify-between">
        <span>Modified:</span>
        <span className="font-mono text-xs">
          {new Date(file.lastModified).toLocaleDateString()}
        </span>
      </div>
    )}
  </div>
);

export const FileControl = forwardRef<HTMLInputElement, FileControlProps>(
  function FileControl(
    {
      value,
      onChange,
      onFileSelect,
      accept = "*/*",
      multiple = false,
      maxSize,
      disabled = false,
      showPreview = true,
      _fileType = "any",
      progress,
      className,
      placeholder = i18n.en.placeholder,
      dragActiveText = i18n.en.dragActive,
      allowedTypes = [],
      renderPreview,
      renderFileInfo,
      showFileIcon = true,
      compact = false,
    },
    ref,
  ) {
    const [isDragActive, setIsDragActive] = useState(false);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [validationError, setValidationError] = useState<string | null>(null);

    const validateFile = useCallback(
      (file: File): string | null => {
        // Size validation
        if (maxSize && file.size > maxSize) {
          return `File size must be less than ${formatFileSize(maxSize)}`;
        }

        // Type validation
        if (
          allowedTypes.length > 0 &&
          !allowedTypes.some((type) => file.type.includes(type))
        ) {
          return `File type not allowed. Allowed types: ${allowedTypes.join(", ")}`;
        }

        return null;
      },
      [maxSize, allowedTypes],
    );

    const handleFileChange = useCallback(
      (file: File | null) => {
        setValidationError(null);

        // Clean up previous preview URL
        if (previewUrl?.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
        }

        if (file) {
          // Validate file
          const error = validateFile(file);
          if (error) {
            setValidationError(error);
            return;
          }

          // Create preview URL for supported file types
          if (
            file.type.startsWith("image/") ||
            file.type.startsWith("video/") ||
            file.type.startsWith("audio/")
          ) {
            const url = URL.createObjectURL(file);
            setPreviewUrl(url);
          }
        } else {
          setPreviewUrl(null);
        }

        // Call callbacks
        onFileSelect?.(file);
        onChange?.({
          target: { value: file },
        } as ChangeEvent<HTMLInputElement> & {
          target: { value: File | null };
        });
      },
      [onChange, onFileSelect, previewUrl, validateFile],
    );

    const handleInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] ?? null;
        handleFileChange(file);
      },
      [handleFileChange],
    );

    const handleDragEnter = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(true);
    }, []);

    const handleDragLeave = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragActive(false);
    }, []);

    const handleDragOver = useCallback((event: DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
    }, []);

    const handleDrop = useCallback(
      (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragActive(false);

        const files = event.dataTransfer.files;
        if (files.length > 0) {
          const file = files[0]!;
          handleFileChange(file);
        }
      },
      [handleFileChange],
    );

    const handleRemove = useCallback(() => {
      handleFileChange(null);
    }, [handleFileChange]);

    const currentFile =
      value && typeof File !== "undefined" && value instanceof File
        ? value
        : null;
    const currentUrl = typeof value === "string" ? value : previewUrl;
    const hasFile = currentFile ?? currentUrl;

    // Clean up on unmount
    useEffect(() => {
      return () => {
        if (previewUrl?.startsWith("blob:")) {
          URL.revokeObjectURL(previewUrl);
        }
      };
    }, [previewUrl]);

    const FileIconComponent = currentFile ? getFileIcon(currentFile) : Upload;

    return (
      <div className={cn("space-y-4", className)}>
        {/* Upload Area */}
        <div
          className={cn(
            "relative rounded-lg border-2 border-dashed transition-colors",
            "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 hover:bg-muted/50",
            isDragActive && "border-primary bg-primary/5",
            disabled && "cursor-not-allowed opacity-50",
            !isDragActive && "border-border",
            validationError && "border-destructive bg-destructive/5",
            compact ? "p-4" : "p-6",
          )}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <div className={cn("text-center", compact && "space-y-1")}>
            <input
              ref={ref}
              type="file"
              accept={accept}
              multiple={multiple}
              onChange={handleInputChange}
              disabled={disabled}
              className="absolute inset-0 size-full cursor-pointer opacity-0 disabled:cursor-not-allowed"
            />

            {showFileIcon && (
              <FileIconComponent
                className={cn(
                  "mx-auto text-muted-foreground",
                  compact ? "mb-1 size-6" : "mb-2 size-8",
                )}
              />
            )}

            <p
              className={cn(
                "text-muted-foreground",
                compact ? "text-xs" : "mb-1 text-sm",
              )}
            >
              {isDragActive ? dragActiveText : placeholder}
            </p>

            {!compact && (maxSize ?? allowedTypes.length > 0) && (
              <div className="space-y-1">
                {maxSize && (
                  <p className="text-xs text-muted-foreground">
                    Max size: {formatFileSize(maxSize)}
                  </p>
                )}
                {allowedTypes.length > 0 && (
                  <div className="flex flex-wrap justify-center gap-1">
                    {allowedTypes.slice(0, 3).map((type, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {type}
                      </Badge>
                    ))}
                    {allowedTypes.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{allowedTypes.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Validation Error */}
        {validationError && (
          <div className="text-sm text-destructive">{validationError}</div>
        )}

        {/* Progress Bar */}
        {typeof progress === "number" && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">{i18n.en.uploading}</span>
              <span className="text-muted-foreground">{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* File Preview */}
        {showPreview && hasFile && !validationError && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4
                className={cn("font-medium", compact ? "text-sm" : "text-sm")}
              >
                {i18n.en.preview}
              </h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleRemove}
                disabled={disabled}
                className="h-7 px-2"
              >
                <X className="mr-1 size-3" />
                {compact ? "" : i18n.en.remove}
              </Button>
            </div>

            <div className="space-y-3">
              {/* Custom or default preview */}
              {currentFile && (
                <div className="flex justify-center">
                  {renderPreview ? (
                    renderPreview({
                      file: currentFile,
                      url: currentUrl ?? undefined,
                    })
                  ) : (
                    <DefaultFilePreview
                      file={currentFile}
                      url={currentUrl ?? undefined}
                      onError={() => setPreviewUrl(null)}
                    />
                  )}
                </div>
              )}

              {/* File Information */}
              {currentFile && !compact && (
                <div>
                  <h5 className="mb-2 text-sm font-medium">
                    {i18n.en.fileInfo}
                  </h5>
                  {renderFileInfo ? (
                    renderFileInfo(currentFile)
                  ) : (
                    <DefaultFileInfo file={currentFile} />
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  },
);

export interface FileFieldProps
  extends Omit<FileControlProps, "value" | "onChange"> {
  name?: string;
  label?: string;
  description?: string;
}

export function FileField({
  name = "file",
  label = i18n.en.label,
  description = i18n.en.description,
  ...props
}: FileFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <FileControl {...props} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
