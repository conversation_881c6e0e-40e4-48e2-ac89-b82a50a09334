import type { ChangeEvent, DragEvent } from "react";

import { forwardRef, useCallback, useState } from "react";
import { Pause, Play, Upload, X } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Progress } from "@/ui/primitives/progress";

const i18n = {
  en: {
    label: "Video Upload",
    description: "Upload a video file",
    placeholder: "Choose a video file or drag and drop",
    dragActive: "Drop the video file here",
    uploading: "Uploading...",
    preview: "Video Preview",
  },
};

export interface VideoControlProps {
  value?: File | string | null;
  onChange?: (
    event: ChangeEvent<HTMLInputElement> & { target: { value: File | null } },
  ) => void;
  onFileSelect?: (file: File | null) => void;
  accept?: string;
  maxSize?: number; // in bytes
  disabled?: boolean;
  showPreview?: boolean;
  progress?: number; // 0-100 for upload progress
  className?: string;
  placeholder?: string;
}

export const VideoControl = forwardRef<HTMLInputElement, VideoControlProps>(
  function VideoControl(
    {
      value,
      onChange,
      onFileSelect,
      accept = "video/*",
      maxSize,
      disabled = false,
      showPreview = true,
      progress,
      className,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) {
    const [isDragActive, setIsDragActive] = useState(false);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const handleFileChange = useCallback(
      (file: File | null) => {
        // Clean up previous preview URL
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl(null);
        }

        if (file) {
          // Create preview URL for video
          const url = URL.createObjectURL(file);
          setPreviewUrl(url);
        }

        // Call callbacks
        onFileSelect?.(file);

        if (onChange) {
          const event = {
            target: { value: file },
          } as ChangeEvent<HTMLInputElement> & {
            target: { value: File | null };
          };
          onChange(event);
        }
      },
      [onChange, onFileSelect, previewUrl],
    );

    const handleInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] ?? null;

        // Check file size if maxSize is specified
        if (file && maxSize && file.size > maxSize) {
          console.warn(`File size ${file.size} exceeds maximum ${maxSize}`);
          return;
        }

        handleFileChange(file);
      },
      [handleFileChange, maxSize],
    );

    const handleDragOver = useCallback(
      (event: DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (!disabled) {
          setIsDragActive(true);
        }
      },
      [disabled],
    );

    const handleDragLeave = useCallback((event: DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragActive(false);
    }, []);

    const handleDrop = useCallback(
      (event: DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDragActive(false);

        if (disabled) return;

        const files = Array.from(event.dataTransfer.files);
        const videoFile = files.find((file) => file.type.startsWith("video/"));

        if (videoFile) {
          // Check file size if maxSize is specified
          if (maxSize && videoFile.size > maxSize) {
            console.warn(
              `File size ${videoFile.size} exceeds maximum ${maxSize}`,
            );
            return;
          }
          handleFileChange(videoFile);
        }
      },
      [disabled, handleFileChange, maxSize],
    );

    const handleRemove = useCallback(() => {
      handleFileChange(null);
    }, [handleFileChange]);

    const togglePlayback = useCallback(() => {
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
      const video = document.querySelector(
        "video[data-video-preview]",
      ) as HTMLVideoElement | null;
      if (!video) return;

      if (isPlaying) {
        video.pause();
      } else {
        const playPromise = video.play();
        void playPromise.catch(() => {
          // Handle potential play failure silently
        });
      }
      setIsPlaying(!isPlaying);
    }, [isPlaying]);

    const currentFile = value instanceof File ? value : null;
    const currentPreviewUrl =
      previewUrl ?? (typeof value === "string" ? value : null);
    const hasFile = Boolean(currentFile ?? currentPreviewUrl);

    return (
      <div className={cn("space-y-4", className)}>
        {/* File Input Area */}
        <div
          className={cn(
            "relative rounded-lg border-2 border-dashed p-6 transition-colors",
            isDragActive
              ? "border-primary bg-primary/10"
              : "border-gray-300 hover:border-gray-400",
            disabled && "cursor-not-allowed opacity-50",
            hasFile && "border-solid border-gray-200",
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            {...props}
            ref={ref}
            type="file"
            accept={accept}
            onChange={handleInputChange}
            disabled={disabled}
            className="absolute inset-0 size-full cursor-pointer opacity-0 disabled:cursor-not-allowed"
          />

          <div className="flex flex-col items-center justify-center text-center">
            <Upload className="mb-2 size-10 text-gray-400" />
            <p className="text-sm text-gray-600">
              {isDragActive ? i18n.en.dragActive : placeholder}
            </p>
            {maxSize && (
              <p className="mt-1 text-xs text-gray-500">
                Max size: {(maxSize / (1024 * 1024)).toFixed(1)}MB
              </p>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {progress !== undefined && progress >= 0 && progress <= 100 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{i18n.en.uploading}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* Video Preview */}
        {showPreview && hasFile && currentPreviewUrl && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{i18n.en.preview}</span>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={togglePlayback}
                  className="size-8 p-0"
                >
                  {isPlaying ? (
                    <Pause className="size-4" />
                  ) : (
                    <Play className="size-4" />
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleRemove}
                  className="size-8 p-0"
                  disabled={disabled}
                >
                  <X className="size-4" />
                </Button>
              </div>
            </div>
            <div className="relative overflow-hidden rounded-lg bg-black">
              <video
                data-video-preview
                src={currentPreviewUrl}
                className="h-auto max-h-64 w-full object-contain"
                controls
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
              >
                Your browser does not support the video tag.
              </video>
            </div>
            {currentFile && (
              <div className="space-y-1 text-xs text-gray-500">
                <p>Name: {currentFile.name}</p>
                <p>Size: {(currentFile.size / (1024 * 1024)).toFixed(2)} MB</p>
                <p>Type: {currentFile.type}</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  },
);

export default VideoControl;

export interface VideoFieldProps extends VideoControlProps {
  name?: string;
  label?: string;
  description?: string;
}

export function VideoField({
  name = "video",
  label = i18n.en.label,
  description = i18n.en.description,
  ...props
}: VideoFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <VideoControl {...props} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
