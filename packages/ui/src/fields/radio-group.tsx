"use client";

import * as React from "react";
import { useFormContext } from "react-hook-form";

import { RadioGroup, RadioGroupItem, RadioGroupCardItem } from "@/ui/primitives/radio-group";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { cn } from "@/ui/lib";

// Option interface for radio group items
export interface RadioOption {
  /** Option value */
  value: string;
  /** Option label */
  label: React.ReactNode;
  /** Whether this option is disabled */
  disabled?: boolean;
  /** Optional description for the option */
  description?: React.ReactNode;
}

// RadioGroupControl Props
export interface RadioGroupControlProps
  extends Omit<React.ComponentPropsWithoutRef<typeof RadioGroup>, 'value' | 'onValueChange'> {
  /** Current selected value */
  value?: string;
  /** Default selected value (for uncontrolled usage) */
  defaultValue?: string;
  /** Callback when selection changes */
  onValueChange?: (value: string) => void;
  /** Array of radio options */
  options: RadioOption[];
  /** Whether the radio group is disabled */
  disabled?: boolean;
  /** Whether the radio group is required */
  required?: boolean;
  /** Custom className for styling */
  className?: string;
  /** Custom className for individual radio items */
  itemClassName?: string;
  /** Layout orientation */
  orientation?: "vertical" | "horizontal";
  /** Use card-style radio items */
  variant?: "default" | "card";
}

/**
 * RadioGroupControl - A versatile, presentational radio group component
 * 
 * Features:
 * - Controlled and uncontrolled modes
 * - Disabled state (for entire group or individual options)
 * - Card and default variants
 * - Horizontal and vertical layouts
 * - Custom styling support
 * - Accessibility built-in via Radix
 */
export const RadioGroupControl = React.forwardRef<
  React.ComponentRef<typeof RadioGroup>,
  RadioGroupControlProps
>(({ 
  className, 
  itemClassName,
  options, 
  disabled, 
  orientation = "vertical",
  variant = "default",
  ...props 
}, ref) => {
  const RadioItem = variant === "card" ? RadioGroupCardItem : RadioGroupItem;

  return (
    <RadioGroup
      ref={ref}
      className={cn(
        orientation === "horizontal" && "flex flex-row space-x-4",
        className
      )}
      disabled={disabled}
      {...props}
    >
      {options.map((option) => (
        <div key={option.value} className="flex items-center space-x-2">
          <RadioItem
            value={option.value}
            id={option.value}
            disabled={disabled || option.disabled}
            className={cn(itemClassName)}
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor={option.value}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              {option.label}
            </label>
            {option.description && (
              <p className="text-xs text-muted-foreground">
                {option.description}
              </p>
            )}
          </div>
        </div>
      ))}
    </RadioGroup>
  );
});

RadioGroupControl.displayName = "RadioGroupControl";

// RadioGroupField Props
export interface RadioGroupFieldProps {
  /** Form field name */
  name?: string;
  /** Field label */
  label?: React.ReactNode;
  /** Field description */
  description?: React.ReactNode;
  /** Array of radio options */
  options: RadioOption[];
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Whether the field is required */
  required?: boolean;
  /** Custom className for the form item */
  className?: string;
  /** Custom className for the radio group */
  radioGroupClassName?: string;
  /** Custom className for individual radio items */
  itemClassName?: string;
  /** Layout orientation */
  orientation?: "vertical" | "horizontal";
  /** Use card-style radio items */
  variant?: "default" | "card";
  /** Additional props to pass to RadioGroupControl */
  radioGroupProps?: Omit<RadioGroupControlProps, 'value' | 'onValueChange' | 'options'>;
}

/**
 * RadioGroupField - Form-integrated radio group field component
 * 
 * Integrates RadioGroupControl with React Hook Form for:
 * - Form validation and error handling
 * - Consistent field layout and labeling
 * - Accessible form structure
 */
export function RadioGroupField({
  name = "radioGroup",
  label,
  description,
  options,
  disabled,
  required,
  className,
  radioGroupClassName,
  itemClassName,
  orientation = "vertical",
  variant = "default",
  radioGroupProps,
}: RadioGroupFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("space-y-3", className)}>
          {label && (
            <FormLabel className={cn(
              required && "after:content-['*'] after:ml-0.5 after:text-destructive"
            )}>
              {label}
            </FormLabel>
          )}
          <FormControl>
            <RadioGroupControl
              value={field.value}
              onValueChange={field.onChange}
              options={options}
              disabled={disabled}
              required={required}
              orientation={orientation}
              variant={variant}
              className={radioGroupClassName}
              itemClassName={itemClassName}
              {...radioGroupProps}
            />
          </FormControl>
          {description && (
            <FormDescription>
              {description}
            </FormDescription>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
