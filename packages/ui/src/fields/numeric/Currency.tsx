import type { ChangeEvent, Ref } from "react";

import { forwardRef, useCallback } from "react";
import { maskitoTransform } from "@maskito/core";
import { maskitoNumberOptionsGenerator } from "@maskito/kit";
import { useMaskito } from "@maskito/react";
import { mergeRefs } from "@react-aria/utils";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    label: "Amount",
    description: "The amount",
    placeholder: "Enter the amount",
  },
};

const mask = maskitoNumberOptionsGenerator({
  decimalZeroPadding: true,
  precision: 2,
  decimalSeparator: ".",
  thousandSeparator: ",",
  min: 0,
  prefix: "$",
});

export interface CurrencyProps extends InputProps {
  placeholder?: string;
}

export const Currency = forwardRef<HTMLInputElement, CurrencyProps>(
  function CurrencyCombined({ value, onChange, ...props }, ref) {
    const inputRef = useMaskito({ options: mask });

    const onInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const { value = "" } = event.target;
        const number = parseFloat(value.replace(/[$,]/g, ""));
        const nextValue = isNaN(number) ? "0" : number.toString();
        onChange?.({
          ...event,
          target: {
            ...event.target,
            value: nextValue,
          },
        });
      },
      [onChange],
    );

    return (
      <Input
        {...props}
        ref={mergeRefs(inputRef, ref) as Ref<HTMLInputElement>}
        value={maskitoTransform((value ?? 0).toString(), mask)}
        onInput={onInputChange}
        onChange={onInputChange}
      />
    );
  },
);

export default Currency;

export interface CurrencyFieldProps extends CurrencyProps {
  name?: string;
  label?: string;
  description?: string;
}

export function CurrencyField({
  name = "amount",
  label = i18n.en.label,
  description = i18n.en.description,
  onChange,
  ...props
}: CurrencyFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <Currency
              {...props}
              {...field}
              onChange={(e) => {
                field.onChange(e);
                onChange?.(e);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
