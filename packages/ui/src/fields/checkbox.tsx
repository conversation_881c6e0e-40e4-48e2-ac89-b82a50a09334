"use client";

import * as React from "react";
import { useFormContext } from "react-hook-form";

import { Checkbox } from "@/ui/primitives/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { cn } from "@/ui/lib";

// CheckboxControl Props
export interface CheckboxControlProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Checkbox>, 'checked' | 'onCheckedChange'> {
  /** Current checked state */
  checked?: boolean;
  /** Default checked state (for uncontrolled usage) */
  defaultChecked?: boolean;
  /** Callback when checked state changes */
  onCheckedChange?: (checked: boolean) => void;
  /** Whether the checkbox is disabled */
  disabled?: boolean;
  /** Whether the checkbox is required */
  required?: boolean;
  /** Whether the checkbox is in an indeterminate state */
  indeterminate?: boolean;
  /** Custom className for styling */
  className?: string;
}

/**
 * CheckboxControl - A versatile, presentational checkbox component
 * 
 * Features:
 * - Controlled and uncontrolled modes
 * - Indeterminate state support
 * - Disabled state
 * - Custom styling support
 * - Accessibility built-in via Radix
 */
export const CheckboxControl = React.forwardRef<
  React.ComponentRef<typeof Checkbox>,
  CheckboxControlProps
>(({ className, indeterminate, ...props }, ref) => {
  return (
    <Checkbox
      ref={ref}
      className={cn(
        // Add any additional styling for indeterminate state if needed
        indeterminate ? "data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground" : "",
        className
      )}
      {...props}
    />
  );
});

CheckboxControl.displayName = "CheckboxControl";

// CheckboxField Props
export interface CheckboxFieldProps {
  /** Form field name */
  name?: string;
  /** Field label */
  label?: React.ReactNode;
  /** Field description */
  description?: React.ReactNode;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Whether the field is required */
  required?: boolean;
  /** Custom className for the form item */
  className?: string;
  /** Custom className for the checkbox control */
  checkboxClassName?: string;
  /** Label position relative to checkbox */
  labelPosition?: "right" | "left";
  /** Additional props to pass to CheckboxControl */
  checkboxProps?: Omit<CheckboxControlProps, 'checked' | 'onCheckedChange'>;
}

/**
 * CheckboxField - Form-integrated checkbox field component
 * 
 * Integrates CheckboxControl with React Hook Form for:
 * - Form validation and error handling
 * - Consistent field layout and labeling
 * - Accessible form structure
 */
export function CheckboxField({
  name = "checkbox",
  label,
  description,
  disabled,
  required,
  className,
  checkboxClassName,
  labelPosition = "right",
  checkboxProps,
}: CheckboxFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem
          className={cn(
            "flex flex-row items-start space-x-3 space-y-0",
            labelPosition === "left" && "flex-row-reverse space-x-reverse",
            className
          )}
        >
          <FormControl>
            <CheckboxControl
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
              required={required}
              className={checkboxClassName}
              {...checkboxProps}
            />
          </FormControl>
          <div className="space-y-1 leading-none">
            {label && (
              <FormLabel className={cn(
                "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                required && "after:content-['*'] after:ml-0.5 after:text-destructive"
              )}>
                {label}
              </FormLabel>
            )}
            {description && (
              <FormDescription className="text-sm text-muted-foreground">
                {description}
              </FormDescription>
            )}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
