"use client";

import type { ComponentPropsWithoutRef } from "react";

import { useCallback, useState } from "react";
import { useFormContext } from "react-hook-form";
import { z } from "zod";

import useGooglePlaces from "@/ui/hooks/google-places";
import { cn } from "@/ui/lib";
import { normalizeGeocodeResults } from "@/ui/lib/google";
import { Card } from "@/ui/primitives/card";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/ui/primitives/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";

const i18n = {
  en: {
    label: "Address",
    description: "Search for an address",
    placeholder: "Type an address...",
    empty: "No results found",
    errors: {
      required: "Please enter an address",
    },
  },
};

export const zAddress = z.object({
  formatted: z.string({
    required_error: i18n.en.errors.required,
  }),
  country: z.string(),
  state: z.string().optional(),
  city: z.string().optional(),
  street: z.string().optional(),
  postal: z.string().optional(),
  latitude: z.number(),
  longitude: z.number(),
});

export interface AddressAutocompleteLocation {
  formatted: string;
  country: string;
  state?: string;
  city?: string;
  street?: string;
  postal?: string;
  latitude: number;
  longitude: number;
}

export interface AddressAutocompleteProps
  extends Omit<
    ComponentPropsWithoutRef<typeof CommandInput>,
    "value" | "onValueChange" | "onSelect"
  > {
  asDialog?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSelect?: (location: AddressAutocompleteLocation) => Promise<void> | void;
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
  empty?: string;
}

export default function AddressAutocomplete({
  asDialog = false,
  open: controlledOpen,
  onOpenChange: onControlledOpenChange,
  onSelect,
  defaultValue = "",
  value: controlledValue = "",
  onValueChange: onControlledValueChange,
  className,
  placeholder = i18n.en.placeholder,
  empty = i18n.en.empty,
  onFocus,
  onBlur,
  ...props
}: AddressAutocompleteProps) {
  const [internalValue, setInternalValue] = useState(defaultValue);
  const [selection, setSelection] = useState(defaultValue || "");
  const [isOpen, setIsOpen] = useState(false);

  const query = controlledValue || internalValue;
  const setQuery = onControlledValueChange ?? setInternalValue;
  const open = controlledOpen ?? isOpen;
  const onOpenChange = onControlledOpenChange ?? setIsOpen;

  const { results, fetching, loading, geocode } = useGooglePlaces(query);

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const predictions = results.predictions ?? [];

  const handleSelect = useCallback(
    async (address: google.maps.places.AutocompletePrediction) => {
      setSelection(address.description);
      setQuery(address.description);
      const geocodeResult = await geocode(address.description);
      const results = normalizeGeocodeResults(geocodeResult);
      if (results && onSelect) {
        setSelection(results.address.formatted ?? "");
        setQuery(results.address.formatted ?? "");
        await onSelect({
          formatted: results.address.formatted ?? "",
          country: results.address.country ?? "",
          state: results.address.state ?? "",
          city: results.address.city ?? "",
          street: results.address.street ?? "",
          postal: results.address.postal ?? "",
          latitude: results.coordinates.latitude ?? 0,
          longitude: results.coordinates.longitude ?? 0,
        });
      }
      onOpenChange(false);
    },
    [geocode, onSelect, onOpenChange, setQuery],
  );

  const input = (
    <CommandInput
      {...props}
      placeholder={placeholder}
      value={query}
      onValueChange={setQuery}
      className="w-full"
      onFocus={(event) => {
        onFocus?.(event);
        setIsOpen(true);
      }}
      onBlur={(event) => {
        onBlur?.(event);
        setIsOpen(false);
      }}
    />
  );

  const list = (
    <CommandList
      className={cn("z-50 w-full rounded-lg bg-background", {
        "border-none": !asDialog,
      })}
    >
      {loading || fetching ? (
        <CommandEmpty>Loading...</CommandEmpty>
      ) : (
        <CommandEmpty>
          {selection !== query && query !== "" ? empty : placeholder}
        </CommandEmpty>
      )}
      {predictions.map((address) => (
        <CommandItem
          key={address.place_id}
          onSelect={() => handleSelect(address)}
        >
          {address.description}
        </CommandItem>
      ))}
    </CommandList>
  );

  if (asDialog) {
    return (
      <CommandDialog open={open} onOpenChange={onOpenChange}>
        {input}
        {list}
      </CommandDialog>
    );
  }

  return (
    <Card
      className={cn(
        "w-full bg-transparent transition-all focus-within:outline-none focus-within:ring-2 focus-within:ring-ring",
        className,
      )}
    >
      <Command
        label="Search for an address"
        className="relative z-50 w-full overflow-visible"
      >
        {input}
        <div
          className={cn(
            "absolute mt-12 max-h-60 w-full overflow-y-auto rounded-lg bg-transparent shadow-xl",
            {
              hidden: selection === query,
            },
          )}
        >
          {list}
        </div>
      </Command>
    </Card>
  );
}
export interface AddressAutocompleteFieldProps
  extends AddressAutocompleteProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  empty?: string;
}

export function AddressAutocompleteField({
  name = "address.formatted",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  empty = i18n.en.empty,
  ...props
}: AddressAutocompleteFieldProps) {
  const form = useFormContext<{
    address: z.infer<typeof zAddress>;
  }>();

  const handleAddressSelect = useCallback(
    (location: AddressAutocompleteLocation) => {
      form.setValue("address", location, { shouldValidate: true });
    },
    [form],
  );

  return (
    <FormField
      control={form.control}
      name={name as "address" | "address.formatted"}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <AddressAutocomplete
              {...props}
              className="w-full"
              placeholder={placeholder}
              empty={empty}
              onBlur={field.onBlur}
              value={field.value as string}
              defaultValue={field.value as string}
              onValueChange={field.onChange}
              onSelect={handleAddressSelect}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
