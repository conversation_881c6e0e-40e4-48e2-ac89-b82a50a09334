"use client";

import * as React from "react";
import { useFormContext } from "react-hook-form";

import { Switch } from "@/ui/primitives/switch";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { cn } from "@/ui/lib";

// SwitchControl Props
export interface SwitchControlProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Switch>, 'checked' | 'onCheckedChange'> {
  /** Current checked/on state */
  checked?: boolean;
  /** Default checked state (for uncontrolled usage) */
  defaultChecked?: boolean;
  /** Callback when checked state changes */
  onCheckedChange?: (checked: boolean) => void;
  /** Whether the switch is disabled */
  disabled?: boolean;
  /** Whether the switch is required */
  required?: boolean;
  /** Custom className for styling */
  className?: string;
}

/**
 * SwitchControl - A versatile, presentational switch component
 * 
 * Features:
 * - Controlled and uncontrolled modes
 * - Disabled state
 * - Custom styling support
 * - Accessibility built-in via Radix
 */
export const SwitchControl = React.forwardRef<
  React.ComponentRef<typeof Switch>,
  SwitchControlProps
>(({ className, ...props }, ref) => {
  return (
    <Switch
      ref={ref}
      className={cn(className)}
      {...props}
    />
  );
});

SwitchControl.displayName = "SwitchControl";

// SwitchField Props
export interface SwitchFieldProps {
  /** Form field name */
  name?: string;
  /** Field label */
  label?: React.ReactNode;
  /** Field description */
  description?: React.ReactNode;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Whether the field is required */
  required?: boolean;
  /** Custom className for the form item */
  className?: string;
  /** Custom className for the switch control */
  switchClassName?: string;
  /** Label position relative to switch */
  labelPosition?: "right" | "left";
  /** Additional props to pass to SwitchControl */
  switchProps?: Omit<SwitchControlProps, 'checked' | 'onCheckedChange'>;
}

/**
 * SwitchField - Form-integrated switch field component
 * 
 * Integrates SwitchControl with React Hook Form for:
 * - Form validation and error handling
 * - Consistent field layout and labeling
 * - Accessible form structure
 */
export function SwitchField({
  name = "switch",
  label,
  description,
  disabled,
  required,
  className,
  switchClassName,
  labelPosition = "right",
  switchProps,
}: SwitchFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem
          className={cn(
            "flex flex-row items-center space-x-3 space-y-0",
            labelPosition === "left" && "flex-row-reverse space-x-reverse",
            className
          )}
        >
          <FormControl>
            <SwitchControl
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
              required={required}
              className={switchClassName}
              {...switchProps}
            />
          </FormControl>
          <div className="space-y-1 leading-none">
            {label && (
              <FormLabel className={cn(
                "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                required && "after:content-['*'] after:ml-0.5 after:text-destructive"
              )}>
                {label}
              </FormLabel>
            )}
            {description && (
              <FormDescription className="text-sm text-muted-foreground">
                {description}
              </FormDescription>
            )}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
