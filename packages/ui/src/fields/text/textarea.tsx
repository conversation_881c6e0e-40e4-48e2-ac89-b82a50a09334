import { forwardRef, useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

import type { TextareaProps } from "@/ui/primitives/textarea";

import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Textarea } from "@/ui/primitives/textarea";

const i18n = {
  en: {
    label: "Description",
    description: "Enter a description",
    placeholder: "Type your message here...",
    charactersRemaining: "characters remaining",
    charactersOver: "characters over limit",
  },
};

export interface TextareaInputProps extends TextareaProps {
  maxLength?: number;
  showCharacterCount?: boolean;
  autoResize?: boolean;
  minRows?: number;
  maxRows?: number;
}

export const TextareaInput = forwardRef<
  HTMLTextAreaElement,
  TextareaInputProps
>(
  (
    {
      className,
      maxLength,
      showCharacterCount = false,
      autoResize = false,
      minRows = 3,
      maxRows = 10,
      onChange,
      ...props
    },
    ref,
  ) => {
    const [characterCount, setCharacterCount] = useState(0);
    const internalRef = useRef<HTMLTextAreaElement>(null);
    const textareaRef = ref || internalRef;

    const adjustHeight = () => {
      const textarea = (textareaRef as React.RefObject<HTMLTextAreaElement>)
        .current;
      if (!textarea || !autoResize) return;

      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto";

      // Calculate the line height
      const computedStyle = window.getComputedStyle(textarea);
      const lineHeight = parseInt(computedStyle.lineHeight, 10);
      const paddingTop = parseInt(computedStyle.paddingTop, 10);
      const paddingBottom = parseInt(computedStyle.paddingBottom, 10);

      // Calculate min and max heights based on rows
      const minHeight = lineHeight * minRows + paddingTop + paddingBottom;
      const maxHeight = lineHeight * maxRows + paddingTop + paddingBottom;

      // Set the new height within bounds
      const newHeight = Math.min(
        Math.max(textarea.scrollHeight, minHeight),
        maxHeight,
      );
      textarea.style.height = `${newHeight}px`;
    };

    useEffect(() => {
      adjustHeight();
    }, [props.value]);

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      setCharacterCount(value.length);

      if (autoResize) {
        adjustHeight();
      }

      onChange?.(e);
    };

    const isOverLimit = maxLength ? characterCount > maxLength : false;
    const remainingCharacters = maxLength ? maxLength - characterCount : 0;

    return (
      <div className="space-y-2">
        <Textarea
          {...props}
          ref={textareaRef}
          className={cn(
            autoResize && "resize-none overflow-hidden",
            isOverLimit && "border-destructive focus-visible:ring-destructive",
            className,
          )}
          maxLength={maxLength}
          onChange={handleChange}
          style={{
            minHeight: autoResize ? `${minRows * 1.5}rem` : undefined,
            ...props.style,
          }}
        />
        {showCharacterCount && maxLength && (
          <div className="flex justify-end">
            <span
              className={cn(
                "text-xs",
                isOverLimit ? "text-destructive" : "text-muted-foreground",
              )}
            >
              {isOverLimit ? (
                <>
                  {Math.abs(remainingCharacters)} {i18n.en.charactersOver}
                </>
              ) : (
                <>
                  {remainingCharacters} {i18n.en.charactersRemaining}
                </>
              )}
            </span>
          </div>
        )}
      </div>
    );
  },
);

TextareaInput.displayName = "TextareaInput";

export interface TextareaFieldProps extends TextareaInputProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
}

export const TextareaField = forwardRef<
  HTMLTextAreaElement,
  TextareaFieldProps
>(
  (
    {
      name = "description",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <TextareaInput
                {...props}
                {...field}
                ref={ref}
                placeholder={placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

TextareaField.displayName = "TextareaField";
