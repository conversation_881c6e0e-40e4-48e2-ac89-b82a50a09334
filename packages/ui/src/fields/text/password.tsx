import { forwardRef, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    password: {
      label: "Password",
      description: "Enter your password",
      placeholder: "••••••••",
    },
    confirmPassword: {
      label: "Confirm Password",
      description: "Re-enter your password",
      placeholder: "••••••••",
    },
    newPassword: {
      label: "New Password",
      description: "Enter your new password",
      placeholder: "••••••••",
    },
    currentPassword: {
      label: "Current Password",
      description: "Enter your current password",
      placeholder: "••••••••",
    },
    showPassword: "Show password",
    hidePassword: "Hide password",
    passwordMismatch: "Passwords do not match",
  },
};

export type PasswordMode =
  | "password"
  | "confirm-password"
  | "new-password"
  | "current-password";

export interface PasswordInputProps extends Omit<InputProps, "type"> {
  showToggle?: boolean;
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, showToggle = true, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    return (
      <div className="relative">
        <Input
          {...props}
          type={showPassword ? "text" : "password"}
          className={cn("pr-12", className)}
          ref={ref}
        />
        {showToggle && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword((prev) => !prev)}
            aria-label={
              showPassword ? i18n.en.hidePassword : i18n.en.showPassword
            }
          >
            {showPassword ? (
              <EyeOff className="size-4" />
            ) : (
              <Eye className="size-4" />
            )}
          </Button>
        )}
      </div>
    );
  },
);

PasswordInput.displayName = "PasswordInput";

export interface PasswordFieldProps extends PasswordInputProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  mode?: PasswordMode;
  confirmPasswordName?: string;
  showStrengthIndicator?: boolean;
}

export const PasswordField = forwardRef<HTMLInputElement, PasswordFieldProps>(
  (
    {
      name = "password",
      label,
      description,
      placeholder,
      mode = "password",
      confirmPasswordName,
      showStrengthIndicator = false,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    // Get the appropriate i18n text based on mode
    const getI18nForMode = (mode: PasswordMode) => {
      switch (mode) {
        case "confirm-password":
          return i18n.en.confirmPassword;
        case "new-password":
          return i18n.en.newPassword;
        case "current-password":
          return i18n.en.currentPassword;
        default:
          return i18n.en.password;
      }
    };

    const modeI18n = getI18nForMode(mode);
    const finalLabel = label || modeI18n.label;
    const finalDescription = description || modeI18n.description;
    const finalPlaceholder = placeholder || modeI18n.placeholder;

    // Watch the main password field for confirmation validation
    const mainPasswordValue = useWatch({
      control: form.control,
      name:
        mode === "confirm-password" && confirmPasswordName
          ? confirmPasswordName
          : name,
    });

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{finalLabel}</FormLabel>
            <FormDescription>{finalDescription}</FormDescription>
            <FormControl>
              <PasswordInput
                {...props}
                {...field}
                ref={ref}
                placeholder={finalPlaceholder}
              />
            </FormControl>
            {mode === "confirm-password" && confirmPasswordName && (
              <PasswordConfirmationMessage
                currentValue={field.value}
                originalValue={mainPasswordValue}
              />
            )}
            {showStrengthIndicator && mode !== "confirm-password" && (
              <PasswordStrengthIndicator password={field.value || ""} />
            )}
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

PasswordField.displayName = "PasswordField";

interface PasswordConfirmationMessageProps {
  currentValue?: string;
  originalValue?: string;
}

const PasswordConfirmationMessage = ({
  currentValue,
  originalValue,
}: PasswordConfirmationMessageProps) => {
  if (!currentValue || !originalValue) return null;

  const isMatch = currentValue === originalValue;

  return (
    <p
      className={cn(
        "text-sm font-medium",
        isMatch ? "text-green-600" : "text-destructive",
      )}
    >
      {isMatch ? "Passwords match" : i18n.en.passwordMismatch}
    </p>
  );
};

interface PasswordStrengthIndicatorProps {
  password: string;
}

const PasswordStrengthIndicator = ({
  password,
}: PasswordStrengthIndicatorProps) => {
  const getPasswordStrength = (password: string) => {
    if (!password) return { score: 0, label: "None" };

    let score = 0;
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[^a-zA-Z0-9]/.test(password),
    ];

    score = checks.filter(Boolean).length;

    const labels = ["Very Weak", "Weak", "Fair", "Good", "Strong"];
    return { score, label: labels[score - 1] || "None" };
  };

  const { score, label } = getPasswordStrength(password);

  if (!password) return null;

  const getStrengthColor = (score: number) => {
    if (score <= 1) return "bg-red-500";
    if (score <= 2) return "bg-orange-500";
    if (score <= 3) return "bg-yellow-500";
    if (score <= 4) return "bg-blue-500";
    return "bg-green-500";
  };

  return (
    <div className="space-y-2">
      <div className="flex space-x-1">
        {Array.from({ length: 5 }, (_, i) => (
          <div
            key={i}
            className={cn(
              "h-2 w-full rounded-full",
              i < score ? getStrengthColor(score) : "bg-muted",
            )}
          />
        ))}
      </div>
      <p className="text-sm text-muted-foreground">
        Strength: <span className="font-medium">{label}</span>
      </p>
    </div>
  );
};

// Helper hook for password confirmation validation
export const usePasswordConfirmation = (
  passwordName = "password",
  confirmPasswordName = "confirmPassword",
) => {
  const form = useFormContext();

  return {
    getConfirmPasswordValidation: () => ({
      validate: (value: string) => {
        const password = form.getValues(passwordName);
        return value === password || i18n.en.passwordMismatch;
      },
    }),
  };
};
