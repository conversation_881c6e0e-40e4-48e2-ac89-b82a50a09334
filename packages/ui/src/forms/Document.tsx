"use client";

import type { PropsWithChildren } from "react";
import type { UseFormReturn } from "react-hook-form";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { DescriptionField, TextField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "The document name",
      placeholder: "Enter the document name",
    },
    file: {
      label: "File",
      description: "The document file",
      placeholder: "Select a file",
    },
    description: {
      label: "Description",
      description: "The document description",
      placeholder: "Enter the document description",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameShort: "The document name has to be at least 2 characters.",
    },
  },
};

export const documentFormSchema = z.object({
  organizationId: z.string(),
  name: z.string().min(2, {
    message: i18n.en.messages.nameShort,
  }),
  description: z.string().optional(),
  size: z.number(),
  type: z.string(),
  url: z.string().optional(),
  file: z.instanceof(File).optional(),
});

export type DocumentFormValues = z.infer<typeof documentFormSchema>;
export type DocumentFormProps = PropsWithChildren<
  Parameters<typeof useForm<DocumentFormValues>>[0] & {
    onSubmit?: (values: DocumentFormValues) => void | Promise<void>;
    dropzone?: (form: UseFormReturn<DocumentFormValues>) => React.ReactNode;
  }
>;

export default function DocumentForm({
  children,
  dropzone,
  onSubmit = () => void 0,
  ...props
}: DocumentFormProps) {
  const form = useForm<DocumentFormValues>({
    ...props,
    resolver: zodResolver(documentFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        {dropzone?.(form)}

        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <DocumentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function DocumentFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<DocumentFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
