"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { SelectField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    role: {
      label: "Role",
      description: "The role of the contact",
      placeholder: "Select the role of the contact",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      personRequired: "Person is required",
      roleRequired: "Role is required",
    },
  },
};

export const contactFormSchema = z.object({
  id: z.string().optional(),
  organizationId: z.string().optional(),
  personId: z.string().min(1, { message: i18n.en.messages.personRequired }),
  role: z.string().min(1, { message: i18n.en.messages.roleRequired }),
  person: z.object({
    id: z.string().min(1, { message: i18n.en.messages.personRequired }),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    email: z.string().optional(),
    phone: z.string().optional(),
    avatar: z.string().optional(),
    isUser: z.boolean().optional(),
  }),
});

export type ContactFormValues = z.infer<typeof contactFormSchema>;
export type ContactFormProps = PropsWithChildren<
  Parameters<typeof useForm<ContactFormValues>>[0] & {
    loading?: boolean;
    roles?: { id: string; value: string }[];
    onSubmit?: (values: ContactFormValues) => void | Promise<void>;
  }
>;

export default function ContactForm({
  children,
  loading,
  roles,
  onSubmit = () => void 0,
  ...props
}: ContactFormProps) {
  const form = useForm<ContactFormValues>({
    ...props,
    resolver: zodResolver(contactFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        <SelectField
          loading={loading}
          name="role"
          label={i18n.en.role.label}
          description={i18n.en.role.description}
          placeholder={i18n.en.role.placeholder}
          options={
            roles?.map((role) => ({
              value: role.value,
              label: role.value,
            })) ?? []
          }
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <ContactFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ContactFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ContactFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
