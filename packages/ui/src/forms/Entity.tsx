"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { DescriptionField, TextField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    title: {
      label: "Title",
      description: "The title of the entity",
      placeholder: "Enter the title",
    },
    description: {
      label: "Description",
      description: "A brief description of the entity",
      placeholder: "Enter a description (optional)",
    },
    actions: {
      submit: "Save",
    },
    messages: {
      titleRequired: "Title is required",
    },
  },
};

export const entityFormSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, { message: i18n.en.messages.titleRequired }),
  description: z.string().optional(),
});

export type EntityFormValues = z.infer<typeof entityFormSchema>;
export type EntityFormProps = PropsWithChildren<
  Parameters<typeof useForm<EntityFormValues>>[0] & {
    onSubmit?: (values: EntityFormValues) => void | Promise<void>;
  }
>;

export default function EntityForm({
  children,
  onSubmit = () => void 0,
  ...props
}: EntityFormProps) {
  const form = useForm<EntityFormValues>({
    ...props,
    resolver: zodResolver(entityFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-6"
      >
        <TextField
          name="title"
          label={i18n.en.title.label}
          description={i18n.en.title.description}
          placeholder={i18n.en.title.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <EntityFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function EntityFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<EntityFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
