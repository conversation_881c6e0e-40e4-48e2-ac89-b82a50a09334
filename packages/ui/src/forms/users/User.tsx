"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { SelectField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

export enum UserRole {
  ADMIN = "ADMIN",
  BILLING = "BILLING",
  INTERNAL = "INTERNAL",
  CLIENT = "CLIENT",
  PROVIDER = "PROVIDER",
  USER = "USER",
}

const i18n = {
  en: {
    role: {
      label: "Role",
      description: "The user role",
      placeholder: "Enter the user role",
      options: {
        [UserRole.ADMIN]: "Admin",
        [UserRole.BILLING]: "Billing",
        [UserRole.INTERNAL]: "Internal",
        [UserRole.CLIENT]: "Client",
        [UserRole.PROVIDER]: "Provider",
        [UserRole.USER]: "User",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

export const userFormSchema = z.object({
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
});

export type UserFormValues = z.infer<typeof userFormSchema>;
export type UserFormProps = PropsWithChildren<
  Parameters<typeof useForm<UserFormValues>>[0] & {
    onSubmit?: (values: UserFormValues) => void | Promise<void>;
    onRoleChange?: (role: UserRole) => void;
  }
>;

export default function UserForm({
  children,
  onSubmit = () => void 0,
  onRoleChange = () => void 0,
  ...props
}: UserFormProps) {
  const form = useForm<UserFormValues>({
    ...props,
    resolver: zodResolver(userFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SelectField
          name="role"
          label={i18n.en.role.label}
          description={i18n.en.role.description}
          placeholder={i18n.en.role.placeholder}
          options={Object.entries(i18n.en.role.options).map(
            ([value, label]) => ({
              value,
              label,
            }),
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <UserFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function UserFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<UserFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
