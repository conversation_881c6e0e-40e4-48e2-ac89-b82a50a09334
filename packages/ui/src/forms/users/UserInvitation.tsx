"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { EmailField, SelectField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    email: {
      label: "Email",
      description: "The email for the member",
      placeholder: "Enter the email for the member",
    },
    role: {
      label: "Role",
      description: "The role for the member",
      placeholder: "Select the role for the member",
      options: {
        ADMIN: "Admin",
        BILLING: "Billing",
        INTERNAL: "Internal",
        CLIENT: "Client",
        PROVIDER: "Provider",
        USER: "User",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

export type UserRole =
  | "ADMIN"
  | "BILLING"
  | "INTERNAL"
  | "CLIENT"
  | "PROVIDER"
  | "USER";

const userInvitationFormSchema = z.object({
  organizationId: z.string().optional(),
  email: z.string().email(),
  role: z.enum(["ADMIN", "BILLING", "INTERNAL", "CLIENT", "PROVIDER", "USER"]),
});

export type UserInvitationFormValues = z.infer<typeof userInvitationFormSchema>;
export type UserInvitationFormProps = PropsWithChildren<
  Parameters<typeof useForm<UserInvitationFormValues>>[0] & {
    onSubmit?: (values: UserInvitationFormValues) => void | Promise<void>;
  }
>;

export default function UserInvitationForm({
  children,
  onSubmit = () => void 0,
  ...props
}: UserInvitationFormProps) {
  const form = useForm<UserInvitationFormValues>({
    ...props,
    resolver: zodResolver(userInvitationFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <EmailField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <SelectField
          name="role"
          label={i18n.en.role.label}
          description={i18n.en.role.description}
          placeholder={i18n.en.role.placeholder}
          options={Object.entries(i18n.en.role.options).map(([key, value]) => ({
            value: key,
            label: value,
          }))}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <UserInvitationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function UserInvitationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<UserInvitationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
