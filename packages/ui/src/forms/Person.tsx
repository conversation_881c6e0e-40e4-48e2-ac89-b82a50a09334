"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { EmailField, PhoneNumberField, TextField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    firstName: {
      label: "First Name",
      description: "The given name of the person",
      placeholder: "Enter the first name of the person",
    },
    lastName: {
      label: "Last Name",
      description: "The family name of the person",
      placeholder: "Enter the last name of the person",
    },
    email: {
      label: "Email Address",
      description: "The email address of the person",
      placeholder: "Enter the email address of the person",
    },
    phone: {
      label: "Phone Number",
      description: "The phone number of the person",
      placeholder: "Enter the phone number of the person",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      email: "Please enter a valid email address.",
      phone: "Please enter a valid phone number.",
    },
  },
};

export const personFormSchema = z.object({
  id: z.string().optional(),
  organizationId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z
    .string()
    .email({
      message: i18n.en.messages.email,
    })
    .optional(),
  phone: z
    .string()
    .min(10, {
      message: i18n.en.messages.phone,
    })
    .optional(),
});

export type PersonFormValues = z.infer<typeof personFormSchema>;
export type PersonFormProps = PropsWithChildren<
  Parameters<typeof useForm<PersonFormValues>>[0] & {
    onSubmit?: (values: PersonFormValues) => void | Promise<void>;
  }
>;

export default function PersonForm({
  children,
  onSubmit = () => void 0,
  ...props
}: PersonFormProps) {
  const form = useForm<PersonFormValues>({
    ...props,
    resolver: zodResolver(personFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        <TextField
          name="firstName"
          label={i18n.en.firstName.label}
          description={i18n.en.firstName.description}
          placeholder={i18n.en.firstName.placeholder}
        />

        <TextField
          name="lastName"
          label={i18n.en.lastName.label}
          description={i18n.en.lastName.description}
          placeholder={i18n.en.lastName.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <PhoneNumberField
          name="phone"
          label={i18n.en.phone.label}
          description={i18n.en.phone.description}
          placeholder={i18n.en.phone.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <PersonFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function PersonFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<PersonFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
