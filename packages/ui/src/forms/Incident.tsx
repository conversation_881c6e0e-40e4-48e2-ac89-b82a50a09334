"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "The incident name",
      placeholder: "Enter the incident name",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameLength: "The incident name has to be at least 2 characters.",
    },
  },
};

const incidentFormSchema = z.object({
  name: z.string().min(2, {
    message: i18n.en.messages.nameLength,
  }),
});

export type IncidentFormProps = PropsWithChildren<
  Parameters<typeof useForm<z.infer<typeof incidentFormSchema>>>[0] & {
    onSubmit?: (
      values: z.infer<typeof incidentFormSchema>,
    ) => void | Promise<void>;
  }
>;

export default function IncidentForm({
  children,
  onSubmit = () => void 0,
  ...props
}: IncidentFormProps) {
  const form = useForm<z.infer<typeof incidentFormSchema>>({
    ...props,
    resolver: zodResolver(incidentFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <IncidentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function IncidentFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<z.infer<typeof incidentFormSchema>>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
