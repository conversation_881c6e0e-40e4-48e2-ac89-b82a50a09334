"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { EmailField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    email: {
      label: "Email",
      description: "The email address to best contact you",
      placeholder: "Enter the email address to best contact you",
    },
    actions: {
      submit: "Subscribe",
    },
    messages: {
      email: "Please enter a valid email address.",
    },
  },
};

const personFormSchema = z.object({
  email: z.string().email({
    message: i18n.en.messages.email,
  }),
});

export type NewsletterFormValues = z.infer<typeof personFormSchema>;
export type NewsletterFormProps = PropsWithChildren<
  Parameters<typeof useForm<NewsletterFormValues>>[0] & {
    onSubmit?: (values: NewsletterFormValues) => void | Promise<void>;
    label?: string;
    description?: string;
    placeholder?: string;
  }
>;

export default function NewsletterForm({
  children,
  label = i18n.en.email.label,
  description = i18n.en.email.description,
  placeholder = i18n.en.email.placeholder,
  onSubmit = () => void 0,
  ...props
}: NewsletterFormProps) {
  const form = useForm<NewsletterFormValues>({
    ...props,
    resolver: zodResolver(personFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
            form.reset({
              email: "",
            });
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        <EmailField
          name="email"
          label={label}
          description={description}
          placeholder={placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <NewsletterFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function NewsletterFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<NewsletterFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
