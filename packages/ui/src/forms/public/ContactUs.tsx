"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import {
  DescriptionField,
  EmailField,
  PhoneNumberField,
  TextField,
} from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    companyName: {
      label: "Company Name",
      description: "The name of the company",
      placeholder: "Enter the name of the company",
    },
    name: {
      label: "Name",
      description: "Your name",
      placeholder: "Enter your name",
    },
    email: {
      label: "Email Address",
      description: "The email address to best contact you",
      placeholder: "Enter the email address to best contact you",
    },
    phone: {
      label: "Phone Number",
      description: "The phone number to best contact you",
      placeholder: "Enter the phone number to best contact you",
    },
    message: {
      label: "Message",
      description: "The message to send",
      placeholder: "Send us a brief message about your needs",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      email: "Please enter a valid email address.",
      phone: "Please enter a valid phone number.",
    },
  },
};

export const contactUsFormSchema = z.object({
  companyName: z.string(),
  name: z.string(),
  email: z.string().email({
    message: i18n.en.messages.email,
  }),
  phone: z.string().min(10, {
    message: i18n.en.messages.phone,
  }),
  message: z.string().optional(),
});

export type ContactUsFormValues = z.infer<typeof contactUsFormSchema>;
export type ContactUsFormProps = PropsWithChildren<
  Parameters<typeof useForm<ContactUsFormValues>>[0] & {
    onSubmit?: (values: ContactUsFormValues) => void | Promise<void>;
  }
>;

export default function ContactUsForm({
  children,
  onSubmit = () => void 0,
  ...props
}: ContactUsFormProps) {
  const form = useForm<ContactUsFormValues>({
    ...props,
    resolver: zodResolver(contactUsFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
            form.reset({
              companyName: "",
              name: "",
              email: "",
              phone: "",
              message: "",
            });
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        <TextField
          name="companyName"
          label={i18n.en.companyName.label}
          description={i18n.en.companyName.description}
          placeholder={i18n.en.companyName.placeholder}
        />

        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <PhoneNumberField
          name="phone"
          label={i18n.en.phone.label}
          description={i18n.en.phone.description}
          placeholder={i18n.en.phone.placeholder}
        />

        <DescriptionField
          name="message"
          label={i18n.en.message.label}
          description={i18n.en.message.description}
          placeholder={i18n.en.message.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <ContactUsFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ContactUsFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ContactUsFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
