"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { SelectField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    role: {
      label: "Role",
      description: "The role for the member",
      placeholder: "Select the role for the member",
      options: {
        "org:admin": "Admin",
        "org:member": "Member",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

export const memberFormSchema = z.object({
  organizationId: z.string().optional(),
  personId: z.string().optional(),
  role: z.string(),
});

export type MemberFormValues = z.infer<typeof memberFormSchema>;
export type MemberFormProps = PropsWithChildren<
  Parameters<typeof useForm<MemberFormValues>>[0] & {
    onSubmit?: (values: MemberFormValues) => void | Promise<void>;
  }
>;

export default function MemberForm({
  children,
  onSubmit = () => void 0,
  ...props
}: MemberFormProps) {
  const form = useForm<MemberFormValues>({
    ...props,
    resolver: zodResolver(memberFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SelectField
          name="role"
          label={i18n.en.role.label}
          description={i18n.en.role.description}
          placeholder={i18n.en.role.placeholder}
          options={Object.entries(i18n.en.role.options).map(
            ([value, label]) => ({
              value,
              label,
            }),
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <MemberFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function MemberFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<MemberFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
