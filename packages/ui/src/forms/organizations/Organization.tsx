"use client";

import type { PropsWithChildren } from "react";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { TextField } from "@/ui/fields/text/Text";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "Name of the Organization",
      placeholder: "Enter the name of the organization",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameLength: "The organization name has to be at least 2 characters.",
      parentRequired: "The parent organization is required",
    },
  },
};

export const organizationFormSchema = z.object({
  fieldNationId: z.string().optional(),
  organizationId: z.string().optional(),
  parentId: z.string().min(1, {
    message: i18n.en.messages.parentRequired,
  }),
  name: z.string().min(2, {
    message: i18n.en.messages.nameLength,
  }),
  type: z.string().optional(),
});

export interface PartialOrganization {
  id: string;
  name: string;
  avatar?: string;
  type?: string;
}

export type OrganizationFormValues = z.infer<typeof organizationFormSchema>;
export type OrganizationFormProps = PropsWithChildren<
  Parameters<typeof useForm<OrganizationFormValues>>[0] & {
    defaultValues?: OrganizationFormValues;
    onSubmit?: (values: OrganizationFormValues) => void | Promise<void>;
    loading?: boolean;
  }
>;

export default function OrganizationForm({
  children,
  loading = false,
  onSubmit = () => void 0,
  ...props
}: OrganizationFormProps) {
  const [initialized, setInitialized] = useState(false);
  const form = useForm<OrganizationFormValues>({
    ...props,
    resolver: zodResolver(organizationFormSchema),
  });

  useEffect(() => {
    if (!loading && props.defaultValues && !initialized) {
      form.reset(props.defaultValues, {
        keepDirtyValues: false,
        keepDefaultValues: false,
        // isSubmitted: false,
        // dirtyFields: new Set(),
      });
      setInitialized(true);
    }
  }, [props.defaultValues, loading, form, initialized]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <OrganizationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function OrganizationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrganizationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
