"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import { SelectField, TextField } from "@/ui/fields";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

const i18n = {
  en: {
    email: {
      label: "Email",
      description: "The email for the member",
      placeholder: "Enter the email for the member",
    },
    role: {
      label: "Role",
      description: "The role for the member",
      placeholder: "Select the role for the member",
      options: {
        "org:admin": "Admin",
        "org:member": "Member",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

export const invitationFormSchema = z.object({
  organizationId: z.string().optional(),
  email: z.string().email(),
  role: z.string(),
});

export type InvitationFormValues = z.infer<typeof invitationFormSchema>;
export type InvitationFormProps = PropsWithChildren<
  Parameters<typeof useForm<InvitationFormValues>>[0] & {
    onSubmit?: (values: InvitationFormValues) => void | Promise<void>;
  }
>;

export default function InvitationForm({
  children,
  onSubmit = () => void 0,
  ...props
}: InvitationFormProps) {
  const form = useForm<InvitationFormValues>({
    ...props,
    resolver: zodResolver(invitationFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <SelectField
          name="role"
          label={i18n.en.role.label}
          description={i18n.en.role.description}
          placeholder={i18n.en.role.placeholder}
          options={Object.entries(i18n.en.role.options).map(
            ([value, label]) => ({
              value,
              label,
            }),
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <InvitationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function InvitationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<InvitationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
