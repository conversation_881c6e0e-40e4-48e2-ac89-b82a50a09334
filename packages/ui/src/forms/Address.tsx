"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/ui/primitives/button";

import {
  AddressAutocompleteField,
  zAddress,
} from "@/ui/fields/AddressAutocomplete";
import { TextField } from "@/ui/fields/text/Text";
import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Form } from "@/ui/primitives/form";

import { DescriptionField } from "../fields/text/Description";

const i18n = {
  en: {
    address: {
      label: "Address",
      description: "Search for an address of a location",
      placeholder: "Begin typing to search for a location",
      empty: "Address not found. Please check to see if the address is valid.",
    },
    name: {
      label: "Name",
      description: "A memorable name for the location",
      placeholder: "Enter a memorable name for the location",
    },
    description: {
      label: "Description",
      description: "Details and directions for the location",
      placeholder: "Enter location details and directions",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      requiredName: "Please enter a name.",
      requiredAddress: "Address is required.",
    },
  },
};

export const addressFormSchema = z.object({
  name: z.string({
    required_error: i18n.en.messages.requiredName,
  }),
  type: z.string().optional(),
  timeZone: z.string().optional(),
  description: z.string().optional().nullable(),
  address: zAddress,
});

export type AddressFormValues = z.infer<typeof addressFormSchema>;
export type AddressFormProps = PropsWithChildren<
  Parameters<typeof useForm<AddressFormValues>>[0] & {
    schema?: z.ZodSchema<AddressFormValues>;
    onSubmit?: (values: AddressFormValues) => void | Promise<void>;
  }
>;

export default function AddressForm({
  schema = addressFormSchema,
  children,
  onSubmit = () => void 0,
  ...props
}: AddressFormProps) {
  const form = useForm<AddressFormValues>({
    ...props,
    resolver: zodResolver(schema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-6"
      >
        <AddressAutocompleteField
          name="address.formatted"
          label={i18n.en.address.label}
          description={i18n.en.address.description}
          placeholder={i18n.en.address.placeholder}
          empty={i18n.en.address.empty}
        />

        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <AddressFormSubmitButton>
              {i18n.en.actions.submit}
            </AddressFormSubmitButton>
          </div>
        )}
      </form>
    </Form>
  );
}

export function AddressFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<AddressFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
