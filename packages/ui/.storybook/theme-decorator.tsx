import type { StoryContext } from "@storybook/react";
import type { FunctionComponent } from "react";

import React, { useEffect } from "react";

import { ThemeProvider, useTheme } from "../src/primitives/theme";
import { Toaster } from "../src/primitives/toast";
import { TooltipProvider } from "../src/primitives/tooltip";

import "../src/styles/index.css";

export function ThemeSwitcher({ theme }: { theme: string }) {
  const { setTheme } = useTheme();

  useEffect(() => {
    setTheme(theme);
  }, [theme, setTheme]);

  return null;
}

export function ThemeDecorator(
  Story: FunctionComponent,
  context: StoryContext,
) {
  const theme = context.globals.theme || "light";
  return (
    <ThemeProvider attribute="class" defaultTheme={theme}>
      <ThemeSwitcher theme={theme} />
      <TooltipProvider>
        <Story />
        <Toaster />
      </TooltipProvider>
    </ThemeProvider>
  );
}
