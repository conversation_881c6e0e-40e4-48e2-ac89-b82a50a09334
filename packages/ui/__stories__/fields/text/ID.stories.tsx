import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { IDFieldProps } from "@/ui/fields/text/ID";

import { IDField } from "@/ui/fields/text/ID";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelIDSchema = z
  .string()
  .min(1, "ID cannot be empty.")
  .regex(/^[a-zA-Z0-9_-]+$/, {
    message: "ID can only contain letters, numbers, hyphens, and underscores.",
  });

// Define args type for this specific story
interface CurrentIDStoryArgs extends IDFieldProps, BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelIDSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentIDStoryArgs> = {
  title: "Fields/Text/IDField",
  component: IDField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for IDField component itself
    label: "Resource ID",
    name: "resourceId", // This will be the key in the form data
    placeholder: "e.g., item_123xyz or user-abc-789",
    description: "Enter the unique identifier for the resource.",

    // Args for the WithFormProvider decorator
    fieldName: "resourceId", // Must match the 'name' prop above
    fieldSchema: storyLevelIDSchema,
    onFormSubmit: fn(),
    defaultValues: { resourceId: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentIDStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialResourceId",
    fieldName: "initialResourceId",
    defaultValues: { initialResourceId: "prod_abc123xyz" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText(
      "e.g., item_123xyz or user-abc-789",
    );
    await expect(input).toHaveValue("prod_abc123xyz");
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveResourceId",
    fieldName: "interactiveResourceId",
    defaultValues: { interactiveResourceId: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText(
      "e.g., item_123xyz or user-abc-789",
    );

    await userEvent.type(input, "usr_test-001");
    await expect(input).toHaveValue("usr_test-001");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "usr_test-001" },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyResourceId",
    fieldName: "emptyResourceId",
    defaultValues: { emptyResourceId: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(canvas.getByText("ID cannot be empty.")).toBeInTheDocument();
  },
};

export const WithErrorInvalidFormat: Story = {
  args: {
    name: "invalidFormatResourceId",
    fieldName: "invalidFormatResourceId",
    defaultValues: { invalidFormatResourceId: "id with spaces!@#" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText(
      "e.g., item_123xyz or user-abc-789",
    );
    await expect(input).toHaveValue("id with spaces!@#");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText(
        "ID can only contain letters, numbers, hyphens, and underscores.",
      ),
    ).toBeInTheDocument();
  },
};
