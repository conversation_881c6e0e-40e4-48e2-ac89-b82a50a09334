import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { TextareaFieldProps } from "@/ui/fields/text/textarea";

import { TextareaField } from "@/ui/fields/text/textarea";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define Zod schemas for different textarea validation scenarios
const basicTextareaSchema = z
  .string()
  .min(1, "Description cannot be empty.")
  .max(500, "Description must not exceed 500 characters.");

const longTextareaSchema = z
  .string()
  .min(10, "Description must be at least 10 characters long.")
  .max(2000, "Description must not exceed 2000 characters.");

const requiredTextareaSchema = z
  .string()
  .min(1, "This field is required.")
  .min(20, "Please provide at least 20 characters.")
  .max(1000, "Maximum 1000 characters allowed.");

// Define args type for this specific story
interface CurrentTextareaStoryArgs
  extends TextareaFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: any; // Zod schema for validation
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentTextareaStoryArgs> = {
  title: "Fields/Text/Textarea",
  component: TextareaField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for TextareaField component itself
    label: "Description",
    name: "description", // This will be the key in the form data
    placeholder: "Type your message here...",
    description: "Enter a detailed description",

    // Args for the WithFormProvider decorator
    fieldName: "description", // Must match the 'name' prop above
    fieldSchema: basicTextareaSchema,
    onFormSubmit: fn(),
    defaultValues: { description: "" },
  },
  parameters: {
    layout: "centered",
  },
  argTypes: {
    maxLength: {
      control: { type: "number", min: 0, max: 5000, step: 50 },
    },
    showCharacterCount: {
      control: "boolean",
    },
    autoResize: {
      control: "boolean",
    },
    minRows: {
      control: { type: "number", min: 1, max: 10, step: 1 },
    },
    maxRows: {
      control: { type: "number", min: 2, max: 20, step: 1 },
    },
    variant: {
      control: {
        type: "select",
        options: ["primary", "outline", "ghost"],
      },
    },
    size: {
      control: {
        type: "select",
        options: ["sm", "md", "lg", "bare"],
      },
    },
  },
} satisfies Meta<CurrentTextareaStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialDescription",
    fieldName: "initialDescription",
    defaultValues: {
      initialDescription:
        "This is some initial content in the textarea that demonstrates how it looks with existing text.",
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");
    await expect(textarea).toHaveValue(
      "This is some initial content in the textarea that demonstrates how it looks with existing text.",
    );
  },
};

export const WithCharacterCount: Story = {
  args: {
    name: "countedDescription",
    fieldName: "countedDescription",
    maxLength: 200,
    showCharacterCount: true,
    defaultValues: { countedDescription: "" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    // Initially should show full character count
    await expect(
      canvas.getByText("200 characters remaining"),
    ).toBeInTheDocument();

    // Type some text
    await userEvent.type(textarea, "Hello world! This is a test message.");

    // Should update character count
    await expect(canvas.getByText(/characters remaining/)).toBeInTheDocument();
  },
};

export const CharacterLimitExceeded: Story = {
  args: {
    name: "exceededDescription",
    fieldName: "exceededDescription",
    maxLength: 50,
    showCharacterCount: true,
    defaultValues: {
      exceededDescription:
        "This is a very long message that definitely exceeds the 50 character limit that was set for this textarea field.",
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    // Should show character overage
    await expect(canvas.getByText(/characters over limit/)).toBeInTheDocument();

    // Textarea should have error styling
    await expect(textarea).toHaveClass("border-destructive");
  },
};

export const AutoResize: Story = {
  args: {
    name: "autoResizeDescription",
    fieldName: "autoResizeDescription",
    autoResize: true,
    minRows: 2,
    maxRows: 6,
    defaultValues: { autoResizeDescription: "" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    // Should have resize-none class when auto-resize is enabled
    await expect(textarea).toHaveClass("resize-none");

    // Type multiple lines of text
    await userEvent.type(textarea, "Line 1\nLine 2\nLine 3\nLine 4\nLine 5");

    // The textarea should have expanded (we can't easily test the exact height)
    await expect(textarea).toHaveValue(
      "Line 1\nLine 2\nLine 3\nLine 4\nLine 5",
    );
  },
};

export const AutoResizeWithCharacterCount: Story = {
  args: {
    name: "fullFeaturedDescription",
    fieldName: "fullFeaturedDescription",
    autoResize: true,
    minRows: 3,
    maxRows: 8,
    maxLength: 500,
    showCharacterCount: true,
    label: "Full Featured Textarea",
    description:
      "This textarea has both auto-resize and character counting enabled",
    defaultValues: { fullFeaturedDescription: "" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    // Should show initial character count
    await expect(
      canvas.getByText("500 characters remaining"),
    ).toBeInTheDocument();

    // Type some multi-line content
    const longText =
      "This is a longer piece of text that spans multiple lines.\n\nIt includes paragraph breaks and demonstrates both the auto-resize functionality and the character counting feature working together.\n\nThis should make the textarea grow taller while also updating the character count.";

    await userEvent.type(textarea, longText);

    // Should update character count
    await expect(canvas.getByText(/characters remaining/)).toBeInTheDocument();
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveDescription",
    fieldName: "interactiveDescription",
    defaultValues: { interactiveDescription: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    const testMessage =
      "This is a test message for the interactive story that validates form submission works correctly.";
    await userEvent.type(textarea, testMessage);
    await expect(textarea).toHaveValue(testMessage);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: testMessage },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyDescription",
    fieldName: "emptyDescription",
    defaultValues: { emptyDescription: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Description cannot be empty."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooLong: Story = {
  args: {
    name: "longDescription",
    fieldName: "longDescription",
    defaultValues: {
      longDescription: "a".repeat(501), // Exceeds 500 character limit
    },
    fieldSchema: basicTextareaSchema,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");
    await expect(textarea).toHaveValue("a".repeat(501));

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Description must not exceed 500 characters."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooShort: Story = {
  args: {
    name: "shortDescription",
    fieldName: "shortDescription",
    defaultValues: { shortDescription: "Short" },
    fieldSchema: requiredTextareaSchema,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");
    await expect(textarea).toHaveValue("Short");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Please provide at least 20 characters."),
    ).toBeInTheDocument();
  },
};

export const DifferentSizes: Story = {
  render: () => (
    <div className="max-w-2xl space-y-6">
      <TextareaField
        name="smallTextarea"
        label="Small Textarea"
        size="sm"
        placeholder="Small size textarea..."
      />
      <TextareaField
        name="mediumTextarea"
        label="Medium Textarea (Default)"
        size="md"
        placeholder="Medium size textarea..."
      />
      <TextareaField
        name="largeTextarea"
        label="Large Textarea"
        size="lg"
        placeholder="Large size textarea..."
      />
    </div>
  ),
};

export const DifferentVariants: Story = {
  render: () => (
    <div className="max-w-2xl space-y-6">
      <TextareaField
        name="primaryTextarea"
        label="Primary Variant (Default)"
        variant="primary"
        placeholder="Primary variant textarea..."
      />
      <TextareaField
        name="outlineTextarea"
        label="Outline Variant"
        variant="outline"
        placeholder="Outline variant textarea..."
      />
      <TextareaField
        name="ghostTextarea"
        label="Ghost Variant"
        variant="ghost"
        placeholder="Ghost variant textarea..."
      />
    </div>
  ),
};

export const AllFeaturesCombined: Story = {
  args: {
    name: "featureShowcase",
    fieldName: "featureShowcase",
    label: "Feature Showcase",
    description:
      "This textarea demonstrates all available features working together",
    autoResize: true,
    minRows: 2,
    maxRows: 10,
    maxLength: 1000,
    showCharacterCount: true,
    fieldSchema: longTextareaSchema,
    defaultValues: {
      featureShowcase:
        "Start typing to see auto-resize and character counting in action...",
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText("Type your message here...");

    // Should have initial value
    await expect(textarea).toHaveValue(
      "Start typing to see auto-resize and character counting in action...",
    );

    // Should show character count
    await expect(canvas.getByText(/characters remaining/)).toBeInTheDocument();

    // Should have auto-resize styling
    await expect(textarea).toHaveClass("resize-none");
  },
};
