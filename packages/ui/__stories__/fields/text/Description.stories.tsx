import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { DescriptionFieldProps } from "@/ui/fields/text/Description";

import { DescriptionField } from "@/ui/fields/text/Description";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelDescriptionSchema = z
  .string()
  .min(1, "Description cannot be empty.")
  .max(200, "Description must be 200 characters or less.");

// Define args type for this specific story
interface CurrentDescriptionStoryArgs
  extends DescriptionFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelDescriptionSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentDescriptionStoryArgs> = {
  title: "Fields/Text/DescriptionField",
  component: DescriptionField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for DescriptionField component itself
    label: "Item Description",
    name: "itemDescription", // This will be the key in the form data
    placeholder: "Enter a detailed description for the item...",
    description: "Provide as much detail as possible.",

    // Args for the WithFormProvider decorator
    fieldName: "itemDescription", // Must match the 'name' prop above
    fieldSchema: storyLevelDescriptionSchema,
    onFormSubmit: fn(),
    defaultValues: { itemDescription: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentDescriptionStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialDescription",
    fieldName: "initialDescription",
    defaultValues: {
      initialDescription:
        "This is a pre-filled detailed description of the product, highlighting its key features and benefits.",
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText(
      "Enter a detailed description for the item...",
    );
    await expect(textarea).toHaveValue(
      "This is a pre-filled detailed description of the product, highlighting its key features and benefits.",
    );
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveDescription",
    fieldName: "interactiveDescription",
    defaultValues: { interactiveDescription: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText(
      "Enter a detailed description for the item...",
    );

    await userEvent.type(textarea, "A new compelling description.");
    await expect(textarea).toHaveValue("A new compelling description.");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "A new compelling description." },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyDescription",
    fieldName: "emptyDescription",
    defaultValues: { emptyDescription: "" }, // Intentionally empty to trigger min(1) error
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Attempt to submit the form with empty description
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Description cannot be empty."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooLong: Story = {
  args: {
    name: "longDescription",
    fieldName: "longDescription",
    defaultValues: {
      longDescription: "a".repeat(201), // 201 characters, exceeding max(200)
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const textarea = canvas.getByPlaceholderText(
      "Enter a detailed description for the item...",
    );
    await expect(textarea).toHaveValue("a".repeat(201));

    // Attempt to submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Description must be 200 characters or less."),
    ).toBeInTheDocument();
  },
};
