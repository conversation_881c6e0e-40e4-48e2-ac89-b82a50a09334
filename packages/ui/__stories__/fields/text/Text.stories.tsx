import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { TextFieldProps } from "@/ui/fields/text/Text";

import { TextField } from "@/ui/fields/text/Text";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelTextSchema = z
  .string()
  .min(1, "This text field cannot be empty.")
  .max(100, "Text input must not exceed 100 characters.");

// Define args type for this specific story
interface CurrentTextStoryArgs extends TextFieldProps, BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelTextSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentTextStoryArgs> = {
  title: "Fields/Text/TextField",
  component: TextField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for TextField component itself
    label: "Sample Text",
    name: "sampleText", // This will be the key in the form data
    placeholder: "Enter some text here...",
    description: "A generic text input field for various purposes.",

    // Args for the WithFormProvider decorator
    fieldName: "sampleText", // Must match the 'name' prop above
    fieldSchema: storyLevelTextSchema,
    onFormSubmit: fn(),
    defaultValues: { sampleText: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentTextStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialText",
    fieldName: "initialText",
    defaultValues: { initialText: "Hello, Storybook!" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter some text here...");
    await expect(input).toHaveValue("Hello, Storybook!");
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveText",
    fieldName: "interactiveText",
    defaultValues: { interactiveText: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter some text here...");

    const testMessage = "This is a user-typed message.";
    await userEvent.type(input, testMessage);
    await expect(input).toHaveValue(testMessage);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: testMessage },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyText",
    fieldName: "emptyText",
    defaultValues: { emptyText: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("This text field cannot be empty."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooLong: Story = {
  args: {
    name: "longText",
    fieldName: "longText",
    defaultValues: { longText: "a".repeat(101) },
    fieldSchema: storyLevelTextSchema, // Ensure this story uses the defined schema
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter some text here...");
    await expect(input).toHaveValue("a".repeat(101));

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Text input must not exceed 100 characters."),
    ).toBeInTheDocument();
  },
};
