import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { PasswordFieldProps } from "@/ui/fields/text/password";

import { PasswordField } from "@/ui/fields/text/password";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define Zod schemas for different password validation scenarios
const basicPasswordSchema = z
  .string()
  .min(1, "Password cannot be empty.")
  .min(8, "Password must be at least 8 characters long.");

const strongPasswordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters long.")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter.")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter.")
  .regex(/\d/, "Password must contain at least one number.")
  .regex(
    /[^a-zA-Z0-9]/,
    "Password must contain at least one special character.",
  );

const passwordConfirmationSchema = z
  .object({
    password: strongPasswordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Define args type for this specific story
interface CurrentPasswordStoryArgs
  extends PasswordFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: any; // Can be string schema or object schema for confirmation
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentPasswordStoryArgs> = {
  title: "Fields/Text/Password",
  component: PasswordField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for PasswordField component itself
    label: "Password",
    name: "password", // This will be the key in the form data
    placeholder: "••••••••",
    description: "Enter your password",

    // Args for the WithFormProvider decorator
    fieldName: "password", // Must match the 'name' prop above
    fieldSchema: basicPasswordSchema,
    onFormSubmit: fn(),
    defaultValues: { password: "" },
  },
  parameters: {
    layout: "centered",
  },
  argTypes: {
    mode: {
      control: {
        type: "select",
        options: [
          "password",
          "new-password",
          "current-password",
          "confirm-password",
        ],
      },
    },
    showStrengthIndicator: {
      control: "boolean",
    },
    showToggle: {
      control: "boolean",
    },
  },
} satisfies Meta<CurrentPasswordStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithVisibilityToggle: Story = {
  args: {
    name: "togglePassword",
    fieldName: "togglePassword",
    defaultValues: { togglePassword: "secret123" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");
    const toggleButton = canvas.getByLabelText("Show password");

    // Initially password should be hidden
    await expect(input).toHaveAttribute("type", "password");
    await expect(input).toHaveValue("secret123");

    // Click toggle to show password
    await userEvent.click(toggleButton);
    await expect(input).toHaveAttribute("type", "text");
    await expect(canvas.getByLabelText("Hide password")).toBeInTheDocument();

    // Click toggle to hide password again
    await userEvent.click(canvas.getByLabelText("Hide password"));
    await expect(input).toHaveAttribute("type", "password");
  },
};

export const NewPasswordWithStrength: Story = {
  args: {
    name: "newPassword",
    fieldName: "newPassword",
    mode: "new-password",
    showStrengthIndicator: true,
    fieldSchema: strongPasswordSchema,
    defaultValues: { newPassword: "" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");

    // Type a weak password
    await userEvent.type(input, "weak");
    await expect(canvas.getByText("Strength:")).toBeInTheDocument();
    await expect(canvas.getByText("Very Weak")).toBeInTheDocument();

    // Clear and type a stronger password
    await userEvent.clear(input);
    await userEvent.type(input, "StrongPass123!");
    await expect(canvas.getByText("Strong")).toBeInTheDocument();
  },
};

export const CurrentPassword: Story = {
  args: {
    name: "currentPassword",
    fieldName: "currentPassword",
    mode: "current-password",
    defaultValues: { currentPassword: "" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText("Current Password")).toBeInTheDocument();
    await expect(
      canvas.getByText("Enter your current password"),
    ).toBeInTheDocument();
  },
};

export const PasswordConfirmation: Story = {
  args: {
    name: "confirmPassword",
    fieldName: "confirmPassword",
    mode: "confirm-password",
    confirmPasswordName: "password",
    fieldSchema: passwordConfirmationSchema,
    defaultValues: { password: "", confirmPassword: "" },
  },
  render: (args) => (
    <div className="space-y-4">
      <PasswordField
        name="password"
        mode="new-password"
        showStrengthIndicator={true}
      />
      <PasswordField {...args} />
    </div>
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const passwordInputs = canvas.getAllByPlaceholderText("••••••••");
    const passwordInput = passwordInputs[0]!;
    const confirmInput = passwordInputs[1]!;

    // Type matching passwords
    await userEvent.type(passwordInput, "MatchingPass123!");
    await userEvent.type(confirmInput, "MatchingPass123!");
    await expect(canvas.getByText("Passwords match")).toBeInTheDocument();

    // Change confirm password to not match
    await userEvent.clear(confirmInput);
    await userEvent.type(confirmInput, "DifferentPass123!");
    await expect(
      canvas.getByText("Passwords do not match"),
    ).toBeInTheDocument();
  },
};

export const Interactive: Story = {
  args: {
    name: "interactivePassword",
    fieldName: "interactivePassword",
    defaultValues: { interactivePassword: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");

    const testPassword = "TestPassword123!";
    await userEvent.type(input, testPassword);
    await expect(input).toHaveValue(testPassword);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: testPassword },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyPassword",
    fieldName: "emptyPassword",
    defaultValues: { emptyPassword: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Password cannot be empty."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooShort: Story = {
  args: {
    name: "shortPassword",
    fieldName: "shortPassword",
    defaultValues: { shortPassword: "short" },
    fieldSchema: basicPasswordSchema,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");
    await expect(input).toHaveValue("short");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Password must be at least 8 characters long."),
    ).toBeInTheDocument();
  },
};

export const WithErrorWeakPassword: Story = {
  args: {
    name: "weakPassword",
    fieldName: "weakPassword",
    defaultValues: { weakPassword: "weakpass" },
    fieldSchema: strongPasswordSchema,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");
    await expect(input).toHaveValue("weakpass");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Should show at least one of the strength validation errors
    await expect(
      canvas.getByText(/Password must contain at least one/),
    ).toBeInTheDocument();
  },
};

export const WithoutToggle: Story = {
  args: {
    name: "noTogglePassword",
    fieldName: "noTogglePassword",
    showToggle: false,
    defaultValues: { noTogglePassword: "hidden" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("••••••••");

    // Should not have toggle button
    expect(() => canvas.getByLabelText("Show password")).toThrow();
    await expect(input).toHaveAttribute("type", "password");
  },
};

export const AllModes: Story = {
  render: () => (
    <div className="max-w-md space-y-6">
      <PasswordField
        name="standardPassword"
        mode="password"
        label="Standard Password"
      />
      <PasswordField name="currentPasswordField" mode="current-password" />
      <PasswordField
        name="newPasswordField"
        mode="new-password"
        showStrengthIndicator={true}
      />
      <PasswordField
        name="confirmPasswordField"
        mode="confirm-password"
        confirmPasswordName="newPasswordField"
      />
    </div>
  ),
};
