import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { URLFieldProps } from "@/ui/fields/text/URL";

import { URLField } from "@/ui/fields/text/URL";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelURLSchema = z
  .string()
  .min(1, "URL cannot be empty.")
  .url({ message: "Please enter a valid URL (e.g., https://example.com)." });

// Define args type for this specific story
interface CurrentURLStoryArgs extends URLFieldProps, BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelURLSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentURLStoryArgs> = {
  title: "Fields/Text/URLField",
  component: URL<PERSON>ield,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for URLField component itself
    label: "Website URL",
    name: "websiteUrl", // This will be the key in the form data
    placeholder: "https://example.com",
    description: "Enter the full website address, including http(s)://",

    // Args for the WithFormProvider decorator
    fieldName: "websiteUrl", // Must match the 'name' prop above
    fieldSchema: storyLevelURLSchema,
    onFormSubmit: fn(),
    defaultValues: { websiteUrl: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentURLStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "companyWebsite",
    fieldName: "companyWebsite",
    defaultValues: { companyWebsite: "https://www.windsurf.ai" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("https://example.com");
    await expect(input).toHaveValue("https://www.windsurf.ai");
  },
};

export const InteractiveValidURL: Story = {
  args: {
    name: "portfolioLink",
    fieldName: "portfolioLink",
    defaultValues: { portfolioLink: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("https://example.com");

    const testURL = "https://github.com/my-profile";
    await userEvent.type(input, testURL);
    await expect(input).toHaveValue(testURL);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: testURL },
      expect.anything(),
    );
  },
};

export const InteractiveInvalidURLFormat: Story = {
  args: {
    name: "invalidFormatUrl",
    fieldName: "invalidFormatUrl",
    defaultValues: { invalidFormatUrl: "this is not a url" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("https://example.com");
    await expect(input).toHaveValue("this is not a url");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Please enter a valid URL (e.g., https://example.com)."),
    ).toBeInTheDocument();
  },
};

export const InteractiveMissingProtocol: Story = {
  args: {
    name: "missingProtocolUrl",
    fieldName: "missingProtocolUrl",
    defaultValues: { missingProtocolUrl: "www.google.com" }, // Missing http(s)://
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("https://example.com");
    await expect(input).toHaveValue("www.google.com");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Please enter a valid URL (e.g., https://example.com)."),
    ).toBeInTheDocument();
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyUrlField",
    fieldName: "emptyUrlField",
    defaultValues: { emptyUrlField: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(canvas.getByText("URL cannot be empty.")).toBeInTheDocument();
  },
};
