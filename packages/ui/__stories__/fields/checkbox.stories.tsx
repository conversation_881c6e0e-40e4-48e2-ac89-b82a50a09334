import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { z } from "zod";

import { CheckboxField } from "@/ui/fields/checkbox";

import { WithFormProvider } from "./WithFormProvider";

// Form validation schema for checkbox stories
const checkboxValidationSchema = z.object({
  terms: z
    .boolean()
    .refine((val) => val === true, "You must accept the terms and conditions"),
  newsletter: z.boolean().optional(),
  notifications: z.boolean().optional(),
  privacy: z.boolean().optional(),
  marketing: z.boolean().optional(),
  defaultChecked: z.boolean().optional(),
  disabled: z.boolean().optional(),
  leftLabel: z.boolean().optional(),
  required: z.boolean().optional(),
});

const meta: Meta<typeof CheckboxField> = {
  title: "Fields/Checkbox",
  component: CheckboxField,
  decorators: [WithFormProvider],
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      description: "Checkbox label text",
      control: "text",
    },
    description: {
      description: "Help text below the checkbox",
      control: "text",
    },
    disabled: {
      description: "Disable the checkbox",
      control: "boolean",
    },
    required: {
      description: "Mark as required field",
      control: "boolean",
    },
    labelPosition: {
      description: "Position of label relative to checkbox",
      control: "radio",
      options: ["right", "left"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "newsletter",
    label: "Subscribe to newsletter",
    description: "Get the latest updates and news",
  },
};

export const Required: Story = {
  args: {
    name: "terms",
    label: "I accept the terms and conditions",
    description: "Please read and accept our terms of service",
    required: true,
  },
};

export const Disabled: Story = {
  args: {
    name: "disabled",
    label: "Disabled checkbox",
    description: "This checkbox cannot be interacted with",
    disabled: true,
  },
};

export const LeftLabel: Story = {
  args: {
    name: "leftLabel",
    label: "Label on the left",
    description: "The label appears to the left of the checkbox",
    labelPosition: "left",
  },
};

export const WithoutDescription: Story = {
  args: {
    name: "privacy",
    label: "Keep my information private",
  },
};

export const LongLabel: Story = {
  args: {
    name: "marketing",
    label:
      "I agree to receive marketing communications, promotional emails, and updates about products and services from this company and its partners",
    description:
      "You can unsubscribe at any time by clicking the link in our emails",
  },
};

export const RequiredWithValidation: Story = {
  args: {
    name: "terms",
    label: "I have read and agree to the Terms of Service and Privacy Policy",
    description: "This field is required to proceed",
    required: true,
  },
};

export const SimpleToggle: Story = {
  args: {
    name: "notifications",
    label: "Enable notifications",
  },
};
