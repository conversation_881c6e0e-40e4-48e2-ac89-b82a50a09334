import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { z } from "zod";

import { SwitchField } from "@/ui/fields/switch";

import { WithFormProvider } from "./WithFormProvider";

// Form validation schema for switch stories
const switchValidationSchema = z.object({
  notifications: z.boolean().optional(),
  marketing: z.boolean().optional(),
  privacy: z.boolean().optional(),
  autoSave: z.boolean().optional(),
  darkMode: z.boolean().optional(),
  twoFactor: z.boolean().optional(),
  emailAlerts: z.boolean().optional(),
  leftLabel: z.boolean().optional(),
  disabled: z.boolean().optional(),
  required: z
    .boolean()
    .refine((val) => val === true, "This setting must be enabled"),
});

const meta: Meta<typeof SwitchField> = {
  title: "Fields/Switch",
  component: SwitchField,
  decorators: [WithFormProvider],
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      description: "Switch label text",
      control: "text",
    },
    description: {
      description: "Help text below the switch",
      control: "text",
    },
    disabled: {
      description: "Disable the switch",
      control: "boolean",
    },
    required: {
      description: "Mark as required field",
      control: "boolean",
    },
    labelPosition: {
      description: "Position of label relative to switch",
      control: "radio",
      options: ["right", "left"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "notifications",
    label: "Enable notifications",
    description: "Receive email alerts and updates",
  },
};

export const Required: Story = {
  args: {
    name: "required",
    label: "Required setting",
    description: "This setting is mandatory",
    required: true,
  },
};

export const Disabled: Story = {
  args: {
    name: "disabled",
    label: "Disabled switch",
    description: "This switch cannot be toggled",
    disabled: true,
  },
};

export const LeftLabel: Story = {
  args: {
    name: "leftLabel",
    label: "Label on the left",
    description: "The label appears to the left of the switch",
    labelPosition: "left",
  },
};

export const WithoutDescription: Story = {
  args: {
    name: "privacy",
    label: "Keep profile private",
  },
};

export const LongLabel: Story = {
  args: {
    name: "marketing",
    label:
      "Allow marketing communications and promotional content from our partners",
    description:
      "You can change this preference at any time in your account settings",
  },
};

export const AutoSave: Story = {
  args: {
    name: "autoSave",
    label: "Auto-save changes",
    description: "Automatically save your work every few minutes",
  },
};

export const DarkMode: Story = {
  args: {
    name: "darkMode",
    label: "Dark mode",
    description: "Use dark theme for better viewing in low light",
  },
};

export const TwoFactorAuth: Story = {
  args: {
    name: "twoFactor",
    label: "Two-factor authentication",
    description: "Add an extra layer of security to your account",
  },
};

export const EmailAlerts: Story = {
  args: {
    name: "emailAlerts",
    label: "Email alerts",
    description: "Get notified about important account activity",
  },
};
