import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { z } from "zod";

import { RadioGroupField } from "@/ui/fields/radio-group";

import { WithFormProvider } from "./WithFormProvider";

// Form validation schema for radio group stories
const _radioGroupValidationSchema = z.object({
  size: z.enum(["small", "medium", "large"]).optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  theme: z.enum(["light", "dark", "auto"]).optional(),
  plan: z.enum(["basic", "pro", "enterprise"]).optional(),
  payment: z.enum(["monthly", "yearly"]).optional(),
  delivery: z.enum(["standard", "express", "overnight"]).optional(),
  support: z.enum(["email", "chat", "phone"]).optional(),
  required: z
    .enum(["option1", "option2", "option3"])
    .refine((val) => val !== undefined, "Please select an option"),
});

const meta: Meta<typeof RadioGroupField> = {
  title: "Fields/RadioGroup",
  component: RadioGroupField,
  decorators: [WithFormProvider],
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      description: "Radio group label text",
      control: "text",
    },
    description: {
      description: "Help text below the radio group",
      control: "text",
    },
    disabled: {
      description: "Disable the entire radio group",
      control: "boolean",
    },
    required: {
      description: "Mark as required field",
      control: "boolean",
    },
    orientation: {
      description: "Layout orientation",
      control: "radio",
      options: ["vertical", "horizontal"],
    },
    variant: {
      description: "Radio group visual variant",
      control: "radio",
      options: ["default", "card"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const basicOptions = [
  { value: "small", label: "Small" },
  { value: "medium", label: "Medium" },
  { value: "large", label: "Large" },
];

const priorityOptions = [
  { value: "low", label: "Low", description: "Non-urgent tasks" },
  { value: "medium", label: "Medium", description: "Standard priority" },
  { value: "high", label: "High", description: "Important tasks" },
  { value: "urgent", label: "Urgent", description: "Critical priority" },
];

const themeOptions = [
  { value: "light", label: "Light Theme", description: "Best for daytime use" },
  { value: "dark", label: "Dark Theme", description: "Easy on the eyes" },
  { value: "auto", label: "Auto", description: "Follows system preference" },
];

const planOptions = [
  { 
    value: "basic", 
    label: "Basic Plan", 
    description: "Perfect for individuals starting out" 
  },
  { 
    value: "pro", 
    label: "Pro Plan", 
    description: "Great for growing teams and businesses" 
  },
  { 
    value: "enterprise", 
    label: "Enterprise", 
    description: "Advanced features for large organizations" 
  },
];

export const Default: Story = {
  args: {
    name: "size",
    label: "Select size",
    description: "Choose your preferred size option",
    options: basicOptions,
  },
};

export const Required: Story = {
  args: {
    name: "required",
    label: "Required selection",
    description: "You must select one of these options",
    options: basicOptions,
    required: true,
  },
};

export const WithDescriptions: Story = {
  args: {
    name: "priority",
    label: "Task priority",
    description: "Set the priority level for this task",
    options: priorityOptions,
  },
};

export const Horizontal: Story = {
  args: {
    name: "payment",
    label: "Billing cycle",
    description: "Choose how often you'd like to be billed",
    orientation: "horizontal",
    options: [
      { value: "monthly", label: "Monthly" },
      { value: "yearly", label: "Yearly" },
    ],
  },
};

export const CardVariant: Story = {
  args: {
    name: "plan",
    label: "Choose your plan",
    description: "Select the plan that best fits your needs",
    variant: "card",
    options: planOptions,
  },
};

export const Disabled: Story = {
  args: {
    name: "theme",
    label: "Theme preference",
    description: "This setting is currently disabled",
    options: themeOptions,
    disabled: true,
  },
};

export const DisabledOption: Story = {
  args: {
    name: "delivery",
    label: "Delivery method",
    description: "Some options may not be available",
    options: [
      { value: "standard", label: "Standard Delivery", description: "5-7 business days" },
      { value: "express", label: "Express Delivery", description: "2-3 business days" },
      { 
        value: "overnight", 
        label: "Overnight Delivery", 
        description: "Next business day", 
        disabled: true 
      },
    ],
  },
};

export const SupportChannels: Story = {
  args: {
    name: "support",
    label: "Preferred support channel",
    description: "How would you like to receive support?",
    options: [
      { 
        value: "email", 
        label: "Email Support", 
        description: "Response within 24 hours" 
      },
      { 
        value: "chat", 
        label: "Live Chat", 
        description: "Instant messaging support" 
      },
      { 
        value: "phone", 
        label: "Phone Support", 
        description: "Direct phone line access" 
      },
    ],
  },
};

export const HorizontalCards: Story = {
  args: {
    name: "theme",
    label: "Theme preference",
    description: "Choose your preferred interface theme",
    orientation: "horizontal",
    variant: "card",
    options: themeOptions,
  },
};
