import type { Decorator } from "@storybook/react";
import type { FieldVal<PERSON>, SubmitHandler } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { fn } from "storybook/test";
import { z } from "zod";

import { Form } from "@/ui/primitives/form";

const noOpSubmit: SubmitHandler<FieldValues> = (data, event) => {
  return fn(() => ({ data, event }))();
};

export interface BaseFormProviderArgs<
  TFieldName extends string = string,
  TFieldSchema extends z.ZodTypeAny = z.ZodTypeAny,
> {
  fieldName: TFieldName;
  fieldSchema: TFieldSchema;
  fieldValue?: z.infer<TFieldSchema>;
  onFormSubmit?: SubmitHandler<
    z.infer<z.ZodObject<Record<TFieldName, TFieldSchema>>>
  >;
}

export const WithFormProvider: Decorator = (Story, context) => {
  const { fieldName, fieldSchema, fieldValue, onFormSubmit } =
    context.args as unknown as BaseFormProviderArgs; // Fixed: Cast via unknown

  const FormWrapper = () => {
    // Removed redundant check for fieldName and fieldSchema here

    const dynamicSchema = z.object({
      [fieldName]: fieldSchema, // fieldName and fieldSchema are guaranteed by outer conditional
    });
    type DynamicFormValues = z.infer<typeof dynamicSchema>;

    const methods = useForm<DynamicFormValues>({
      resolver: zodResolver(dynamicSchema),
      defaultValues: {
        [fieldName]: fieldValue,
      } as DynamicFormValues,
    });

    const handleActualSubmit = onFormSubmit ?? noOpSubmit;

    return (
      <FormProvider {...methods}>
        <Form {...methods}>
          <form
            onSubmit={methods.handleSubmit(
              handleActualSubmit as SubmitHandler<DynamicFormValues>,
            )}
            className="w-80 space-y-4"
          >
            <Story />
          </form>
        </Form>
      </FormProvider>
    );
  };

  return <FormWrapper />;
};
