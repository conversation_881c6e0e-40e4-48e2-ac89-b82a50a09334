import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";

import { z } from "zod";

import { FileField } from "@/ui/fields/files/file";

import { WithFormProvider } from "../WithFormProvider";

// Form validation schema for validation stories
const fileValidationSchema = z.object({
  requiredFile: z
    .instanceof(File, { message: "Please select a file" })
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB",
    )
    .refine(
      (file) => file.type.startsWith("image/"),
      "Only image files are allowed",
    ),
  optionalFile: z.instanceof(File).optional(),
  file: z.instanceof(File).optional(),
  image: z.instanceof(File).optional(),
  document: z.instanceof(File).optional(),
  video: z.instanceof(File).optional(),
  audio: z.instanceof(File).optional(),
  uploadFile: z.instanceof(File).optional(),
  compactFile: z.instanceof(File).optional(),
  noPreview: z.instanceof(File).optional(),
  customFile: z.instanceof(File).optional(),
  multipleTypes: z.instanceof(File).optional(),
  disabledFile: z.instanceof(File).optional(),
  avatar: z.instanceof(File).optional(),
  archive: z.instanceof(File).optional(),
  largeFile: z.instanceof(File).optional(),
});

const meta: Meta<typeof FileField> = {
  title: "Fields/Files/File",
  component: FileField,
  decorators: [WithFormProvider],
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    accept: {
      description: "File types to accept",
      control: "text",
    },
    maxSize: {
      description: "Maximum file size in bytes",
      control: "number",
    },
    showPreview: {
      description: "Whether to show file preview",
      control: "boolean",
    },
    progress: {
      description: "Upload progress (0-100)",
      control: { type: "range", min: 0, max: 100, step: 1 },
    },
    compact: {
      description: "Compact layout",
      control: "boolean",
    },
    disabled: {
      description: "Disable the input",
      control: "boolean",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "file",
    label: "File Upload",
    description: "Upload any type of file",
  },
};

export const ImageOnly: Story = {
  args: {
    name: "image",
    label: "Image Upload",
    description: "Upload an image file",
    accept: "image/*",
    allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    maxSize: 5 * 1024 * 1024, // 5MB
    placeholder: "Drop your image here or click to browse",
  },
};

export const DocumentUpload: Story = {
  args: {
    name: "document",
    label: "Document Upload",
    description: "Upload PDF or Word documents",
    accept: ".pdf,.doc,.docx",
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
    maxSize: 10 * 1024 * 1024, // 10MB
    placeholder: "Upload your document",
  },
};

export const VideoUpload: Story = {
  args: {
    name: "video",
    label: "Video Upload",
    description: "Upload a video file",
    accept: "video/*",
    allowedTypes: ["video/mp4", "video/webm", "video/ogg"],
    maxSize: 50 * 1024 * 1024, // 50MB
    placeholder: "Drop your video here",
  },
};

export const AudioUpload: Story = {
  args: {
    name: "audio",
    label: "Audio Upload",
    description: "Upload an audio file",
    accept: "audio/*",
    allowedTypes: ["audio/mp3", "audio/wav", "audio/ogg"],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
};

export const WithProgress: Story = {
  args: {
    name: "uploadFile",
    label: "File Upload with Progress",
    description: "Uploading file...",
    progress: 65,
    accept: "image/*",
    placeholder: "Uploading your file...",
  },
};

export const CompactMode: Story = {
  args: {
    name: "compactFile",
    label: "Compact File Upload",
    description: "A more compact file upload component",
    compact: true,
    showPreview: false,
    placeholder: "Choose file",
  },
};

export const NoPreview: Story = {
  args: {
    name: "noPreview",
    label: "File Upload (No Preview)",
    description: "Upload file without preview",
    showPreview: false,
    accept: "*/*",
  },
};

export const CustomPlaceholder: Story = {
  args: {
    name: "customFile",
    label: "Custom Placeholder",
    description: "File upload with custom placeholder text",
    placeholder: "📎 Attach your files here",
    dragActiveText: "✨ Release to upload!",
  },
};

export const MultipleFileTypes: Story = {
  args: {
    name: "multipleTypes",
    label: "Multiple File Types",
    description: "Accept various file types with different restrictions",
    allowedTypes: [
      "image/jpeg",
      "image/png",
      "application/pdf",
      "text/plain",
      "application/zip",
    ],
    maxSize: 15 * 1024 * 1024, // 15MB
    placeholder: "Upload images, PDFs, text files, or ZIP archives",
  },
};

export const Disabled: Story = {
  args: {
    name: "disabledFile",
    label: "Disabled File Upload",
    description: "This file upload is disabled",
    disabled: true,
    placeholder: "File upload is disabled",
  },
};

export const WithValidation: Story = {
  args: {
    name: "requiredFile",
    label: "Required Image Upload",
    description: "This field is required and must be an image under 5MB",
    accept: "image/*",
    maxSize: 5 * 1024 * 1024,
  },
};

export const OptionalFile: Story = {
  args: {
    name: "optionalFile",
    label: "Optional File Upload",
    description: "This field is optional",
    placeholder: "Optionally upload a file",
  },
};

// Avatar upload example
export const AvatarUpload: Story = {
  args: {
    name: "avatar",
    label: "Profile Picture",
    description: "Upload your profile picture",
    accept: "image/*",
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    maxSize: 2 * 1024 * 1024, // 2MB
    placeholder: "Upload your avatar",
    compact: true,
  },
};

// Archive upload example
export const ArchiveUpload: Story = {
  args: {
    name: "archive",
    label: "Archive Upload",
    description: "Upload compressed files",
    accept: ".zip,.rar,.tar,.gz,.7z",
    allowedTypes: [
      "application/zip",
      "application/x-rar-compressed",
      "application/x-tar",
      "application/gzip",
    ],
    maxSize: 100 * 1024 * 1024, // 100MB
    placeholder: "Drop your archive here",
  },
};

// Large file upload
export const LargeFileUpload: Story = {
  args: {
    name: "largeFile",
    label: "Large File Upload",
    description: "Upload large files up to 500MB",
    maxSize: 500 * 1024 * 1024, // 500MB
    placeholder: "Upload large files",
    progress: 25,
  },
};
