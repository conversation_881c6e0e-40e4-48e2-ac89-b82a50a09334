import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { ImageFieldProps } from "@/ui/fields/files/image";

import { ImageField } from "@/ui/fields/files/image";

import { WithFormProvider } from "../WithFormProvider";

const meta: Meta<typeof ImageField> = {
  component: ImageField,
  title: "Fields/Files/ImageField",
  decorators: [WithFormProvider],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<ImageFieldProps>;

// Base story configuration
const baseArgs: ImageFieldProps = {
  name: "image",
  label: "Upload Image",
  description: "Please upload an image file.",
};

// Stories
export const Default: Story = {
  args: {
    ...baseArgs,
  },
};

export const Square: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "square",
    label: "Profile Picture",
    description: "Upload a square profile picture (1:1 aspect ratio).",
  },
};

export const Portrait: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "portrait",
    label: "Portrait Photo",
    description: "Upload a portrait image (3:4 aspect ratio).",
  },
};

export const Landscape: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "landscape",
    label: "Landscape Photo",
    description: "Upload a landscape image (4:3 aspect ratio).",
  },
};

export const Wide: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "wide",
    label: "Banner Image",
    description: "Upload a wide banner image (16:9 aspect ratio).",
  },
};

export const Circle: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "circle",
    label: "Avatar",
    description: "Upload a circular avatar image.",
  },
};

export const CustomAspectRatio: Story = {
  args: {
    ...baseArgs,
    aspectRatio: 2.5,
    label: "Custom Ratio",
    description: "Upload an image with 2.5:1 custom aspect ratio.",
  },
};

export const WithProgress: Story = {
  args: {
    ...baseArgs,
    progress: 65,
    aspectRatio: "square",
    description: "Image upload in progress... 65% complete",
  },
};

export const WithProgressComplete: Story = {
  args: {
    ...baseArgs,
    progress: 100,
    aspectRatio: "square",
    description: "Image upload completed successfully!",
  },
};

export const WithMaxSize: Story = {
  args: {
    ...baseArgs,
    maxSize: 1024 * 1024 * 5, // 5MB
    description: "Upload an image file. Maximum size is 5MB.",
  },
};

export const Disabled: Story = {
  args: {
    ...baseArgs,
    disabled: true,
    description: "Image upload is disabled.",
  },
};

export const WithoutPreview: Story = {
  args: {
    ...baseArgs,
    showPreview: false,
    description: "Image upload without preview functionality.",
  },
};

export const CustomPlaceholder: Story = {
  args: {
    ...baseArgs,
    placeholder: "Drop your amazing photo here or click to browse",
    description: "Custom placeholder text for the upload area.",
  },
};

export const AcceptOnlyJPEG: Story = {
  args: {
    ...baseArgs,
    accept: "image/jpeg",
    description: "Only JPEG images are allowed.",
  },
};

export const AvatarUpload: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "circle",
    maxSize: 1024 * 1024 * 2, // 2MB
    label: "Profile Avatar",
    description: "Upload your profile avatar (circular, max 2MB).",
    placeholder: "Choose your avatar image",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Perfect for user avatar uploads with circular preview and size constraints.",
      },
    },
  },
};

export const BannerUpload: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "wide",
    maxSize: 1024 * 1024 * 10, // 10MB
    label: "Cover Banner",
    description: "Upload a cover banner image (16:9, max 10MB).",
    placeholder: "Choose your banner image",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Ideal for cover photos, banners, and hero images with wide aspect ratio.",
      },
    },
  },
};

export const ProductImage: Story = {
  args: {
    ...baseArgs,
    aspectRatio: "square",
    maxSize: 1024 * 1024 * 3, // 3MB
    accept: "image/jpeg,image/png,image/webp",
    label: "Product Photo",
    description: "Upload a product image (square, JPEG/PNG/WebP, max 3MB).",
    placeholder: "Choose product image",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Optimized for e-commerce product images with square aspect ratio.",
      },
    },
  },
};
