import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { VideoFieldProps } from "@/ui/fields/files/video";

import { VideoField } from "@/ui/fields/files/video";

import { WithFormProvider } from "../WithFormProvider";

const meta: Meta<typeof VideoField> = {
  component: VideoField,
  title: "Fields/Files/VideoField",
  decorators: [WithFormProvider],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<VideoFieldProps>;

// Base story configuration
const baseArgs: VideoFieldProps = {
  name: "video",
  label: "Upload Video",
  description: "Please upload a video file.",
};

// Stories
export const Default: Story = {
  args: {
    ...baseArgs,
  },
};

export const WithProgress: Story = {
  args: {
    ...baseArgs,
    progress: 75,
    description: "Video upload in progress... 75% complete",
  },
};

export const WithProgressComplete: Story = {
  args: {
    ...baseArgs,
    progress: 100,
    description: "Video upload completed successfully!",
  },
};

export const WithMaxSize: Story = {
  args: {
    ...baseArgs,
    maxSize: 1024 * 1024 * 50, // 50MB
    description: "Upload a video file. Maximum size is 50MB.",
  },
};

export const Disabled: Story = {
  args: {
    ...baseArgs,
    disabled: true,
    description: "Video upload is disabled.",
  },
};

export const WithoutPreview: Story = {
  args: {
    ...baseArgs,
    showPreview: false,
    description: "Video upload without preview functionality.",
  },
};

export const CustomPlaceholder: Story = {
  args: {
    ...baseArgs,
    placeholder: "Drop your awesome video here or click to browse",
    description: "Custom placeholder text for the upload area.",
  },
};

export const AcceptMultipleFormats: Story = {
  args: {
    ...baseArgs,
    accept: "video/mp4,video/mov,video/avi,video/webm",
    description: "Accepts MP4, MOV, AVI, and WebM video formats.",
  },
};

export const LargeVideoSupport: Story = {
  args: {
    ...baseArgs,
    maxSize: 1024 * 1024 * 500, // 500MB
    description: "Supports large video files up to 500MB.",
    progress: 45,
  },
};

export const WithValidation: Story = {
  args: {
    ...baseArgs,
    description: "Video file is required for this field.",
  },
  parameters: {
    docs: {
      description: {
        story: "This story demonstrates form validation for the video field.",
      },
    },
  },
};
