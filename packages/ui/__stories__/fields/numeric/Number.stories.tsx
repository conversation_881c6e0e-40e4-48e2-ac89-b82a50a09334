import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { NumberFieldProps } from "@/ui/fields/numeric/Number";

import { NumberField } from "@/ui/fields/numeric/Number";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define args type for this specific story
interface CurrentNumberStoryArgs
  extends NumberFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: z.ZodEffects<z.ZodNumber, number, number> | z.ZodNumber; // Accommodate refined schemas
  defaultValues?: Record<string, string | number | undefined>;
}

const meta: Meta<CurrentNumberStoryArgs> = {
  title: "Fields/Numeric/NumberField",
  component: NumberField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for NumberField component itself
    label: "Quantity",
    name: "quantity",
    placeholder: "Enter a whole number",
    description: "Specify the number of items.",
    min: 0,
    allowDecimals: false,

    // Args for the WithFormProvider decorator
    fieldName: "quantity",
    fieldSchema: z.coerce
      .number()
      .min(0, "Quantity must be 0 or greater.")
      .int("Quantity must be a whole number."),
    onFormSubmit: fn(),
    defaultValues: { quantity: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentNumberStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialQuantity",
    fieldName: "initialQuantity",
    defaultValues: { initialQuantity: "123" },
    fieldSchema: z.coerce.number().min(0).int(),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter a whole number");
    await expect(input).toHaveValue(123);
  },
};

export const InteractiveInteger: Story = {
  args: {
    name: "interactiveQty",
    fieldName: "interactiveQty",
    min: 1,
    max: 10,
    allowDecimals: false,
    fieldSchema: z.coerce.number().min(1).max(10).int(),
    defaultValues: { interactiveQty: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter a whole number");

    await userEvent.type(input, "5");
    await expect(input).toHaveValue(5);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    // The component stores values as strings after its own parsing
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "5" },
      expect.anything(),
    );
  },
};

export const AllowDecimals: Story = {
  args: {
    name: "decimalValue",
    fieldName: "decimalValue",
    label: "Price",
    placeholder: "Enter a price",
    description: "Price, can include two decimal places.",
    min: 0.01,
    allowDecimals: true,
    step: 0.01,
    fieldSchema: z.coerce.number().min(0.01, "Price must be at least 0.01."),
    defaultValues: { decimalValue: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter a price");
    await userEvent.type(input, "123.45");
    await expect(input).toHaveValue(123.45);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "123.45" },
      expect.anything(),
    );
  },
};

export const AllowNegative: Story = {
  args: {
    name: "temperature",
    fieldName: "temperature",
    label: "Temperature (°C)",
    placeholder: "Enter temperature",
    allowNegative: true,
    allowDecimals: true,
    fieldSchema: z.coerce.number(), // Basic schema, component handles negativity
    defaultValues: { temperature: "-5.5" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter temperature");
    await expect(input).toHaveValue(-5.5);
  },
};

export const WithErrorMin: Story = {
  args: {
    name: "minErrorQty",
    fieldName: "minErrorQty",
    min: 5,
    allowDecimals: false,
    fieldSchema: z.coerce.number().min(5, "Value must be at least 5.").int(),
    defaultValues: { minErrorQty: "3" }, // Value below min
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Message from component's internal validation or Zod
    await expect(
      canvas.getByText("Value must be at least 5"),
    ).toBeInTheDocument();
  },
};

export const WithErrorNotInteger: Story = {
  args: {
    name: "notIntQty",
    fieldName: "notIntQty",
    allowDecimals: false,
    fieldSchema: z.coerce.number().int("Must be a whole number."),
    defaultValues: { notIntQty: "3.14" }, // Decimal value when not allowed
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Message from component's internal validation or Zod
    await expect(
      canvas.getByText("Please enter a whole number"),
    ).toBeInTheDocument();
  },
};
