import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { PhoneNumberFieldProps } from "@/ui/fields/numeric/PhoneNumber";

import { PhoneNumberField } from "@/ui/fields/numeric/PhoneNumber";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelPhoneSchema = z
  .string()
  .regex(/^\d{10}$/, "Phone number must be exactly 10 digits.");

// Define args type for this specific story
interface CurrentPhoneStoryArgs
  extends PhoneNumberFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelPhoneSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentPhoneStoryArgs> = {
  title: "Fields/Numeric/PhoneNumberField",
  component: PhoneNumberField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for PhoneNumberField component itself
    label: "Primary Phone",
    name: "primaryPhone", // This will be the key in the form data
    placeholder: "(*************",
    description: "Enter your 10-digit phone number.",

    // Args for the WithFormProvider decorator
    fieldName: "primaryPhone", // Must match the 'name' prop above
    fieldSchema: storyLevelPhoneSchema,
    onFormSubmit: fn(),
    defaultValues: { primaryPhone: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentPhoneStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialPhone",
    fieldName: "initialPhone",
    defaultValues: { initialPhone: "**********" }, // Raw digits
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("(*************");
    // The component formats the raw digits for display
    await expect(input).toHaveValue("(*************");
  },
};

export const Interactive: Story = {
  args: {
    name: "interactivePhone",
    fieldName: "interactivePhone",
    defaultValues: { interactivePhone: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("(*************");

    // User types, maskito formats it
    await userEvent.type(input, "5551234567");
    await expect(input).toHaveValue("(*************");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    // The component submits raw digits
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "5551234567" },
      expect.anything(),
    );
  },
};

export const WithErrorTooShort: Story = {
  args: {
    name: "shortPhone",
    fieldName: "shortPhone",
    defaultValues: { shortPhone: "12345" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("(*************");
    await expect(input).toHaveValue("(123) 45"); // Mask attempts to format

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Phone number must be exactly 10 digits."),
    ).toBeInTheDocument();
  },
};

export const WithErrorTooLong: Story = {
  args: {
    name: "longPhone",
    fieldName: "longPhone",
    defaultValues: { longPhone: "1234567890123" }, // Mask will likely truncate input to 10 digits
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("(*************");
    // Type more than 10 digits; mask should prevent overflow or use first 10
    await userEvent.clear(input);
    await userEvent.type(input, "1234567890123");
    await expect(input).toHaveValue("(*************"); // Mask keeps it to 10 digits

    // This will pass because the mask ensures it's 10 digits before Zod sees it.
    // To truly test 'too long' against Zod, the mask would need to allow more digits.
    // For this setup, the mask effectively prevents 'too long' for Zod.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "1234567890" }, // Mask ensures 10 digits
      expect.anything(),
    );
  },
};

export const WithErrorNonNumeric: Story = {
  args: {
    name: "nonNumericPhone",
    fieldName: "nonNumericPhone",
    defaultValues: { nonNumericPhone: "abcdefghij" }, // Maskito should prevent this
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("(*************");

    // Clear existing value if any (from defaultValues which mask might format)
    await userEvent.clear(input);
    await userEvent.type(input, "abcdefghij");
    // Maskito prevents non-numeric input, so value should be empty or only contain digits if any were typed
    await expect(input).toHaveValue("");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Phone number must be exactly 10 digits."),
    ).toBeInTheDocument();
  },
};
