import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { CurrencyFieldProps } from "@/ui/fields/numeric/Currency";

import { CurrencyField } from "@/ui/fields/numeric/Currency";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelCurrencySchema = z.coerce
  .number({ invalid_type_error: "Please enter a valid number." })
  .min(0, "Amount must be zero or positive.");

// Define args type for this specific story
interface CurrentCurrencyStoryArgs
  extends CurrencyFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // Making fieldName flexible for different currency contexts
  fieldSchema: typeof storyLevelCurrencySchema;
  defaultValues: Record<string, number | undefined>;
}

const meta: Meta<CurrentCurrencyStoryArgs> = {
  title: "Fields/Numeric/CurrencyField",
  component: CurrencyField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for CurrencyField component itself
    label: "Item Price",
    placeholder: "Enter amount (e.g., $123.45)",
    description: "The price of the item in USD.",

    // Args for the WithFormProvider decorator
    fieldName: "itemPrice", // Example field name
    fieldSchema: storyLevelCurrencySchema,
    onChange: fn(),
    onFormSubmit: fn(),
    defaultValues: { itemPrice: undefined }, // Default to undefined or a specific number
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentCurrencyStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Default story uses meta.args
    // Ensure a unique fieldName if multiple Default stories are needed or rely on meta.args
  },
};

export const WithInitialValue: Story = {
  args: {
    fieldName: "initialPrice",
    defaultValues: { initialPrice: 1234.56 },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter amount (e.g., $123.45)");
    // Maskito formats the number, so we expect the formatted string
    await expect(input).toHaveValue("$1,234.56");
  },
};

export const Interactive: Story = {
  args: {
    fieldName: "interactivePrice",
    defaultValues: { interactivePrice: undefined },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter amount (e.g., $123.45)");

    // Type into the currency field
    // User types numbers, maskito formats it
    await userEvent.clear(input);
    await userEvent.type(input, "7890.12");
    await expect(input).toHaveValue("$7,890.12"); // Check formatted value

    // Click submit
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert onFormSubmit was called with the numeric value
    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.fieldName]: 7890.12 }, // Expecting the raw number
      expect.anything(),
    );
  },
};

export const WithErrorTooLow: Story = {
  args: {
    fieldName: "errorPriceLow",
    fieldSchema: z.coerce.number().min(10, "Amount must be at least $10."),
    defaultValues: { errorPriceLow: 5 }, // Value below the min constraint
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter amount (e.g., $123.45)");
    await expect(input).toHaveValue("$5.00");

    // Attempt to submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert onFormSubmit was NOT called because of validation
    await expect(args.onFormSubmit).not.toHaveBeenCalled();

    // Assert error message is shown
    await expect(
      canvas.getByText("Amount must be at least $10."),
    ).toBeInTheDocument();
  },
};

export const WithErrorInvalidInput: Story = {
  args: {
    fieldName: "errorPriceInvalid",
    defaultValues: { errorPriceInvalid: undefined },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter amount (e.g., $123.45)");

    // Type invalid characters
    await userEvent.type(input, "abc");
    // Maskito might prevent totally invalid chars or clear them,
    // but if it allows submission of an empty/unparseable value, Zod should catch it.
    // If input remains empty or effectively unparseable after typing "abc"
    // and the schema requires a number.

    // Click submit
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Check for Zod's 'invalid_type_error' message
    await expect(
      canvas.getByText("Please enter a valid number."),
    ).toBeInTheDocument();
  },
};
