import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form";

import { format } from "date-fns";
import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { DateFieldProps } from "@/ui/fields/date-time/Date";

import { DateField } from "@/ui/fields/date-time/Date";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelDateSchema = z.date({
  required_error: "Please select a date.",
  invalid_type_error: "That's not a valid date!",
});

// Define args type for this specific story
interface CurrentDateStoryArgs
  extends DateFieldProps,
    // Use string for TFieldName because stories override it (e.g. "initialDate", "interactiveDate")
    // Use typeof storyLevelDateSchema for TFieldSchema for type safety with z.date()
    BaseFormProviderArgs<string, typeof storyLevelDateSchema> {
  defaultValues?: Record<string, Date | undefined>; // Make defaultValues optional for stories that don't set it
}

const meta: Meta<CurrentDateStoryArgs> = {
  title: "Fields/DateTime/DateField",
  component: DateField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for DateField component itself
    label: "Event Date",
    name: "date", // Must be one of DateFieldProps['name']
    description: "Select the date for the event.",
    placeholder: "Pick a date",

    // Args for the WithFormProvider decorator
    fieldName: "eventDate", // This is the RHF field name
    fieldSchema: storyLevelDateSchema,
    onFormSubmit: fn() as SubmitHandler<Record<string, Date>>,
    defaultValues: { eventDate: undefined },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentDateStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

const testDate = new Date(2024, 5, 15); // June 15, 2024 (month is 0-indexed)

export const WithInitialValue: Story = {
  args: {
    name: "date", // Must be one of DateFieldProps['name']
    fieldName: "initialDate", // RHF field name
    defaultValues: { initialDate: testDate },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = canvas.getByRole("button", { name: /pick a date/i });
    // The button text should display the formatted date
    await expect(button).toHaveTextContent(format(testDate, "PPP"));
  },
};

export const Interactive: Story = {
  args: {
    name: "date", // Must be one of DateFieldProps['name']
    fieldName: "interactiveDate", // RHF field name
    defaultValues: { interactiveDate: undefined },
    // Disable past dates for a more realistic interactive scenario
    disabled: (date) => date < new Date(new Date().setHours(0, 0, 0, 0)),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const today = new Date();
    const targetDay = today.getDate() + 1; // Pick tomorrow
    const targetDate = new Date(today.setDate(targetDay));

    // Open the calendar
    await userEvent.click(
      canvas.getByRole("button", { name: args.placeholder }),
    );

    // Click on the day (e.g., 15th of the current month/year in the calendar)
    // Note: This selector might need adjustment based on how react-day-picker renders days
    await userEvent.click(
      canvas.getByRole("gridcell", { name: targetDay.toString() }),
    );

    // Check if the button text updated
    await expect(
      canvas.getByRole("button", { name: format(targetDate, "PPP") }),
    ).toBeInTheDocument();

    // Click submit
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);

    const onFormSubmitFn = args.onFormSubmit;
    if (!onFormSubmitFn) {
      throw new Error(
        "Test Error: args.onFormSubmit is undefined. This is required for the Interactive story's play function.",
      );
    }
    const mockSubmit = onFormSubmitFn as ReturnType<typeof fn>;

    const firstCallArgs = mockSubmit.mock.calls[0];

    if (firstCallArgs === undefined || firstCallArgs[0] === undefined) {
      // This should be unreachable if toHaveBeenCalledTimes(1) passed
      // and the submit handler received its arguments correctly.
      throw new Error(
        "Test Error: Expected onFormSubmit to have been called with defined arguments.",
      );
    }

    const formData = firstCallArgs[0] as Record<string, Date>;
    const submittedDate = formData[args.fieldName];

    if (submittedDate === undefined) {
      throw new Error(
        `Test Error: submittedDate for field '${args.fieldName}' is undefined. This should not happen after successful form validation.`,
      );
    }
    // Now submittedDate is confirmed to be Date
    expect(format(submittedDate, "yyyy-MM-dd")).toEqual(
      format(targetDate, "yyyy-MM-dd"),
    );
  },
};

export const WithError: Story = {
  args: {
    name: "date", // Must be one of DateFieldProps['name']
    fieldName: "errorDate", // RHF field name
    defaultValues: { errorDate: undefined }, // No date selected
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Attempt to submit the form without selecting a date
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert onFormSubmit was NOT called
    await expect(args.onFormSubmit).not.toHaveBeenCalled();

    // Assert error message is shown
    await expect(canvas.getByText("Please select a date.")).toBeInTheDocument();
  },
};
