import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form";

import { format } from "date-fns";
import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { DateTimeFieldProps } from "@/ui/fields/date-time/DateTime";

import { DateTimeField } from "@/ui/fields/date-time/DateTime";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema factory for this story's form validation needs
const createStoryLevelDateTimeSchema = (minDate?: Date, maxDate?: Date) =>
  z
    .date({
      invalid_type_error: "Please select a valid date and time.",
      required_error: "Date and time cannot be empty.",
    })
    .refine((date) => !minDate || date >= minDate, {
      message: `Date must be on or after ${minDate ? format(minDate, "MMM d, yyyy p") : "the minimum date"}.`,
    })
    .refine((date) => !maxDate || date <= maxDate, {
      message: `Date must be on or before ${maxDate ? format(maxDate, "MMM d, yyyy p") : "the maximum date"}.`,
    });

// Define args type for this specific story
interface CurrentDateTimeStoryArgs
  extends DateTimeFieldProps,
    BaseFormProviderArgs<
      string,
      ReturnType<typeof createStoryLevelDateTimeSchema>
    > {
  fieldName: string; // To match the 'name' prop for the decorator
  defaultValues?: Record<string, Date | undefined>;
}

const meta: Meta<CurrentDateTimeStoryArgs> = {
  title: "Fields/DateTime/DateTimeField",
  component: DateTimeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for DateTimeField component itself
    label: "Appointment Time",
    name: "appointmentDateTime",
    description: "Select the date and time for the appointment.",

    // Args for the WithFormProvider decorator
    fieldName: "appointmentDateTime",
    fieldSchema: createStoryLevelDateTimeSchema(),
    onFormSubmit: fn() as SubmitHandler<Record<string, Date>>,
    defaultValues: { appointmentDateTime: undefined },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentDateTimeStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    fieldSchema: createStoryLevelDateTimeSchema(),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Check that the DatetimePicker is rendered (presence of its specific segments)
    // This is a basic check, specific segment values are hard to assert without knowing the primitive's structure
    await expect(canvas.getByText("Month")).toBeInTheDocument(); // Assuming DatetimePicker has such labels or segments
    await expect(canvas.getByText("Day")).toBeInTheDocument();
    await expect(canvas.getByText("Year")).toBeInTheDocument();
  },
};

const initialDate = new Date(2024, 5, 15, 10, 30, 0); // June 15, 2024, 10:30 AM

export const WithInitialValue: Story = {
  args: {
    name: "initialEventTime",
    fieldName: "initialEventTime",
    defaultValues: { initialEventTime: initialDate },
    fieldSchema: createStoryLevelDateTimeSchema(),
    onFormSubmit: fn() as SubmitHandler<Record<string, Date>>,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // DatetimePicker's display format is complex. We'll check if the calendar icon shows the formatted date.
    // The component internally formats the value for display in the DatetimePicker.
    // A more robust test would involve inspecting the DatetimePicker's segments if possible.
    // For now, we trust the component sets its internal state correctly.
    // Let's try to submit and check the value.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Access onFormSubmit from the story's own args
    const { onFormSubmit, fieldName } = args;
    if (!onFormSubmit) {
      throw new Error(
        "onFormSubmit mock must be provided in WithInitialValue.args",
      );
    }
    if (!fieldName) {
      throw new Error("fieldName must be provided in WithInitialValue.args");
    }

    await expect(onFormSubmit).toHaveBeenCalledWith(
      { [fieldName]: initialDate },
      expect.anything(),
    );
  },
};

export const InteractiveCalendarChange: Story = {
  args: {
    name: "calendarChangeEventTime",
    fieldName: "calendarChangeEventTime",
    defaultValues: { calendarChangeEventTime: new Date(2024, 5, 15, 14, 0, 0) }, // June 15, 2024, 2:00 PM
    fieldSchema: createStoryLevelDateTimeSchema(),
    onFormSubmit: fn() as SubmitHandler<Record<string, Date>>,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const calendarButton = canvas.getByRole("button", {
      name: /select date from calendar/i,
    });
    await userEvent.click(calendarButton);

    // Calendar opens, select a new date (e.g., 20th)
    const dateToSelect = canvas.getByRole("gridcell", { name: "20" });
    await userEvent.click(dateToSelect);

    // Calendar should close, DatetimePicker should update the date part, time should be preserved (2:00 PM)
    // The exact display update is hard to assert without deep knowledge of DatetimePicker.
    // We'll verify by submitting.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    const { onFormSubmit, name: storyName } = args; // Use storyName to avoid conflict with DOM element name property
    if (!onFormSubmit) {
      throw new Error(
        "onFormSubmit mock must be provided in InteractiveCalendarChange.args",
      );
    }
    if (!storyName) {
      throw new Error(
        "name must be provided in InteractiveCalendarChange.args for field key",
      );
    }

    const expectedDate = new Date(2024, 5, 20, 14, 0, 0);
    await expect(onFormSubmit).toHaveBeenCalledWith(
      { [storyName]: expectedDate },
      expect.anything(),
    );
  },
};

const minDateValidation = new Date(2024, 6, 10, 0, 0, 0); // July 10, 2024, 00:00
const invalidDateBeforeMin = new Date(2024, 6, 5, 10, 0, 0); // July 5, 2024, 10:00 AM

export const WithErrorBeforeMinDateTime: Story = {
  args: {
    name: "minDateTimeCheck",
    fieldName: "minDateTimeCheck",
    minDateTime: minDateValidation,
    defaultValues: { minDateTimeCheck: invalidDateBeforeMin },
    fieldSchema: createStoryLevelDateTimeSchema(minDateValidation),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Component's internal validation message is used here
    const expectedMessage = `Must be after ${format(minDateValidation, "yyyy-MM-dd")}`;
    await expect(canvas.getByText(expectedMessage)).toBeInTheDocument();
  },
};

const maxDateValidation = new Date(2024, 7, 20, 23, 59, 59); // August 20, 2024, 23:59:59
const invalidDateAfterMax = new Date(2024, 7, 25, 10, 0, 0); // August 25, 2024, 10:00 AM

export const WithErrorAfterMaxDateTime: Story = {
  args: {
    name: "maxDateTimeCheck",
    fieldName: "maxDateTimeCheck",
    maxDateTime: maxDateValidation,
    defaultValues: { maxDateTimeCheck: invalidDateAfterMax },
    fieldSchema: createStoryLevelDateTimeSchema(undefined, maxDateValidation),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Component's internal validation message
    const expectedMessage = `Must be before ${format(maxDateValidation, "yyyy-MM-dd")}`;
    await expect(canvas.getByText(expectedMessage)).toBeInTheDocument();
  },
};

export const Disabled: Story = {
  args: {
    name: "disabledDateTime",
    fieldName: "disabledDateTime",
    defaultValues: { disabledDateTime: new Date() },
    disabled: true,
    fieldSchema: createStoryLevelDateTimeSchema(),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Check if the DatetimePicker's inputs (if any are standard inputs) are disabled
    // Or check if the calendar button is disabled
    const calendarButton = canvas.getByRole("button", {
      name: /select date from calendar/i,
    });
    await expect(calendarButton).toBeDisabled();
    // Further checks would depend on DatetimePicker's internal structure
  },
};
