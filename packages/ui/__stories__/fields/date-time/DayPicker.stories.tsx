import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { DayPickerFieldProps } from "@/ui/fields/date-time/DayPicker";

import DayPickerField from "@/ui/fields/date-time/DayPicker";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelDayPickerSchema = z
  .string({
    required_error: "Please select a day.",
    invalid_type_error: "Please select a day.",
  })
  .transform((val, ctx) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed) || parsed < 0 || parsed > 6) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid day selection.",
      });
      return z.NEVER;
    }
    return parsed; // The final output of the schema is a number
  });

// Define args type for this specific story
interface CurrentDayPickerStoryArgs
  extends DayPickerFieldProps,
    BaseFormProviderArgs<string, typeof storyLevelDayPickerSchema> {
  fieldName: string; // To match the 'name' prop for the decorator
  defaultValues?: Record<string, string | undefined>; // The RadioGroup uses a string value
}

const meta: Meta<CurrentDayPickerStoryArgs> = {
  title: "Fields/DateTime/DayPickerField",
  component: DayPickerField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for DayPickerField component itself
    label: "Select your preferred day for deliveries",
    name: "preferredDay",
    description: "We will try to schedule your deliveries on this day.",

    // Args for the WithFormProvider decorator
    fieldName: "preferredDay",
    fieldSchema: storyLevelDayPickerSchema,
    onFormSubmit: fn() as SubmitHandler<Record<string, number>>,
    defaultValues: { preferredDay: undefined },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentDayPickerStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialDay",
    fieldName: "initialDay",
    defaultValues: { initialDay: "3" }, // Wednesday
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // The radio item's value is its index (0-6). We find the label for it.
    const wednesdayRadio = canvas.getByLabelText("We");
    await expect(wednesdayRadio).toBeChecked();
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveDay",
    fieldName: "interactiveDay",
    defaultValues: { interactiveDay: undefined },
    onFormSubmit: fn() as SubmitHandler<Record<string, number>>,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const fridayRadio = canvas.getByLabelText("Fr");

    await userEvent.click(fridayRadio);
    await expect(fridayRadio).toBeChecked();

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    // Zod schema transforms the string "5" to the number 5
    const { name: storyName, onFormSubmit } = args;
    if (!storyName) {
      throw new Error(
        "name must be provided in Interactive.args for field key",
      );
    }
    if (!onFormSubmit) {
      throw new Error("onFormSubmit mock must be provided in Interactive.args");
    }
    await expect(onFormSubmit).toHaveBeenCalledWith(
      { [storyName]: 5 },
      expect.anything(),
    );
  },
};

export const WithError: Story = {
  args: {
    name: "errorDay",
    fieldName: "errorDay",
    defaultValues: { errorDay: undefined },
    onFormSubmit: fn() as SubmitHandler<Record<string, number>>,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(canvas.getByText("Please select a day.")).toBeInTheDocument();
  },
};
