import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { TimeRangeFieldProps } from "@/ui/fields/date-time/TimeRange";

import { TimeRangeField } from "@/ui/fields/date-time/TimeRange";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WithFormProvider } from "../WithFormProvider";

// Zod schema for the form data managed by TimeRangeField
const createTimeRangeSchema = (
  startName: string,
  endName: string,
  minDurationProp?: number, // in minutes, from component props
  maxDurationProp?: number, // in minutes, from component props
) =>
  z
    .object({
      [startName]: z
        .number({
          required_error: "Start time is required.",
          invalid_type_error: "Start time must be a number.",
        })
        .int()
        .min(0, "Time cannot be negative.")
        .max(1439, "Time cannot exceed 23:59."),
      [endName]: z
        .number({
          required_error: "End time is required.",
          invalid_type_error: "End time must be a number.",
        })
        .int()
        .min(0, "Time cannot be negative.")
        .max(1439, "Time cannot exceed 23:59."),
    })
    .superRefine((data, ctx) => {
      // After individual field parsing, startTime and endTime are guaranteed to be numbers
      // if they passed their respective schema validations (including required checks).
      // Non-null assertions used here due to TypeScript's difficulty in tracking this for dynamic keys.
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const startTime = data[startName]!;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const endTime = data[endName]!;

      // The check for null is no longer needed as the fields are non-nullable numbers.
      // Zod ensures that superRefine is only called if individual field validations pass.
      // If a required field is missing, parsing would have failed before reaching superRefine.
      if (startTime >= endTime) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Start time must be before end time.",
          path: [startName], // Or a general path for the component
        });
      }
      const duration = endTime - startTime;
      if (minDurationProp !== undefined && duration < minDurationProp) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Duration must be at least ${minDurationProp} minutes.`,
          path: [endName], // Or a general path
        });
      }
      if (maxDurationProp !== undefined && duration > maxDurationProp) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Duration must be at most ${maxDurationProp} minutes.`,
          path: [endName], // Or a general path
        });
      }
      // Removed the closing brace for the 'if (startTime !== null && endTime !== null)' block
      // as the block itself is removed.
    });

interface CurrentTimeRangeStoryArgs
  extends TimeRangeFieldProps, // Includes startName, endName, minDuration, maxDuration etc.
    Omit<BaseFormProviderArgs, "fieldName"> {
  // fieldName not used
  fieldSchema: ReturnType<typeof createTimeRangeSchema>;
  defaultValues: Record<string, number | undefined | null>; // Allow undefined for defaultValues
}

const defaultStartName = "appointmentStart";
const defaultEndName = "appointmentEnd";

const meta: Meta<CurrentTimeRangeStoryArgs> = {
  title: "Fields/DateTime/TimeRangeField",
  component: TimeRangeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for TimeRangeField component
    label: "Appointment Time",
    description: "Select the start and end time for the appointment.",
    startName: defaultStartName,
    endName: defaultEndName,
    minDuration: 30, // e.g., appointment must be at least 30 mins

    // Args for WithFormProvider decorator
    fieldSchema: createTimeRangeSchema(defaultStartName, defaultEndName, 30),
    onFormSubmit: fn() as SubmitHandler<
      z.infer<ReturnType<typeof createTimeRangeSchema>>
    >,
    defaultValues: {
      [defaultStartName]: 9 * 60, // 9:00 AM
      [defaultEndName]: 10 * 60, // 10:00 AM
    },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentTimeRangeStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Check if time pickers are rendered (they show formatted time)
    // 9:00 AM
    await expect(canvas.getByText("09")).toBeInTheDocument();
    await expect(canvas.getByText(":")).toBeInTheDocument(); // Separator
    await expect(canvas.getAllByText("00")[0]).toBeInTheDocument(); // Start minute
    // 10:00 AM
    await expect(canvas.getByText("10")).toBeInTheDocument();
    await expect(canvas.getAllByText("00")[1]).toBeInTheDocument(); // End minute
  },
};

export const WithInitialValues: Story = {
  args: {
    startName: "meetingStart",
    endName: "meetingEnd",
    minDuration: 15,
    fieldSchema: createTimeRangeSchema("meetingStart", "meetingEnd", 15),
    defaultValues: {
      meetingStart: 14 * 60 + 30, // 2:30 PM
      meetingEnd: 15 * 60, // 3:00 PM
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // 2:30 PM
    await expect(canvas.getByText("14")).toBeInTheDocument();
    await expect(canvas.getByText("30")).toBeInTheDocument();
    // 3:00 PM
    await expect(canvas.getByText("15")).toBeInTheDocument();
    await expect(canvas.getAllByText("00")[0]).toBeInTheDocument(); // Could be multiple '00's

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      args.defaultValues,
      expect.anything(),
    );
  },
};

export const ErrorStartTimeAfterEndTime: Story = {
  args: {
    startName: "errorRangeStart",
    endName: "errorRangeEnd",
    fieldSchema: createTimeRangeSchema("errorRangeStart", "errorRangeEnd"),
    defaultValues: {
      errorRangeStart: 11 * 60, // 11:00 AM
      errorRangeEnd: 10 * 60, // 10:00 AM (Error)
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Start time must be before end time."),
    ).toBeInTheDocument();
  },
};

export const ErrorMinDuration: Story = {
  args: {
    startName: "minDurationStart",
    endName: "minDurationEnd",
    minDuration: 60, // 1 hour
    fieldSchema: createTimeRangeSchema(
      "minDurationStart",
      "minDurationEnd",
      60,
    ),
    defaultValues: {
      minDurationStart: 9 * 60, // 9:00 AM
      minDurationEnd: 9 * 60 + 30, // 9:30 AM (30 min duration - Error)
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Duration must be at least 60 minutes."),
    ).toBeInTheDocument();
  },
};

export const ErrorMaxDuration: Story = {
  args: {
    startName: "maxDurationStart",
    endName: "maxDurationEnd",
    maxDuration: 120, // 2 hours
    fieldSchema: createTimeRangeSchema(
      "maxDurationStart",
      "maxDurationEnd",
      0,
      120,
    ),
    defaultValues: {
      maxDurationStart: 13 * 60, // 1:00 PM
      maxDurationEnd: 13 * 60 + 150, // 3:30 PM (150 min duration - Error)
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Duration must be at most 120 minutes."),
    ).toBeInTheDocument();
  },
};

export const ErrorRequiredFields: Story = {
  args: {
    startName: "reqStart",
    endName: "reqEnd",
    fieldSchema: createTimeRangeSchema("reqStart", "reqEnd"),
    defaultValues: {
      // For testing required fields, we can omit them or set to undefined.
      // Setting to null might satisfy a 'nullable' schema but not a 'required number' schema.
      // Let's use undefined to ensure the required_error messages are triggered.
      reqStart: undefined,
      reqEnd: undefined,
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Start time is required."),
    ).toBeInTheDocument();
    await expect(canvas.getByText("End time is required.")).toBeInTheDocument();
  },
};
