import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form";

import { addDays, format } from "date-fns";
import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { DateRangeFieldProps } from "@/ui/fields/date-time/DateRange";

// BaseFormProviderArgs is not imported as it's not directly suitable for multi-field components like DateRange

import { DateRangeField } from "@/ui/fields/date-time/DateRange";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelDateRangeSchema = z
  .object({
    // These will be dynamic based on startName and endName args
    // For the meta, we'll use default names 'startDate' and 'endDate'
    startDate: z
      .string({
        required_error: "Start date is required.",
        invalid_type_error: "Start date must be a string.",
      })
      .refine((val) => /^\d{4}-\d{2}-\d{2}$/.test(val), {
        message: "Start date must be in YYYY-MM-DD format.",
      }),
    endDate: z
      .string({
        required_error: "End date is required.",
        invalid_type_error: "End date must be a string.",
      })
      .refine((val) => /^\d{4}-\d{2}-\d{2}$/.test(val), {
        message: "End date must be in YYYY-MM-DD format.",
      }),
  })
  .refine(
    (data) => {
      // This refinement assumes defaultNames, actual names will be used in story args
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return start < end;
    },
    {
      message: "Start date must be before end date.",
      path: ["endDate"], // Point error to endDate or a general form error
    },
  );

// Define args type for this specific story, generic over the schema type
interface CurrentDateRangeStoryArgs<
  TSchema extends z.AnyZodObject | z.ZodEffects<z.AnyZodObject>,
> extends DateRangeFieldProps {
  fieldSchema: TSchema;
  onFormSubmit?: SubmitHandler<z.infer<TSchema>>;
  defaultValues?: Record<string, string | Date | undefined>; // Keep flexible for partial/undefined initial values
}

const defaultStartName = "startDate";
const defaultEndName = "endDate";

const meta: Meta<CurrentDateRangeStoryArgs<typeof storyLevelDateRangeSchema>> =
  {
    title: "Fields/DateTime/DateRangeField",
    component: DateRangeField,
    tags: ["autodocs"],
    decorators: [WithFormProvider],
    args: {
      // Props for DateRangeField component itself
      label: "Select Project Duration",
      description: "Specify the start and end dates for the project.",
      startName: defaultStartName, // Default RHF name for the start date
      endName: defaultEndName, // Default RHF name for the end date

      // Args for the WithFormProvider decorator
      fieldSchema: storyLevelDateRangeSchema, // This schema uses defaultStartName and defaultEndName
      onFormSubmit: fn() as SubmitHandler<
        z.infer<typeof storyLevelDateRangeSchema>
      >,
      defaultValues: {
        [defaultStartName]: undefined,
        [defaultEndName]: undefined,
      },
    },
    parameters: {
      layout: "centered",
    },
  } satisfies Meta<CurrentDateRangeStoryArgs<typeof storyLevelDateRangeSchema>>;

export default meta;

// Generic Story type, defaulting to the meta's schema type for convenience
type Story<
  TSchema extends
    | z.AnyZodObject
    | z.ZodEffects<z.AnyZodObject> = typeof storyLevelDateRangeSchema,
> = StoryObj<CurrentDateRangeStoryArgs<TSchema>>;

export const Default: Story = {
  args: {},
};

const today = new Date();
const tomorrow = addDays(today, 1);
const nextWeek = addDays(today, 7);

const initialValuesSchema = z.object({
  initialStart: z.string().date(),
  initialEnd: z.string().date(),
});
export const WithInitialValues: Story<typeof initialValuesSchema> = {
  args: {
    startName: "initialStart",
    endName: "initialEnd",
    defaultValues: {
      initialStart: format(tomorrow, "yyyy-MM-dd"),
      initialEnd: format(nextWeek, "yyyy-MM-dd"),
    },
    fieldSchema: initialValuesSchema,
    // onFormSubmit can be added here if needed for testing, typed with z.infer<typeof initialValuesSchema>
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(
      canvas.getByDisplayValue(format(tomorrow, "yyyy-MM-dd")),
    ).toBeInTheDocument();
    await expect(
      canvas.getByDisplayValue(format(nextWeek, "yyyy-MM-dd")),
    ).toBeInTheDocument();
  },
};

const interactiveDateRangeSchema = z
  .object({
    interactiveStart: z.string().date(),
    interactiveEnd: z.string().date(),
  })
  .refine(
    (data) => new Date(data.interactiveStart) < new Date(data.interactiveEnd),
    {
      message: "Start must be before end.",
      path: ["interactiveEnd"],
    },
  );

export const Interactive: Story<typeof interactiveDateRangeSchema> = {
  args: {
    startName: "interactiveStart",
    endName: "interactiveEnd",
    defaultValues: {
      interactiveStart: undefined,
      interactiveEnd: undefined,
    },
    fieldSchema: interactiveDateRangeSchema,
    onFormSubmit: fn() as SubmitHandler<
      z.infer<typeof interactiveDateRangeSchema>
    >,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const { onFormSubmit, startName, endName } = args;

    if (!startName || !endName) {
      throw new Error(
        "startName and endName must be provided in story args for Interactive.play",
      );
    }

    const startDateInput = canvas.getByRole("textbox", {
      name: new RegExp(startName, "i"), // Use startName from args
    });
    const endDateInput = canvas.getByRole("textbox", {
      name: new RegExp(endName, "i"), // Use endName from args
    });

    const interactiveStartDate = format(addDays(today, 2), "yyyy-MM-dd");
    const interactiveEndDate = format(addDays(today, 9), "yyyy-MM-dd");

    // Open start date picker and select a date
    await userEvent.click(startDateInput);
    await userEvent.click(
      canvas.getByText(addDays(today, 2).getDate().toString(), {
        selector: 'button[name="day"]',
      }),
    );
    await expect(startDateInput).toHaveValue(interactiveStartDate);

    // Open end date picker and select a date
    await userEvent.click(endDateInput);
    await userEvent.click(
      canvas.getByText(addDays(today, 9).getDate().toString(), {
        selector: 'button[name="day"]',
      }),
    );
    await expect(endDateInput).toHaveValue(interactiveEndDate);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    if (!onFormSubmit) {
      // Type guard for onFormSubmit from args
      throw new Error(
        "onFormSubmit mock must be provided in story args for Interactive.play",
      );
    }
    await expect(onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(onFormSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        [startName]: interactiveStartDate,
        [endName]: interactiveEndDate,
      }),
      expect.anything(),
    );
  },
};

const errorEndBeforeStartSchema = z
  .object({
    errorRangeStart: z.string().date(),
    errorRangeEnd: z.string().date(),
  })
  .refine(
    (data) => new Date(data.errorRangeStart) < new Date(data.errorRangeEnd),
    {
      message: "Start date must be before end date.",
      path: ["errorRangeEnd"],
    },
  );
export const WithErrorEndBeforeStart: Story<typeof errorEndBeforeStartSchema> =
  {
    args: {
      startName: "errorRangeStart",
      endName: "errorRangeEnd",
      defaultValues: {
        errorRangeStart: format(nextWeek, "yyyy-MM-dd"),
        errorRangeEnd: format(tomorrow, "yyyy-MM-dd"), // End date is before start date
      },
      fieldSchema: errorEndBeforeStartSchema,
    },
    play: async ({ canvasElement, args }) => {
      const canvas = within(canvasElement);
      await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

      await expect(args.onFormSubmit).not.toHaveBeenCalled();
      // This message comes from the component's internal validation or Zod refinement
      await expect(
        canvas.getByText("Start date must be before end date."),
      ).toBeInTheDocument();
    },
  };

const errorMinDurationSchema = z.object({
  minDurationStart: z.string().date(),
  minDurationEnd: z.string().date(),
});
export const WithErrorMinDuration: Story<typeof errorMinDurationSchema> = {
  args: {
    startName: "minDurationStart",
    endName: "minDurationEnd",
    minDuration: 5, // Minimum 5 days duration
    defaultValues: {
      minDurationStart: format(today, "yyyy-MM-dd"),
      minDurationEnd: format(addDays(today, 2), "yyyy-MM-dd"), // Only 2 days duration
    },
    fieldSchema: errorMinDurationSchema, // Basic schema, component handles duration validation
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Message from component's internal validation
    await expect(
      canvas.getByText(/Duration must be at least 5 days/i),
    ).toBeInTheDocument();
  },
};
