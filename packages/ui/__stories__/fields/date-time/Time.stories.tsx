import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import type { TimeValue } from "react-aria";
import type { SubmitHandler } from "react-hook-form";

import { Time } from "@internationalized/date";
import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { TimeFieldProps } from "@/ui/fields/date-time/Time";

// BaseFormProviderArgs is no longer directly used by CurrentTimeStoryArgs
// import type { BaseFormProviderArgs } from "../WithFormProvider";

import { TimeField } from "@/ui/fields/date-time/Time";

import { WithFormProvider } from "../WithFormProvider";

// Zod schema for the TimeValue object from react-aria
const timeValueSchemaPart = z.object({
  hour: z.number().min(0).max(23),
  minute: z.number().min(0).max(59),
  // Omitting second and millisecond as TimePicker primitive might not use them by default
});

// Zod schema for the form data managed by TimeField
const createTimeObjectSchema = (showHours: boolean) => {
  const baseSchema = z.object({
    startTime: timeValueSchemaPart.nullable().refine((val) => val !== null, {
      message: "Start time is required.",
    }),
    endTime: timeValueSchemaPart.nullable().refine((val) => val !== null, {
      message: "End time is required.",
    }),
  });

  if (showHours) {
    return baseSchema.extend({
      hours: z
        .number({ invalid_type_error: "Hours must be a number." })
        .optional(),
    });
  }
  return baseSchema;
};

interface CurrentTimeStoryArgs extends TimeFieldProps {
  // showHours
  fieldSchema: ReturnType<typeof createTimeObjectSchema>;
  onFormSubmit: SubmitHandler<
    z.infer<ReturnType<typeof createTimeObjectSchema>>
  >;
  // This interface defines the component's own props, the schema structure,
  // the submit handler type, and the structure of defaultValues it expects for the form provider.
  defaultValues: {
    startTime?: TimeValue | null;
    endTime?: TimeValue | null;
    hours?: number;
  };
}

const meta: Meta<CurrentTimeStoryArgs> = {
  title: "Fields/DateTime/TimeField",
  component: TimeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for TimeField component
    showHours: true,

    // Args for WithFormProvider decorator
    fieldSchema: createTimeObjectSchema(true),
    onFormSubmit: fn() as SubmitHandler<
      z.infer<ReturnType<typeof createTimeObjectSchema>>
    >,
    defaultValues: {
      startTime: new Time(9, 0),
      endTime: new Time(17, 0),
      hours: 8, // This will be calculated by the component based on start/end
    },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentTimeStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultWithHours: Story = {
  args: {
    showHours: true,
    fieldSchema: createTimeObjectSchema(true),
    defaultValues: {
      startTime: new Time(9, 0),
      endTime: new Time(17, 0),
      // hours will be auto-calculated
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Check if time pickers are populated
    await expect(canvas.getByDisplayValue("09")).toBeInTheDocument(); // Start Hour
    await expect(canvas.getAllByDisplayValue("00")[0]).toBeInTheDocument(); // Start Minute
    await expect(canvas.getByDisplayValue("17")).toBeInTheDocument(); // End Hour
    await expect(canvas.getAllByDisplayValue("00")[1]).toBeInTheDocument(); // End Minute

    // Check if hours input is populated and correct (9 AM to 5 PM is 8 hours)
    const hoursInput = canvas.getByPlaceholderText("Enter the number of hours");
    await expect(hoursInput).toHaveValue(8);
  },
};

export const DefaultWithoutHours: Story = {
  args: {
    showHours: false,
    fieldSchema: createTimeObjectSchema(false),
    defaultValues: {
      startTime: new Time(10, 30),
      endTime: new Time(18, 15),
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByDisplayValue("10")).toBeInTheDocument();
    await expect(canvas.getByDisplayValue("30")).toBeInTheDocument();
    await expect(canvas.getByDisplayValue("18")).toBeInTheDocument();
    await expect(canvas.getByDisplayValue("15")).toBeInTheDocument();
    await expect(
      canvas.queryByPlaceholderText("Enter the number of hours"),
    ).not.toBeInTheDocument();
  },
};

export const InteractivePresetSelection: Story = {
  args: {
    showHours: true,
    fieldSchema: createTimeObjectSchema(true),
    defaultValues: {
      startTime: null, // Start empty
      endTime: null,
    },
    onFormSubmit: fn() as SubmitHandler<
      z.infer<ReturnType<typeof createTimeObjectSchema>>
    >,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const presetButton = canvas.getByRole("button", { name: /presets/i });
    await userEvent.click(presetButton);

    const eightToFourPreset = await canvas.findByText("8:00 AM - 4:00 PM");
    await userEvent.click(eightToFourPreset);

    // Check if time pickers and hours updated
    await expect(canvas.getByDisplayValue("08")).toBeInTheDocument();
    await expect(canvas.getAllByDisplayValue("00")[0]).toBeInTheDocument(); // Start Minute
    await expect(canvas.getByDisplayValue("16")).toBeInTheDocument(); // 4 PM
    await expect(canvas.getAllByDisplayValue("00")[1]).toBeInTheDocument(); // End Minute
    await expect(
      canvas.getByPlaceholderText("Enter the number of hours"),
    ).toHaveValue(8);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      {
        startTime: { hour: 8, minute: 0 },
        endTime: { hour: 16, minute: 0 },
        hours: 8,
      },
      expect.anything(),
    );
  },
};

export const InteractiveOvernightHoursCalculation: Story = {
  args: {
    showHours: true,
    fieldSchema: createTimeObjectSchema(true),
    defaultValues: {
      startTime: new Time(22, 0), // 10 PM
      endTime: new Time(6, 0), // 6 AM (next day)
    },
    onFormSubmit: fn() as SubmitHandler<
      z.infer<ReturnType<typeof createTimeObjectSchema>>
    >,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Values should be pre-filled
    await expect(canvas.getByDisplayValue("22")).toBeInTheDocument();
    await expect(canvas.getByDisplayValue("06")).toBeInTheDocument();

    // 10 PM to 6 AM is 8 hours
    const hoursInput = canvas.getByPlaceholderText("Enter the number of hours");
    await expect(hoursInput).toHaveValue(8);

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      {
        startTime: { hour: 22, minute: 0 },
        endTime: { hour: 6, minute: 0 },
        hours: 8,
      },
      expect.anything(),
    );
  },
};

export const WithErrorRequiredTimes: Story = {
  args: {
    showHours: true,
    fieldSchema: createTimeObjectSchema(true),
    defaultValues: {
      startTime: null,
      endTime: null,
    },
    onFormSubmit: fn() as SubmitHandler<
      z.infer<ReturnType<typeof createTimeObjectSchema>>
    >,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Start time is required."),
    ).toBeInTheDocument();
    await expect(canvas.getByText("End time is required.")).toBeInTheDocument();
  },
};
