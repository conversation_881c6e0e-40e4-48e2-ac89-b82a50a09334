import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { UserFormValues } from "@/ui/forms/users/User";

import UserForm, { UserRole } from "@/ui/forms/users/User";

const meta = {
  title: "Forms/Users/<USER>",
  component: UserForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    onRoleChange: fn(),
    defaultValues: {
      organizationId: "org-user-form-123",
      userId: "user-form-456",
      role: undefined, // Optional, will be selected by user
    } as Partial<UserFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof UserForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const selectedRole: UserRole = UserRole.ADMIN;

    // 1. Find the Role select trigger and click it
    await userEvent.click(
      canvas.getByRole("button", { name: /enter the user role/i }),
    );

    // 2. Select a role from the list (e.g., "Admin")
    await userEvent.click(await canvas.findByRole("option", { name: "Admin" }));

    // 3. Assert onRoleChange was called with the selected role
    await expect(args.onRoleChange).toHaveBeenCalledTimes(1);
    await expect(args.onRoleChange).toHaveBeenCalledWith(selectedRole);

    // 4. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 5. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: "org-user-form-123",
        userId: "user-form-456",
        role: selectedRole,
      }),
      expect.anything(),
    );
  },
};
