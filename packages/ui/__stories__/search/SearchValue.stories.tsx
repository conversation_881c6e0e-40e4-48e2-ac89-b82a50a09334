import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useState } from "react";

import { SearchValue } from "../../src/search/SearchValue";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchValue",
  component: SearchValue,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg"],
    },
    variant: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
    useDialog: {
      control: { type: "boolean" },
    },
  },
} satisfies Meta<typeof SearchValue>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample options for the selector
const sampleOptions = [
  { value: "option1", label: "Option 1", id: "option1" },
  { value: "option2", label: "Option 2", id: "option2" },
  { value: "option3", label: "Option 3", id: "option3" },
  { value: "option4", label: "Option 4", id: "option4" },
];

const largerDataSet = [
  { value: "", label: "No filter", id: "none" },
  { value: "electronics", label: "Electronics", id: "electronics" },
  { value: "clothing", label: "Clothing", id: "clothing" },
  { value: "books", label: "Books", id: "books" },
  { value: "home", label: "Home & Garden", id: "home" },
  { value: "sports", label: "Sports & Outdoors", id: "sports" },
  { value: "toys", label: "Toys & Games", id: "toys" },
  { value: "automotive", label: "Automotive", id: "automotive" },
  { value: "health", label: "Health & Beauty", id: "health" },
];

export const Default: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithCustomGroup: Story = {
  args: {
    name: "value",
    group: "products",
    placeholder: "Select product",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithDefaultValue: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    defaultValue: "option2",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithDialog: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    useDialog: true,
    data: largerDataSet,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const Loading: Story = {
  args: {
    name: "value",
    placeholder: "Loading...",
    loading: true,
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

// Size variants
export const SmallSize: Story = {
  args: {
    name: "value",
    size: "sm",
    placeholder: "Small value selector",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "Size: Small",
};

export const MediumSize: Story = {
  args: {
    name: "value",
    size: "md",
    placeholder: "Medium value selector",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "Size: Medium (Default)",
};

export const LargeSize: Story = {
  args: {
    name: "value",
    size: "lg",
    placeholder: "Large value selector",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "Size: Large",
};

// Size comparison
export const SizeComparison: Story = {
  args: {
    name: "value",
    data: sampleOptions,
  },
  render: () => (
    <div className="flex flex-col gap-4" style={{ width: "400px" }}>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Small:</span>
        <SearchValue
          name="value-sm"
          size="sm"
          data={sampleOptions}
          placeholder="Small selector"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Medium:</span>
        <SearchValue
          name="value-md"
          size="md"
          data={sampleOptions}
          placeholder="Medium selector"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Large:</span>
        <SearchValue
          name="value-lg"
          size="lg"
          data={sampleOptions}
          placeholder="Large selector"
        />
      </div>
    </div>
  ),
  name: "All Sizes",
};

// Variant comparison
export const VariantComparison: Story = {
  args: {
    name: "value",
    data: sampleOptions,
  },
  render: () => (
    <div className="flex flex-col gap-4" style={{ width: "400px" }}>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Primary:</span>
        <SearchValue
          name="value-default"
          variant="primary"
          data={sampleOptions}
          placeholder="Default variant"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Secondary:</span>
        <SearchValue
          name="value-compact"
          variant="secondary"
          data={sampleOptions}
          placeholder="Compact variant"
        />
      </div>
    </div>
  ),
  name: "All Variants",
};

// With default value and clear functionality
export const WithDefaultAndClear: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    defaultValue: "option3",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "With Default Value & Clear",
};

// Empty value handling
export const EmptyValueHandling: Story = {
  args: {
    name: "value",
    placeholder: "Select option",
    defaultValue: "",
    data: [
      { value: "", label: "No filter", id: "none" },
      { value: "option1", label: "Option 1", id: "option1" },
      { value: "option2", label: "Option 2", id: "option2" },
    ],
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "Empty Value Handling",
};

// Dialog mode with large dataset
export const DialogMode: Story = {
  args: {
    name: "value",
    placeholder: "Search categories",
    useDialog: true,
    data: largerDataSet,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
  name: "Dialog Mode (Large Dataset)",
};
