import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SearchDate, SearchDateRange } from "../../src/search/SearchDate";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchDate",
  component: SearchDateRange,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchDateRange>;

export default meta;

type Story = StoryObj<typeof meta>;
type SingleDateStory = StoryObj<Meta<typeof SearchDate>>;

export const Default: Story = {
  args: {
    placeholder: "Select date range...",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "customDateRange",
    placeholder: "Custom date range...",
  },
};

export const WithCustomGroup: Story = {
  args: {
    group: "events",
    placeholder: "Event date range...",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    placeholder: "Loading date range...",
  },
};

// Single Date Stories
export const SingleDate: SingleDateStory = {
  render: (args) => <SearchDate {...args} />,
  args: {
    placeholder: "Select date...",
  },
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  decorators: [withSearchParams],
};

export const SingleDateWithCustomName: SingleDateStory = {
  render: (args) => <SearchDate {...args} />,
  args: {
    name: "dueDate",
    placeholder: "Due date...",
  },
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  decorators: [withSearchParams],
};

export const SingleDateWithCustomGroup: SingleDateStory = {
  render: (args) => <SearchDate {...args} />,
  args: {
    group: "task",
    placeholder: "Task date...",
  },
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  decorators: [withSearchParams],
};

export const SingleDateLoading: SingleDateStory = {
  render: (args) => <SearchDate {...args} />,
  args: {
    loading: true,
    placeholder: "Loading date...",
  },
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  decorators: [withSearchParams],
};
