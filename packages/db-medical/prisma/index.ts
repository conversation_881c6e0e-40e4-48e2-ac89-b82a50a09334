import type { ExtendedPrismaClient } from "./factory";
// import type { $Enums, Prisma } from ".prisma/client/medical";
import type { PrismaClientOptions } from ".prisma/client/medical/runtime/library";

import { extendPrismaClient } from "./factory";
import { PrismaClient } from "@/prisma/client/medical";

export * from ".prisma/client/medical";
export * from "./enums";
export * from "./factory";

export default (() => {
  let prisma: PrismaClient | undefined;

  return function createClient(options?: PrismaClientOptions) {
    prisma ??= new PrismaClient(
      // @ts-expect-error - prisma types are not exposed for proper typing
      options,
    );

    return extendPrismaClient(
      prisma,
    ) as unknown as ExtendedPrismaClient<PrismaClient>;
  };
})();
