import { createClerkClient } from "@clerk/nextjs/server";
import { config } from "dotenv";

import type { OrganizationType } from "../../../node_modules/.prisma/client/tech";
import type {
  ClerkOrganization,
  DatabaseOrganization,
  SyncIssue,
} from "./checks";

import {
  checkClerkOrganizationsInDatabase,
  checkDatabaseOrganizationsInClerk,
  runComprehensiveSyncCheck,
} from "./checks";

const { PrismaClient } = await import(
  "../../../node_modules/.prisma/client/tech"
);

const OrganizationTypeEnum = await import(
  "../../../node_modules/.prisma/client/tech"
).then((m) => m.OrganizationType);

// Load environment variables from .env file
config({ path: ".env" });

const clerkSK = process.env.CLERK_SECRET_KEY;

if (!clerkSK) {
  throw new Error("CLERK_SECRET_KEY environment variable is required");
}

const clerk = createClerkClient({
  secretKey: clerkSK,
});

const prisma = new PrismaClient();

interface SyncResult {
  success: boolean;
  operation: string;
  organizationId: string;
  organizationName: string;
  details: string;
  error?: string;
}

interface SyncSummary {
  totalAttempted: number;
  successful: number;
  failed: number;
  results: SyncResult[];
}

/**
 * Check if database organizations exist in Clerk (using our Prisma instance)
 */
async function checkDatabaseOrganizationsInClerk(): Promise<{
  totalChecked: number;
  foundInClerk: number;
  missingInClerk: DatabaseOrganization[];
  issues: SyncIssue[];
}> {
  console.log("🔍 Checking database organizations in Clerk...");

  // Get all organizations from database using our existing Prisma client
  const dbOrganizations = await prisma.organization.findMany({
    select: {
      id: true,
      name: true,
      type: true,
      avatar: true,
      managerId: true,
    },
  });

  console.log(`📊 Found ${dbOrganizations.length} organizations in database`);

  const missingInClerk: DatabaseOrganization[] = [];
  const issues: SyncIssue[] = [];
  let foundInClerk = 0;

  // Check each database organization in Clerk
  for (const dbOrg of dbOrganizations) {
    try {
      console.log(`  Checking ${dbOrg.name} (${dbOrg.id})...`);

      const clerkOrg = await clerk.organizations.getOrganization({
        organizationId: dbOrg.id,
      });

      if (clerkOrg) {
        foundInClerk++;
        console.log(`    ✅ Found in Clerk`);

        // Check for metadata mismatches
        const clerkType = clerkOrg.publicMetadata?.type;
        const clerkManagerId = clerkOrg.publicMetadata?.managerId;

        if (clerkType && clerkType !== dbOrg.type) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Type mismatch - DB: ${dbOrg.type}, Clerk: ${clerkType}`,
            source: "database",
          });
        }

        if (clerkManagerId !== dbOrg.managerId) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Manager mismatch - DB: ${dbOrg.managerId || "null"}, Clerk: ${clerkManagerId || "null"}`,
            source: "database",
          });
        }
      } else {
        console.log(`    ❌ Not found in Clerk`);
        missingInClerk.push(dbOrg);
        issues.push({
          type: "missing-in-clerk",
          organizationId: dbOrg.id,
          organizationName: dbOrg.name,
          details: "Organization exists in database but not in Clerk",
          source: "database",
        });
      }
    } catch (error) {
      console.log(`    ❌ Error checking in Clerk: ${error}`);
      missingInClerk.push(dbOrg);
      issues.push({
        type: "missing-in-clerk",
        organizationId: dbOrg.id,
        organizationName: dbOrg.name,
        details: `Error accessing Clerk: ${error}`,
        source: "database",
      });
    }
  }

  console.log(`\n📋 Database → Clerk Check Results:`);
  console.log(`  Total checked: ${dbOrganizations.length}`);
  console.log(`  Found in Clerk: ${foundInClerk}`);
  console.log(`  Missing in Clerk: ${missingInClerk.length}`);
  console.log(`  Issues found: ${issues.length}`);

  return {
    totalChecked: dbOrganizations.length,
    foundInClerk,
    missingInClerk,
    issues,
  };
}

/**
 * Safely create missing organizations in Clerk
 */
async function syncMissingOrganizationsToClerk(
  missingOrgs: DatabaseOrganization[],
): Promise<SyncSummary> {
  console.log(
    `🔄 Syncing ${missingOrgs.length} missing organizations to Clerk...`,
  );

  const results: SyncResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const org of missingOrgs) {
    try {
      console.log(`  Creating ${org.name} (${org.id}) in Clerk...`);

      // Prepare public metadata
      const publicMetadata: Record<string, any> = {
        type: org.type,
        version: 1,
      };

      if (org.managerId) {
        publicMetadata.managerId = org.managerId;
      }

      // Create organization in Clerk
      const clerkOrg = await clerk.organizations.createOrganization({
        name: org.name,
        publicMetadata,
      });

      // Update the organization ID in database to match Clerk
      if (clerkOrg.id !== org.id) {
        console.log(
          `    ⚠️  Clerk assigned different ID: ${clerkOrg.id}, updating database...`,
        );

        await prisma.organization.update({
          where: { id: org.id },
          data: { id: clerkOrg.id },
        });
      }

      results.push({
        success: true,
        operation: "create-in-clerk",
        organizationId: org.id,
        organizationName: org.name,
        details: `Successfully created organization in Clerk with ID: ${clerkOrg.id}`,
      });

      successful++;
      console.log(`    ✅ Created successfully`);
    } catch (error) {
      results.push({
        success: false,
        operation: "create-in-clerk",
        organizationId: org.id,
        organizationName: org.name,
        details: "Failed to create organization in Clerk",
        error: String(error),
      });

      failed++;
      console.log(`    ❌ Failed: ${error}`);
    }
  }

  return {
    totalAttempted: missingOrgs.length,
    successful,
    failed,
    results,
  };
}

/**
 * Safely create missing organizations in database
 */
async function syncMissingOrganizationsToDatabase(
  missingOrgs: ClerkOrganization[],
): Promise<SyncSummary> {
  console.log(
    `🔄 Syncing ${missingOrgs.length} missing organizations to database...`,
  );

  const results: SyncResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const org of missingOrgs) {
    try {
      console.log(`  Creating ${org.name} (${org.id}) in database...`);

      // Extract metadata from Clerk
      const orgType =
        (org.publicMetadata?.type as OrganizationType) ||
        OrganizationTypeEnum.CLIENT;
      const managerId = org.publicMetadata?.managerId || null;
      const parentId = org.publicMetadata?.parentId || null;

      // Determine if this should be a root, child, or managed organization
      let createResult;

      if (parentId) {
        // Create as child of parent
        createResult = await prisma.organization.createChild({
          where: { id: parentId },
          data: {
            id: org.id,
            name: org.name,
            avatar: org.imageUrl,
            type: orgType,
            manager: managerId ? { connect: { id: managerId } } : undefined,
          },
        });
      } else if (managerId) {
        // Create as child of manager
        createResult = await prisma.organization.createChild({
          where: { id: managerId },
          data: {
            id: org.id,
            name: org.name,
            avatar: org.imageUrl,
            type: orgType,
            manager: { connect: { id: managerId } },
          },
        });
      } else {
        // Create as root organization
        createResult = await prisma.organization.createRoot({
          data: {
            id: org.id,
            name: org.name,
            avatar: org.imageUrl,
            type: orgType,
          },
        });
      }

      results.push({
        success: true,
        operation: "create-in-database",
        organizationId: org.id,
        organizationName: org.name,
        details: `Successfully created organization in database as ${parentId ? "child" : managerId ? "managed" : "root"}`,
      });

      successful++;
      console.log(`    ✅ Created successfully`);
    } catch (error) {
      results.push({
        success: false,
        operation: "create-in-database",
        organizationId: org.id,
        organizationName: org.name,
        details: "Failed to create organization in database",
        error: String(error),
      });

      failed++;
      console.log(`    ❌ Failed: ${error}`);
    }
  }

  return {
    totalAttempted: missingOrgs.length,
    successful,
    failed,
    results,
  };
}

/**
 * Fix metadata mismatches between database and Clerk
 */
async function syncMetadataMismatches(
  mismatches: SyncIssue[],
): Promise<SyncSummary> {
  console.log(`🔄 Fixing ${mismatches.length} metadata mismatches...`);

  const results: SyncResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const mismatch of mismatches) {
    try {
      console.log(
        `  Fixing ${mismatch.organizationName} (${mismatch.organizationId})...`,
      );
      console.log(`    Issue: ${mismatch.details}`);

      // Get current data from both systems
      const [dbOrg, clerkOrg] = await Promise.all([
        prisma.organization.findUnique({
          where: { id: mismatch.organizationId },
          select: {
            id: true,
            name: true,
            type: true,
            avatar: true,
            managerId: true,
          },
        }),
        clerk.organizations
          .getOrganization({
            organizationId: mismatch.organizationId,
          })
          .catch(() => null),
      ]);

      if (!dbOrg || !clerkOrg) {
        throw new Error("Organization not found in one or both systems");
      }

      // Determine which system should be the source of truth
      // Default strategy: Database is source of truth for core data
      const sourceOfTruth =
        mismatch.source === "database" ? "database" : "database"; // Always prefer database

      if (sourceOfTruth === "database") {
        // Update Clerk with database values
        const updatedMetadata = {
          ...clerkOrg.publicMetadata,
          type: dbOrg.type,
          managerId: dbOrg.managerId,
          version: 1,
        };

        await clerk.organizations.updateOrganization(mismatch.organizationId, {
          name: dbOrg.name,
          publicMetadata: updatedMetadata,
        });

        results.push({
          success: true,
          operation: "fix-metadata-to-clerk",
          organizationId: mismatch.organizationId,
          organizationName: mismatch.organizationName,
          details: `Updated Clerk with database values: name="${dbOrg.name}", type="${dbOrg.type}", manager="${dbOrg.managerId || "null"}"`,
        });
      } else {
        // Update database with Clerk values
        const clerkType =
          (clerkOrg.publicMetadata?.type as OrganizationType) || dbOrg.type;
        const clerkManagerId = clerkOrg.publicMetadata?.managerId || null;

        await prisma.organization.update({
          where: { id: mismatch.organizationId },
          data: {
            name: clerkOrg.name,
            type: clerkType,
            avatar: clerkOrg.imageUrl,
            managerId: clerkManagerId,
          },
        });

        results.push({
          success: true,
          operation: "fix-metadata-to-database",
          organizationId: mismatch.organizationId,
          organizationName: mismatch.organizationName,
          details: `Updated database with Clerk values: name="${clerkOrg.name}", type="${clerkType}", manager="${clerkManagerId || "null"}"`,
        });
      }

      successful++;
      console.log(`    ✅ Fixed successfully`);
    } catch (error) {
      results.push({
        success: false,
        operation: "fix-metadata",
        organizationId: mismatch.organizationId,
        organizationName: mismatch.organizationName,
        details: "Failed to fix metadata mismatch",
        error: String(error),
      });

      failed++;
      console.log(`    ❌ Failed: ${error}`);
    }
  }

  return {
    totalAttempted: mismatches.length,
    successful,
    failed,
    results,
  };
}

/**
 * Comprehensive sync operation
 */
async function runComprehensiveSync(dryRun: boolean = false): Promise<{
  dryRun: boolean;
  summary: {
    totalOperations: number;
    successful: number;
    failed: number;
  };
  operations: {
    missingInClerk: SyncSummary;
    missingInDatabase: SyncSummary;
    metadataMismatches: SyncSummary;
  };
}> {
  console.log(
    `🚀 Starting comprehensive sync ${dryRun ? "(DRY RUN)" : "(LIVE)"}...`,
  );
  console.log("=".repeat(70));

  // First, run checks to identify issues
  console.log("📋 Running initial checks...");
  const checkResults = await runComprehensiveSyncCheck();

  if (dryRun) {
    console.log("\n🔍 DRY RUN - No changes will be made");
    console.log("Issues that would be fixed:");
    console.log(
      `  - Missing in Clerk: ${checkResults.databaseToClerk.missingInClerk.length}`,
    );
    console.log(
      `  - Missing in Database: ${checkResults.clerkToDatabase.missingInDatabase.length}`,
    );
    console.log(
      `  - Metadata Mismatches: ${checkResults.summary.metadataMismatches}`,
    );

    return {
      dryRun: true,
      summary: {
        totalOperations: checkResults.summary.totalIssues,
        successful: 0,
        failed: 0,
      },
      operations: {
        missingInClerk: {
          totalAttempted: 0,
          successful: 0,
          failed: 0,
          results: [],
        },
        missingInDatabase: {
          totalAttempted: 0,
          successful: 0,
          failed: 0,
          results: [],
        },
        metadataMismatches: {
          totalAttempted: 0,
          successful: 0,
          failed: 0,
          results: [],
        },
      },
    };
  }

  // Perform sync operations
  console.log("\n🔄 Starting sync operations...");

  const operations = {
    missingInClerk: await syncMissingOrganizationsToClerk(
      checkResults.databaseToClerk.missingInClerk,
    ),
    missingInDatabase: await syncMissingOrganizationsToDatabase(
      checkResults.clerkToDatabase.missingInDatabase,
    ),
    metadataMismatches: await syncMetadataMismatches(
      [
        ...checkResults.databaseToClerk.issues,
        ...checkResults.clerkToDatabase.issues,
      ].filter((issue) => issue.type === "metadata-mismatch"),
    ),
  };

  const summary = {
    totalOperations:
      operations.missingInClerk.totalAttempted +
      operations.missingInDatabase.totalAttempted +
      operations.metadataMismatches.totalAttempted,
    successful:
      operations.missingInClerk.successful +
      operations.missingInDatabase.successful +
      operations.metadataMismatches.successful,
    failed:
      operations.missingInClerk.failed +
      operations.missingInDatabase.failed +
      operations.metadataMismatches.failed,
  };

  console.log("\n🎯 SYNC OPERATION SUMMARY:");
  console.log("=".repeat(70));
  console.log(`Total operations attempted: ${summary.totalOperations}`);
  console.log(`Successful: ${summary.successful}`);
  console.log(`Failed: ${summary.failed}`);
  console.log(
    `Success rate: ${summary.totalOperations > 0 ? Math.round((summary.successful / summary.totalOperations) * 100) : 0}%`,
  );

  // Detailed breakdown
  console.log("\n📊 DETAILED BREAKDOWN:");
  console.log(
    `Missing in Clerk: ${operations.missingInClerk.successful}/${operations.missingInClerk.totalAttempted} synced`,
  );
  console.log(
    `Missing in Database: ${operations.missingInDatabase.successful}/${operations.missingInDatabase.totalAttempted} synced`,
  );
  console.log(
    `Metadata Mismatches: ${operations.metadataMismatches.successful}/${operations.metadataMismatches.totalAttempted} fixed`,
  );

  // Show failures if any
  const allFailures = [
    ...operations.missingInClerk.results.filter((r) => !r.success),
    ...operations.missingInDatabase.results.filter((r) => !r.success),
    ...operations.metadataMismatches.results.filter((r) => !r.success),
  ];

  if (allFailures.length > 0) {
    console.log("\n❌ FAILED OPERATIONS:");
    allFailures.forEach((failure, index) => {
      console.log(
        `  ${index + 1}. [${failure.operation.toUpperCase()}] ${failure.organizationName} (${failure.organizationId})`,
      );
      console.log(`     Error: ${failure.error}`);
    });
  }

  return {
    dryRun: false,
    summary,
    operations,
  };
}

/**
 * Sync specific organizations by ID
 */
async function syncSpecificOrganizations(
  organizationIds: string[],
  dryRun: boolean = false,
): Promise<SyncSummary> {
  console.log(
    `🎯 Syncing specific organizations: ${organizationIds.join(", ")} ${dryRun ? "(DRY RUN)" : "(LIVE)"}`,
  );

  const results: SyncResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const orgId of organizationIds) {
    try {
      console.log(`  Processing ${orgId}...`);

      // Check current state
      const [dbOrg, clerkOrg] = await Promise.all([
        prisma.organization.findUnique({
          where: { id: orgId },
          select: {
            id: true,
            name: true,
            type: true,
            avatar: true,
            managerId: true,
          },
        }),
        clerk.organizations
          .getOrganization({ organizationId: orgId })
          .catch(() => null),
      ]);

      if (!dbOrg && !clerkOrg) {
        throw new Error("Organization not found in either system");
      }

      if (dryRun) {
        results.push({
          success: true,
          operation: "dry-run-check",
          organizationId: orgId,
          organizationName: dbOrg?.name || clerkOrg?.name || "Unknown",
          details: `Would sync - DB: ${dbOrg ? "exists" : "missing"}, Clerk: ${clerkOrg ? "exists" : "missing"}`,
        });
        successful++;
        continue;
      }

      // Perform actual sync operations based on what exists
      if (dbOrg && !clerkOrg) {
        // Create in Clerk
        const syncResult = await syncMissingOrganizationsToClerk([dbOrg]);
        results.push(...syncResult.results);
        if (syncResult.successful > 0) successful++;
        else failed++;
      } else if (!dbOrg && clerkOrg) {
        // Create in Database
        const syncResult = await syncMissingOrganizationsToDatabase([
          clerkOrg as ClerkOrganization,
        ]);
        results.push(...syncResult.results);
        if (syncResult.successful > 0) successful++;
        else failed++;
      } else if (dbOrg && clerkOrg) {
        // Fix metadata mismatches
        const issues: SyncIssue[] = [];

        if (dbOrg.name !== clerkOrg.name) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: orgId,
            organizationName: dbOrg.name,
            details: `Name mismatch`,
            source: "database",
          });
        }

        if (issues.length > 0) {
          const syncResult = await syncMetadataMismatches(issues);
          results.push(...syncResult.results);
          if (syncResult.successful > 0) successful++;
          else failed++;
        } else {
          results.push({
            success: true,
            operation: "already-synced",
            organizationId: orgId,
            organizationName: dbOrg.name,
            details: "Organization already in sync",
          });
          successful++;
        }
      }
    } catch (error) {
      results.push({
        success: false,
        operation: "specific-sync",
        organizationId: orgId,
        organizationName: "Unknown",
        details: "Failed to sync specific organization",
        error: String(error),
      });
      failed++;
      console.log(`    ❌ Failed: ${error}`);
    }
  }

  return {
    totalAttempted: organizationIds.length,
    successful,
    failed,
    results,
  };
}

// Wrapper functions for easy use
async function runDryRunSync() {
  return await runComprehensiveSync(true);
}

async function runLiveSync() {
  return await runComprehensiveSync(false);
}

async function syncOrganizations(
  organizationIds: string[],
  dryRun: boolean = false,
) {
  return await syncSpecificOrganizations(organizationIds, dryRun);
}

if (import.meta.url === new URL(import.meta.url).href) {
  // Main execution - Focus on syncing missing organizations to Clerk only
  (async () => {
    try {
      console.log("🚀 Starting organization sync to Clerk...");
      
      // First, run checks to identify what's missing in Clerk using our local function
      console.log("📋 Checking for organizations missing in Clerk...");
      const checkResults = await checkDatabaseOrganizationsInClerk();
      
      console.log(`\n📊 Found ${checkResults.missingInClerk.length} organizations missing in Clerk:`);
      checkResults.missingInClerk.forEach((org, index) => {
        console.log(`  ${index + 1}. ${org.name} (${org.id}) - Type: ${org.type}`);
      });
      
      if (checkResults.missingInClerk.length === 0) {
        console.log("✅ All database organizations already exist in Clerk!");
        return;
      }
      
      console.log("\n" + "=".repeat(70));
      console.log("🔄 Syncing missing organizations to Clerk...");
      
      // Sync only the missing organizations to Clerk
      const syncResult = await syncMissingOrganizationsToClerk(
        checkResults.missingInClerk
      );
      
      console.log("\n🎯 SYNC TO CLERK SUMMARY:");
      console.log("=".repeat(70));
      console.log(`Total organizations attempted: ${syncResult.totalAttempted}`);
      console.log(`Successfully created in Clerk: ${syncResult.successful}`);
      console.log(`Failed to create: ${syncResult.failed}`);
      console.log(
        `Success rate: ${syncResult.totalAttempted > 0 ? Math.round((syncResult.successful / syncResult.totalAttempted) * 100) : 0}%`
      );
      
      // Show successful creations
      const successes = syncResult.results.filter(r => r.success);
      if (successes.length > 0) {
        console.log("\n✅ SUCCESSFULLY CREATED IN CLERK:");
        successes.forEach((success, index) => {
          console.log(`  ${index + 1}. ${success.organizationName} (${success.organizationId})`);
          console.log(`     ${success.details}`);
        });
      }
      
      // Show failures if any
      const failures = syncResult.results.filter(r => !r.success);
      if (failures.length > 0) {
        console.log("\n❌ FAILED TO CREATE IN CLERK:");
        failures.forEach((failure, index) => {
          console.log(`  ${index + 1}. ${failure.organizationName} (${failure.organizationId})`);
          console.log(`     Error: ${failure.error}`);
        });
      }
      
      console.log("\n🎉 Sync to Clerk completed!");
      
    } catch (error) {
      console.error("💥 Sync to Clerk failed:", error);
      process.exit(1);
    } finally {
      await prisma.$disconnect();
    }
  })();
}

// Export functions for use in other scripts
export {
  syncMissingOrganizationsToClerk,
  syncMissingOrganizationsToDatabase,
  syncMetadataMismatches,
  runComprehensiveSync,
  syncSpecificOrganizations,
  runDryRunSync,
  runLiveSync,
  syncOrganizations,
  type SyncResult,
  type SyncSummary,
};
