import { createClerkClient } from "@clerk/nextjs/server";
import { config } from "dotenv";

import type {
  Organization,
  OrganizationType,
} from "../../../node_modules/.prisma/client/tech";

const { PrismaClient } = await import(
  "../../../node_modules/.prisma/client/tech"
);

const OrganizationTypeEnum = await import(
  "../../../node_modules/.prisma/client/tech"
).then((m) => m.OrganizationType);

// Load environment variables from .env file
config({ path: ".env" });

const clerkSK = process.env.CLERK_SECRET_KEY;

const clerk = createClerkClient({
  secretKey: clerkSK,
});

const prisma = new PrismaClient();

interface Violation {
  type: "duplicate" | "no-manager" | "no-clerk-organization";
  organization: {
    id: string;
    name: string;
    type: OrganizationType;
    avatar?: string | null;
  };
  duplicates?: {
    id: string;
    name: string;
    type: OrganizationType;
    avatar?: string | null;
  }[];
}

/**
 * Get all violations in organizations
 * - Duplicate organizations (same name)
 * - Account organizations with no manager
 * - Organizations that don't exist in Clerk
 */
async function getViolations(): Promise<{ violations: Violation[] }> {
  console.log("🔍 Checking for organization violations...");

  const organizations = await prisma.organization.findMany({
    select: {
      id: true,
      managerId: true,
      type: true,
      name: true,
      avatar: true,
    },
  });

  console.log(`📊 Found ${organizations.length} ACCOUNT type organizations`);

  const violations: Violation[] = [];

  for (const organization of organizations) {
    try {
      const clerkOrganization = await clerk.organizations.getOrganization({
        organizationId: organization.id,
      });

      if (!clerkOrganization) {
        violations.push({
          type: "no-clerk-organization",
          organization: {
            id: organization.id,
            name: organization.name,
            type: organization.type,
            avatar: organization.avatar,
          },
        });
        // continue;
      }
    } catch (error) {
      // Organization doesn't exist in Clerk
      violations.push({
        type: "no-clerk-organization",
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          avatar: organization.avatar,
        },
      });
      // continue;
    }

    if (
      !organization.managerId &&
      organization.type === OrganizationTypeEnum.ACCOUNT
    ) {
      violations.push({
        type: "no-manager",
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          avatar: organization.avatar,
        },
      });
    }

    const duplicates = organizations.filter(
      (o) => o.name === organization.name,
    );

    if (duplicates.length > 1) {
      violations.push({
        type: "duplicate",
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          avatar: organization.avatar,
        },
        duplicates: duplicates
          .filter((d) => d.id !== organization.id)
          .map((d) => ({
            id: d.id,
            name: d.name,
            type: d.type,
            avatar: d.avatar,
          })),
      });
    }
  }

  console.log(`⚠️  Found ${violations.length} violations`);
  return { violations };
}

/**
 * Deduplicate organizations by merging specific duplicates into the target organization
 * @param organizationId - The ID of the organization to keep (target)
 * @param duplicateIds - Optional array of specific organization IDs to merge into the target
 *                      If not provided, falls back to finding duplicates by name
 */
async function deduplicateOrganizations(
  organizationId: string,
  duplicateIds?: string[],
): Promise<{
  mergedCount: number;
  targetOrganization: Organization;
}> {
  console.log(`🔄 Starting deduplication for organization: ${organizationId}`);

  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    throw new Error(`Organization with ID ${organizationId} not found`);
  }

  let duplicates;

  if (duplicateIds && duplicateIds.length > 0) {
    // Use specific IDs provided
    console.log(
      `📋 Using specific organization IDs: ${duplicateIds.join(", ")}`,
    );

    duplicates = await prisma.organization.findMany({
      where: {
        id: { in: duplicateIds },
      },
    });

    console.log(duplicates);

    // Verify all provided IDs exist
    const foundIds = duplicates.map((d) => d.id);
    const missingIds = duplicateIds.filter((id) => !foundIds.includes(id));

    if (missingIds.length > 0) {
      console.warn(
        `⚠️  Warning: These organization IDs were not found: ${missingIds.join(", ")}`,
      );
    }

    // Remove the target organization if it was accidentally included in duplicateIds
    duplicates = duplicates.filter((d) => d.id !== organizationId);
  } else {
    // Fall back to name-based matching
    console.log(
      `📋 Falling back to name-based matching for: "${organization.name}"`,
    );

    duplicates = await prisma.organization.findMany({
      where: {
        name: organization.name,
        id: { not: organizationId },
      },
    });
  }

  if (duplicates.length === 0) {
    console.log(
      `✅ No duplicates found for organization: ${organization.name}`,
    );
    return {
      mergedCount: 0,
      targetOrganization: organization as Organization,
    };
  }

  console.log(`📋 Found ${duplicates.length} duplicates to merge:`);
  duplicates.forEach((dup) => console.log(`  - ${dup.name} (${dup.id})`));

  // Confirm before proceeding with merge
  console.log(`\n🎯 TARGET: ${organization.name} (${organization.id})`);
  console.log(`🔀 MERGING: ${duplicates.length} organizations`);

  // Merge all related entities from duplicates to the main organization
  for (const duplicate of duplicates) {
    console.log(`🔀 Merging entities from duplicate: ${duplicate.id}`);

    // Transfer all related entities to the target organization
    await prisma.$transaction(async (tx) => {
      // Update people memberships
      const peopleCount = await tx.person.count({
        where: { organizationId: duplicate.id },
      });
      if (peopleCount > 0) {
        await tx.person.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${peopleCount} people`);
      }

      // Update work orders
      const workOrderCount = await tx.workOrder.count({
        where: { organizationId: duplicate.id },
      });
      if (workOrderCount > 0) {
        await tx.workOrder.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${workOrderCount} work orders`);
      }

      // Update templates
      const templateCount = await tx.template.count({
        where: { organizationId: duplicate.id },
      });
      if (templateCount > 0) {
        await tx.template.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${templateCount} templates`);
      }

      // Update projects
      const projectCount = await tx.project.count({
        where: { organizationId: duplicate.id },
      });
      if (projectCount > 0) {
        await tx.project.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${projectCount} projects`);
      }

      // Update contacts
      const contactCount = await tx.contact.count({
        where: { organizationId: duplicate.id },
      });
      if (contactCount > 0) {
        await tx.contact.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${contactCount} contacts`);
      }

      // Update locations
      const locationCount = await tx.location.count({
        where: { organizationId: duplicate.id },
      });
      if (locationCount > 0) {
        await tx.location.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${locationCount} locations`);
      }

      // Update documents
      const documentCount = await tx.document.count({
        where: { organizationId: duplicate.id },
      });
      if (documentCount > 0) {
        await tx.document.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${documentCount} documents`);
      }

      // Update invoices
      const invoiceCount = await tx.invoice.count({
        where: { organizationId: duplicate.id },
      });
      if (invoiceCount > 0) {
        await tx.invoice.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${invoiceCount} invoices`);
      }

      // Update statements
      const statementCount = await tx.statement.count({
        where: { organizationId: duplicate.id },
      });
      if (statementCount > 0) {
        await tx.statement.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${statementCount} statements`);
      }

      // Update value stores
      const valueStoreCount = await tx.valueStore.count({
        where: { organizationId: duplicate.id },
      });
      if (valueStoreCount > 0) {
        await tx.valueStore.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${valueStoreCount} value stores`);
      }

      // Update email templates
      const emailTemplateCount = await tx.emailTemplate.count({
        where: { organizationId: duplicate.id },
      });
      if (emailTemplateCount > 0) {
        await tx.emailTemplate.updateMany({
          where: { organizationId: duplicate.id },
          data: { organizationId: organizationId },
        });
        console.log(`  ✓ Moved ${emailTemplateCount} email templates`);
      }

      // Handle account manager relationships - update child organizations
      const childOrgCount = await tx.organization.count({
        where: { managerId: duplicate.id },
      });
      if (childOrgCount > 0) {
        await tx.organization.updateMany({
          where: { managerId: duplicate.id },
          data: { managerId: organizationId },
        });
        console.log(`  ✓ Updated ${childOrgCount} child organizations`);
      }

      // Delete the duplicate organization from database
      await tx.organization.delete({
        where: { id: duplicate.id },
      });
      console.log(`  ✓ Deleted duplicate organization from database`);
    });

    // Delete the organization from Clerk (outside of transaction)
    try {
      await clerk.organizations.deleteOrganization(duplicate.id);
      console.log(`  ✓ Deleted organization from Clerk`);
    } catch (error) {
      // Log error but don't fail the entire operation if Clerk deletion fails
      console.error(
        `  ❌ Failed to delete organization ${duplicate.id} from Clerk:`,
        error,
      );
    }
  }

  console.log(
    `✅ Successfully merged ${duplicates.length} duplicates into ${organization.name}`,
  );

  return {
    mergedCount: duplicates.length,
    targetOrganization: organization as Organization,
  };
}

// Example usage functions
async function runViolationCheck() {
  try {
    const result = await getViolations();

    console.log("\n📊 VIOLATION SUMMARY:");
    console.log("=".repeat(50));

    const duplicateViolations = result.violations.filter(
      (v) => v.type === "duplicate",
    );
    const noManagerViolations = result.violations.filter(
      (v) => v.type === "no-manager",
    );
    const noClerkViolations = result.violations.filter(
      (v) => v.type === "no-clerk-organization",
    );

    console.log(`🔄 Duplicate organizations: ${duplicateViolations.length}`);
    console.log(`👤 Missing managers: ${noManagerViolations.length}`);
    console.log(`🔗 Missing Clerk orgs: ${noClerkViolations.length}`);

    if (duplicateViolations.length > 0) {
      console.log("\n🔄 DUPLICATE ORGANIZATIONS:");
      duplicateViolations.forEach((violation) => {
        console.log(
          `  - ${violation.organization.name} (${violation.organization.id})`,
        );
        violation.duplicates?.forEach((dup) => {
          console.log(`    └─ Duplicate: ${dup.id}`);
        });
      });
    }

    if (noManagerViolations.length > 0) {
      console.log("\n👤 ORGANIZATIONS WITHOUT MANAGERS:");
      noManagerViolations.forEach((violation) => {
        console.log(
          `  - ${violation.organization.name} (${violation.organization.id})`,
        );
      });
    }

    if (noClerkViolations.length > 0) {
      console.log("\n🔗 ORGANIZATIONS NOT IN CLERK:");
      noClerkViolations.forEach((violation) => {
        console.log(
          `  - ${violation.organization.name} (${violation.organization.id})`,
        );
      });
    }

    return result;
  } catch (error) {
    console.error("❌ Error running violation check:", error);
    throw error;
  }
}

async function runDeduplication(
  organizationId: string,
  duplicateIds?: string[],
) {
  try {
    const result = await deduplicateOrganizations(organizationId, duplicateIds);
    console.log(
      `\n✅ Deduplication completed! Merged ${result.mergedCount} organizations.`,
    );
    return result;
  } catch (error) {
    console.error("❌ Error running deduplication:", error);
    throw error;
  }
}

// Add this to the main execution section
async function runSafetyCheck(organizationId: string) {
  try {
    const result = await safetyCheckOrganization(organizationId, "merge");
    return result;
  } catch (error) {
    console.error("❌ Error running safety check:", error);
    throw error;
  }
}

async function runDatabaseScan(organizationIds: string[]) {
  try {
    const results = await scanMultipleOrganizations(organizationIds);
    return results;
  } catch (error) {
    console.error("❌ Error running database scan:", error);
    throw error;
  }
}

/**
 * Scan the entire database for references to a specific organization ID
 * This helps identify all data that would be affected by merging or deleting an organization
 * @param organizationId - The organization ID to scan for
 */
async function scanOrganizationReferences(organizationId: string): Promise<{
  organizationId: string;
  organizationName?: string;
  totalReferences: number;
  references: {
    [tableName: string]: {
      count: number;
      sampleIds?: string[];
    };
  };
  managerRelationships: {
    childOrganizations: number;
    parentOrganization?: string;
  };
}> {
  console.log(
    `🔍 Scanning database for references to organization: ${organizationId}`,
  );

  // First, get the organization details
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
    select: { id: true, name: true, managerId: true },
  });

  if (!organization) {
    throw new Error(`Organization with ID ${organizationId} not found`);
  }

  console.log(`📋 Scanning references for: ${organization.name}`);

  const references: {
    [tableName: string]: { count: number; sampleIds?: string[] };
  } = {};
  let totalReferences = 0;

  // Define table checks with proper Prisma queries
  const tableChecks = [
    {
      name: "Person",
      countFn: () => prisma.person.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.person.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Document",
      countFn: () => prisma.document.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.document.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Location",
      countFn: () => prisma.location.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.location.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "WorkOrder",
      countFn: () => prisma.workOrder.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.workOrder.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Template",
      countFn: () => prisma.template.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.template.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Project",
      countFn: () => prisma.project.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.project.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Contact",
      countFn: () => prisma.contact.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.contact.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Invoice",
      countFn: () => prisma.invoice.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.invoice.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "Statement",
      countFn: () => prisma.statement.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.statement.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "ValueStore",
      countFn: () => prisma.valueStore.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.valueStore.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
    {
      name: "EmailTemplate",
      countFn: () => prisma.emailTemplate.count({ where: { organizationId } }),
      sampleFn: () =>
        prisma.emailTemplate.findMany({
          where: { organizationId },
          select: { id: true },
          take: 5,
        }),
    },
  ];

  // Process tables SEQUENTIALLY to avoid prepared statement conflicts
  console.log(`⏳ Scanning ${tableChecks.length} tables sequentially...`);

  for (const table of tableChecks) {
    try {
      console.log(`  Checking ${table.name}...`);

      // Execute count and sample queries sequentially
      const count = await table.countFn();

      if (count > 0) {
        const samples = await table.sampleFn();
        const sampleIds = samples.map((r) => r.id);

        references[table.name] = {
          count,
          sampleIds: sampleIds.length > 0 ? sampleIds : undefined,
        };
        totalReferences += count;

        console.log(`    ✓ Found ${count} records`);
      } else {
        console.log(`    ✓ No records found`);
      }
    } catch (error) {
      console.error(`    ❌ Error querying ${table.name}:`, error);
      // Continue with other tables even if one fails
    }
  }

  // Check manager relationships
  console.log(`  Checking manager relationships...`);
  try {
    const childOrganizations = await prisma.organization.count({
      where: { managerId: organizationId },
    });

    const managerRelationships = {
      childOrganizations,
      parentOrganization: organization.managerId || undefined,
    };

    // Log summary
    console.log(`\n📊 REFERENCE SCAN RESULTS for ${organization.name}:`);
    console.log("=".repeat(60));
    console.log(`Total references found: ${totalReferences}`);

    if (totalReferences > 0) {
      console.log(`\n📋 References by table:`);
      Object.entries(references).forEach(([tableName, data]) => {
        console.log(`  ${tableName}: ${data.count} records`);
        if (data.sampleIds && data.sampleIds.length > 0) {
          console.log(`    Sample IDs: ${data.sampleIds.join(", ")}`);
        }
      });
    }

    if (managerRelationships.childOrganizations > 0) {
      console.log(`\n👥 Manager relationships:`);
      console.log(
        `  Child organizations: ${managerRelationships.childOrganizations}`,
      );
    }

    if (managerRelationships.parentOrganization) {
      console.log(
        `  Parent organization: ${managerRelationships.parentOrganization}`,
      );
    }

    return {
      organizationId,
      organizationName: organization.name,
      totalReferences,
      references,
      managerRelationships,
    };
  } catch (error) {
    console.error(`❌ Error checking manager relationships:`, error);

    // Return partial results even if manager relationship check fails
    return {
      organizationId,
      organizationName: organization.name,
      totalReferences,
      references,
      managerRelationships: {
        childOrganizations: 0,
        parentOrganization: organization.managerId || undefined,
      },
    };
  }
}

/**
 * Scan multiple organizations and provide a comprehensive report
 * @param organizationIds - Array of organization IDs to scan
 */
async function scanMultipleOrganizations(organizationIds: string[]) {
  console.log(
    `🔍 Scanning ${organizationIds.length} organizations for database references...`,
  );

  const results = await Promise.all(
    organizationIds.map((id) => scanOrganizationReferences(id)),
  );

  console.log(`\n📊 MULTI-ORGANIZATION SCAN SUMMARY:`);
  console.log("=".repeat(60));

  const summary = results.reduce(
    (acc, result) => {
      acc.totalOrganizations += 1;
      acc.totalReferences += result.totalReferences;

      if (result.totalReferences > 0) {
        acc.organizationsWithReferences += 1;
      }

      return acc;
    },
    {
      totalOrganizations: 0,
      totalReferences: 0,
      organizationsWithReferences: 0,
    },
  );

  console.log(`Organizations scanned: ${summary.totalOrganizations}`);
  console.log(`Total references found: ${summary.totalReferences}`);
  console.log(
    `Organizations with references: ${summary.organizationsWithReferences}`,
  );

  // Show detailed results for organizations with references
  const organizationsWithData = results.filter((r) => r.totalReferences > 0);
  if (organizationsWithData.length > 0) {
    console.log(`\n⚠️  Organizations that would be affected by deletion:`);
    organizationsWithData.forEach((result) => {
      console.log(
        `  - ${result.organizationName} (${result.organizationId}): ${result.totalReferences} references`,
      );
    });
  }

  return results;
}

/**
 * Safety check before performing organization operations
 * @param organizationId - The organization ID to check before operations
 * @param operation - The operation being performed ('merge' or 'delete')
 */
async function safetyCheckOrganization(
  organizationId: string,
  operation: "merge" | "delete" = "delete",
) {
  console.log(
    `🛡️  Running safety check for ${operation} operation on: ${organizationId}`,
  );

  const scanResult = await scanOrganizationReferences(organizationId);

  if (scanResult.totalReferences === 0) {
    console.log(`✅ Safe to ${operation}: No references found`);
    return { safe: true, scanResult };
  }

  console.log(
    `⚠️  ${operation.toUpperCase()} WARNING: ${scanResult.totalReferences} references found`,
  );
  console.log(
    `This organization has data in ${Object.keys(scanResult.references).length} tables`,
  );

  if (operation === "delete") {
    console.log(`❌ DELETION NOT RECOMMENDED: Would cause data loss`);
    return { safe: false, scanResult };
  } else {
    console.log(
      `ℹ️  MERGE OPERATION: All references will be transferred to target organization`,
    );
    return { safe: true, scanResult };
  }
}

/**
 * Alternative scan function using raw SQL with better error handling
 * Use this if you need more complex queries or better performance
 */
async function scanOrganizationReferencesRawSQL(organizationId: string) {
  console.log(
    `🔍 Scanning database for references (Raw SQL): ${organizationId}`,
  );

  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
    select: { id: true, name: true, managerId: true },
  });

  if (!organization) {
    throw new Error(`Organization with ID ${organizationId} not found`);
  }

  const references: {
    [tableName: string]: { count: number; sampleIds?: string[] };
  } = {};
  let totalReferences = 0;

  // Use $queryRaw with template literals instead of $queryRawUnsafe
  const tablesToCheck = [
    { name: "Person", table: "Person" },
    { name: "Document", table: "Document" },
    { name: "Location", table: "Location" },
    { name: "WorkOrder", table: "WorkOrder" },
    { name: "Template", table: "Template" },
    { name: "Project", table: "Project" },
    { name: "Contact", table: "Contact" },
    { name: "Invoice", table: "Invoice" },
    { name: "Statement", table: "Statement" },
    { name: "ValueStore", table: "ValueStore" },
    { name: "EmailTemplate", table: "EmailTemplate" },
  ];

  // Process each table sequentially to avoid prepared statement conflicts
  for (const table of tablesToCheck) {
    try {
      console.log(`  Checking ${table.name}...`);

      // Use template literals with $queryRaw
      const countResult = await prisma.$queryRaw<{ count: bigint }[]>`
        SELECT COUNT(*) as count 
        FROM ${prisma.$queryRaw`"${table.table}"`} 
        WHERE "organizationId" = ${organizationId}
      `;

      const count = Number(countResult[0]?.count || 0);

      if (count > 0) {
        const sampleResult = await prisma.$queryRaw<{ id: string }[]>`
          SELECT id 
          FROM ${prisma.$queryRaw`"${table.table}"`} 
          WHERE "organizationId" = ${organizationId} 
          LIMIT 5
        `;

        const sampleIds = sampleResult.map((r) => r.id);

        references[table.name] = {
          count,
          sampleIds: sampleIds.length > 0 ? sampleIds : undefined,
        };
        totalReferences += count;
      }
    } catch (error) {
      console.error(`Error querying ${table.name}:`, error);
      // Continue with other tables even if one fails
    }
  }

  // Check manager relationships
  const childOrgResult = await prisma.$queryRaw<{ count: bigint }[]>`
    SELECT COUNT(*) as count 
    FROM "Organization" 
    WHERE "managerId" = ${organizationId}
  `;

  const childOrganizations = Number(childOrgResult[0]?.count || 0);

  const managerRelationships = {
    childOrganizations,
    parentOrganization: organization.managerId || undefined,
  };

  // Log results
  console.log(`\n📊 REFERENCE SCAN RESULTS for ${organization.name}:`);
  console.log("=".repeat(60));
  console.log(`Total references found: ${totalReferences}`);

  if (totalReferences > 0) {
    console.log(`\n📋 References by table:`);
    Object.entries(references).forEach(([tableName, data]) => {
      console.log(`  ${tableName}: ${data.count} records`);
    });
  }

  return {
    organizationId,
    organizationName: organization.name,
    totalReferences,
    references,
    managerRelationships,
  };
}

// Main execution
(async () => {
  try {
    console.log("🚀 Starting hotfix script...");

    // Run violation check first
    // const violations = await runViolationCheck();
    // console.log(violations);

    // Example: Safety check before operations
    // await runSafetyCheck("org_2sLw3MhsEdauNA05gMoAjlve7Sl");

    // Example: Scan multiple organizations
    // await runDatabaseScan(["org_id_1", "org_id_2", "org_id_3"]);

    // Example: Batch process with safety checks
    const sets = [
      {
        duplicates: ["org_2sLvzD47Ef5t8UiFOwbcIZEHaNA"],
      },
      {
        duplicates: ["org_2v06BpLq9GyCV9CAF34Gidhs0jh"],
      },
      // {
      //   target: "org_2sLw3MhsEdauNA05gMoAjlve7Sl",
      //   duplicates: ["org_2sLvuceuuhcSgHZp0xFQlXWPg5T"],
      // },
    ] as { target: string; duplicates: string[] }[];

    for (const set of sets) {
      // Safety check each duplicate before merging
      for (const duplicateId of set.duplicates) {
        console.log(
          `\n🔍 Pre-merge safety check for duplicate: ${duplicateId}`,
        );
        await runSafetyCheck(duplicateId);
      }

      // Perform the merge
      // await runDeduplication(set.target, set.duplicates);
    }

    console.log("\n🎉 Hotfix script completed successfully!");
  } catch (error) {
    console.error("💥 Hotfix script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
})();
