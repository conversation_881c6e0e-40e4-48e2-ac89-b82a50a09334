import { createClerkClient } from "@clerk/nextjs/server";
import { config } from "dotenv";

import type { OrganizationType } from "../../../node_modules/.prisma/client/tech";

const { PrismaClient } = await import(
  "../../../node_modules/.prisma/client/tech"
);

const OrganizationTypeEnum = await import(
  "../../../node_modules/.prisma/client/tech"
).then((m) => m.OrganizationType);

// Load environment variables from .env file
config({ path: ".env" });

const clerkSK = process.env.CLERK_SECRET_KEY;

if (!clerkSK) {
  throw new Error("CLERK_SECRET_KEY environment variable is required");
}

const clerk = createClerkClient({
  secretKey: clerkSK,
});

const prisma = new PrismaClient();

interface DatabaseOrganization {
  id: string;
  name: string;
  type: OrganizationType;
  avatar?: string | null;
  managerId?: string | null;
}

interface ClerkOrganization {
  id: string;
  name: string;
  imageUrl?: string | null;
  publicMetadata?: {
    managerId?: string;
    parentId?: string;
    type?: string;
  };
}

interface SyncIssue {
  type: "missing-in-clerk" | "missing-in-database" | "metadata-mismatch";
  organizationId: string;
  organizationName: string;
  details: string;
  source: "database" | "clerk";
}

/**
 * Check if database organizations exist in Clerk
 * This function takes all organizations from our database and verifies they exist in Clerk
 */
async function checkDatabaseOrganizationsInClerk(): Promise<{
  totalChecked: number;
  foundInClerk: number;
  missingInClerk: DatabaseOrganization[];
  issues: SyncIssue[];
}> {
  console.log("🔍 Checking database organizations in Clerk...");

  // Get all organizations from database
  const dbOrganizations = await prisma.organization.findMany({
    select: {
      id: true,
      name: true,
      type: true,
      avatar: true,
      managerId: true,
    },
  });

  console.log(`📊 Found ${dbOrganizations.length} organizations in database`);

  const missingInClerk: DatabaseOrganization[] = [];
  const issues: SyncIssue[] = [];
  let foundInClerk = 0;

  // Check each database organization in Clerk
  for (const dbOrg of dbOrganizations) {
    try {
      console.log(`  Checking ${dbOrg.name} (${dbOrg.id})...`);

      const clerkOrg = await clerk.organizations.getOrganization({
        organizationId: dbOrg.id,
      });

      if (clerkOrg) {
        foundInClerk++;
        console.log(`    ✅ Found in Clerk`);

        // Check for metadata mismatches
        const clerkType = clerkOrg.publicMetadata?.type;
        const clerkManagerId = clerkOrg.publicMetadata?.managerId;

        if (clerkType && clerkType !== dbOrg.type) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Type mismatch - DB: ${dbOrg.type}, Clerk: ${clerkType}`,
            source: "database",
          });
        }

        if (clerkManagerId !== dbOrg.managerId) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Manager mismatch - DB: ${dbOrg.managerId || "null"}, Clerk: ${clerkManagerId || "null"}`,
            source: "database",
          });
        }
      } else {
        console.log(`    ❌ Not found in Clerk`);
        missingInClerk.push(dbOrg);
        issues.push({
          type: "missing-in-clerk",
          organizationId: dbOrg.id,
          organizationName: dbOrg.name,
          details: "Organization exists in database but not in Clerk",
          source: "database",
        });
      }
    } catch (error) {
      console.log(`    ❌ Error checking in Clerk: ${error}`);
      missingInClerk.push(dbOrg);
      issues.push({
        type: "missing-in-clerk",
        organizationId: dbOrg.id,
        organizationName: dbOrg.name,
        details: `Error accessing Clerk: ${error}`,
        source: "database",
      });
    }
  }

  console.log(`\n📋 Database → Clerk Check Results:`);
  console.log(`  Total checked: ${dbOrganizations.length}`);
  console.log(`  Found in Clerk: ${foundInClerk}`);
  console.log(`  Missing in Clerk: ${missingInClerk.length}`);
  console.log(`  Issues found: ${issues.length}`);

  return {
    totalChecked: dbOrganizations.length,
    foundInClerk,
    missingInClerk,
    issues,
  };
}

/**
 * Check if Clerk organizations exist in database
 * This function takes all organizations from Clerk and verifies they exist in our database
 */
async function checkClerkOrganizationsInDatabase(): Promise<{
  totalChecked: number;
  foundInDatabase: number;
  missingInDatabase: ClerkOrganization[];
  issues: SyncIssue[];
}> {
  console.log("🔍 Checking Clerk organizations in database...");

  // Get all organizations from Clerk
  const clerkOrgsResponse = await clerk.organizations.getOrganizationList({
    limit: 500, // Adjust as needed
  });

  const clerkOrgs = clerkOrgsResponse.data;
  console.log(`📊 Found ${clerkOrgs.length} organizations in Clerk`);

  const missingInDatabase: ClerkOrganization[] = [];
  const issues: SyncIssue[] = [];
  let foundInDatabase = 0;

  // Check each Clerk organization in database
  for (const clerkOrg of clerkOrgs) {
    try {
      console.log(`  Checking ${clerkOrg.name} (${clerkOrg.id})...`);

      const dbOrg = await prisma.organization.findUnique({
        where: { id: clerkOrg.id },
        select: {
          id: true,
          name: true,
          type: true,
          avatar: true,
          managerId: true,
        },
      });

      if (dbOrg) {
        foundInDatabase++;
        console.log(`    ✅ Found in database`);

        // Check for metadata mismatches
        const clerkType = clerkOrg.publicMetadata?.type;
        const clerkManagerId = clerkOrg.publicMetadata?.managerId;

        if (clerkType && clerkType !== dbOrg.type) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: clerkOrg.id,
            organizationName: clerkOrg.name,
            details: `Type mismatch - Clerk: ${clerkType}, DB: ${dbOrg.type}`,
            source: "clerk",
          });
        }

        if (clerkManagerId !== dbOrg.managerId) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: clerkOrg.id,
            organizationName: clerkOrg.name,
            details: `Manager mismatch - Clerk: ${clerkManagerId || "null"}, DB: ${dbOrg.managerId || "null"}`,
            source: "clerk",
          });
        }

        if (clerkOrg.name !== dbOrg.name) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: clerkOrg.id,
            organizationName: clerkOrg.name,
            details: `Name mismatch - Clerk: "${clerkOrg.name}", DB: "${dbOrg.name}"`,
            source: "clerk",
          });
        }
      } else {
        console.log(`    ❌ Not found in database`);
        missingInDatabase.push(clerkOrg as ClerkOrganization);
        issues.push({
          type: "missing-in-database",
          organizationId: clerkOrg.id,
          organizationName: clerkOrg.name,
          details: "Organization exists in Clerk but not in database",
          source: "clerk",
        });
      }
    } catch (error) {
      console.log(`    ❌ Error checking in database: ${error}`);
      issues.push({
        type: "missing-in-database",
        organizationId: clerkOrg.id,
        organizationName: clerkOrg.name,
        details: `Error accessing database: ${error}`,
        source: "clerk",
      });
    }
  }

  console.log(`\n📋 Clerk → Database Check Results:`);
  console.log(`  Total checked: ${clerkOrgs.length}`);
  console.log(`  Found in database: ${foundInDatabase}`);
  console.log(`  Missing in database: ${missingInDatabase.length}`);
  console.log(`  Issues found: ${issues.length}`);

  return {
    totalChecked: clerkOrgs.length,
    foundInDatabase,
    missingInDatabase,
    issues,
  };
}

/**
 * Run comprehensive sync check between database and Clerk
 */
async function runComprehensiveSyncCheck(): Promise<{
  databaseToClerk: Awaited<
    ReturnType<typeof checkDatabaseOrganizationsInClerk>
  >;
  clerkToDatabase: Awaited<
    ReturnType<typeof checkClerkOrganizationsInDatabase>
  >;
  summary: {
    totalIssues: number;
    missingInClerk: number;
    missingInDatabase: number;
    metadataMismatches: number;
  };
}> {
  console.log("🚀 Starting comprehensive sync check...");
  console.log("=".repeat(60));

  // Run both checks
  const databaseToClerk = await checkDatabaseOrganizationsInClerk();
  console.log("\n" + "=".repeat(60));
  const clerkToDatabase = await checkClerkOrganizationsInDatabase();

  // Combine all issues
  const allIssues = [...databaseToClerk.issues, ...clerkToDatabase.issues];

  const summary = {
    totalIssues: allIssues.length,
    missingInClerk: allIssues.filter((i) => i.type === "missing-in-clerk")
      .length,
    missingInDatabase: allIssues.filter((i) => i.type === "missing-in-database")
      .length,
    metadataMismatches: allIssues.filter((i) => i.type === "metadata-mismatch")
      .length,
  };

  // Print comprehensive summary
  console.log("\n🎯 COMPREHENSIVE SYNC SUMMARY:");
  console.log("=".repeat(60));
  console.log(`Database organizations: ${databaseToClerk.totalChecked}`);
  console.log(`Clerk organizations: ${clerkToDatabase.totalChecked}`);
  console.log(
    `Organizations synced both ways: ${Math.min(databaseToClerk.foundInClerk, clerkToDatabase.foundInDatabase)}`,
  );
  console.log(`\n⚠️  ISSUES SUMMARY:`);
  console.log(`  Total issues: ${summary.totalIssues}`);
  console.log(`  Missing in Clerk: ${summary.missingInClerk}`);
  console.log(`  Missing in database: ${summary.missingInDatabase}`);
  console.log(`  Metadata mismatches: ${summary.metadataMismatches}`);

  if (allIssues.length > 0) {
    console.log(`\n📋 DETAILED ISSUES:`);
    allIssues.forEach((issue, index) => {
      console.log(
        `  ${index + 1}. [${issue.type.toUpperCase()}] ${issue.organizationName} (${issue.organizationId})`,
      );
      console.log(`     Source: ${issue.source} | ${issue.details}`);
    });
  }

  return {
    databaseToClerk,
    clerkToDatabase,
    summary,
  };
}

/**
 * Get organizations that exist in both systems but have different data
 */
async function findMetadataMismatches(): Promise<SyncIssue[]> {
  console.log("🔍 Finding metadata mismatches...");

  const issues: SyncIssue[] = [];

  const dbOrganizations = await prisma.organization.findMany({
    select: {
      id: true,
      name: true,
      type: true,
      avatar: true,
      managerId: true,
    },
  });

  for (const dbOrg of dbOrganizations) {
    try {
      const clerkOrg = await clerk.organizations.getOrganization({
        organizationId: dbOrg.id,
      });

      if (clerkOrg) {
        // Check name mismatch
        if (clerkOrg.name !== dbOrg.name) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Name: DB="${dbOrg.name}" vs Clerk="${clerkOrg.name}"`,
            source: "database",
          });
        }

        // Check type mismatch
        const clerkType = clerkOrg.publicMetadata?.type;
        if (clerkType && clerkType !== dbOrg.type) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Type: DB="${dbOrg.type}" vs Clerk="${clerkType}"`,
            source: "database",
          });
        }

        // Check manager mismatch
        const clerkManagerId = clerkOrg.publicMetadata?.managerId;
        if (clerkManagerId !== dbOrg.managerId) {
          issues.push({
            type: "metadata-mismatch",
            organizationId: dbOrg.id,
            organizationName: dbOrg.name,
            details: `Manager: DB="${dbOrg.managerId || "null"}" vs Clerk="${clerkManagerId || "null"}"`,
            source: "database",
          });
        }
      }
    } catch (error) {
      // Skip organizations that don't exist in Clerk
      continue;
    }
  }

  console.log(`📋 Found ${issues.length} metadata mismatches`);
  return issues;
}

// Example usage functions
async function runDatabaseToClerkCheck() {
  try {
    return await checkDatabaseOrganizationsInClerk();
  } catch (error) {
    console.error("❌ Error running database to Clerk check:", error);
    throw error;
  }
}

async function runClerkToDatabaseCheck() {
  try {
    return await checkClerkOrganizationsInDatabase();
  } catch (error) {
    console.error("❌ Error running Clerk to database check:", error);
    throw error;
  }
}

async function runMetadataMismatchCheck() {
  try {
    return await findMetadataMismatches();
  } catch (error) {
    console.error("❌ Error running metadata mismatch check:", error);
    throw error;
  }
}

if (import.meta.url === new URL(import.meta.url).href) {
  // Main execution
  (async () => {
    try {
      console.log("🚀 Starting organization sync checks...");

      // Run comprehensive check
      const result = await runComprehensiveSyncCheck();

      // Optionally run individual checks
      // const dbToClerk = await runDatabaseToClerkCheck();
      // const clerkToDb = await runClerkToDatabaseCheck();
      // const mismatches = await runMetadataMismatchCheck();

      console.log("\n🎉 Sync checks completed successfully!");
    } catch (error) {
      console.error("💥 Sync checks failed:", error);
      process.exit(1);
    } finally {
      await prisma.$disconnect();
    }
  })();
}

// Export functions for use in other scripts
export {
  checkDatabaseOrganizationsInClerk,
  checkClerkOrganizationsInDatabase,
  runComprehensiveSyncCheck,
  findMetadataMismatches,
  runDatabaseToClerkCheck,
  runClerkToDatabaseCheck,
  runMetadataMismatchCheck,
  type SyncIssue,
  type DatabaseOrganization,
  type ClerkOrganization,
};
