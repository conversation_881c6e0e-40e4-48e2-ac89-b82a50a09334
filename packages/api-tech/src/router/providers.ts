import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  BillingType,
  Prisma,
  Provider,
  Technician,
  TimeSheet,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import onWorkOrderEvent from "../jobs/work-order-event";
import {
  adminProcedure,
  createTRPCRouter,
  internalProcedure,
  protectedProcedure,
} from "../trpc";

const zBillingType = z.union([z.literal("HOURLY"), z.literal("FIXED")]);

const selections = {
  provider: {
    id: true,
    level: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  technician: {
    id: true,
    level: true,
    paymentRate: true,
    paymentType: true,
    billingRate: true,
    billingType: true,
  },
  timeSheet: {
    id: true,
    status: true,
    hours: true,
    billingType: true,
    billingRate: true,
    paymentRate: true,
    paymentType: true,
  },
} satisfies {
  provider: Partial<Record<keyof Provider, boolean>>;
  technician: Partial<Record<keyof Technician, boolean>>;
  timeSheet: Partial<Record<keyof TimeSheet, boolean>>;
};

export const providerRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const provider = await ctx.prisma.provider.findUnique({
        where: {
          id: input.id,
        },
        select: selections.provider,
      });

      if (!provider) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Provider not found",
        });
      }

      return provider;
    }),
  getMany: internalProcedure
    .input(
      z.object({
        query: z.string().optional(),
        level: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const search = input.query?.replace(/\s+/g, " ").trim() ?? "";
      const searchQuery: Prisma.ProviderWhereInput = {};

      if (search) {
        const splitQuery = search.split(" ");

        if (splitQuery.length > 1) {
          searchQuery.AND = splitQuery.map((word) => ({
            OR: [
              {
                firstName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                email: {
                  contains: word,
                  mode: "insensitive",
                },
              },
            ],
          }));
        } else {
          searchQuery.OR = [
            {
              firstName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              email: {
                contains: search,
                mode: "insensitive",
              },
            },
          ];
        }
      }

      if (input.level) {
        searchQuery.level = {
          equals: parseInt(input.level),
        };
      }

      const query = {
        deletedAt: null,
        ...searchQuery,
      } as Prisma.ProviderFindManyArgs["where"];

      const [total, providers] = await Promise.all([
        ctx.prisma.provider.count({
          where: query,
        }),
        ctx.prisma.provider.findMany({
          where: query,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select: selections.provider,
        }),
      ]);

      return {
        providers,
        total,
      };
    }),
  create: internalProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        firstName: z.string(),
        lastName: z.string(),
        email: z.string().email(),
        phone: z.string(),
        level: z.number(),
        avatar: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.provider.create({
        data: {
          firstName: input.firstName,
          lastName: input.lastName,
          email: input.email,
          phone: input.phone,
          level: input.level,
          avatar: input.avatar,
        },
        select: selections.provider,
      });
    }),
  update: internalProcedure
    .input(
      z.object({
        id: z.string(),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        level: z.number().optional(),
        avatar: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.provider.update({
        where: {
          id: input.id,
        },
        data: {
          firstName: input.firstName,
          lastName: input.lastName,
          email: input.email,
          phone: input.phone,
          level: input.level,
          avatar: input.avatar,
        },
        select: selections.provider,
      });
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        shiftId: z.string().optional(),
        timeSheetId: z.string().optional(),
        technicianId: z.string().optional(),
        addressId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.provider.update({
        where: {
          id: input.id,
        },
        data: {
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          shifts: input.shiftId
            ? {
                connect: {
                  id: input.shiftId,
                },
              }
            : undefined,
          timeSheets: input.timeSheetId
            ? {
                connect: {
                  id: input.timeSheetId,
                },
              }
            : undefined,
          technicians: input.technicianId
            ? {
                connect: {
                  id: input.technicianId,
                },
              }
            : undefined,
          address: input.addressId
            ? {
                connect: {
                  id: input.addressId,
                },
              }
            : undefined,
        },
        select: selections.provider,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        shiftId: z.string().optional(),
        timeSheetId: z.string().optional(),
        technicianId: z.string().optional(),
        addressId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.provider.update({
        where: {
          id: input.id,
        },
        data: {
          address: input.addressId
            ? {
                disconnect: true,
              }
            : undefined,
          technicians: input.technicianId
            ? {
                disconnect: [
                  {
                    id: input.technicianId,
                  },
                ],
              }
            : undefined,
          orders: input.orderId
            ? {
                disconnect: [
                  {
                    id: input.orderId,
                  },
                ],
              }
            : undefined,
          shifts: input.shiftId
            ? {
                disconnect: [
                  {
                    id: input.shiftId,
                  },
                ],
              }
            : undefined,
          timeSheets: input.timeSheetId
            ? {
                disconnect: [
                  {
                    id: input.timeSheetId,
                  },
                ],
              }
            : undefined,
        },
        select: selections.provider,
      });
    }),
  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.provider.delete({
          where: {
            id: input.id,
          },
          select: {
            id: true,
          },
        });
      }

      return ctx.prisma.provider.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: {
          id: true,
        },
      });
    }),
});

export const techniciansRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            provider: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const technician = await ctx.prisma.technician.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.technician,
          provider: input.include?.provider
            ? {
                select: selections.provider,
              }
            : undefined,
          timeSheets: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!technician) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Technician not found",
        });
      }

      return technician;
    }),
  getMany: protectedProcedure
    .input(
      z.object({
        level: z.number().optional(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        include: z
          .object({
            provider: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const query = {
        deletedAt: null,
        level: input.level,
        templates: input.templateId
          ? {
              equals: input.templateId,
            }
          : undefined,
        orders: input.orderId
          ? {
              equals: input.orderId,
            }
          : undefined,
      } as Prisma.TechnicianFindManyArgs["where"];

      const [total, technicians] = await Promise.all([
        ctx.prisma.technician.count({
          where: query,
        }),
        ctx.prisma.technician.findMany({
          where: query,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select: {
            ...selections.technician,
            provider: input.include?.provider
              ? {
                  select: selections.provider,
                }
              : undefined,
            timeSheets: {
              select: {
                id: true,
              },
            },
          },
        }),
      ]);

      return {
        technicians,
        total,
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        level: z.number().optional(),
        billingRate: z.number(),
        billingType: zBillingType,
        paymentRate: z.number().optional(),
        paymentType: zBillingType.optional(),
        providerId: z.string().optional(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = input.orderId
        ? await ctx.prisma.workOrder.findUniqueOrThrow({
            where: {
              id: input.orderId,
            },
            select: {
              id: true,
              organizationId: true,
              invoiceId: true,
              schedule: {
                select: {
                  id: true,
                  shifts: {
                    select: {
                      id: true,
                      hours: true,
                    },
                  },
                },
              },
            },
          })
        : null;

      const technician = await ctx.prisma.technician.create({
        select: selections.technician,
        data: {
          level: input.level,
          billingRate: input.billingRate,
          billingType: input.billingType,
          paymentRate: input.paymentRate,
          paymentType: input.paymentType as BillingType | undefined,
          provider: input.providerId
            ? {
                connect: {
                  id: input.providerId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          shifts: order
            ? {
                connect: order.schedule.shifts.map((shift) => ({
                  id: shift.id,
                })),
              }
            : undefined,
          timeSheets: order
            ? {
                create: order.schedule.shifts.map((shift) => ({
                  hours: shift.hours,
                  paymentRate: input.paymentRate ?? 0,
                  paymentType: input.paymentType ?? "HOURLY",
                  billingRate: input.billingRate,
                  billingType: input.billingType,
                  total:
                    input.billingType === "HOURLY"
                      ? input.billingRate * shift.hours
                      : input.billingRate,
                  invoice: {
                    connect: {
                      id: order.invoiceId,
                    },
                  },
                  order: {
                    connect: {
                      id: order.id,
                    },
                  },
                  shift: shift.id
                    ? {
                        connect: {
                          id: shift.id,
                        },
                      }
                    : undefined,
                })),
              }
            : undefined,
        },
      });

      if (order) {
        await onWorkOrderEvent({
          action: "UPDATE",
          resource: "TECHNICIAN",
          orderId: order.id,
          actorId: ctx.user.id,
          meta: {
            id: technician.id,
            level: technician.level,
            billingRate: input.billingRate,
            billingType: input.billingType,
            paymentRate: input.paymentRate,
            paymentType: input.paymentType,
            mode: "ADD",
          },
        });
      }

      return technician;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        level: z.number().optional(),
        billingRate: z.number().optional(),
        billingType: zBillingType.optional(),
        paymentRate: z.number().optional(),
        paymentType: zBillingType.optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const technician = await ctx.prisma.technician.update({
        where: {
          id: input.id,
        },
        data: {
          level: input.level,
          billingRate: input.billingRate,
          billingType: input.billingType,
          paymentRate: input.paymentRate,
          paymentType: input.paymentType,
        },
        select: {
          ...selections.technician,
          orders: {
            select: {
              id: true,
            },
          },
          timeSheets: {
            select: {
              ...selections.timeSheet,
              expenses: {
                select: {
                  amount: true,
                },
              },
            },
          },
        },
      });

      const transactions = [];
      for (const timeSheet of technician.timeSheets) {
        if (["PENDING", "ASSIGNED"].includes(timeSheet.status) === true) {
          const expenses = timeSheet.expenses.reduce(
            (acc, expense) => acc + expense.amount,
            0,
          );
          const totalRate =
            input.billingType === "HOURLY"
              ? (input.billingRate ?? timeSheet.billingRate) * timeSheet.hours
              : (input.billingRate ?? timeSheet.billingRate);
          const total = totalRate + expenses;

          transactions.push(
            ctx.prisma.timeSheet.updateMany({
              where: {
                id: timeSheet.id,
              },
              data: {
                billingRate: input.billingRate,
                billingType: input.billingType,
                paymentRate: input.paymentRate,
                paymentType: input.paymentType,
                total,
              },
            }),
          );
        }
      }
      await ctx.prisma.$transaction(transactions);

      if (technician.orders.length > 0) {
        for (const order of technician.orders) {
          await onWorkOrderEvent({
            action: "UPDATE",
            resource: "TECHNICIAN",
            orderId: order.id,
            actorId: ctx.user.id,
            meta: {
              id: technician.id,
              level: technician.level,
              billingRate: input.billingRate,
              billingType: input.billingType,
              paymentRate: input.paymentRate,
              paymentType: input.paymentType,
              mode: "UPDATE",
            },
          });
        }
      }

      return {
        id: technician.id,
        level: technician.level,
        paymentRate: technician.paymentRate,
        paymentType: technician.paymentType,
        billingRate: technician.billingRate,
        billingType: technician.billingType,
      };
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        shiftId: z.string().optional(),
        timeSheetId: z.string().optional(),
        providerId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.technician.update({
        where: {
          id: input.id,
        },
        data: {
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          shifts: input.shiftId
            ? {
                connect: {
                  id: input.shiftId,
                },
              }
            : undefined,
          timeSheets: input.timeSheetId
            ? {
                connect: {
                  id: input.timeSheetId,
                },
              }
            : undefined,
          provider: input.providerId
            ? {
                connect: {
                  id: input.providerId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
        },
        select: selections.technician,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        shiftId: z.string().optional(),
        timeSheetId: z.string().optional(),
        providerId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.technician.update({
        where: {
          id: input.id,
        },
        data: {
          provider: input.providerId
            ? {
                disconnect: true,
              }
            : undefined,
          template: input.templateId
            ? {
                disconnect: true,
              }
            : undefined,
          orders: input.orderId
            ? {
                disconnect: [
                  {
                    id: input.orderId,
                  },
                ],
              }
            : undefined,
          shifts: input.shiftId
            ? {
                disconnect: [
                  {
                    id: input.shiftId,
                  },
                ],
              }
            : undefined,
          timeSheets: input.timeSheetId
            ? {
                disconnect: [
                  {
                    id: input.timeSheetId,
                  },
                ],
              }
            : undefined,
        },
        select: selections.technician,
      });
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const technician = await ctx.prisma.technician.delete({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          level: true,
          providerId: true,
          orders: {
            select: {
              id: true,
            },
          },
        },
      });

      if (technician.orders.length > 0) {
        if (technician.providerId) {
          await ctx.prisma.provider.update({
            where: {
              id: technician.providerId,
            },
            data: {
              orders: {
                disconnect: technician.orders.map((order) => ({
                  id: order.id,
                })),
              },
            },
          });

          await ctx.prisma.$transaction(
            technician.orders.map((order) =>
              ctx.prisma.workOrder.update({
                where: {
                  id: order.id,
                },
                data: {
                  status: "PENDING",
                },
              }),
            ),
          );
        }

        await Promise.all(
          technician.orders.map(async (order) => {
            await onWorkOrderEvent({
              action: "UPDATE",
              resource: "TECHNICIAN",
              orderId: order.id,
              actorId: ctx.user.id,
              meta: {
                id: technician.id,
                level: technician.level,
                mode: "REMOVE",
              },
            });
          }),
        );
      }

      return {
        id: technician.id,
      };
    }),
});
