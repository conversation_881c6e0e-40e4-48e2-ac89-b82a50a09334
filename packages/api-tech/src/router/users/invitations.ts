import type { OrganizationInvitation } from "@clerk/backend";

import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createURL } from "@axa/lib/utils/url";

import type { UserRole } from "../../constants";

import { roles } from "../../constants";
import { createTRPCRouter, internalProcedure } from "../../trpc";

export const userInvitationsRouter = createTRPCRouter({
  getMany: internalProcedure
    .input(
      z.object({
        query: z.string().optional(),
        status: z
          .enum(["accepted", "pending", "revoked", "expired"] as const)
          .optional(),
        limit: z.number().optional().default(10),
        offset: z.number().optional().default(0),
      }),
    )
    .query(async ({ input, ctx }) => {
      const invitations = await ctx.clerk.invitations.getInvitationList({
        query: input.query,
        status: input.status,
        limit: input.limit,
        offset: input.offset,
      });
      return {
        invites: invitations.data.map((invitation) => ({
          id: invitation.id,
          role: invitation.publicMetadata?.role as UserRole,
          organizationId: invitation.publicMetadata?.organizationId as string,
          email: invitation.emailAddress,
          status: invitation.status as NonNullable<
            OrganizationInvitation["status"]
          >,
          revoked: invitation.revoked,
          createdAt: invitation.createdAt,
        })),
        count: invitations.totalCount,
      };
    }),
  send: internalProcedure
    .input(
      z.object({
        email: z.string().email(),
        role: z.enum(roles),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const isInternal = ["ADMIN", "BILLING", "INTERNAL"].includes(input.role);

      if (isInternal === false && !input.organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required for AXA clients",
        });
      }

      try {
        await ctx.clerk.invitations.createInvitation({
          emailAddress: input.email,
          redirectUrl: createURL("sign-up").toString(),
          ignoreExisting: true,
          publicMetadata: {
            role: input.role,
            organizationId: input.organizationId,
          },
        });
      } catch (error) {
        console.error("Error creating invitation", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Error creating invitation",
        });
      }
      return true;
    }),
  revoke: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await ctx.clerk.invitations.revokeInvitation(input.id);
      return true;
    }),
});
