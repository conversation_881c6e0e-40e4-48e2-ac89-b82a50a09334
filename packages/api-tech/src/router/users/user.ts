import { z } from "zod";

import {
  createTRPCRouter,
  internalProcedure,
  protectedProcedure,
} from "../../trpc";

export const userRouter = createTRPCRouter({
  me: protectedProcedure.query(async ({ ctx }) => {
    const person = await ctx.prisma.person.findUniqueOrThrow({
      where: {
        id: ctx.user.id,
      },
      select: {
        organization: {
          select: {
            id: true,
            name: true,
            avatar: true,
            type: true,
          },
        },
        preferences: true,
      },
    });

    return {
      role: ctx.role,
      id: ctx.user.id,
      firstName: ctx.user.firstName,
      lastName: ctx.user.lastName,
      nickname: ctx.user.publicMetadata.nickname as string,
      avatarUrl: ctx.user.imageUrl,
      hasImage: ctx.user.hasImage,
      hasPassword: ctx.user.passwordEnabled,
      email: ctx.user.emailAddresses
        .map((e) => ({
          id: e.id,
          emailAddress: e.emailAddress,
          status: e.verification?.status,
        }))
        .find((e) => e.id === ctx.user.primaryEmailAddressId),
      phone: ctx.user.phoneNumbers
        .map((e) => ({
          id: e.id,
          phoneNumber: e.phoneNumber,
          status: e.verification?.status,
        }))
        .find((p) => p.id === ctx.user.primaryPhoneNumberId),
      organization:
        person.organization ??
        (ctx.isInternal
          ? {
              id: "AXA",
              name: "AXA Professionals",
              avatar: null,
              type: "INTERNAL",
            }
          : {
              id: "AXA-UNKNOWN",
              name: "Unknown",
              avatar: null,
              type: null,
            }),
      preferences: person.preferences,
    };
  }),
  deleteAccount: protectedProcedure.mutation(async ({ ctx }) => {
    await ctx.clerk.users.deleteUser(ctx.user.id);
    await ctx.prisma.person.update({
      where: { id: ctx.user.id },
      data: { deletedAt: new Date(), isUser: false, role: "NONE" },
    });

    return true;
  }),
  updatePreferences: protectedProcedure
    .input(z.object({ preferences: z.record(z.any()) }))
    .mutation(async ({ input, ctx }) => {
      await ctx.prisma.person.update({
        where: {
          id: ctx.user.id,
        },
        data: {
          preferences: input.preferences,
        },
      });

      return true;
    }),
  switchOrganization: internalProcedure
    .input(z.object({ organizationId: z.string().nullable() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.person.update({
        where: { id: ctx.user.id },
        data: {
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : input.organizationId === null
              ? {
                  disconnect: true,
                }
              : undefined,
        },
      });

      await ctx.clerk.users.updateUserMetadata(ctx.user.id, {
        publicMetadata: {
          organizationId: input.organizationId,
        },
      });

      return true;
    }),
});
