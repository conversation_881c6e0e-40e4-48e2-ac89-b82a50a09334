import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Organization, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils/index";

import {
  adminProcedure,
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
  publicProcedure,
} from "../../trpc";

export const organizationsRouter = createTRPCRouter({
  getMetadata: publicProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const org = await ctx.prisma.organization.findUnique({
        where: {
          id: input.id,
        },
        select: {
          name: true,
        },
      });

      if (!org) {
        return {
          title: "Organization not found",
        };
      }

      return {
        title: org.name,
      };
    }),
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            manager: z.boolean().optional().default(true),
            parent: z.boolean().optional().default(true),
            accounts: z.boolean().optional().default(true),
            members: z.boolean().optional().default(false),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const organization = await ctx.prisma.organization.findUnique({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          name: true,
          type: true,
          path: true,
          depth: true,
          numchild: true,
          avatar: true,
          managerId: true,
          settings: true,
          fieldNationId: ctx.isInternal,
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      const manager =
        organization.managerId && input.include?.manager
          ? await ctx.prisma.organization.findUnique({
              where: {
                id: organization.managerId,
              },
              select: {
                id: true,
                name: true,
                type: true,
                avatar: true,
              },
            })
          : null;

      const parent: Organization | null = input.include?.parent
        ? ((await ctx.prisma.organization.findParent({
            where: {
              id: organization.id,
            },
            select: {
              id: true,
              name: true,
              type: true,
              avatar: true,
              managerId: true,
            },
          })) as Organization | null)
        : null;

      const accounts = input.include?.accounts
        ? (((await ctx.prisma.organization.findDescendants({
            where: {
              id: organization.id,
            },
            select: {
              id: true,
              name: true,
              type: true,
              avatar: true,
              managerId: true,
            },
          })) ?? []) as Organization[])
        : [];

      return {
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          avatar: organization.avatar,
          fieldNationId: organization.fieldNationId,
          managerId: organization.managerId,
          settings: organization.settings as Record<
            string,
            boolean | string | number
          >,
          manager: manager ?? null,
          parent: parent ?? null,
        },
        accounts: accounts,
      };
    }),
  getMany: organizationProcedure
    .input(
      z
        .object({
          organizations: z.array(z.string()).optional(),
          organizationId: z.string().optional(),
          types: z
            .array(z.enum(["ACCOUNT", "CLIENT", "INTERNAL"]))
            .optional()
            .default(["ACCOUNT", "CLIENT"]),
          query: z.string().optional(),
          pageSize: z.number().optional(),
          pageNumber: z.number().optional(),
          showDeleted: z.boolean().optional(),
          include: z
            .object({
              manager: z.boolean().optional(),
              parent: z.boolean().optional(),
              members: z.boolean().optional(),
            })
            .optional(),
          ensure: z.array(z.string()).optional(),
        })
        .optional(),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input?.showDeleted) {
          deletedAt = undefined;
        }

        if (input?.organizationId) {
          organizations.push(input.organizationId);
        } else if (input?.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input?.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input?.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }
      const query = {
        deletedAt,
        type: {
          in: input?.types ?? ["CLIENT", "ACCOUNT"],
        },
        name: {
          contains: input?.query,
          mode: "insensitive",
        },
        id:
          organizations && organizations.length > 0
            ? { in: organizations }
            : undefined,
      } as Prisma.OrganizationFindManyArgs["where"];

      const orgSelection: Prisma.OrganizationSelect = {
        id: true,
        type: true,
        name: true,
        avatar: true,
        managerId: true,
        manager: input?.include?.manager
          ? {
              select: {
                id: true,
                name: true,
                type: true,
                avatar: true,
              },
            }
          : undefined,
        fieldNationId: ctx.isInternal,
      };

      const [orgs, count] = await Promise.all([
        ctx.prisma.organization.findMany({
          where: query,
          take: input?.pageSize,
          skip: calculateSkip({
            pageNumber: input?.pageNumber,
            pageSize: input?.pageSize,
          }),
          select: orgSelection,
        }),
        ctx.prisma.organization.count({
          where: query,
        }),
      ]);

      if (input?.ensure) {
        const ensured = await ctx.prisma.organization.findMany({
          where: {
            id: {
              in: input.ensure,
            },
          },
          select: orgSelection,
        });

        orgs.unshift(...ensured);
      }

      return {
        total: count,
        organizations: await Promise.all(
          orgs.map(async (org) => {
            let parent = null;
            if (input?.include?.parent === true) {
              parent = (await ctx.prisma.organization.findParent({
                where: {
                  id: org.id,
                },
                select: {
                  id: true,
                  name: true,
                  type: true,
                  avatar: true,
                  managerId: true,
                },
              })) as Organization | null;
            }
            let members = null;
            if (input?.include?.members === true) {
              const clerkMembers =
                await ctx.clerk.organizations.getOrganizationMembershipList({
                  organizationId: org.id,
                });

              members = clerkMembers.data.map((member) => ({
                id: member.id,
                role: member.role,
                user: {
                  id: member.publicUserData?.userId,
                  avatar: member.publicUserData?.imageUrl,
                  firstName: member.publicUserData?.firstName,
                  lastName: member.publicUserData?.lastName,
                },
              }));
            }

            return {
              ...org,
              members,
              parent: parent
                ? {
                    id: parent.id,
                    name: parent.name,
                    type: parent.type,
                    avatar: parent.avatar,
                    managerId: parent.managerId,
                  }
                : null,
            };
          }),
        ),
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        fieldNationId: z.string().optional(),
        parentId: z.string().optional(),
        name: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const parentOrganization = input.parentId
        ? await ctx.prisma.organization.findUnique({
            where: {
              id: input.parentId,
            },
          })
        : null;

      const managerId =
        (parentOrganization?.type === "CLIENT"
          ? parentOrganization.id
          : parentOrganization?.managerId) ?? null;
      const parentId = parentOrganization?.id ?? null;

      const clerkOrg = await ctx.clerk.organizations.createOrganization({
        name: input.name,
        createdBy: ctx.user.id,
        publicMetadata: {
          parentId,
          managerId,
        },
      });

      const organization = parentOrganization
        ? ((await ctx.prisma.organization.createChild({
            where: {
              id: parentOrganization.id,
            },
            data: {
              id: clerkOrg.id,
              name: input.name,
              avatar: clerkOrg.imageUrl,
              type: "ACCOUNT",
              fieldNationId: input.fieldNationId
                ? parseInt(input.fieldNationId)
                : undefined,
              manager: managerId
                ? {
                    connect: {
                      id: managerId,
                    },
                  }
                : undefined,
            },
          })) as Organization)
        : ((await ctx.prisma.organization.createRoot({
            data: {
              id: clerkOrg.id,
              name: input.name,
              avatar: clerkOrg.imageUrl,
              type: "CLIENT",
              fieldNationId: input.fieldNationId
                ? parseInt(input.fieldNationId)
                : undefined,
            },
          })) as Organization);

      return {
        id: organization.id,
        name: organization.name,
        avatar: organization.avatar,
      };
    }),
  update: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        name: z.string(),
        fieldNationId: z.string().optional(),
        parentId: z.string().optional().nullable(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const org = await ctx.prisma.organization.findUnique({
        where: {
          id: input.organizationId,
        },
      });

      if (!org) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      if (org.name !== input.name) {
        await ctx.clerk.organizations.updateOrganization(input.organizationId, {
          name: input.name,
        });
      }

      if (input.parentId) {
        const currentParent = (await ctx.prisma.organization.findParent({
          where: {
            id: input.organizationId,
          },
          select: {
            id: true,
            type: true,
            managerId: true,
          },
        })) as Organization | null;

        if (currentParent?.id !== input.parentId) {
          const parent = await ctx.prisma.organization.findUniqueOrThrow({
            where: {
              id: input.parentId,
            },
            select: {
              id: true,
              type: true,
              managerId: true,
            },
          });
          const newParentId = input.parentId;
          const newManagerId =
            parent.type === "CLIENT" ? parent.id : parent.managerId;

          await ctx.clerk.organizations.updateOrganization(
            input.organizationId,
            {
              publicMetadata: {
                parentId: newParentId,
                managerId: newManagerId,
              },
            },
          );

          await ctx.prisma.organization.move({
            position: "last-child",
            where: {
              id: org.id,
            },
            reference: {
              where: {
                id: newParentId,
              },
            },
          });

          await ctx.prisma.organization.update({
            where: {
              id: input.organizationId,
            },
            data: {
              type: "ACCOUNT",
              manager: newManagerId
                ? {
                    connect: {
                      id: newManagerId,
                    },
                  }
                : undefined,
            },
          });
        }
      } else if (input.parentId === null && org.type === "ACCOUNT") {
        await ctx.clerk.organizations.updateOrganization(input.organizationId, {
          publicMetadata: {
            parentId: null,
            managerId: null,
          },
        });

        const lastRoot = (await ctx.prisma.organization.findLastRoot({
          select: undefined,
        })) as Organization | null;

        await ctx.prisma.organization.move({
          where: {
            id: input.organizationId,
          },
          position: "last-sibling",
          reference: {
            where: {
              id: lastRoot?.id,
            },
          },
        });

        await ctx.prisma.organization.update({
          where: {
            id: input.organizationId,
          },
          data: {
            type: "CLIENT",
            manager: {
              disconnect: true,
            },
          },
        });
      }

      return ctx.prisma.organization.update({
        where: {
          id: input.organizationId,
        },
        data: {
          name: input.name,
          fieldNationId: input.fieldNationId
            ? parseInt(input.fieldNationId)
            : null,
        },
        select: {
          path: true,
          id: true,
          name: true,
          avatar: true,
          fieldNationId: ctx.isInternal,
          managerId: true,
        },
      });
    }),
  updateSettings: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        settings: z.object({
          reports: z.object({
            daily: z.boolean(),
            weekly: z.boolean(),
          }),
        }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.organization.update({
        where: {
          id: input.organizationId,
        },
        data: {
          settings: input.settings,
        },
        select: {
          id: true,
          settings: true,
        },
      });
    }),
  delete: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const organization = await ctx.prisma.organization.findUnique({
        where: {
          id: input.organizationId,
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      } else if (organization.type === "CLIENT") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete client organization",
        });
      }

      const workOrderCount = await ctx.prisma.workOrder.count({
        where: {
          organizationId: input.organizationId,
        },
      });

      if (ctx.options.permanentDelete || workOrderCount === 0) {
        await ctx.clerk.organizations.deleteOrganization(input.organizationId);
        await ctx.prisma.organization.deleteNode({
          where: {
            id: input.organizationId,
          },
        });

        return {
          id: input.organizationId,
        };
      }

      return ctx.prisma.organization.update({
        where: {
          id: input.organizationId,
        },
        data: {
          deletedAt: new Date(),
        },
        select: {
          id: true,
        },
      });
    }),
  changeManager: adminProcedure
    .input(
      z.object({
        organizationId: z.string(),
        managerId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organization = await ctx.prisma.organization.update({
        where: {
          id: input.organizationId,
        },
        data: {
          manager: {
            connect: {
              id: input.managerId,
            },
          },
        },
      });

      return organization;
    }),
  moveOrganization: adminProcedure
    .input(
      z.object({
        organizationId: z.string(),
        parentId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organization = await ctx.prisma.organization.findUnique({
        where: {
          id: input.organizationId,
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      // @ts-expect-error - types need to be fixed
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
      await organization.node.move({
        position: "last-child",
        node: await ctx.prisma.organization.findUnique({
          where: {
            id: input.parentId,
          },
        }),
      });

      return organization;
    }),
});
