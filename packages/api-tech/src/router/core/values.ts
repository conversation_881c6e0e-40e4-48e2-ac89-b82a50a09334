import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Organization, Prisma, ValueStore } from "@axa/database-tech";
import { ValueStoreType } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils/index";

import { createTRPCRouter, protectedProcedure } from "../../trpc";

export const selectors = {
  values: {
    id: true,
    type: true,
    value: true,
    key: true,
    subtypes: true,
  },
  organization: {
    id: true,
    name: true,
  },
} satisfies {
  values: Partial<Record<keyof ValueStore, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const valuesRouter = createTRPCRouter({
  getMany: protectedProcedure
    .input(
      z.object({
        query: z.string().optional(),
        type: z
          .union([
            z.nativeEnum(ValueStoreType),
            z.array(z.nativeEnum(ValueStoreType)),
          ])
          .optional(),
        organization: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.ValueStoreFindManyArgs["where"] = {
        deletedAt: null,
      };

      if (input.query) {
        where.OR = [
          {
            key: {
              contains: input.query,
              mode: "insensitive",
            },
          },
          {
            value: {
              contains: input.query,
              mode: "insensitive",
            },
          },
        ];
      }

      if (input.type) {
        if (Array.isArray(input.type)) {
          where.type = {
            in: input.type,
          };
        } else {
          where.type = input.type as ValueStoreType;
        }
      }

      if (input.organization) {
        where.organization = {
          id: input.organization,
        };
      }

      const [total, values] = await Promise.all([
        ctx.prisma.valueStore.count({ where }),
        ctx.prisma.valueStore.findMany({
          where,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          orderBy: {
            createdAt: "desc",
          },
          select: {
            ...selectors.values,
            organization: {
              select: selectors.organization,
            },
          },
        }),
      ]);

      return {
        total,
        values,
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        key: z.string(),
        value: z.string(),
        type: z.nativeEnum(ValueStoreType),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const value = await ctx.prisma.valueStore.create({
        data: {
          key: input.key,
          value: input.value,
          type: input.type,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: {
          id: true,
        },
      });

      return value;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        value: z.string().optional(),
        type: z.nativeEnum(ValueStoreType).optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const value = await ctx.prisma.valueStore.update({
        where: {
          id: input.id,
        },
        data: {
          value: input.value,
          type: input.type,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: {
          id: true,
        },
      });
      return value;
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const value = await ctx.prisma.valueStore.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!value) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Value not found",
        });
      }
      if (ctx.options.permanentDelete) {
        await ctx.prisma.valueStore.delete({
          where: {
            id: input.id,
          },
        });
      } else {
        await ctx.prisma.valueStore.update({
          where: {
            id: input.id,
          },
          data: {
            deletedAt: new Date(),
          },
        });
      }

      return true;
    }),
});
