import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Organization, Person, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const selections = {
  person: {
    id: true,
    role: true,
    isUser: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  person: Partial<Record<keyof Person, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const peopleRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const person = await ctx.prisma.person.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.person,
          organization: {
            select: selections.organization,
          },
        },
      });

      if (!person) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Person not found",
        });
      }

      return {
        ...person,
        organization: person.organization
          ? person.organization
          : ["ADMIN", "BILLING", "INTERNAL"].includes(person.role)
            ? {
                id: "AXA",
                name: "AXA Professionals",
                avatar: null,
              }
            : {
                id: "AXA-UNKNOWN",
                name: "Unknown",
                avatar: null,
              },
      };
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        organizations: z.array(z.string()).optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        showDeleted: z.boolean().optional(),
        onlyUsers: z.boolean().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const query = {
        deletedAt,
        isUser: input.onlyUsers ? true : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
        OR: input.query
          ? [
              {
                firstName: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
              {
                email: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
              {
                phone: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
            ]
          : undefined,
      } as Prisma.PersonFindManyArgs["where"];

      const [total, people] = await Promise.all([
        ctx.prisma.person.count({
          where: query,
        }),
        ctx.prisma.person.findMany({
          where: query,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select: {
            ...selections.person,
            organization: {
              select: selections.organization,
            },
          },
        }),
      ]);

      return {
        total,
        people: people.map((person) => ({
          ...person,
          isUser: ctx.options.roles?.includes(person.role),
          organization: person.organization
            ? person.organization
            : ["ADMIN", "BILLING", "INTERNAL"].includes(person.role)
              ? {
                  id: "AXA",
                  name: "AXA Professionals",
                  avatar: null,
                }
              : {
                  id: "AXA-UNKNOWN",
                  name: "Unknown",
                  avatar: null,
                },
        })),
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        firstName: z.string(),
        lastName: z.string(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        avatar: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      return ctx.prisma.person.create({
        select: selections.person,
        data: {
          firstName: input.firstName,
          lastName: input.lastName,
          email: input.email,
          phone: input.phone,
          avatar: input.avatar,
          organization: {
            connect: {
              id: organizationId,
            },
          },
        },
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        avatar: z.string().optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const person = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          userId: true,
        },
      });

      if (person.userId && person.userId !== ctx.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to update this person",
        });
      }

      return ctx.prisma.person.update({
        where: {
          id: input.id,
        },
        data: {
          firstName: input.firstName,
          lastName: input.lastName,
          email: input.email,
          phone: input.phone,
          avatar: input.avatar,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: selections.person,
      });
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const person = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          userId: true,
        },
      });

      if (person.userId && person.userId !== ctx.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to delete this person",
        });
      }

      if (ctx.options.permanentDelete) {
        return ctx.prisma.person.delete({
          where: {
            id: input.id,
          },
        });
      }

      return ctx.prisma.person.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
});
