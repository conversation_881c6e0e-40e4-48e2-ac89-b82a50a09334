import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Organization, PersonRole } from "@axa/database-tech";
import { OrganizationType } from "@axa/database-tech";

import { adminProcedure, createTRPCRouter } from "../../trpc";

export const clerkRouter = createTRPCRouter({
  loadClerk: adminProcedure.mutation(async ({ ctx }) => {
    const users = await ctx.clerk.users.getUserList();
    const organizations = await ctx.clerk.organizations.getOrganizationList();

    return { users: users.data, organizations: organizations.data };
  }),
  syncClerkUsers: adminProcedure.mutation(async ({ ctx }) => {
    const users = await ctx.clerk.users.getUserList();

    for (const user of users.data) {
      const primaryEmail = user.primaryEmailAddress?.emailAddress;
      const primaryPhone = user.primaryPhoneNumber?.phoneNumber;
      const role = user.publicMetadata.role as PersonRole;
      const userId = user.id;
      const organizationId = user.publicMetadata.organizationId as
        | string
        | undefined;

      // TODO: add provider in the future
      // const providerId = user.publicMetadata.providerId as string | undefined;
      // const provider = providerId
      //   ? await ctx.prisma.provider.findUnique({
      //       where: {
      //         id: providerId,
      //       },
      //       select: {
      //         id: true,
      //       },
      //     })
      //   : undefined;

      const organization = organizationId
        ? await ctx.prisma.organization.findUnique({
            where: {
              id: organizationId,
            },
            select: {
              id: true,
            },
          })
        : undefined;

      await ctx.prisma.person.upsert({
        where: {
          email: primaryEmail,
        },
        update: {
          id: userId,
          role,
          firstName: user.firstName ?? "",
          lastName: user.lastName ?? "",
          email: primaryEmail,
          phone: primaryPhone,
          avatar: user.imageUrl,
          organization: organization
            ? {
                connect: {
                  id: organization.id,
                },
              }
            : undefined,
        },
        create: {
          id: userId,
          role,
          firstName: user.firstName ?? "",
          lastName: user.lastName ?? "",
          email: primaryEmail,
          phone: primaryPhone,
          avatar: user.imageUrl,
          organization: organization
            ? {
                connect: {
                  id: organization.id,
                },
              }
            : undefined,
        },
      });
    }

    return true;
  }),
  syncClerkOrganizations: adminProcedure.mutation(async ({ ctx }) => {
    const organizations = await ctx.clerk.organizations.getOrganizationList();

    const orgs = organizations.data.map((org) => ({
      id: org.id,
      managerId: org.publicMetadata?.managerId,
      parentId: org.publicMetadata?.parentId,
      type: org.publicMetadata?.type,
    }));
    // order organizations by depth
    const clients = organizations.data.filter(
      (org) => org.publicMetadata?.managerId === null,
    );
    const accounts = organizations.data
      .filter((org) => org.publicMetadata?.managerId !== null)
      .sort((a, b) => {
        const aParent = orgs.find((o) => o.id === a.publicMetadata?.parentId);
        const bParent = orgs.find((o) => o.id === b.publicMetadata?.parentId);
        if (!aParent || !bParent) {
          return 0;
        }
        if (a.id === bParent.id) {
          return -1;
        } else if (a.id === aParent.id) {
          return -1;
        } else if (aParent.id === b.id) {
          return -1;
        } else if (b.id === bParent.id) {
          return -1;
        }

        if (aParent.id === bParent.id) {
          return 0;
        }

        return 1;
      });

    for (const organization of [...clients, ...accounts]) {
      const managerId = organization.publicMetadata?.managerId as
        | string
        | undefined;
      const parentId = organization.publicMetadata?.parentId as
        | string
        | undefined;
      const type =
        (organization.publicMetadata?.type as OrganizationType | undefined) ??
        (managerId ? OrganizationType.ACCOUNT : OrganizationType.CLIENT);

      let org = await ctx.prisma.organization.findUnique({
        where: {
          id: organization.id,
        },
      });
      const parent = parentId
        ? await ctx.prisma.organization.findUnique({
            where: {
              id: parentId,
            },
          })
        : undefined;
      const manager = managerId
        ? await ctx.prisma.organization.findUnique({
            where: {
              id: managerId,
            },
          })
        : undefined;

      if (org) {
        org = (await ctx.prisma.organization.update({
          where: {
            id: organization.id,
          },
          data: {
            name: organization.name,
            avatar: organization.imageUrl,
            type,
            manager: manager?.id
              ? {
                  connect: {
                    id: manager.id,
                  },
                }
              : undefined,
          },
        })) as unknown as Organization;
      } else {
        if (parent?.id) {
          org = (await ctx.prisma.organization.createChild({
            where: {
              id: parent.id,
            },
            data: {
              id: organization.id,
              name: organization.name,
              avatar: organization.imageUrl,
              type,
              manager: manager?.id
                ? {
                    connect: {
                      id: manager.id,
                    },
                  }
                : undefined,
            },
          })) as Organization;
        } else if (manager?.id) {
          org = (await ctx.prisma.organization.createChild({
            where: {
              id: manager.id,
            },
            data: {
              id: organization.id,
              name: organization.name,
              avatar: organization.imageUrl,
              type,
              manager: {
                connect: {
                  id: manager.id,
                },
              },
            },
          })) as Organization;
        } else {
          org = (await ctx.prisma.organization.createRoot({
            data: {
              id: organization.id,
              name: organization.name,
              avatar: organization.imageUrl,
              type,
            },
          })) as Organization;
        }
      }

      // finally link the organization to the users
      const users = await ctx.prisma.person.findMany({
        where: {
          organizationId: org.id,
        },
      });

      const members =
        await ctx.clerk.organizations.getOrganizationMembershipList({
          organizationId: org.id,
        });

      for (const member of members.data) {
        const userId = member.publicUserData?.userId;
        if (!userId) {
          continue;
        }

        const user = users.find((u) => u.id === userId);
        if (!user) {
          continue;
        }
      }

      for (const user of users) {
        const member = members.data.find(
          (m) => m.publicUserData?.userId === user.id,
        );
        if (!member) {
          await ctx.clerk.organizations.createOrganizationMembership({
            organizationId: org.id,
            userId: user.id,
            role: "org:admin",
          });
        }
      }
    }

    return true;
  }),
  getViolations: adminProcedure.query(async ({ ctx }) => {
    const organizations = await ctx.prisma.organization.findMany({
      where: {
        type: OrganizationType.ACCOUNT,
      },
      select: {
        id: true,
        managerId: true,
        type: true,
        name: true,
        avatar: true,
      },
    });

    const violations: {
      type: "duplicate" | "no-manager" | "no-clerk-organization";
      organization: {
        id: string;
        name: string;
        type: OrganizationType;
        avatar?: string | null;
      };
      duplicates?: {
        id: string;
        name: string;
        type: OrganizationType;
        avatar?: string | null;
      }[];
    }[] = [];

    for (const organization of organizations) {
      const clerkOrganization = await ctx.clerk.organizations.getOrganization({
        organizationId: organization.id,
      });

      if (!clerkOrganization) {
        violations.push({
          type: "no-clerk-organization",
          organization: {
            id: organization.id,
            name: organization.name,
            type: organization.type,
            avatar: organization.avatar,
          },
        });
        continue;
      }

      if (
        !organization.managerId &&
        organization.type === OrganizationType.ACCOUNT
      ) {
        violations.push({
          type: "no-manager",
          organization: {
            id: organization.id,
            name: organization.name,
            type: organization.type,
            avatar: organization.avatar,
          },
        });
      }

      const duplicates = organizations.filter(
        (o) => o.name === organization.name,
      );

      if (duplicates.length > 1) {
        violations.push({
          type: "duplicate",
          organization: {
            id: organization.id,
            name: organization.name,
            type: organization.type,
            avatar: organization.avatar,
          },
          duplicates: duplicates
            .filter((d) => d.id !== organization.id)
            .map((d) => ({
              id: d.id,
              name: d.name,
              type: d.type,
              avatar: d.avatar,
            })),
        });
      }
    }

    return {
      violations,
    };
  }),

  deduplicateOrganizations: adminProcedure
    .input(
      z.object({
        organizationId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { organizationId } = input;

      const organization = await ctx.prisma.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      const duplicates = await ctx.prisma.organization.findMany({
        where: {
          name: organization.name,
          id: { not: organizationId },
        },
      });

      if (duplicates.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No duplicates found",
        });
      }

      // Merge all related entities from duplicates to the main organization
      for (const duplicate of duplicates) {
        // Transfer all related entities to the target organization
        await ctx.prisma.$transaction(async (tx) => {
          // Update people memberships
          await tx.person.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update work orders
          await tx.workOrder.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update templates
          await tx.template.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update projects
          await tx.project.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update contacts
          await tx.contact.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update locations
          await tx.location.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update documents
          await tx.document.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update invoices
          await tx.invoice.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update statements
          await tx.statement.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update value stores
          await tx.valueStore.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Update email templates
          await tx.emailTemplate.updateMany({
            where: { organizationId: duplicate.id },
            data: { organizationId: organizationId },
          });

          // Handle account manager relationships - update child organizations
          await tx.organization.updateMany({
            where: { managerId: duplicate.id },
            data: { managerId: organizationId },
          });

          // Delete the duplicate organization from database
          await tx.organization.delete({
            where: { id: duplicate.id },
          });
        });

        // Delete the organization from Clerk (outside of transaction)
        try {
          await ctx.clerk.organizations.deleteOrganization(duplicate.id);
        } catch (error) {
          // Log error but don't fail the entire operation if Clerk deletion fails
          console.error(
            `Failed to delete organization ${duplicate.id} from Clerk:`,
            error,
          );
        }
      }

      return {
        mergedCount: duplicates.length,
        targetOrganization: organization,
      };
    }),
});
