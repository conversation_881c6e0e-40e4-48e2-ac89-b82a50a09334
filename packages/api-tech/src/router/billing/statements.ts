import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  Organization,
  Prisma,
  Provider,
  Statement,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import {
  accountsProcedure,
  adminProcedure,
  billingProcedure,
  createTRPCRouter,
} from "../../trpc";

const selections = {
  statement: {
    id: true,
    type: true,
    createdAt: true,
    createdBy: true,
    balance: true,
    period: true,
    notes: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  provider: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
} satisfies {
  statement: Partial<Record<keyof Statement, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
  provider: Partial<Record<keyof Provider, boolean>>;
};

export const statementsRouter = createTRPCRouter({
  get: accountsProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const statement = await ctx.prisma.statement.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.statement,
          organization: {
            select: selections.organization,
          },
          actor: {
            select: selections.provider,
          },
        },
      });

      if (!statement) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Statement not found",
        });
      }

      if (
        ctx.accounts &&
        ctx.accounts.includes(statement.organization.id) === false
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Statement not found",
        });
      }

      return statement;
    }),
  getMany: accountsProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        invoiceId: z.string().optional(),
        orderId: z.string().optional(),
        type: z.enum(["FUNDING", "PAYMENT", "FEE"]).optional(),
        types: z.array(z.enum(["FUNDING", "PAYMENT", "FEE"])).optional(),
        dates: z
          .object({
            startDate: z.date().optional(),
            endDate: z.date().optional(),
          })
          .optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      let organizations: string[] | undefined = [];

      if (ctx.accounts) {
        if (input.organizationId) {
          if (ctx.accounts.includes(input.organizationId)) {
            organizations.push(input.organizationId);
          }
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.accounts?.includes(id),
          );
        }
      } else {
        if (input.organizationId) {
          organizations = [input.organizationId];
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = ctx.accounts;
        }
      }

      const query = {
        deletedAt: null,
        organizationId: organizations
          ? {
              in: organizations,
            }
          : undefined,
        orderId: input.orderId,
        invoiceId: input.invoiceId,
        type: input.types
          ? {
              in: input.types,
            }
          : {
              equals: input.type,
            },
        createdAt: input.dates
          ? {
              gte: input.dates.startDate,
              lte: input.dates.endDate,
            }
          : undefined,
      } as Prisma.StatementFindManyArgs["where"];

      const [count, statements] = await Promise.all([
        ctx.prisma.statement.count({
          where: query,
        }),
        ctx.prisma.statement.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            ...selections.statement,
            actor: {
              select: selections.provider,
            },
          },
        }),
      ]);

      return {
        statements,
        total: count,
      };
    }),
  update: billingProcedure
    .input(
      z.object({
        id: z.string(),
        balance: z.number().optional(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.statement.update({
        where: {
          id: input.id,
        },
        data: {
          balance: input.balance,
          notes: input.notes,
        },
        select: selections.statement,
      });
    }),
  delete: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.statement.delete({
          where: {
            id: input.id,
          },
          select: selections.statement,
        });
      }

      return ctx.prisma.statement.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.statement,
      });
    }),
});
