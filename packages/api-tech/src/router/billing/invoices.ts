import { TRPCError } from "@trpc/server";
import { DateTime } from "luxon";
import { z } from "zod";

import type {
  Invoice,
  InvoiceStatus,
  Organization,
  Prisma,
  TimeSheet,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import { generateInvoiceReportCSV } from "../../jobs/cycle-invoices";
import {
  accountsProcedure,
  adminProcedure,
  billingProcedure,
  createTRPCRouter,
} from "../../trpc";

const selections = {
  invoice: {
    id: true,
    name: true,
    status: true,
    dueDate: true,
    timePeriodStart: true,
    timePeriodEnd: true,
  },
  timeSheet: {
    id: true,
    notes: true,
    status: true,
    hours: true,
    billingRate: true,
    billingType: true,
    paymentRate: true,
    paymentType: true,
    total: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  invoice: Partial<Record<keyof Invoice, boolean>>;
  timeSheet: Partial<Record<keyof TimeSheet, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const invoicesRouter = createTRPCRouter({
  get: accountsProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.invoice,
          organization: {
            select: selections.organization,
          },
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      if (ctx.accounts && !ctx.accounts.includes(invoice.organization.id)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Invoice not found",
        });
      }

      const cancellationFees = await ctx.prisma.statement.aggregate({
        where: {
          invoiceId: invoice.id,
          type: "FEE",
        },
        _sum: {
          balance: true,
        },
      });

      const balance = await ctx.prisma.timeSheet.aggregate({
        where: {
          invoiceId: invoice.id,
          status: "APPROVED",
        },
        _sum: {
          total: true,
        },
      });
      const pending = await ctx.prisma.timeSheet.aggregate({
        where: {
          invoiceId: invoice.id,
          status: {
            in: ["PENDING", "ASSIGNED"],
          },
        },
        _sum: {
          total: true,
        },
      });

      const bal = balance._sum.total ?? 0;
      const pen = pending._sum.total ?? 0;
      const fees = cancellationFees._sum.balance ?? 0;

      return {
        balance: bal + pen + fees,
        pending: pen,
        fees,
        ...invoice,
      };
    }),
  getMany: accountsProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        status: z.enum(["DRAFT", "OPEN", "DUE", "PAID", "VOID"]).optional(),
        dates: z
          .object({
            startDate: z.date().optional(),
            endDate: z.date().optional(),
          })
          .optional(),
        include: z
          .object({
            descendants: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      let organizations: string[] | undefined = [];

      if (ctx.accounts) {
        if (input.organizationId) {
          if (ctx.accounts.includes(input.organizationId)) {
            organizations.push(input.organizationId);
          }
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.accounts?.includes(id),
          );
        }
      } else {
        if (input.organizationId) {
          organizations = [input.organizationId];
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = ctx.accounts;
        }
      }

      const query: Prisma.InvoiceFindManyArgs["where"] = {
        deletedAt: null,
        name: {
          contains: input.query,
          mode: "insensitive",
        },
        status: input.status,
        organizationId: organizations
          ? {
              in: organizations,
            }
          : undefined,
      };

      if (input.dates) {
        if (input.dates.startDate || input.dates.endDate) {
          query.dueDate = {
            gte: input.dates.startDate,
            lte: input.dates.endDate,
          };
        }
      }

      const [count, invoices] = await Promise.all([
        ctx.prisma.invoice.count({
          where: query,
        }),
        ctx.prisma.invoice.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          orderBy: {
            dueDate: "desc",
          },
          select: {
            ...selections.invoice,
            organization: {
              select: selections.organization,
            },
          },
        }),
      ]);

      return {
        invoices: await Promise.all(
          invoices.map(async (invoice) => {
            const cancellationFees = await ctx.prisma.statement.aggregate({
              where: {
                invoiceId: invoice.id,
                type: "FEE",
              },
              _sum: {
                balance: true,
              },
            });
            const balance = await ctx.prisma.timeSheet.aggregate({
              where: {
                invoiceId: invoice.id,
                status: "APPROVED",
              },
              _sum: {
                total: true,
              },
            });
            const pending = await ctx.prisma.timeSheet.aggregate({
              where: {
                invoiceId: invoice.id,
                status: {
                  in: ["PENDING", "ASSIGNED"],
                },
              },
              _sum: {
                total: true,
              },
            });
            const bal = balance._sum.total ?? 0;
            const pen = pending._sum.total ?? 0;
            const fees = cancellationFees._sum.balance ?? 0;
            return {
              balance: bal + pen + fees,
              pending: pen,
              fees,
              ...invoice,
            };
          }),
        ),
        total: count,
      };
    }),
  export: accountsProcedure
    .input(
      z.object({
        invoices: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const invoices = await Promise.all(
        (input.invoices ?? []).map((id) =>
          generateInvoiceReportCSV({
            invoiceId: id,
            internal: ctx.isBilling,
          }),
        ),
      );

      return invoices.map((invoice) => ({
        fileName: invoice.fileName,
        csv: invoice.csv,
      }));
    }),
  create: billingProcedure
    .input(
      z.object({
        organizationId: z.string(),
        dueDate: z.date(),
        timePeriodStart: z.date(),
        timePeriodEnd: z.date(),
        status: z.enum(["DRAFT", "OPEN", "DUE", "PAID", "VOID"]),
        name: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.invoice.create({
        data: {
          name:
            input.name ??
            `${
              (await ctx.prisma.invoice.count({
                where: {
                  organizationId: input.organizationId,
                },
              })) + 1
            }`,
          status: input.status,
          timePeriodStart: DateTime.fromJSDate(input.timePeriodStart)
            .setZone("utc")
            .endOf("day")
            .toJSDate(),
          timePeriodEnd: DateTime.fromJSDate(input.timePeriodEnd)
            .setZone("utc")
            .startOf("day")
            .toJSDate(),
          dueDate: DateTime.fromJSDate(input.dueDate)
            .setZone("utc")
            .startOf("day")
            .toJSDate(),
          organization: {
            connect: {
              id: input.organizationId,
            },
          },
        },
        select: selections.invoice,
      });
    }),
  update: billingProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        status: z.string().optional(),
        dueDate: z.date().optional(),
        timePeriodStart: z.date().optional(),
        timePeriodEnd: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.invoice.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          status: input.status as InvoiceStatus,
          timePeriodStart: input.timePeriodStart
            ? DateTime.fromJSDate(input.timePeriodStart)
                .setZone("utc")
                .endOf("day")
                .toJSDate()
            : undefined,
          timePeriodEnd: input.timePeriodEnd
            ? DateTime.fromJSDate(input.timePeriodEnd)
                .setZone("utc")
                .startOf("day")
                .toJSDate()
            : undefined,
          dueDate: input.dueDate
            ? DateTime.fromJSDate(input.dueDate)
                .setZone("utc")
                .startOf("day")
                .toJSDate()
            : undefined,
        },
        select: selections.invoice,
      });
    }),
  delete: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.invoice.delete({
          where: {
            id: input.id,
          },
          select: selections.invoice,
        });
      }

      return ctx.prisma.invoice.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.invoice,
      });
    }),
  markPaid: billingProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const invoice = await ctx.prisma.invoice.findUniqueOrThrow({
        where: {
          status: "DUE",
          id: input.id,
        },
        select: {
          ...selections.invoice,
          number: true,
          organization: {
            select: {
              id: true,
              managerId: true,
            },
          },
          statements: {
            where: {
              type: {
                in: ["FEE"],
              },
            },
            select: {
              id: true,
              balance: true,
            },
          },
          timeSheets: {
            where: {
              status: {
                in: ["APPROVED"],
              },
            },
            select: {
              ...selections.timeSheet,
            },
          },
        },
      });

      const total =
        invoice.timeSheets.reduce((acc, ts) => acc + ts.total, 0) +
        invoice.statements.reduce((acc, st) => acc + st.balance, 0);

      await ctx.prisma.$transaction([
        ctx.prisma.invoice.update({
          where: {
            id: input.id,
          },
          data: {
            status: "PAID",
          },
        }),
        ctx.prisma.statement.create({
          data: {
            createdBy: ctx.user.id,
            balance: total,
            type: "PAYMENT",
            period: new Date(),
            notes: `Invoice payment for ${invoice.name ?? `#${invoice.number}`}`,
            organization: {
              connect: {
                id: invoice.organization.id,
              },
            },
            actor: {
              connect: {
                id: ctx.user.id,
              },
            },
          },
        }),
        ctx.prisma.organization.update({
          where: {
            id: invoice.organization.id,
          },
          data: {
            balance: {
              decrement: total,
            },
          },
        }),
      ]);

      return invoice;
    }),
});
