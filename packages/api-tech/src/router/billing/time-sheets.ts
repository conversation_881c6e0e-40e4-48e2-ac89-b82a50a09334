import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  Expense,
  Invoice,
  Organization,
  Prisma,
  Provider,
  TimeSheet,
  WorkOrder,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import onWorkOrderEvent from "../../jobs/work-order-event";
import {
  accountsProcedure,
  adminProcedure,
  billingProcedure,
  createTRPCRouter,
  organizationProcedure,
} from "../../trpc";

const selections = {
  invoice: {
    id: true,
    name: true,
    status: true,
    dueDate: true,
    timePeriodStart: true,
    timePeriodEnd: true,
  },
  timeSheet: {
    id: true,
    notes: true,
    status: true,
    hours: true,
    billingRate: true,
    billingType: true,
    paymentRate: true,
    paymentType: true,
    total: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  provider: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  expense: {
    id: true,
    type: true,
    amount: true,
    notes: true,
  },
  order: {
    id: true,
    status: true,
    summary: true,
    scope: true,
  },
} satisfies {
  invoice: Partial<Record<keyof Invoice, boolean>>;
  timeSheet: Partial<Record<keyof TimeSheet, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
  provider: Partial<Record<keyof Provider, boolean>>;
  expense: Partial<Record<keyof Expense, boolean>>;
  order: Partial<Record<keyof WorkOrder, boolean>>;
};

export const timeSheetsRouter = createTRPCRouter({
  get: billingProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            order: z.boolean().optional(),
            organization: z.boolean().optional(),
            invoice: z.boolean().optional(),
            expenses: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const timeSheet = await ctx.prisma.timeSheet.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.timeSheet,
          provider: {
            select: selections.provider,
          },
          order: input.include?.order
            ? {
                select: selections.order,
              }
            : undefined,
          invoice: input.include?.invoice
            ? {
                select: {
                  ...selections.invoice,
                  organization: input.include.organization
                    ? {
                        select: selections.organization,
                      }
                    : undefined,
                },
              }
            : undefined,
          expenses: input.include?.expenses
            ? {
                select: selections.expense,
              }
            : undefined,
        },
      });

      if (!timeSheet) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Time Sheet not found",
        });
      }

      return {
        id: timeSheet.id,
        notes: timeSheet.notes,
        hours: timeSheet.hours,
        status: timeSheet.status,
        billingRate: timeSheet.billingRate,
        billingType: timeSheet.billingType,
        paymentRate: timeSheet.paymentRate,
        paymentType: timeSheet.paymentType,
        total: timeSheet.total,
        provider: timeSheet.provider,
        expenses: timeSheet.expenses,
        order: timeSheet.order,
        invoice: timeSheet.invoice,
        organization: (
          timeSheet.invoice as
            | (Partial<Invoice> & {
                organization?: Pick<Organization, "id" | "name" | "avatar">;
              })
            | null
            | undefined
        )?.organization,
      };
    }),
  getMany: accountsProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        providerId: z.string().nullable().optional(),
        technicianId: z.string().nullable().optional(),
        invoiceId: z.string().nullable().optional(),
        orderId: z.string().nullable().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        status: z
          .enum(["PENDING", "ASSIGNED", "APPROVED", "REJECTED"])
          .optional(),
        dates: z
          .object({
            startDate: z.date().optional(),
            endDate: z.date().optional(),
          })
          .optional(),
        include: z
          .object({
            order: z.boolean().optional(),
            organization: z.boolean().optional(),
            invoice: z.boolean().optional(),
            expenses: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      let organizations: string[] | undefined = [];

      if (ctx.accounts) {
        if (input.organizationId) {
          if (ctx.accounts.includes(input.organizationId)) {
            organizations.push(input.organizationId);
          }
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.accounts?.includes(id),
          );
        }
      } else {
        if (input.organizationId) {
          organizations = [input.organizationId];
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = ctx.accounts;
        }
      }

      const query: Prisma.TimeSheetFindManyArgs["where"] = {
        deletedAt: null,
        providerId: input.providerId,
        technicianId: input.technicianId,
        orderId: input.orderId,
        status: input.status,
        invoice:
          input.invoiceId || (organizations?.length ?? 0) > 0
            ? {
                id: {
                  equals: input.invoiceId ?? undefined,
                },
                organizationId: organizations
                  ? {
                      in: organizations,
                    }
                  : undefined,
              }
            : undefined,
      };

      if (input.dates) {
        if (input.dates.startDate || input.dates.endDate) {
          query.order = {
            schedule: {
              shifts: {
                some: {
                  date: {
                    gte: input.dates.startDate,
                    lte: input.dates.endDate,
                  },
                },
              },
            },
          };
        }
      }

      const [count, timeSheets] = await Promise.all([
        ctx.prisma.timeSheet.count({
          where: query,
        }),
        ctx.prisma.timeSheet.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            ...selections.timeSheet,
            provider: {
              select: selections.provider,
            },
            order: input.include?.order
              ? {
                  select: selections.order,
                }
              : undefined,
            invoice: input.include?.invoice
              ? {
                  select: {
                    ...selections.invoice,
                    organization: input.include.organization
                      ? {
                          select: selections.organization,
                        }
                      : undefined,
                  },
                }
              : undefined,
            expenses: input.include?.expenses
              ? {
                  select: selections.expense,
                }
              : undefined,
          },
        }),
      ]);

      return {
        timeSheets: timeSheets.map((timeSheet) => ({
          id: timeSheet.id,
          notes: timeSheet.notes,
          hours: timeSheet.hours,
          status: timeSheet.status,
          billingRate: timeSheet.billingRate,
          billingType: timeSheet.billingType,
          paymentRate: timeSheet.paymentRate,
          paymentType: timeSheet.paymentType,
          total: timeSheet.total,
          provider: timeSheet.provider,
          expenses: timeSheet.expenses,
          order: timeSheet.order,
          invoice: timeSheet.invoice,
          organization: (
            timeSheet.invoice as
              | (Partial<Invoice> & {
                  organization?: Pick<Organization, "id" | "name" | "avatar">;
                })
              | null
              | undefined
          )?.organization,
        })),
        total: count,
      };
    }),
  create: billingProcedure
    .input(
      z.object({
        providerId: z.string().optional(),
        technicianId: z.string().optional(),
        invoiceId: z.string().optional(),
        orderId: z.string().optional(),
        hours: z.number(),
        billingRate: z.number(),
        billingType: z.enum(["HOURLY", "FIXED"]),
        paymentRate: z.number().optional(),
        paymentType: z.enum(["HOURLY", "FIXED"]).optional(),
        date: z.date().optional(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const timeSheet = await ctx.prisma.timeSheet.create({
        data: {
          total:
            input.billingType === "HOURLY"
              ? input.billingRate * input.hours
              : input.billingRate,
          hours: input.hours,
          billingRate: input.billingRate,
          paymentRate: input.paymentRate,
          billingType: input.billingType,
          paymentType: input.paymentType,
          notes: input.notes,
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          provider: input.providerId
            ? {
                connect: {
                  id: input.providerId,
                },
              }
            : undefined,
          technician: input.technicianId
            ? {
                connect: {
                  id: input.technicianId,
                },
              }
            : undefined,
          invoice: input.invoiceId
            ? {
                connect: {
                  id: input.invoiceId,
                },
              }
            : undefined,
        },
        select: selections.timeSheet,
      });

      return timeSheet;
    }),
  update: billingProcedure
    .input(
      z.object({
        id: z.string(),
        hours: z.number().optional(),
        billingRate: z.number().optional(),
        billingType: z.enum(["HOURLY", "FIXED"]).optional(),
        paymentRate: z.number().optional(),
        paymentType: z.enum(["HOURLY", "FIXED"]).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const timeSheet = await ctx.prisma.timeSheet.update({
        where: {
          id: input.id,
        },
        data: {
          hours: input.hours,
          billingRate: input.billingRate,
          billingType: input.billingType,
          paymentRate: input.paymentRate,
          paymentType: input.paymentType,
        },
        select: {
          ...selections.timeSheet,
          orderId: true,
          technicianId: true,
          shift: {
            select: {
              id: true,
              hours: true,
            },
          },
          expenses: {
            select: {
              amount: true,
            },
          },
        },
      });

      const totalExpenses = timeSheet.expenses.reduce(
        (acc, expense) => acc + expense.amount,
        0,
      );
      const totalRate =
        timeSheet.billingType === "HOURLY"
          ? timeSheet.billingRate * timeSheet.hours
          : timeSheet.billingRate;
      const total = totalRate + totalExpenses;

      const transactions = [];
      if (total !== timeSheet.total) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: input.id,
            },
            data: {
              total,
            },
          }),
        );
      }

      if (timeSheet.technicianId) {
        const tech = await ctx.prisma.technician.findUnique({
          where: {
            id: timeSheet.technicianId,
          },
          select: {
            id: true,
            billingRate: true,
            billingType: true,
            paymentRate: true,
            paymentType: true,
          },
        });

        if (tech) {
          transactions.push(
            ctx.prisma.technician.update({
              where: {
                id: timeSheet.technicianId,
              },
              data: {
                billingRate:
                  tech.billingRate !== timeSheet.billingRate
                    ? timeSheet.billingRate
                    : undefined,
                billingType:
                  tech.billingType !== timeSheet.billingType
                    ? timeSheet.billingType
                    : undefined,
                paymentRate:
                  tech.paymentRate !== timeSheet.paymentRate
                    ? timeSheet.paymentRate
                    : undefined,
                paymentType:
                  tech.paymentType !== timeSheet.paymentType
                    ? timeSheet.paymentType
                    : undefined,
              },
            }),
          );
        }
      }

      if (timeSheet.shift && timeSheet.shift.hours !== timeSheet.hours) {
        transactions.push(
          ctx.prisma.shift.update({
            where: {
              id: timeSheet.shift.id,
            },
            data: {
              hours: timeSheet.hours,
            },
          }),
        );
      }

      await ctx.prisma.$transaction(transactions);

      if (timeSheet.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: timeSheet.orderId,
          resource: "TIMESHEET",
          action: "UPDATE",
          emergency: false,
          internal: true,
          meta: {
            id: timeSheet.id,
          },
        });
      }

      return {
        id: timeSheet.id,
      };
    }),
  delete: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.timeSheet.delete({
          where: {
            id: input.id,
          },
          select: selections.timeSheet,
        });
      }

      return ctx.prisma.timeSheet.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.timeSheet,
      });
    }),
  changeStatus: billingProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.enum(["PENDING", "ASSIGNED", "APPROVED", "REJECTED"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.timeSheet.update({
        where: {
          id: input.id,
        },
        data: {
          status: input.status,
        },
        select: {
          id: true,
          status: true,
        },
      });
    }),
  signOffTimeSheet: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.enum(["APPROVED", "REJECTED"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.timeSheet.update({
        where: {
          id: input.id,
        },
        data: {
          status: input.status,
        },
        select: {
          id: true,
          status: true,
        },
      });
    }),
});
