import { DateTime } from "luxon";

import type { TimeStruct } from "@axa/lib/utils/time";
import { setTimeToInteger } from "@axa/lib/utils/time";

export function createDateForTimeZone({
  timeZone,
  date,
  time,
}: {
  timeZone?: string;
  date?: Date;
  time?: TimeStruct | number;
}) {
  // Validate input date
  if (date && !isValidDate(date)) {
    console.error("Invalid input date:", date);
    throw new Error(`Invalid input date: ${date}`);
  }

  // Default to system timezone if none provided, fallback to UTC
  const resolvedTimeZone = timeZone || DateTime.local().zoneName || "UTC";

  let dateTime = date
    ? DateTime.fromJSDate(date).setZone(resolvedTimeZone)
    : DateTime.now().setZone(resolvedTimeZone);

  // Check if DateTime creation was successful
  if (!dateTime.isValid) {
    console.error("Invalid DateTime created:", {
      dateTime: String(dateTime),
      invalidReason: (dateTime as any).invalidReason,
      invalidExplanation: (dateTime as any).invalidExplanation,
      timeZone: resolvedTimeZone,
      date,
    });
    throw new Error(
      `Invalid DateTime: ${(dateTime as any).invalidReason} - ${(dateTime as any).invalidExplanation}`,
    );
  }

  if (typeof time === "object" && time) {
    // Validate time object
    if (typeof time.hour !== "number" || typeof time.minute !== "number") {
      console.error("Invalid time object:", time);
      throw new Error(`Invalid time object: ${JSON.stringify(time)}`);
    }

    if (
      time.hour < 0 ||
      time.hour > 23 ||
      time.minute < 0 ||
      time.minute > 59
    ) {
      console.error("Time values out of range:", time);
      throw new Error(
        `Time values out of range: hour=${time.hour}, minute=${time.minute}`,
      );
    }

    dateTime = dateTime.set({
      hour: time.hour,
      minute: time.minute,
    });

    // Check if setting time was successful
    if (!dateTime.isValid) {
      console.error("Invalid DateTime after setting time:", {
        dateTime: String(dateTime),
        invalidReason: (dateTime as any).invalidReason,
        invalidExplanation: (dateTime as any).invalidExplanation,
        time,
      });
      throw new Error(
        `Invalid DateTime after setting time: ${(dateTime as any).invalidReason}`,
      );
    }
  }

  const resultDate = dateTime.toUTC().toJSDate();

  // Validate result
  if (!isValidDate(resultDate)) {
    console.error("Invalid result date:", resultDate);
    throw new Error(`Invalid result date: ${resultDate}`);
  }

  return resultDate;
}

export function createShiftDateAndTime(input: {
  type?: "SERVICE_DATE" | "SERVICE_WINDOW" | "SERVICE_TERM";
  timeZone?: string;
  date?: Date | null;
  startDate?: Date | null;
  endDate?: Date | null;
  startTime?: TimeStruct | number | null;
  endTime?: TimeStruct | number | null;
  hours?: number | null;
}) {
  // Validate input dates
  if (input.date && !isValidDate(input.date)) {
    console.error("Invalid input.date:", input.date);
    throw new Error(`Invalid input.date: ${input.date}`);
  }
  if (input.startDate && !isValidDate(input.startDate)) {
    console.error("Invalid input.startDate:", input.startDate);
    throw new Error(`Invalid input.startDate: ${input.startDate}`);
  }
  if (input.endDate && !isValidDate(input.endDate)) {
    console.error("Invalid input.endDate:", input.endDate);
    throw new Error(`Invalid input.endDate: ${input.endDate}`);
  }

  // Default to system timezone if none provided
  const resolvedTimeZone = input.timeZone || DateTime.local().zoneName || "UTC";

  const initialHours = input.hours ?? 0;
  const startTime = setTimeToInteger(input.startTime ?? undefined);

  const endTime = input.endTime
    ? setTimeToInteger(input.endTime)
    : startTime + initialHours * 60;

  // Validate time calculations
  if (typeof startTime !== "number" || isNaN(startTime)) {
    console.error(
      "Invalid startTime:",
      startTime,
      "from input:",
      input.startTime,
    );
    throw new Error(`Invalid startTime: ${startTime}`);
  }
  if (typeof endTime !== "number" || isNaN(endTime)) {
    console.error("Invalid endTime:", endTime, "from input:", input.endTime);
    throw new Error(`Invalid endTime: ${endTime}`);
  }

  const hours = initialHours || (endTime - startTime) / 60;
  const type = input.type ?? (input.date ? "SERVICE_DATE" : "SERVICE_WINDOW");

  const result: any = {
    timeZone: resolvedTimeZone,
    type,
    hours,
    startTime,
    endTime,
  };

  try {
    if (["SERVICE_DATE"].includes(type)) {
      result.date = createDateForTimeZone({
        timeZone: resolvedTimeZone,
        date: input.date ?? undefined,
        time: input.startTime ?? undefined,
      });
      result.startDate = null;
      result.endDate = null;
    } else {
      result.date = undefined;
    }

    if (["SERVICE_WINDOW", "SERVICE_TERM"].includes(type)) {
      result.startDate = createDateForTimeZone({
        timeZone: resolvedTimeZone,
        date: input.startDate ?? undefined,
        time: input.startTime ?? undefined,
      });

      result.endDate = createDateForTimeZone({
        timeZone: resolvedTimeZone,
        date: input.endDate ?? undefined,
        time: input.endTime ?? undefined,
      });
    } else {
      result.startDate = null;
      result.endDate = null;
    }
  } catch (error) {
    console.error("Error in createShiftDateAndTime:", error);
    console.error("Input that caused error:", input);
    throw error;
  }

  return result;
}

// Helper function to validate dates
function isValidDate(date: Date): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}
