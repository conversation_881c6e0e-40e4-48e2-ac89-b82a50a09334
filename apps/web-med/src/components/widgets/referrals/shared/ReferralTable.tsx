import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@axa/ui/primitives/table";
import { Button } from "@axa/ui/primitives/button";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { CheckCircle, XCircle, DollarSign } from "lucide-react";
import { cn } from "@axa/ui/lib";

import type { Referral } from "./types";
import { ReferralStatusBadge, PayoutStatusBadge } from "./ReferralStatusBadge";

interface ReferralTableProps {
  referrals: Referral[];
  isLoading?: boolean;
  onApprove?: (referralId: string) => void;
  onReject?: (referralId: string) => void;
  onProcessPayout?: (referralId: string) => void;
  showActions?: boolean;
  className?: string;
}

export function ReferralTable({
  referrals,
  isLoading = false,
  onApprove,
  onReject,
  onProcessPayout,
  showActions = false,
  className,
}: ReferralTableProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse flex items-center space-x-4 p-4">
            <div className="size-10 bg-muted rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded w-1/4" />
              <div className="h-3 bg-muted rounded w-1/2" />
            </div>
            <div className="h-6 bg-muted rounded w-20" />
          </div>
        ))}
      </div>
    );
  }

  if (referrals.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>No referrals found</p>
      </div>
    );
  }

  return (
    <div className={cn("rounded-md border", className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Referred By</TableHead>
            <TableHead>Invited User</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Payout</TableHead>
            <TableHead>Date</TableHead>
            {showActions && <TableHead>Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {referrals.map((referral) => (
            <TableRow key={referral.id}>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <Avatar className="size-8">
                    <AvatarImage 
                      src={referral.invitedBy.avatar || undefined} 
                      alt={`${referral.invitedBy.firstName} ${referral.invitedBy.lastName}`} 
                    />
                    <AvatarFallback className="text-xs">
                      {getInitials(referral.invitedBy.firstName, referral.invitedBy.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-sm">
                      {referral.invitedBy.firstName} {referral.invitedBy.lastName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {referral.invitedBy.specialty}
                    </p>
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                {referral.invitedUser ? (
                  <div>
                    <p className="text-sm font-medium">
                      {referral.invitedUser.firstName} {referral.invitedUser.lastName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {referral.invitedUser.email}
                    </p>
                  </div>
                ) : (
                  <span className="text-sm text-muted-foreground">Pending signup</span>
                )}
              </TableCell>

              <TableCell>
                <div className="space-y-1">
                  <ReferralStatusBadge status={referral.status} />
                  {referral.payoutStatus && (
                    <PayoutStatusBadge status={referral.payoutStatus} />
                  )}
                </div>
              </TableCell>

              <TableCell>
                <div className="flex items-center space-x-1">
                  <DollarSign className="size-4 text-green-600" />
                  <span className="font-medium">${referral.payoutAmount}</span>
                </div>
              </TableCell>

              <TableCell>
                <span className="text-sm text-muted-foreground">
                  {formatDate(referral.createdAt)}
                </span>
              </TableCell>

              {showActions && (
                <TableCell>
                  <div className="flex items-center space-x-1">
                    {referral.status === "signed_up" && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onApprove?.(referral.id)}
                          className="h-8 px-2"
                        >
                          <CheckCircle className="size-3 text-green-600" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onReject?.(referral.id)}
                          className="h-8 px-2"
                        >
                          <XCircle className="size-3 text-red-600" />
                        </Button>
                      </>
                    )}
                    
                    {referral.payoutStatus === "pending" && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onProcessPayout?.(referral.id)}
                        className="h-8 px-2"
                      >
                        <DollarSign className="size-3 text-blue-600" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
