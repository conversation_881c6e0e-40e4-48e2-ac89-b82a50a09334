export type ReferralStatus = 
  | "invited" 
  | "signed_up" 
  | "approved" 
  | "rejected" 
  | "expired";

export type PayoutStatus = 
  | "pending" 
  | "processing" 
  | "completed" 
  | "failed";

export interface ReferralUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string | null;
  title?: string;
  specialty?: string;
  experience?: number;
  location?: string;
}

export interface Referral {
  id: string;
  inviteLink: string;
  invitedBy: ReferralUser;
  invitedUser?: ReferralUser;
  status: ReferralStatus;
  payoutStatus?: PayoutStatus;
  payoutAmount: number;
  createdAt: string;
  updatedAt: string;
  signedUpAt?: string;
  approvedAt?: string;
  rejectedAt?: string;
  expiredAt?: string;
  payoutProcessedAt?: string;
  notes?: string;
}

export interface ReferralStats {
  totalInvited: number;
  totalSignedUp: number;
  totalApproved: number;
  totalRejected: number;
  totalExpired: number;
  totalPayoutAmount: number;
  pendingPayouts: number;
  completedPayouts: number;
}

export interface ReferralFilters {
  status?: ReferralStatus[];
  payoutStatus?: PayoutStatus[];
  dateRange?: {
    from: Date;
    to: Date;
  };
  invitedBy?: string[];
  search?: string;
}

export interface ReferralInviteFormData {
  email: string;
  firstName?: string;
  lastName?: string;
  personalMessage?: string;
}
