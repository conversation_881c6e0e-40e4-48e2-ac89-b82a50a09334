import { Badge } from "@axa/ui/primitives/badge";
import { cn } from "@axa/ui/lib";
import { 
  Clock, 
  UserCheck, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Mail
} from "lucide-react";

import type { ReferralStatus, PayoutStatus } from "./types";

interface ReferralStatusBadgeProps {
  status: ReferralStatus;
  className?: string;
  showIcon?: boolean;
}

interface PayoutStatusBadgeProps {
  status: PayoutStatus;
  className?: string;
  showIcon?: boolean;
}

const statusConfig = {
  invited: {
    label: "Invited",
    variant: "secondary" as const,
    icon: Mail,
    className: "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100",
  },
  signed_up: {
    label: "Signed Up",
    variant: "secondary" as const,
    icon: UserCheck,
    className: "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100",
  },
  approved: {
    label: "Approved",
    variant: "default" as const,
    icon: CheckCircle,
    className: "bg-green-50 text-green-700 border-green-200 hover:bg-green-100",
  },
  rejected: {
    label: "Rejected",
    variant: "destructive" as const,
    icon: XCircle,
    className: "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
  },
  expired: {
    label: "Expired",
    variant: "outline" as const,
    icon: AlertTriangle,
    className: "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100",
  },
};

const payoutStatusConfig = {
  pending: {
    label: "Payout Pending",
    variant: "secondary" as const,
    icon: Clock,
    className: "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100",
  },
  processing: {
    label: "Processing",
    variant: "secondary" as const,
    icon: Clock,
    className: "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100",
  },
  completed: {
    label: "Paid",
    variant: "default" as const,
    icon: CheckCircle,
    className: "bg-green-50 text-green-700 border-green-200 hover:bg-green-100",
  },
  failed: {
    label: "Payout Failed",
    variant: "destructive" as const,
    icon: XCircle,
    className: "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
  },
};

export function ReferralStatusBadge({ 
  status, 
  className, 
  showIcon = true 
}: ReferralStatusBadgeProps) {
  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {showIcon && <Icon className="mr-1 size-3" />}
      {config.label}
    </Badge>
  );
}

export function PayoutStatusBadge({ 
  status, 
  className, 
  showIcon = true 
}: PayoutStatusBadgeProps) {
  const config = payoutStatusConfig[status];
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {showIcon && <Icon className="mr-1 size-3" />}
      {config.label}
    </Badge>
  );
}
