import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@axa/ui/primitives/card";
import { Badge } from "@axa/ui/primitives/badge";
import { 
  Users, 
  User<PERSON>heck, 
  CheckCircle, 
  DollarSign,
  TrendingUp,
  Clock,
  XCircle
} from "lucide-react";
import { cn } from "@axa/ui/lib";

import type { ReferralStats } from "./types";

interface ReferralStatsProps {
  stats: ReferralStats;
  className?: string;
  variant?: "default" | "compact";
}

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  compact?: boolean;
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  description, 
  trend, 
  className,
  compact = false
}: StatCardProps) {
  return (
    <Card className={cn("", className)}>
      <CardHeader className={cn("pb-2", compact && "pb-1")}>
        <CardTitle className={cn(
          "flex items-center justify-between text-sm font-medium",
          compact && "text-xs"
        )}>
          <span className="flex items-center space-x-2">
            <Icon className={cn("size-4", compact && "size-3")} />
            <span>{title}</span>
          </span>
          {trend && (
            <Badge 
              variant={trend.isPositive ? "default" : "destructive"}
              className="text-xs"
            >
              <TrendingUp className="mr-1 size-3" />
              {trend.isPositive ? "+" : ""}{trend.value}%
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className={cn("pt-0", compact && "pb-3")}>
        <div className={cn("text-2xl font-bold", compact && "text-lg")}>
          {value}
        </div>
        {description && (
          <p className={cn(
            "text-xs text-muted-foreground mt-1",
            compact && "text-xs"
          )}>
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

export function ReferralStats({ 
  stats, 
  className, 
  variant = "default" 
}: ReferralStatsProps) {
  const compact = variant === "compact";
  
  const conversionRate = stats.totalInvited > 0 
    ? Math.round((stats.totalApproved / stats.totalInvited) * 100)
    : 0;

  const signupRate = stats.totalInvited > 0 
    ? Math.round((stats.totalSignedUp / stats.totalInvited) * 100)
    : 0;

  return (
    <div className={cn("grid gap-4", className)}>
      {/* Primary Stats Row */}
      <div className={cn(
        "grid gap-4",
        compact ? "grid-cols-2 lg:grid-cols-4" : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
      )}>
        <StatCard
          title="Total Invited"
          value={stats.totalInvited}
          icon={Users}
          description="All invitations sent"
          compact={compact}
        />
        
        <StatCard
          title="Signed Up"
          value={stats.totalSignedUp}
          icon={UserCheck}
          description={`${signupRate}% conversion rate`}
          compact={compact}
        />
        
        <StatCard
          title="Approved"
          value={stats.totalApproved}
          icon={CheckCircle}
          description={`${conversionRate}% of invites`}
          compact={compact}
        />
        
        <StatCard
          title="Total Earnings"
          value={`$${stats.totalPayoutAmount.toLocaleString()}`}
          icon={DollarSign}
          description="Lifetime referral earnings"
          compact={compact}
        />
      </div>

      {/* Secondary Stats Row */}
      {!compact && (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          <StatCard
            title="Pending Payouts"
            value={stats.pendingPayouts}
            icon={Clock}
            description="Awaiting processing"
            className="border-yellow-200 bg-yellow-50/50"
          />
          
          <StatCard
            title="Completed Payouts"
            value={stats.completedPayouts}
            icon={CheckCircle}
            description="Successfully processed"
            className="border-green-200 bg-green-50/50"
          />
          
          <StatCard
            title="Rejected"
            value={stats.totalRejected}
            icon={XCircle}
            description="Applications declined"
            className="border-red-200 bg-red-50/50"
          />
        </div>
      )}

      {/* Summary Card */}
      {!compact && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-lg">Referral Performance</h3>
                <p className="text-sm text-muted-foreground">
                  You've successfully referred {stats.totalApproved} professionals
                  {stats.totalApproved > 0 && ` and earned $${stats.totalPayoutAmount}`}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">
                  {conversionRate}%
                </div>
                <div className="text-xs text-muted-foreground">
                  Success Rate
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
