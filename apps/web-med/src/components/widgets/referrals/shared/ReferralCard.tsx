import { <PERSON>, CardContent, CardHeader } from "@axa/ui/primitives/card";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Button } from "@axa/ui/primitives/button";
import { Badge } from "@axa/ui/primitives/badge";
import { 
  Calendar, 
  DollarSign, 
  Mail, 
  MapPin, 
  MoreHorizontal,
  Copy,
  ExternalLink
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { cn } from "@axa/ui/lib";

import type { Referral } from "./types";
import { ReferralStatusBadge, PayoutStatusBadge } from "./ReferralStatusBadge";

interface ReferralCardProps {
  referral: Referral;
  className?: string;
  showActions?: boolean;
  onCopyLink?: (link: string) => void;
  onViewDetails?: (referral: Referral) => void;
  onResendInvite?: (referral: Referral) => void;
}

export function ReferralCard({
  referral,
  className,
  showActions = true,
  onCopyLink,
  onViewDetails,
  onResendInvite,
}: ReferralCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const handleCopyLink = () => {
    if (onCopyLink) {
      onCopyLink(referral.inviteLink);
    } else {
      navigator.clipboard.writeText(referral.inviteLink);
    }
  };

  return (
    <Card className={cn("relative", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="size-10">
              <AvatarImage 
                src={referral.invitedBy.avatar || undefined} 
                alt={`${referral.invitedBy.firstName} ${referral.invitedBy.lastName}`} 
              />
              <AvatarFallback>
                {getInitials(referral.invitedBy.firstName, referral.invitedBy.lastName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-sm">
                {referral.invitedBy.firstName} {referral.invitedBy.lastName}
              </h3>
              <p className="text-xs text-muted-foreground">
                {referral.invitedBy.title} • {referral.invitedBy.specialty}
              </p>
            </div>
          </div>
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="size-8 p-0">
                  <MoreHorizontal className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleCopyLink}>
                  <Copy className="mr-2 size-4" />
                  Copy Invite Link
                </DropdownMenuItem>
                {onViewDetails && (
                  <DropdownMenuItem onClick={() => onViewDetails(referral)}>
                    <ExternalLink className="mr-2 size-4" />
                    View Details
                  </DropdownMenuItem>
                )}
                {onResendInvite && referral.status === "invited" && (
                  <DropdownMenuItem onClick={() => onResendInvite(referral)}>
                    <Mail className="mr-2 size-4" />
                    Resend Invite
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Invited User Info */}
        {referral.invitedUser ? (
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
            <Avatar className="size-8">
              <AvatarImage 
                src={referral.invitedUser.avatar || undefined} 
                alt={`${referral.invitedUser.firstName} ${referral.invitedUser.lastName}`} 
              />
              <AvatarFallback className="text-xs">
                {getInitials(referral.invitedUser.firstName, referral.invitedUser.lastName)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm truncate">
                {referral.invitedUser.firstName} {referral.invitedUser.lastName}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {referral.invitedUser.email}
              </p>
            </div>
          </div>
        ) : (
          <div className="p-3 bg-muted/30 rounded-lg border-2 border-dashed">
            <p className="text-sm text-muted-foreground text-center">
              Invitation pending
            </p>
          </div>
        )}

        {/* Status and Payout */}
        <div className="flex items-center justify-between">
          <ReferralStatusBadge status={referral.status} />
          {referral.payoutStatus && (
            <PayoutStatusBadge status={referral.payoutStatus} />
          )}
        </div>

        {/* Metadata */}
        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Calendar className="size-3" />
            <span>Invited {formatDate(referral.createdAt)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <DollarSign className="size-3" />
            <span>${referral.payoutAmount}</span>
          </div>
        </div>

        {/* Location if available */}
        {referral.invitedUser?.location && (
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <MapPin className="size-3" />
            <span>{referral.invitedUser.location}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
