import { RefreshCw, Users } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";

import type { Referral, ReferralStats } from "../shared/types";

import { ReferralStats as StatsComponent } from "../shared/ReferralStats";
import { ReferralTable } from "../shared/ReferralTable";

interface ReferralDashboardProps {
  referrals: Referral[];
  stats: ReferralStats;
  isLoading?: boolean;
  onApproveReferral?: (referralId: string) => void;
  onRejectReferral?: (referralId: string) => void;
  onProcessPayout?: (referralId: string) => void;
  onRefresh?: () => void;
  className?: string;
}

export function ReferralDashboard({
  referrals,
  stats,
  isLoading = false,
  onApproveReferral,
  onRejectReferral,
  onProcessPayout,
  onRefresh,
  className,
}: ReferralDashboardProps) {
  const hasReferrals = referrals.length > 0;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Referral Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage referral activities
          </p>
        </div>
        <Button variant="outline" onClick={onRefresh} className="gap-2">
          <RefreshCw className="size-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Overview */}
      <StatsComponent stats={stats} />

      {/* Referrals Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Referrals</CardTitle>
        </CardHeader>
        <CardContent>
          {!hasReferrals && !isLoading ? (
            <div className="py-12 text-center">
              <Users className="mx-auto mb-4 size-12 text-muted-foreground" />
              <h3 className="mb-2 text-lg font-semibold">No referrals found</h3>
              <p className="text-muted-foreground">
                Referrals will appear here once providers start inviting users.
              </p>
            </div>
          ) : (
            <ReferralTable
              referrals={referrals}
              isLoading={isLoading}
              onApprove={onApproveReferral}
              onReject={onRejectReferral}
              onProcessPayout={onProcessPayout}
              showActions
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
