import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@axa/ui/primitives/card";
import { Button } from "@axa/ui/primitives/button";
import { Badge } from "@axa/ui/primitives/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@axa/ui/primitives/tabs";
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Download,
  Filter,
  Search,
  MoreHorizontal
} from "lucide-react";
import { cn } from "@axa/ui/lib";

import type { Referral, ReferralStats } from "../shared/types";
import { ReferralStats as StatsComponent } from "../shared/ReferralStats";
import { ReferralTable } from "./ReferralTable";

interface ReferralDashboardProps {
  referrals: Referral[];
  stats: ReferralStats;
  isLoading?: boolean;
  onApproveReferral?: (referralId: string) => void;
  onRejectReferral?: (referralId: string) => void;
  onProcessPayout?: (referralId: string) => void;
  onExportData?: () => void;
  className?: string;
}

interface QuickStatProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    isPositive: boolean;
  };
  icon: React.ComponentType<{ className?: string }>;
  className?: string;
}

function QuickStat({ title, value, change, icon: Icon, className }: QuickStatProps) {
  return (
    <Card className={cn("", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {change && (
              <div className={cn(
                "flex items-center text-xs mt-1",
                change.isPositive ? "text-green-600" : "text-red-600"
              )}>
                <TrendingUp className="size-3 mr-1" />
                {change.isPositive ? "+" : ""}{change.value}% from last month
              </div>
            )}
          </div>
          <div className="bg-muted p-3 rounded-full">
            <Icon className="size-6 text-muted-foreground" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ReferralDashboard({
  referrals,
  stats,
  isLoading = false,
  onApproveReferral,
  onRejectReferral,
  onProcessPayout,
  onExportData,
  className,
}: ReferralDashboardProps) {
  const pendingApprovals = referrals.filter(r => r.status === "signed_up").length;
  const pendingPayouts = referrals.filter(r => r.payoutStatus === "pending").length;
  const recentReferrals = referrals.slice(0, 5);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Referral Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage all referral activities across the platform
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onExportData} className="gap-2">
            <Download className="size-4" />
            Export Data
          </Button>
          <Button variant="outline" className="gap-2">
            <Filter className="size-4" />
            Filters
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <QuickStat
          title="Pending Approvals"
          value={pendingApprovals}
          icon={Clock}
          className="border-yellow-200 bg-yellow-50/50"
          change={{ value: 12, isPositive: true }}
        />
        <QuickStat
          title="Pending Payouts"
          value={pendingPayouts}
          icon={DollarSign}
          className="border-blue-200 bg-blue-50/50"
          change={{ value: 8, isPositive: true }}
        />
        <QuickStat
          title="Total Approved"
          value={stats.totalApproved}
          icon={CheckCircle}
          className="border-green-200 bg-green-50/50"
          change={{ value: 15, isPositive: true }}
        />
        <QuickStat
          title="Success Rate"
          value={`${stats.totalInvited > 0 ? Math.round((stats.totalApproved / stats.totalInvited) * 100) : 0}%`}
          icon={TrendingUp}
          className="border-purple-200 bg-purple-50/50"
          change={{ value: 3, isPositive: true }}
        />
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pending">
            Pending
            {pendingApprovals > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {pendingApprovals}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Overview */}
          <StatsComponent stats={stats} />

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Recent Referrals</span>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReferralTable
                referrals={recentReferrals}
                isLoading={isLoading}
                onApprove={onApproveReferral}
                onReject={onRejectReferral}
                onProcessPayout={onProcessPayout}
                variant="compact"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="size-5 text-yellow-600" />
                <span>Pending Approvals</span>
                <Badge variant="secondary">{pendingApprovals}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReferralTable
                referrals={referrals.filter(r => r.status === "signed_up")}
                isLoading={isLoading}
                onApprove={onApproveReferral}
                onReject={onRejectReferral}
                showActions
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payouts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="size-5 text-green-600" />
                <span>Payout Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReferralTable
                referrals={referrals.filter(r => r.payoutStatus)}
                isLoading={isLoading}
                onProcessPayout={onProcessPayout}
                showPayoutActions
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  <TrendingUp className="size-12 mx-auto mb-4" />
                  <p>Analytics dashboard coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
