import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@axa/ui/primitives/table";
import { But<PERSON> } from "@axa/ui/primitives/button";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { 
  CheckCircle, 
  XCircle, 
  DollarSign, 
  Eye,
  MoreHorizontal,
  Clock,
  Mail
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@axa/ui/primitives/alert-dialog";
import { cn } from "@axa/ui/lib";

import type { Referral } from "../shared/types";
import { ReferralStatusBadge, PayoutStatusBadge } from "../shared/ReferralStatusBadge";

interface ReferralTableProps {
  referrals: Referral[];
  isLoading?: boolean;
  onApprove?: (referralId: string) => void;
  onReject?: (referralId: string) => void;
  onProcessPayout?: (referralId: string) => void;
  onViewDetails?: (referral: Referral) => void;
  showActions?: boolean;
  showPayoutActions?: boolean;
  variant?: "default" | "compact";
  className?: string;
}

interface ActionDialogState {
  isOpen: boolean;
  type: "approve" | "reject" | "payout" | null;
  referral: Referral | null;
}

export function ReferralTable({
  referrals,
  isLoading = false,
  onApprove,
  onReject,
  onProcessPayout,
  onViewDetails,
  showActions = false,
  showPayoutActions = false,
  variant = "default",
  className,
}: ReferralTableProps) {
  const [actionDialog, setActionDialog] = useState<ActionDialogState>({
    isOpen: false,
    type: null,
    referral: null,
  });

  const isCompact = variant === "compact";

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: isCompact ? "2-digit" : "numeric",
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const handleAction = (type: "approve" | "reject" | "payout", referral: Referral) => {
    setActionDialog({
      isOpen: true,
      type,
      referral,
    });
  };

  const confirmAction = () => {
    if (!actionDialog.referral || !actionDialog.type) return;

    switch (actionDialog.type) {
      case "approve":
        onApprove?.(actionDialog.referral.id);
        break;
      case "reject":
        onReject?.(actionDialog.referral.id);
        break;
      case "payout":
        onProcessPayout?.(actionDialog.referral.id);
        break;
    }

    setActionDialog({ isOpen: false, type: null, referral: null });
  };

  const getActionDialogContent = () => {
    if (!actionDialog.referral || !actionDialog.type) return null;

    const actions = {
      approve: {
        title: "Approve Referral",
        description: `Are you sure you want to approve this referral? This will trigger a $${actionDialog.referral.payoutAmount} payout to the referring provider.`,
        actionText: "Approve",
        actionVariant: "default" as const,
      },
      reject: {
        title: "Reject Referral",
        description: "Are you sure you want to reject this referral? This action cannot be undone.",
        actionText: "Reject",
        actionVariant: "destructive" as const,
      },
      payout: {
        title: "Process Payout",
        description: `Process the $${actionDialog.referral.payoutAmount} payout for this approved referral?`,
        actionText: "Process Payout",
        actionVariant: "default" as const,
      },
    };

    return actions[actionDialog.type];
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse flex items-center space-x-4 p-4">
            <div className="size-10 bg-muted rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded w-1/4" />
              <div className="h-3 bg-muted rounded w-1/2" />
            </div>
            <div className="h-6 bg-muted rounded w-20" />
          </div>
        ))}
      </div>
    );
  }

  if (referrals.length === 0) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <Mail className="size-12 mx-auto mb-4" />
        <p>No referrals found</p>
      </div>
    );
  }

  const actionDialogContent = getActionDialogContent();

  return (
    <>
      <div className={cn("rounded-md border", className)}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Referred By</TableHead>
              {!isCompact && <TableHead>Invited User</TableHead>}
              <TableHead>Status</TableHead>
              {!isCompact && <TableHead>Payout</TableHead>}
              <TableHead>Date</TableHead>
              {(showActions || showPayoutActions) && <TableHead className="w-[100px]">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {referrals.map((referral) => (
              <TableRow key={referral.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className={cn("size-8", !isCompact && "size-10")}>
                      <AvatarImage 
                        src={referral.invitedBy.avatar || undefined} 
                        alt={`${referral.invitedBy.firstName} ${referral.invitedBy.lastName}`} 
                      />
                      <AvatarFallback className="text-xs">
                        {getInitials(referral.invitedBy.firstName, referral.invitedBy.lastName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className={cn("font-medium", isCompact ? "text-sm" : "text-base")}>
                        {referral.invitedBy.firstName} {referral.invitedBy.lastName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {referral.invitedBy.specialty}
                      </p>
                    </div>
                  </div>
                </TableCell>
                
                {!isCompact && (
                  <TableCell>
                    {referral.invitedUser ? (
                      <div className="flex items-center space-x-2">
                        <Avatar className="size-8">
                          <AvatarImage 
                            src={referral.invitedUser.avatar || undefined} 
                            alt={`${referral.invitedUser.firstName} ${referral.invitedUser.lastName}`} 
                          />
                          <AvatarFallback className="text-xs">
                            {getInitials(referral.invitedUser.firstName, referral.invitedUser.lastName)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">
                            {referral.invitedUser.firstName} {referral.invitedUser.lastName}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {referral.invitedUser.email}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">Pending signup</span>
                    )}
                  </TableCell>
                )}

                <TableCell>
                  <div className="space-y-1">
                    <ReferralStatusBadge status={referral.status} />
                    {referral.payoutStatus && !isCompact && (
                      <PayoutStatusBadge status={referral.payoutStatus} />
                    )}
                  </div>
                </TableCell>

                {!isCompact && (
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="size-4 text-green-600" />
                      <span className="font-medium">${referral.payoutAmount}</span>
                    </div>
                  </TableCell>
                )}

                <TableCell>
                  <span className="text-sm text-muted-foreground">
                    {formatDate(referral.createdAt)}
                  </span>
                </TableCell>

                {(showActions || showPayoutActions) && (
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      {showActions && referral.status === "signed_up" && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAction("approve", referral)}
                            className="h-8 px-2"
                          >
                            <CheckCircle className="size-3 text-green-600" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAction("reject", referral)}
                            className="h-8 px-2"
                          >
                            <XCircle className="size-3 text-red-600" />
                          </Button>
                        </>
                      )}
                      
                      {showPayoutActions && referral.payoutStatus === "pending" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAction("payout", referral)}
                          className="h-8 px-2"
                        >
                          <DollarSign className="size-3 text-blue-600" />
                        </Button>
                      )}

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onViewDetails?.(referral)}>
                            <Eye className="mr-2 size-4" />
                            View Details
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Action Confirmation Dialog */}
      <AlertDialog 
        open={actionDialog.isOpen} 
        onOpenChange={(open) => !open && setActionDialog({ isOpen: false, type: null, referral: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{actionDialogContent?.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {actionDialogContent?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmAction}
              className={actionDialogContent?.actionVariant === "destructive" ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
            >
              {actionDialogContent?.actionText}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
