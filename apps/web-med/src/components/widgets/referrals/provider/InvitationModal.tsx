import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON>Header, 
  <PERSON>alogTitle, 
  DialogDescription,
  DialogFooter 
} from "@axa/ui/primitives/dialog";
import { Button } from "@axa/ui/primitives/button";
import { Input } from "@axa/ui/primitives/input";
import { Textarea } from "@axa/ui/primitives/textarea";
import { Label } from "@axa/ui/primitives/label";
import { Badge } from "@axa/ui/primitives/badge";
import { 
  Mail, 
  Copy, 
  ExternalLink, 
  DollarSign,
  Users,
  CheckCircle,
  Loader2
} from "lucide-react";
import { cn } from "@axa/ui/lib";

import type { ReferralInviteFormData } from "../shared/types";

interface InvitationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSendInvite: (data: ReferralInviteFormData) => Promise<void>;
  isLoading?: boolean;
  generatedLink?: string;
  className?: string;
}

export function InvitationModal({
  open,
  onOpenChange,
  onSendInvite,
  isLoading = false,
  generatedLink,
  className,
}: InvitationModalProps) {
  const [formData, setFormData] = useState<ReferralInviteFormData>({
    email: "",
    firstName: "",
    lastName: "",
    personalMessage: "",
  });
  const [step, setStep] = useState<"form" | "success">("form");
  const [linkCopied, setLinkCopied] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onSendInvite(formData);
      setStep("success");
    } catch (error) {
      // Error handling would be done by parent component
      console.error("Failed to send invite:", error);
    }
  };

  const handleCopyLink = async () => {
    if (generatedLink) {
      await navigator.clipboard.writeText(generatedLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    }
  };

  const handleClose = () => {
    setStep("form");
    setFormData({
      email: "",
      firstName: "",
      lastName: "",
      personalMessage: "",
    });
    setLinkCopied(false);
    onOpenChange(false);
  };

  const isFormValid = formData.email.trim() !== "";

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn("sm:max-w-md", className)}>
        {step === "form" ? (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Mail className="size-5 text-blue-600" />
                <span>Invite a Professional</span>
              </DialogTitle>
              <DialogDescription>
                Refer a healthcare professional and earn $5 when they're approved.
              </DialogDescription>
            </DialogHeader>

            {/* Incentive Banner */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-2 rounded-full">
                    <DollarSign className="size-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Earn $5 per referral</p>
                    <p className="text-xs text-muted-foreground">
                      Paid automatically when approved
                    </p>
                  </div>
                </div>
                <Badge className="bg-green-100 text-green-700 hover:bg-green-200">
                  $5.00
                </Badge>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    value={formData.firstName}
                    onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    value={formData.lastName}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Personal Message (Optional)</Label>
                <Textarea
                  id="message"
                  placeholder="Hi! I wanted to share this great opportunity with you..."
                  value={formData.personalMessage}
                  onChange={(e) => setFormData({ ...formData, personalMessage: e.target.value })}
                  rows={3}
                />
              </div>

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!isFormValid || isLoading}
                  className="min-w-[100px]"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 size-4" />
                      Send Invite
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <CheckCircle className="size-5 text-green-600" />
                <span>Invitation Sent!</span>
              </DialogTitle>
              <DialogDescription>
                Your referral invitation has been sent successfully.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Success Message */}
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="size-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-sm text-green-800">
                      Invitation sent to {formData.email}
                    </p>
                    <p className="text-xs text-green-700 mt-1">
                      They'll receive an email with your referral link and personal message.
                    </p>
                  </div>
                </div>
              </div>

              {/* Generated Link */}
              {generatedLink && (
                <div className="space-y-2">
                  <Label>Your Referral Link</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={generatedLink}
                      readOnly
                      className="font-mono text-xs"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      className="shrink-0"
                    >
                      {linkCopied ? (
                        <CheckCircle className="size-4 text-green-600" />
                      ) : (
                        <Copy className="size-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    You can also share this link directly with other professionals.
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button onClick={handleClose} className="w-full">
                Done
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
