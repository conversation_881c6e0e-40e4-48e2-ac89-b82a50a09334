import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@axa/ui/primitives/card";
import { Button } from "@axa/ui/primitives/button";
import { Badge } from "@axa/ui/primitives/badge";
import { 
  DollarSign, 
  Users, 
  CheckCircle, 
  Clock,
  Gift,
  TrendingUp,
  Star,
  ArrowRight,
  Info
} from "lucide-react";
import { cn } from "@axa/ui/lib";

interface ReferralIncentiveExplanationProps {
  onGetStarted?: () => void;
  className?: string;
  variant?: "full" | "compact";
}

interface IncentiveStepProps {
  step: number;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  isActive?: boolean;
}

function IncentiveStep({ 
  step, 
  title, 
  description, 
  icon: Icon, 
  badge,
  isActive = false 
}: IncentiveStepProps) {
  return (
    <div className={cn(
      "flex items-start space-x-4 p-4 rounded-lg transition-colors",
      isActive ? "bg-blue-50 border border-blue-200" : "bg-muted/30"
    )}>
      <div className={cn(
        "flex items-center justify-center size-10 rounded-full font-semibold text-sm",
        isActive 
          ? "bg-blue-600 text-white" 
          : "bg-muted text-muted-foreground"
      )}>
        {step}
      </div>
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">{title}</h3>
          {badge && (
            <Badge variant="secondary" className="text-xs">
              {badge}
            </Badge>
          )}
        </div>
        <p className="text-sm text-muted-foreground">{description}</p>
        <div className="flex items-center space-x-2 text-blue-600">
          <Icon className="size-4" />
        </div>
      </div>
    </div>
  );
}

export function ReferralIncentiveExplanation({
  onGetStarted,
  className,
  variant = "full",
}: ReferralIncentiveExplanationProps) {
  const isCompact = variant === "compact";

  return (
    <div className={cn("space-y-6", className)}>
      {/* Hero Section */}
      <Card className="bg-gradient-to-r from-green-50 via-blue-50 to-purple-50 border-green-200">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <Gift className="size-8 text-green-600" />
              <h1 className={cn(
                "font-bold text-green-800",
                isCompact ? "text-xl" : "text-3xl"
              )}>
                Earn $5 Per Referral
              </h1>
            </div>
            <p className={cn(
              "text-muted-foreground max-w-2xl mx-auto",
              isCompact ? "text-sm" : "text-lg"
            )}>
              Help grow our healthcare community and get rewarded for every professional you refer who joins our platform.
            </p>
            <div className="flex items-center justify-center space-x-6 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">$5</div>
                <div className="text-xs text-muted-foreground">Per Approval</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">100%</div>
                <div className="text-xs text-muted-foreground">Automated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">24h</div>
                <div className="text-xs text-muted-foreground">Payout Time</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* How It Works */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="size-5 text-blue-600" />
            <span>How It Works</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <IncentiveStep
            step={1}
            title="Send Invitation"
            description="Invite healthcare professionals using their email address or share your unique referral link."
            icon={Users}
            badge="Easy"
            isActive
          />
          
          <div className="flex justify-center">
            <ArrowRight className="size-5 text-muted-foreground" />
          </div>
          
          <IncentiveStep
            step={2}
            title="They Sign Up"
            description="Your referral creates an account and completes their professional profile on our platform."
            icon={CheckCircle}
            badge="Automatic"
          />
          
          <div className="flex justify-center">
            <ArrowRight className="size-5 text-muted-foreground" />
          </div>
          
          <IncentiveStep
            step={3}
            title="Get Approved"
            description="Once their credentials are verified and they're approved, you earn your $5 referral bonus."
            icon={DollarSign}
            badge="$5.00"
          />
          
          <div className="flex justify-center">
            <ArrowRight className="size-5 text-muted-foreground" />
          </div>
          
          <IncentiveStep
            step={4}
            title="Automatic Payout"
            description="Your earnings are automatically sent to your account within 24 hours via Stripe."
            icon={Clock}
            badge="24h"
          />
        </CardContent>
      </Card>

      {/* Benefits */}
      {!isCompact && (
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Star className="size-5 text-yellow-600" />
                <span>Why Refer?</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="size-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-sm">Help Your Network</p>
                  <p className="text-xs text-muted-foreground">
                    Connect colleagues with great opportunities
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="size-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-sm">Earn Extra Income</p>
                  <p className="text-xs text-muted-foreground">
                    Get paid for successful referrals
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="size-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-sm">Build Community</p>
                  <p className="text-xs text-muted-foreground">
                    Grow the healthcare professional network
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Info className="size-5 text-blue-600" />
                <span>Important Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="space-y-1">
                <p className="font-medium">Eligibility Requirements:</p>
                <ul className="text-xs text-muted-foreground space-y-1 ml-4">
                  <li>• Referred professional must be new to the platform</li>
                  <li>• Must complete profile verification</li>
                  <li>• Must be approved by our team</li>
                </ul>
              </div>
              <div className="space-y-1">
                <p className="font-medium">Payment Terms:</p>
                <ul className="text-xs text-muted-foreground space-y-1 ml-4">
                  <li>• $5 USD per approved referral</li>
                  <li>• Paid via Stripe within 24 hours</li>
                  <li>• No limit on number of referrals</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* CTA */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6 text-center">
          <h3 className="font-semibold text-lg mb-2">Ready to Start Referring?</h3>
          <p className="text-muted-foreground mb-4">
            Send your first invitation and start earning referral bonuses today.
          </p>
          <Button onClick={onGetStarted} size="lg" className="gap-2">
            <Users className="size-4" />
            Send First Invitation
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
