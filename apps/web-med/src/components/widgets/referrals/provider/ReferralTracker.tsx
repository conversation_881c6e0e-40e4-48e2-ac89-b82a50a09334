import { Plus, Users } from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";

import type { Referral, ReferralStats } from "../shared/types";

import { ReferralStats as StatsComponent } from "../shared/ReferralStats";
import { ReferralTable } from "../shared/ReferralTable";

interface ReferralTrackerProps {
  referrals: Referral[];
  stats: ReferralStats;
  onInviteNew?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function ReferralTracker({
  referrals,
  stats,
  onInviteNew,
  isLoading = false,
  className,
}: ReferralTrackerProps) {
  const hasReferrals = referrals.length > 0;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">My Referrals</h1>
          <p className="text-muted-foreground">
            Track your referrals and earnings
          </p>
        </div>
        <Button onClick={onInviteNew} className="gap-2">
          <Plus className="size-4" />
          Invite Someone
        </Button>
      </div>

      {/* Stats Overview */}
      <StatsComponent stats={stats} variant="compact" />

      {/* Referrals Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Referrals</CardTitle>
        </CardHeader>
        <CardContent>
          {!hasReferrals && !isLoading ? (
            <div className="py-12 text-center">
              <Users className="mx-auto mb-4 size-12 text-muted-foreground" />
              <h3 className="mb-2 text-lg font-semibold">No referrals yet</h3>
              <p className="mb-4 text-muted-foreground">
                Start inviting healthcare professionals to earn $5 per approved
                referral.
              </p>
              <Button onClick={onInviteNew} className="gap-2">
                <Plus className="size-4" />
                Send Your First Invitation
              </Button>
            </div>
          ) : (
            <ReferralTable referrals={referrals} isLoading={isLoading} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
