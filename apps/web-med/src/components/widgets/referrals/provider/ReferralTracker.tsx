import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@axa/ui/primitives/card";
import { But<PERSON> } from "@axa/ui/primitives/button";
import { Badge } from "@axa/ui/primitives/badge";
import { Input } from "@axa/ui/primitives/input";
import { 
  Search, 
  Filter, 
  Plus, 
  Users,
  TrendingUp,
  Eye,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { cn } from "@axa/ui/lib";

import type { Referral, ReferralStats } from "../shared/types";
import { ReferralCard } from "../shared/ReferralCard";
import { ReferralStats as StatsComponent } from "../shared/ReferralStats";

interface ReferralTrackerProps {
  referrals: Referral[];
  stats: ReferralStats;
  isLoading?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  onInviteNew?: () => void;
  onViewDetails?: (referral: Referral) => void;
  onCopyLink?: (link: string) => void;
  onResendInvite?: (referral: Referral) => void;
  className?: string;
}

export function ReferralTracker({
  referrals,
  stats,
  isLoading = false,
  searchQuery = "",
  onSearchChange,
  onInviteNew,
  onViewDetails,
  onCopyLink,
  onResendInvite,
  className,
}: ReferralTrackerProps) {
  const handleCopyLink = (link: string) => {
    if (onCopyLink) {
      onCopyLink(link);
    } else {
      navigator.clipboard.writeText(link);
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">My Referrals</h2>
          <p className="text-muted-foreground">
            Track your referral invitations and earnings
          </p>
        </div>
        <Button onClick={onInviteNew} className="gap-2">
          <Plus className="size-4" />
          Invite Professional
        </Button>
      </div>

      {/* Stats Overview */}
      <StatsComponent stats={stats} variant="compact" />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Users className="size-5" />
              Recent Activity
            </span>
            <Badge variant="outline" className="gap-1">
              <TrendingUp className="size-3" />
              {referrals.length} Total
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
              <Input
                placeholder="Search referrals..."
                value={searchQuery}
                onChange={(e) => onSearchChange?.(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm" className="gap-2">
              <Filter className="size-4" />
              Filter
            </Button>
          </div>

          {/* Referrals List */}
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-32 bg-muted rounded-lg" />
                </div>
              ))}
            </div>
          ) : referrals.length > 0 ? (
            <div className="space-y-4">
              {referrals.map((referral) => (
                <ReferralCard
                  key={referral.id}
                  referral={referral}
                  onCopyLink={handleCopyLink}
                  onViewDetails={onViewDetails}
                  onResendInvite={onResendInvite}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Users className="size-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">No referrals yet</h3>
              <p className="text-muted-foreground mb-4">
                Start inviting healthcare professionals to earn referral bonuses.
              </p>
              <Button onClick={onInviteNew} className="gap-2">
                <Plus className="size-4" />
                Send Your First Invite
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Insights */}
      {referrals.length > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="font-semibold text-lg">Performance Insights</h3>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <p>• Your referral success rate is above average</p>
                  <p>• Most successful invites are sent on weekdays</p>
                  <p>• Personal messages increase signup rates by 40%</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="gap-2">
                <Eye className="size-4" />
                View Tips
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
