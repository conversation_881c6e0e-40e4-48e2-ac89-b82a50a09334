"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, PlusCircleIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { SelectDocumentProps } from "@axa/ui/selectors/SelectDocument";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import { SelectDocument } from "@axa/ui/selectors/SelectDocument";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  DocumentFormValues,
  OrganizationDocumentFormProps,
} from "@/components/forms/OrgDocument";

import { api } from "@/api/client";
import { OrganizationDocumentForm } from "@/components/forms/OrgDocument";

const i18n = {
  en: {
    titles: {
      add: "Add Document",
      update: "Update Document",
      delete: "Delete Document",
    },
    descriptions: {
      delete: "Are you sure you want to delete this document?",
    },
    actions: {
      label: "Actions",
      add: "Add",

      update: "Update",
      delete: "Delete",
      selectDocument: "Select Document",
    },
    messages: {
      created: "Document created successfully.",
      updated: "Document updated successfully.",
      deleted: "Document deleted successfully.",
      failedCreate: "Failed to create document: ",
      failedUpdate: "Failed to update document: ",
      failedDelete: "Failed to delete document: ",
      failedUpload: "Failed to upload document: ",
    },
  },
  links: {
    documents: "/app/documents",
  },
};

export function AddDocument(
  props: Omit<OrganizationDocumentFormProps, "omSubmit">,
) {
  return (
    <DialogForm<OrganizationDocumentFormProps, DocumentFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      Component={OrganizationDocumentForm}
      onSubmit={() => void 0}
    />
  );
}

export function UpdateDocument(
  props: PropsWithChildren<
    OrganizationDocumentFormProps & {
      document?:
        | RouterOutputs["documents"]["get"]
        | RouterOutputs["documents"]["getMany"]["items"][number];
    }
  >,
) {
  const utils = api.useUtils();
  const updateDocumentMutation = api.documents.update.useMutation({
    onSuccess: async () => {
      await utils.documents.get.invalidate({
        id: props.document?.id ?? "",
      });
      // TODO: granular invalidation
      await utils.documents.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<OrganizationDocumentFormProps, DocumentFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationDocumentForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<
        NonNullable<OrganizationDocumentFormProps["onSubmit"]>
      >(
        async (values) => {
          await updateDocumentMutation.mutateAsync({
            id: props.document?.id ?? "",
            data: {
              organizationId: values.organizationId,
              name: values.name,
              size: values.size,
              type: values.type,
              url: values.url,
            },
          });
        },
        [updateDocumentMutation, props.document?.id],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteDocument({
  documentId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ documentId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteDocumentMutation = api.documents.delete.useMutation({
    onSuccess: async () => {
      await utils.documents.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.documents);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      {...props}
      onOpenChange={onOpenChange}
      open={open}
      disabled={deleteDocumentMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteDocumentMutation.mutateAsync({ id: documentId });
        onOpenChange?.(false);
      }, [deleteDocumentMutation, documentId, onOpenChange])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function DocumentMenu({
  loading = false,
  rerouteOnDelete,
  document,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    rerouteOnDelete?: boolean;
    document?:
      | RouterOutputs["documents"]["get"]
      | RouterOutputs["documents"]["getMany"]["items"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading || !document}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateDocument
            onOpenChange={handleDialogOpenChange}
            document={document}
            defaultValues={
              loading
                ? undefined
                : {
                    organizationId: document?.organization?.id ?? undefined,
                    name: document?.name,
                    size: document?.size,
                    type: document?.type,
                    url: document?.url,
                  }
            }
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateDocument>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteDocument
            onOpenChange={handleDialogOpenChange}
            documentId={document?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteDocument>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface SearchDocumentsProps
  extends Pick<
    SelectDocumentProps<RouterOutputs["documents"]["getMany"]["items"][number]>,
    "onSelect" | "defaultValue" | "children" | "variant" | "className"
  > {
  useDialog?: boolean;
}

export function SearchDocuments({
  useDialog = true,
  defaultValue,
  onSelect,
  variant = "ghost",
  className = "size-full",
  children,
}: SearchDocumentsProps) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(defaultValue ?? "");
  const documents = api.documents.getMany.useQuery(
    {
      query: value,
      pageNumber: 0,
      pageSize: 50,
    },
    {
      enabled: open,
    },
  );

  return (
    <SelectDocument
      useDialog={useDialog}
      data={documents.data?.items ?? []}
      loading={documents.isLoading}
      defaultValue={defaultValue}
      open={open}
      onOpenChange={setOpen}
      onSelect={onSelect}
      onValueChange={setValue}
      variant={variant}
      className={className}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectDocument}</span>
        </>
      )}
    </SelectDocument>
  );
}
