"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useState } from "react";
import { InfoIcon } from "lucide-react"; // Import an icon

import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import { Alert, AlertDescription, AlertTitle } from "@axa/ui/primitives/alert"; // Import Alert components
import { Button } from "@axa/ui/primitives/button";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";

import type { ShiftStruct } from "./general"; // Assuming ShiftStruct remains in index.tsx or a shared types file

import { api } from "@/api/client";
import { useGeolocation } from "@/hooks/use-geolocation"; // Import the hook

const i18n = {
  en: {
    titles: {
      cancel: "Cancel Shift",
      confirm: "Confirm Shift",
      checkIn: "Check In to Shift",
      checkOut: "Check Out of Shift",
    },
    descriptions: {
      cancel: "Are you sure you want to cancel this shift?",
      confirm: "Are you ready to confirm this shift?",
      checkIn: "Are you ready to check in to this shift?",
      checkOut: "Are you ready to check out of this shift?",
    },
    actions: {
      cancel: "Cancel",
      confirm: "Confirm",
      checkIn: "Check In",
      checkOut: "Check Out",
    },
    messages: {
      cancelled: "Shift cancelled successfully.",
      confirmed: "Shift confirmed successfully.",
      checkedIn: "Checked in to shift successfully.",
      checkedOut: "Checked out of shift successfully.",
      failedCancel: "Failed to cancel shift: ",
      failedConfirm: "Failed to confirm shift: ",
      failedCheckIn: "Failed to check in to shift: ",
      failedCheckOut: "Failed to check out of shift: ",
    },
  },
};

// Banner component for geolocation permission notice
function GeolocationPermissionBanner() {
  return (
    <Alert className="mb-4 border-blue-500 bg-blue-50 text-blue-800 dark:border-blue-700 dark:bg-blue-950 dark:text-blue-300">
      <InfoIcon className="size-4 !text-blue-700 dark:!text-blue-400" />
      <AlertTitle className="font-semibold">
        Location Permission Required
      </AlertTitle>
      <AlertDescription>
        Checking in or out requires access to your current location. You will be
        prompted by your browser to grant permission if you haven't already.
      </AlertDescription>
    </Alert>
  );
}

// Provider-specific shift actions

export interface ProviderCancelShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onCancel?: (
    shift: RouterInputs["shifts"]["provider"]["cancel"],
  ) => void | Promise<RouterOutputs["shifts"]["provider"]["cancel"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["provider"]["cancel"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ProviderCancelShift({
  shift,
  onCancel,
  onSuccess,
  onError,
  variant = "destructive",
  ...props
}: PropsWithChildren<ProviderCancelShiftProps>) {
  const utils = api.useUtils();
  const cancelShiftMutation = api.shifts.provider.cancel.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.cancelled);
      }
      props.onOpenChange?.(false); // Close dialog on success
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCancel + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.cancel}
      description={i18n.en.descriptions.cancel}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={cancelShiftMutation.isPending}
      action={i18n.en.actions.cancel}
      variant={variant}
      onClick={useCallback(async () => {
        if (onCancel) {
          await onCancel({ id: shift?.id ?? "" });
        } else {
          await cancelShiftMutation.mutateAsync({ id: shift?.id ?? "" });
        }
      }, [cancelShiftMutation, shift, onCancel])}
    >
      {props.children ?? (
        <Button variant="destructive">{i18n.en.actions.cancel}</Button>
      )}
    </DialogConfirmation>
  );
}

export interface ProviderConfirmShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onConfirm?: (
    shift: RouterInputs["shifts"]["provider"]["confirm"],
  ) => void | Promise<RouterOutputs["shifts"]["provider"]["confirm"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["provider"]["confirm"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ProviderConfirmShift({
  shift,
  onConfirm,
  onSuccess,
  onError,
  variant = "primary",
  ...props
}: PropsWithChildren<ProviderConfirmShiftProps>) {
  const utils = api.useUtils();
  const confirmShiftMutation = api.shifts.provider.confirm.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.confirmed);
      }
      props.onOpenChange?.(false); // Close dialog on success
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedConfirm + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.confirm}
      description={i18n.en.descriptions.confirm}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      action={i18n.en.actions.confirm}
      disabled={confirmShiftMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onConfirm) {
          await onConfirm({ id: shift?.id ?? "" });
        } else {
          await confirmShiftMutation.mutateAsync({ id: shift?.id ?? "" });
        }
      }, [confirmShiftMutation, shift, onConfirm])}
    >
      {props.children ?? (
        <Button variant={variant}>{i18n.en.actions.confirm}</Button>
      )}
    </DialogConfirmation>
  );
}

export interface ProviderCheckInShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onCheckIn?: (
    shift: RouterInputs["shifts"]["provider"]["checkIn"],
  ) => void | Promise<RouterOutputs["shifts"]["provider"]["checkIn"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["provider"]["checkIn"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ProviderCheckInShift({
  shift,
  onCheckIn,
  onSuccess,
  onError,
  variant = "primary",
  ...props
}: PropsWithChildren<ProviderCheckInShiftProps>) {
  const utils = api.useUtils();
  const {
    getCurrentPosition,
    loading: isFetchingLocation, // Rename loading from hook
    error: geolocationError, // Get error state from hook
  } = useGeolocation();

  const checkInShiftMutation = api.shifts.provider.checkIn.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.checkedIn);
      }
      props.onOpenChange?.(false);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCheckIn + error.message);
      }
    },
  });

  const handleCheckIn = useCallback(async () => {
    if (!shift?.id) return;

    try {
      // Call the hook's function to get current position
      const locationPayload = await getCurrentPosition();

      // Proceed with mutation only if geolocation was successful
      if (locationPayload.latitude && locationPayload.longitude) {
        const coords = {
          latitude: locationPayload.latitude,
          longitude: locationPayload.longitude,
        };

        // Call original handlers or the mutation
        if (onCheckIn) {
          await onCheckIn({ id: shift.id, location: coords });
        } else {
          await checkInShiftMutation.mutateAsync({
            id: shift.id,
            location: coords,
          });
        }
      }
    } catch (error) {
      // Handle errors from getCurrentPosition (e.g., permissions)
      console.error("Geolocation hook error:", error);
      const message =
        error instanceof Error ? error.message : "Could not get location.";
      toast.error(
        `Geolocation failed: ${message}. Please ensure location services are enabled and permission is granted.`,
      );
      // Loading state is handled by the hook automatically
    }
  }, [shift, onCheckIn, checkInShiftMutation, getCurrentPosition]);

  return (
    <DialogConfirmation
      title={i18n.en.titles.checkIn}
      description={i18n.en.descriptions.checkIn}
      body={<GeolocationPermissionBanner />}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.checkIn}
      {...props}
      disabled={checkInShiftMutation.isPending || isFetchingLocation}
      variant={variant}
      onClick={handleCheckIn}
    >
      {props.children ?? (
        <Button variant={variant}>{i18n.en.actions.checkIn}</Button>
      )}
    </DialogConfirmation>
  );
}

export interface ProviderCheckOutShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onCheckOut?: (
    shift: RouterInputs["shifts"]["provider"]["checkOut"],
  ) => void | Promise<RouterOutputs["shifts"]["provider"]["checkOut"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["provider"]["checkOut"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ProviderCheckOutShift({
  shift,
  onCheckOut,
  onSuccess,
  onError,
  variant = "primary",
  ...props
}: PropsWithChildren<ProviderCheckOutShiftProps>) {
  const utils = api.useUtils();
  const {
    getCurrentPosition,
    loading: isFetchingLocation, // Rename loading from hook
    error: geolocationError, // Get error state from hook
  } = useGeolocation();

  const checkOutShiftMutation = api.shifts.provider.checkOut.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.checkedOut);
      }
      props.onOpenChange?.(false); // Close dialog on success
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCheckOut + error.message);
      }
    },
  });

  const handleCheckOut = useCallback(async () => {
    if (!shift?.id) return;

    try {
      // Call the hook's function to get current position
      const locationPayload = await getCurrentPosition();

      // Proceed with mutation only if geolocation was successful
      if (locationPayload.latitude && locationPayload.longitude) {
        const coords = {
          latitude: locationPayload.latitude,
          longitude: locationPayload.longitude,
        };

        // Call original handlers or the mutation
        if (onCheckOut) {
          await onCheckOut({ id: shift.id, location: coords });
        } else {
          await checkOutShiftMutation.mutateAsync({
            id: shift.id,
            location: coords,
          });
        }
      }
    } catch (error) {
      // Handle errors from getCurrentPosition (e.g., permissions)
      console.error("Geolocation hook error:", error);
      const message =
        error instanceof Error ? error.message : "Could not get location.";
      toast.error(
        `Geolocation failed: ${message}. Please ensure location services are enabled and permission is granted.`,
      );
      // Loading state is handled by the hook automatically
    }
  }, [shift, onCheckOut, checkOutShiftMutation, getCurrentPosition]);

  return (
    <DialogConfirmation
      title={i18n.en.titles.checkOut}
      description={i18n.en.descriptions.checkOut}
      body={<GeolocationPermissionBanner />}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.checkOut}
      {...props}
      disabled={checkOutShiftMutation.isPending || isFetchingLocation}
      variant={variant}
      onClick={handleCheckOut}
    >
      {props.children ?? (
        <Button variant={variant}>{i18n.en.actions.checkOut}</Button>
      )}
    </DialogConfirmation>
  );
}
