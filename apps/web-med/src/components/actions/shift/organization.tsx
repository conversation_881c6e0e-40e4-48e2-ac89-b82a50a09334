"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useState } from "react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import { Button } from "@axa/ui/primitives/button";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type { ShiftDateFormValues } from "@/components/forms/ShiftDateForm";

import type { ShiftStruct } from "./general"; // Assuming ShiftStruct remains in index.tsx or a shared types file

import { api } from "@/api/client";
import ShiftDateForm from "@/components/forms/ShiftDateForm";

const i18n = {
  en: {
    titles: {
      organizationCancel: "Cancel Shift",
      organizationReschedule: "Reschedule Shift",
      organizationApprove: "Approve Shift",
    },
    descriptions: {
      organizationCancel: "Are you sure you want to cancel this shift?",
      organizationReschedule: "Select a new date for this shift.",
      organizationApprove: "Are you sure you want to approve this shift?",
    },
    actions: {
      organizationCancel: "Cancel",
      organizationReschedule: "Reschedule",
      organizationApprove: "Approve",
    },
    messages: {
      organizationCancelled: "Shift cancelled successfully.",
      organizationRescheduled: "Shift rescheduled successfully.",
      organizationApproved: "Shift approved successfully.",
      failedOrganizationCancel: "Failed to cancel shift: ",
      failedOrganizationReschedule: "Failed to reschedule shift: ",
      failedOrganizationApprove: "Failed to approve shift: ",
    },
  },
};

// Organization-specific shift actions

export interface OrganizationCancelShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onCancel?: (
    shift: RouterInputs["shifts"]["organization"]["cancel"],
  ) => void | Promise<RouterOutputs["shifts"]["organization"]["cancel"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["organization"]["cancel"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function OrganizationCancelShift({
  shift,
  onCancel,
  onSuccess,
  onError,
  variant = "destructive",
  ...props
}: PropsWithChildren<OrganizationCancelShiftProps>) {
  const utils = api.useUtils();
  const cancelShiftMutation = api.shifts.organization.cancel.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.organizationCancelled);
      }
      props.onOpenChange?.(false); // Close dialog on success
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedOrganizationCancel + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.organizationCancel}
      description={i18n.en.descriptions.organizationCancel}
      action={i18n.en.actions.organizationCancel}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={cancelShiftMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onCancel) {
          await onCancel({ id: shift?.id ?? "" });
        } else {
          await cancelShiftMutation.mutateAsync({ id: shift?.id ?? "" });
        }
      }, [cancelShiftMutation, shift, onCancel])}
    >
      {props.children ?? (
        <Button variant="destructive">
          {i18n.en.actions.organizationCancel}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export interface OrganizationRescheduleShiftProps {
  shift?: ShiftStruct;
  onReschedule?: (shift: { id: string; date: Date }) => void | Promise<unknown>;
  onSuccess?: (result: unknown) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function OrganizationRescheduleShift({
  shift,
  onReschedule,
  onSuccess,
  onError,
  variant = "outline",
  children, // Explicitly handle children
  ...props
}: PropsWithChildren<OrganizationRescheduleShiftProps & ButtonProps>) {
  const [open, setOpen] = useState(false);
  const utils = api.useUtils();

  const rescheduleShiftMutation =
    api.shifts.organization.reschedule.useMutation({
      onSuccess: async (result) => {
        if (onSuccess) {
          await onSuccess(result);
        } else {
          await utils.shifts.getMany.invalidate();
          await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
          toast.success(i18n.en.messages.organizationRescheduled);
        }
        setOpen(false); // Close dialog on success
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else if (error instanceof Error) {
          toast.error(
            i18n.en.messages.failedOrganizationReschedule + error.message,
          );
        }
      },
    });

  const handleReschedule = useCallback(
    async (values: ShiftDateFormValues) => {
      if (!shift?.id) return;

      if (onReschedule) {
        await onReschedule({
          id: shift.id,
          date: values.date,
        });
        // Assume onReschedule handles closing if needed
      } else {
        await rescheduleShiftMutation.mutateAsync({
          id: shift.id,
          date: values.date,
        });
      }
    },
    [shift, onReschedule, rescheduleShiftMutation],
  );

  return (
    <DialogForm
      title={i18n.en.titles.organizationReschedule}
      description={i18n.en.descriptions.organizationReschedule}
      open={open}
      onOpenChange={setOpen}
      Component={({
        onSubmit,
        ...formProps
      }: {
        onSubmit: (values: ShiftDateFormValues) => Promise<void>;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [key: string]: any; // Loosen type for passthrough props
      }) => (
        <ShiftDateForm
          onSubmit={onSubmit}
          defaultValues={{ date: shift?.startDate ?? new Date() }} // Add default date
          {...formProps}
        />
      )}
      onSubmit={handleReschedule}
    >
      {children ?? (
        <Button
          variant={variant}
          disabled={!shift?.id || rescheduleShiftMutation.isPending}
          {...props}
        >
          {i18n.en.actions.organizationReschedule}
        </Button>
      )}
    </DialogForm>
  );
}

export interface OrganizationApproveShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  onApprove?: (
    shift: RouterInputs["shifts"]["organization"]["approve"],
  ) => void | Promise<RouterOutputs["shifts"]["organization"]["approve"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["organization"]["approve"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function OrganizationApproveShift({
  shift,
  onApprove,
  onSuccess,
  onError,
  variant = "primary",
  ...props
}: PropsWithChildren<OrganizationApproveShiftProps>) {
  const utils = api.useUtils();
  const approveShiftMutation = api.shifts.organization.approve.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        toast.success(i18n.en.messages.organizationApproved);
      }
      props.onOpenChange?.(false); // Close dialog on success
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedOrganizationApprove + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.organizationApprove}
      description={i18n.en.descriptions.organizationApprove}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.organizationApprove}
      {...props}
      disabled={approveShiftMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onApprove) {
          await onApprove({ id: shift?.id ?? "" });
        } else {
          await approveShiftMutation.mutateAsync({ id: shift?.id ?? "" });
        }
      }, [approveShiftMutation, shift, onApprove])}
    >
      {props.children ?? (
        <Button variant="primary">{i18n.en.actions.organizationApprove}</Button>
      )}
    </DialogConfirmation>
  );
}
