"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { SelectOrganizationField } from "@axa/ui/selectors/SelectOrganization";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  ShiftFormProps,
  ShiftFormValues,
} from "@/components/forms/ShiftForm";

import type { AddReviewProps, UpdateReviewProps } from "../review";
import type {
  OrganizationApproveShiftProps,
  OrganizationCancelShiftProps,
  OrganizationRescheduleShiftProps,
} from "./organization";
import type {
  ProviderCancelShiftProps,
  ProviderCheckInShiftProps,
  ProviderCheckOutShiftProps,
  ProviderConfirmShiftProps,
} from "./provider";

import { ShiftStatus } from "@/api";
import { api } from "@/api/client";
import { useUser } from "@/components/contexts/User";
import ShiftForm, { ShiftFormSubmitButton } from "@/components/forms/ShiftForm";

import { AddIncident } from "../incident";
import { AddReview, UpdateReview } from "../review";
import {
  OrganizationApproveShift,
  OrganizationCancelShift,
  OrganizationRescheduleShift,
} from "./organization";
import {
  ProviderCancelShift,
  ProviderCheckInShift,
  ProviderCheckOutShift,
  ProviderConfirmShift,
} from "./provider";

const i18n = {
  en: {
    titles: {
      add: "Add Shift",
      update: "Update Shift",
      delete: "Delete Shift",
    },
    descriptions: {
      add: "Add a new shift to your schedule.",
      update: "Update an existing shift.",
      delete: "Are you sure you want to delete this shift?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      updateReview: "Update Review",
      addReview: "Add Review",
      delete: "Delete",
    },
    messages: {
      created: "Shift created successfully.",
      updated: "Shift updated successfully.",
      deleted: "Shift deleted successfully.",
      failedCreate: "Failed to create shift: ",
      failedUpdate: "Failed to update shift: ",
      failedDelete: "Failed to delete shift: ",
    },
    common: {
      report_incident: "Report Incident",
    },
  },
  links: {
    shifts: "/app/shifts",
  },
};

export type CoreShiftStruct = RouterOutputs["shifts"]["get"];
export type ShiftStruct = Pick<
  CoreShiftStruct,
  | "id"
  | "summary"
  | "scope"
  | "startDate"
  | "endDate"
  | "status"
  | "review"
  | "organization"
  | "provider"
  | "location"
> & {
  location?: Pick<NonNullable<CoreShiftStruct["location"]>, "address">;
};

export interface BaseShiftFormProps
  extends Omit<
    DialogFormProps<ShiftFormProps, ShiftFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  showOrganization?: boolean;
  defaultValues?: Partial<ShiftFormValues>;
}

export function OrganizationShiftForm(props: BaseShiftFormProps) {
  const organizations = api.organizations.getMany.useQuery(undefined, {
    enabled: props.open,
  });

  return (
    <ShiftForm {...props}>
      {props.showOrganization && (
        <SelectOrganizationField
          data={organizations.data?.items ?? []}
          loading={organizations.isLoading}
        />
      )}
      <div className="flex w-full justify-center">
        <ShiftFormSubmitButton />
      </div>
    </ShiftForm>
  );
}

export interface AddShiftProps extends BaseShiftFormProps {
  onAdd?: (
    shift: ShiftFormValues,
  ) => void | Promise<RouterOutputs["shifts"]["create"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddShift({
  onAdd,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<AddShiftProps>) {
  const utils = api.useUtils();
  const createShiftMutation = api.shifts.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
      }
      props.onOpenChange?.(false);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        // No specific UI action needed on create error
      }
    },
  });

  return (
    <DialogForm<BaseShiftFormProps, ShiftFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.titles.add}
      type="add"
      {...props}
      showOrganization
      Component={OrganizationShiftForm}
      onSubmit={useCallback<NonNullable<ShiftFormProps["onSubmit"]>>(
        async (values: ShiftFormValues) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            await createShiftMutation.mutateAsync({
              summary: values.summary,
              scope: values.scope,
              role: values.role ?? "",
              timeZone: values.timeZone ?? "",
              startDate: values.startDate,
              endDate: values.endDate,
              paymentType: values.paymentType,
              paymentRate: values.paymentRate,
              location: values.location ?? "",
              job: values.job ?? "",
              position: values.position ?? "",
              paymentTotal: values.paymentTotal ?? 0,
            });
          }
        },
        [createShiftMutation, onAdd],
      )}
    />
  );
}

export interface UpdateShiftProps extends BaseShiftFormProps {
  shift?: ShiftStruct;
  onUpdate?: (
    shift: ShiftFormValues,
  ) => void | Promise<RouterOutputs["shifts"]["update"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateShift({
  shift,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  children,
  ...props
}: PropsWithChildren<UpdateShiftProps>) {
  const utils = api.useUtils();
  const updateShiftMutation = api.shifts.update.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.get.invalidate({ id: shift?.id ?? "" });
        await utils.shifts.getMany.invalidate();
      }
      props.onOpenChange?.(false);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        // No specific UI action needed on update error
      }
    },
  });

  const mergedDefaultValues = useMemo(
    () => ({
      summary: shift?.summary,
      scope: shift?.scope,
      startDate: shift?.startDate,
      endDate: shift?.endDate,
      ...defaultValues,
    }),
    [shift, defaultValues],
  );

  return (
    <DialogForm<BaseShiftFormProps, ShiftFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={OrganizationShiftForm}
      defaultValues={mergedDefaultValues}
      onSubmit={useCallback<NonNullable<ShiftFormProps["onSubmit"]>>(
        async (values) => {
          if (!shift?.id) return;
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateShiftMutation.mutateAsync({
              id: shift.id,
              data: {
                summary: values.summary,
                scope: values.scope,
                role: values.role,
                timeZone: values.timeZone,
                startDate: values.startDate,
                endDate: values.endDate,
                paymentType: values.paymentType,
                paymentRate: values.paymentRate,
              },
            });
          }
        },
        [updateShiftMutation, shift, onUpdate],
      )}
    >
      {children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export interface DeleteShiftProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  shift?: ShiftStruct;
  reroute?: boolean;
  onDelete?: (
    shift: RouterInputs["shifts"]["delete"],
  ) => void | Promise<RouterOutputs["shifts"]["delete"]>;
  onSuccess?: (
    shift: RouterOutputs["shifts"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteShift({
  shift,
  reroute,
  onDelete,
  onSuccess,
  onError,
  variant = "destructive",
  children,
  ...props
}: PropsWithChildren<DeleteShiftProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteShiftMutation = api.shifts.delete.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.shifts.getMany.invalidate();
        if (reroute) {
          router.replace(i18n.links.shifts);
        }
      }
      props.onOpenChange?.(false);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        // No specific UI action needed on delete error
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.delete}
      {...props}
      disabled={deleteShiftMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (!shift?.id) return;
        if (onDelete) {
          await onDelete({ id: shift.id });
        } else {
          await deleteShiftMutation.mutateAsync({ id: shift.id });
        }
      }, [deleteShiftMutation, shift, onDelete])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function ShiftMenu({
  loading,
  rerouteOnDelete,
  shift,
  onUpdate,
  onDelete,
  onCancel,
  onApprove,
  onReschedule,
  onCheckIn,
  onCheckOut,
  onConfirm,
  onReviewAdd,
  onReviewUpdate,
  children,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    rerouteOnDelete?: boolean;
    shift?: RouterOutputs["shifts"]["get"] | ShiftStruct;
    onUpdate?: UpdateShiftProps["onUpdate"];
    onDelete?: DeleteShiftProps["onDelete"];
    onCancel?:
      | OrganizationCancelShiftProps["onCancel"]
      | ProviderCancelShiftProps["onCancel"];
    onApprove?: OrganizationApproveShiftProps["onApprove"];
    onConfirm?: ProviderConfirmShiftProps["onConfirm"];
    onReschedule?: OrganizationRescheduleShiftProps["onReschedule"];
    onCheckIn?: ProviderCheckInShiftProps["onCheckIn"];
    onCheckOut?: ProviderCheckOutShiftProps["onCheckOut"];
    onReviewAdd?: AddReviewProps["onAdd"];
    onReviewUpdate?: UpdateReviewProps["onUpdate"];
  } & ButtonProps
>) {
  const { isInternal, isProvider, isClient } = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  // Determine available actions based on user type and shift status
  const allowedCancelStatuses: ShiftStatus[] = [
    ShiftStatus.PENDING,
    ShiftStatus.CONFIRMED,
  ];
  const canCancel = shift && allowedCancelStatuses.includes(shift.status);
  const allowedRescheduleStatuses: ShiftStatus[] = [
    ShiftStatus.PENDING,
    ShiftStatus.CONFIRMED,
  ];
  const canReschedule =
    shift && allowedRescheduleStatuses.includes(shift.status);
  const canConfirm =
    shift?.status === ShiftStatus.PENDING &&
    shift.startDate &&
    getHoursBetweenDates(shift.startDate, new Date()) < 48;
  const canCheckIn =
    shift?.status === ShiftStatus.CONFIRMED &&
    shift.startDate &&
    getHoursBetweenDates(shift.startDate, new Date()) < 1;
  const canCheckOut =
    shift?.status === ShiftStatus.ACTIVE &&
    shift.endDate &&
    getHoursBetweenDates(shift.endDate, new Date()) < 1;
  const canApprove = shift && shift.status === ShiftStatus.COMPLETED;
  const canRate = shift && shift.status === ShiftStatus.APPROVED;

  return (
    <DropdownMenu modal open={dropdownOpen} onOpenChange={setDropdownOpen}>
      <DropdownMenuTrigger asChild>
        {children ?? (
          <Button
            variant="outline"
            size="icon"
            disabled={loading}
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          {isInternal && (
            <>
              <DropdownMenuSeparator />
              <UpdateShift
                onOpenChange={handleDialogOpenChange}
                shift={shift}
                onUpdate={onUpdate}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.update}
                </DropdownMenuItem>
              </UpdateShift>
            </>
          )}
        </DropdownMenuGroup>

        {shift && (
          <AddIncident
            organizationId={shift.organization?.id}
            shiftId={shift.id}
          >
            <DropdownMenuItem onSelect={(e: Event) => e.preventDefault()}>
              {i18n.en.common.report_incident}
            </DropdownMenuItem>
          </AddIncident>
        )}

        {canRate && isClient && shift && (
          <DropdownMenuGroup>
            {shift.review ? (
              <UpdateReview
                onOpenChange={handleDialogOpenChange}
                shiftId={shift.id}
                review={shift.review}
                onUpdate={onReviewUpdate}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.updateReview}
                </DropdownMenuItem>
              </UpdateReview>
            ) : (
              <AddReview
                onOpenChange={handleDialogOpenChange}
                shiftId={shift.id}
                onAdd={onReviewAdd}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.addReview}
                </DropdownMenuItem>
              </AddReview>
            )}
          </DropdownMenuGroup>
        )}

        {(canReschedule ||
          canApprove ||
          canConfirm ||
          canCheckIn ||
          canCheckOut) && (
          <DropdownMenuGroup>
            {canConfirm && isProvider && (
              <ProviderConfirmShift
                onOpenChange={handleDialogOpenChange}
                shift={shift}
                onConfirm={onConfirm}
                variant="ghost"
                className="w-full justify-start rounded-sm px-2 py-1.5 text-sm"
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span>Check In</span>
                </DropdownMenuItem>
              </ProviderConfirmShift>
            )}
            {canReschedule && isClient && (
              <OrganizationRescheduleShift
                shift={shift}
                onReschedule={onReschedule}
                disabled={loading}
                variant="ghost"
                className="w-full justify-start rounded-sm px-2 py-1.5 text-sm"
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span>Reschedule Shift</span>
                </DropdownMenuItem>
              </OrganizationRescheduleShift>
            )}
            {canApprove && isClient && (
              <OrganizationApproveShift
                onOpenChange={handleDialogOpenChange}
                shift={shift}
                onApprove={onApprove}
                variant="ghost"
                className="w-full justify-start rounded-sm px-2 py-1.5 text-sm"
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span>Approve Shift</span>
                </DropdownMenuItem>
              </OrganizationApproveShift>
            )}
            {canCheckIn && isProvider && (
              <ProviderCheckInShift
                onOpenChange={handleDialogOpenChange}
                shift={shift}
                onCheckIn={onCheckIn}
                variant="ghost"
                className="w-full justify-start rounded-sm px-2 py-1.5 text-sm"
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span>Check In</span>
                </DropdownMenuItem>
              </ProviderCheckInShift>
            )}
            {canCheckOut && isProvider && (
              <ProviderCheckOutShift
                onOpenChange={handleDialogOpenChange}
                shift={shift}
                onCheckOut={onCheckOut}
                variant="ghost"
                className="w-full justify-start rounded-sm px-2 py-1.5 text-sm"
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span>Check Out</span>
                </DropdownMenuItem>
              </ProviderCheckOutShift>
            )}
          </DropdownMenuGroup>
        )}

        {canCancel && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {isProvider && (
                <ProviderCancelShift
                  onOpenChange={handleDialogOpenChange}
                  shift={shift}
                  onCancel={onCancel}
                  variant="ghost"
                  className="w-full justify-start rounded-sm px-2 py-1.5 text-sm text-destructive focus:text-destructive"
                >
                  <DropdownMenuItem onSelect={handleItemSelect}>
                    <span>Cancel Shift</span>
                  </DropdownMenuItem>
                </ProviderCancelShift>
              )}
              {isClient && (
                <OrganizationCancelShift
                  onOpenChange={handleDialogOpenChange}
                  shift={shift}
                  onCancel={onCancel}
                  variant="ghost"
                  className="w-full justify-start rounded-sm px-2 py-1.5 text-sm text-destructive focus:text-destructive"
                >
                  <DropdownMenuItem onSelect={handleItemSelect}>
                    <span>Cancel Shift</span>
                  </DropdownMenuItem>
                </OrganizationCancelShift>
              )}
              {isInternal && (
                <DeleteShift
                  onOpenChange={handleDialogOpenChange}
                  shift={shift}
                  onDelete={onDelete}
                  reroute={rerouteOnDelete}
                  variant="ghost"
                  className="w-full justify-start rounded-sm px-2 py-1.5 text-sm text-destructive focus:text-destructive"
                >
                  <DropdownMenuItem
                    onSelect={handleItemSelect}
                    className="text-destructive focus:text-destructive"
                  >
                    <span>Delete Shift</span>
                  </DropdownMenuItem>
                </DeleteShift>
              )}
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Create a utility that checks time and returns the amount of hours between two dates
export function getHoursBetweenDates(startDate: Date, endDate: Date) {
  const diff = endDate.getTime() - startDate.getTime();
  return diff / (1000 * 60 * 60);
}
