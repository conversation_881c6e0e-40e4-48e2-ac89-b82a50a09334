"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { captureException } from "@sentry/nextjs";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@axa/ui/primitives/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  CoreContractFormProps,
  CoreContractFormValues,
} from "@/components/forms/contracts/core";
import type {
  SimpleContractUpdateFormProps,
  SimpleContractUpdateFormValues,
} from "@/components/forms/contracts/simple-update";

import { ContractStatus, ContractType } from "@/api";
import { api } from "@/api/client";
import CoreContractForm from "@/components/forms/contracts/core";
import SimpleContractUpdateForm from "@/components/forms/contracts/simple-update";
import { useDocumentUploader } from "@/hooks/use-document-uploader";

const i18n = {
  en: {
    unknownContract: "Unknown Contract",
    titles: {
      add: "Add Contract",
      update: "Update Contract",
      delete: "Delete Contract",
      sign: "Sign Contract",
      reject: "Reject Contract",
      view: "View Contract",
    },
    descriptions: {
      add: "Add a new contract.",
      update: "Update an existing contract.",
      delete: "Are you sure you want to delete this contract?",
      sign: "Are you sure you want to sign this contract?",
      reject: "Are you sure you want to reject this contract?",
      view: "Review the contract document.",
    },
    actions: {
      label: "Actions",
      add: "Add Contract",
      update: "Update",
      delete: "Delete",
      open: "Open Contract",
      sign: "Sign Contract",
      reject: "Reject Contract",
      view: "View Document",
      selectContract: "Select Contract",
    },
    messages: {
      created: "Contract created successfully",
      updated: "Contract updated successfully",
      deleted: "Contract deleted successfully",
      signed: "Contract signed successfully",
      rejected: "Contract rejected successfully",
      failedCreate: "Failed to create contract: ",
      failedUpdate: "Failed to update contract: ",
      failedDelete: "Failed to delete contract: ",
      failedSign: "Failed to sign contract: ",
      failedReject: "Failed to reject contract: ",
      failedLoad: "Failed to load contract document: ",
    },
  },
  links: {
    contracts: "/app/contracts/[id]",
  },
};

export type ContractStruct =
  | RouterOutputs["contracts"]["get"]
  | RouterOutputs["contracts"]["getMany"]["items"][number];

export interface BaseContractFormProps
  extends Omit<
    DialogFormProps<CoreContractFormProps, CoreContractFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  providerId?: string;
  organizationId?: string;
  defaultValues?: Partial<CoreContractFormValues>;
}

export interface AddContractProps extends BaseContractFormProps {
  onAdd?: (
    contract: CoreContractFormValues,
  ) => void | Promise<RouterOutputs["contracts"]["create"]>;
  onSuccess?: (
    contract: RouterOutputs["contracts"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddContract({
  onAdd,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<AddContractProps>) {
  const utils = api.useUtils();
  const createContractMutation = api.contracts.create.useMutation({
    onSuccess: async (result) => {
      await utils.contracts.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.created);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(`${i18n.en.messages.failedCreate}${error.message}`);
      }
    },
  });

  // Add document mutations
  const { uploadDocument } = useDocumentUploader({
    bucketName: "contracts",
    folderPath: "agreements",
  });
  const createDocumentMutation = api.documents.create.useMutation();

  return (
    <DialogForm<CoreContractFormProps, CoreContractFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.actions.add}
      {...props}
      Component={CoreContractForm}
      onSubmit={useCallback(
        async (values: CoreContractFormValues) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            try {
              // Transform the form values to match the API input format
              const { title, type, organization, signers, agreement } = values;

              // Handle document upload if present
              let documentId: string | undefined = undefined;
              let documensoId: string | undefined = undefined;

              // Check if we have a new document to upload (File object)
              if (agreement.document && agreement.document.length > 0) {
                // Upload the document to Supabase
                const file = agreement.document[0];
                if (!file) {
                  throw new Error("File is missing");
                }

                const uploadResult = await uploadDocument(file);

                if (!uploadResult) {
                  throw new Error("Failed to upload document");
                }

                // Create a new document in the database
                const document = await createDocumentMutation.mutateAsync({
                  name: uploadResult.fileName,
                  type: "CONTRACT",
                  url: uploadResult.path,
                  size: uploadResult.fileSize,
                });

                // Use the new document ID
                documentId = document.id;
              } else if (agreement.documensoId) {
                // Use the provided Documenso ID
                documensoId = agreement.documensoId;
              }

              // Ensure signers array is properly formed
              if (!signers || signers.length === 0) {
                throw new Error(
                  "At least one signer is required for a contract",
                );
              }

              await createContractMutation.mutateAsync({
                title,
                type,
                organizationId: organization,
                // Include signers data if available - use id instead of personId
                signers: signers.map((signer) => ({
                  id: signer.id,
                  role: signer.role,
                })),
                // Include agreement data if available
                agreement: agreement && {
                  generate: agreement.generate ?? false,
                  documentId,
                  documensoId,
                },
              });
            } catch (error) {
              captureException(error, {
                level: "error",
                extra: {
                  type: "contract.add",
                  values,
                },
              });
              const errorMessage =
                error instanceof Error
                  ? error.message
                  : "Unknown error occurred";
              toast.error(i18n.en.messages.failedCreate + errorMessage);
            }
          }
        },
        [onAdd, createContractMutation, createDocumentMutation, uploadDocument],
      )}
    />
  );
}

export interface UpdateContractProps extends BaseContractFormProps {
  contract?: ContractStruct;
  onUpdate?: (
    contract: SimpleContractUpdateFormValues,
  ) => void | Promise<RouterOutputs["contracts"]["update"]>;
  onSuccess?: (
    contract: RouterOutputs["contracts"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateContract({
  contract,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  ...props
}: PropsWithChildren<UpdateContractProps>) {
  const utils = api.useUtils();
  const updateContractMutation = api.contracts.update.useMutation({
    onSuccess: async (result) => {
      await utils.contracts.getMany.invalidate();
      if (contract?.id) {
        await utils.contracts.get.invalidate({ id: contract.id });
      }
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.updated);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(`${i18n.en.messages.failedUpdate}${error.message}`);
      }
    },
  });

  return (
    <DialogForm<SimpleContractUpdateFormProps, SimpleContractUpdateFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      type="update"
      {...props}
      Component={SimpleContractUpdateForm}
      defaultValues={{
        title: contract?.title ?? "",
        ...defaultValues,
      }}
      onSubmit={useCallback(
        async (values: SimpleContractUpdateFormValues) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            if (!contract?.id) {
              throw new Error("Contract ID is required for update");
            }

            // Only update the title
            await updateContractMutation.mutateAsync({
              id: contract.id,
              data: {
                title: values.title,
              },
            });
          }
        },
        [onUpdate, updateContractMutation, contract?.id],
      )}
    />
  );
}

export interface DeleteContractProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  contract?: ContractStruct;
  reroute?: boolean;
  onDelete?: (
    contract: RouterInputs["contracts"]["delete"],
  ) => void | Promise<RouterOutputs["contracts"]["delete"]>;
  onSuccess?: (
    contract: RouterOutputs["contracts"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteContract({
  onDelete,
  onSuccess,
  onError,
  contract,
  reroute,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteContractProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteContractMutation = api.contracts.delete.useMutation({
    onSuccess: async (result) => {
      await utils.contracts.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.contracts.replace("[id]", ""));
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteContractMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: contract?.id ?? "" });
        } else {
          await deleteContractMutation.mutateAsync({
            id: contract?.id ?? "",
          });
        }
      }, [deleteContractMutation, contract, onDelete])}
    />
  );
}

export interface SignContractProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  contract?: ContractStruct;
  onSign?: (
    contractId: string,
  ) => void | Promise<RouterOutputs["contracts"]["approve"]>;
  onSuccess?: (
    result: RouterOutputs["contracts"]["approve"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function SignContract({
  onSign,
  onSuccess,
  onError,
  contract,
  variant = "outline",
  ...props
}: PropsWithChildren<SignContractProps>) {
  const utils = api.useUtils();
  const approveContractMutation = api.contracts.approve.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.contracts.get.invalidate({ id: contract?.id });
        await utils.contracts.getMany.invalidate();
        toast.success(i18n.en.messages.signed);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedSign + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.sign}
      description={i18n.en.descriptions.sign}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.sign}
      {...props}
      disabled={approveContractMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (!contract?.id) {
          toast.error("Contract ID is required");
          return;
        }

        try {
          if (onSign) {
            await onSign(contract.id);
          } else {
            await approveContractMutation.mutateAsync({
              id: contract.id,
            });
          }
        } catch (error) {
          captureException(error, {
            level: "error",
            extra: {
              type: "contract.sign",
              contractId: contract.id,
            },
          });

          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";
          toast.error(i18n.en.messages.failedSign + errorMessage);
        }
      }, [approveContractMutation, contract, onSign])}
    />
  );
}

export interface RejectContractProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  contract?: ContractStruct;
  onReject?: (
    contractId: string,
  ) => void | Promise<RouterOutputs["contracts"]["reject"]>;
  onSuccess?: (
    result: RouterOutputs["contracts"]["reject"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function RejectContract({
  onReject,
  onSuccess,
  onError,
  contract,
  variant = "destructive",
  ...props
}: PropsWithChildren<RejectContractProps>) {
  const utils = api.useUtils();
  const rejectContractMutation = api.contracts.reject.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.contracts.get.invalidate({ id: contract?.id });
        await utils.contracts.getMany.invalidate();
        toast.success(i18n.en.messages.rejected);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedReject + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.reject}
      description={i18n.en.descriptions.reject}
      onOpenChange={props.onOpenChange}
      open={props.open}
      action={i18n.en.actions.reject}
      {...props}
      disabled={rejectContractMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (!contract?.id) {
          toast.error("Contract ID is required");
          return;
        }

        try {
          if (onReject) {
            await onReject(contract.id);
          } else {
            await rejectContractMutation.mutateAsync({
              id: contract.id,
            });
          }
        } catch (error) {
          captureException(error, {
            level: "error",
            extra: {
              type: "contract.reject",
              contractId: contract.id,
            },
          });

          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";
          toast.error(i18n.en.messages.failedReject + errorMessage);
        }
      }, [rejectContractMutation, contract, onReject])}
    />
  );
}

export interface ViewContractProps {
  contract?: ContractStruct;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ViewContract({
  contract,
  open,
  onOpenChange,
}: ViewContractProps) {
  const agreements = contract?.agreements;
  const latestAgreement = agreements?.[agreements.length - 1];
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>{i18n.en.titles.view}</DialogTitle>
          <DialogDescription>{i18n.en.descriptions.view}</DialogDescription>
        </DialogHeader>
        {latestAgreement && (
          <div key={latestAgreement.id}>
            <h3>{latestAgreement.document?.name}</h3>
            <iframe
              src={`/api/assets/${latestAgreement.document?.url}`}
              className="h-full w-full"
            />
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange?.(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function ContractMenu({
  loading = false,
  contract,
  onUpdate,
  onDelete,
  onSign,
  onReject,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    contract?: ContractStruct;
    onUpdate?: UpdateContractProps["onUpdate"];
    onDelete?: DeleteContractProps["onDelete"];
    onSign?: SignContractProps["onSign"];
    onReject?: RejectContractProps["onReject"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const [viewOpen, setViewOpen] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    setViewOpen(true);
  }, [setViewOpen]);

  const isPending = contract?.status === ContractStatus.PENDING;

  return (
    <>
      <DropdownMenu
        modal={dropdownOpen}
        open={dropdownOpen}
        onOpenChange={setDropdownOpen}
      >
        <DropdownMenuTrigger asChild>
          {props.children ?? (
            <Button
              disabled={loading}
              variant="ghost"
              size="icon"
              {...props}
              ref={dropdownTriggerRef}
            >
              <MoreVerticalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          hidden={hasOpenDialog}
          onCloseAutoFocus={(event) => {
            if (focusRef.current) {
              focusRef.current.focus();
              focusRef.current = null;
              event.preventDefault();
            }
          }}
        >
          <DropdownMenuItem onSelect={handleOpen}>
            {i18n.en.actions.view}
          </DropdownMenuItem>
          {/* <DropdownMenuItem onSelect={handleView}>
            {i18n.en.actions.view}
          </DropdownMenuItem> */}
          <DropdownMenuSeparator />
          <UpdateContract
            onUpdate={onUpdate}
            onOpenChange={handleDialogOpenChange}
            contract={contract}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateContract>
          {isPending && (
            <>
              <SignContract
                onOpenChange={handleDialogOpenChange}
                contract={contract}
                onSign={onSign}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.sign}
                </DropdownMenuItem>
              </SignContract>
              <RejectContract
                onOpenChange={handleDialogOpenChange}
                contract={contract}
                onReject={onReject}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.reject}
                </DropdownMenuItem>
              </RejectContract>
            </>
          )}
          <DropdownMenuSeparator />
          <DeleteContract
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            contract={contract}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              className="text-red-600 dark:text-red-400"
            >
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteContract>
        </DropdownMenuContent>
      </DropdownMenu>
      <ViewContract
        contract={contract}
        open={viewOpen}
        onOpenChange={setViewOpen}
      />
    </>
  );
}
