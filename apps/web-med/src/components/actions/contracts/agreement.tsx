"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useMemo } from "react";
import { EmbedSignDocument } from "@documenso/embed-react";
import { captureException } from "@sentry/nextjs";

import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import { Button } from "@axa/ui/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@axa/ui/primitives/dialog";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterError, RouterOutputs } from "@/api";
import type { AgreementFormValues } from "@/components/forms/contracts/agreement-form";

import { ContractType, PersonRole } from "@/api";
import { api } from "@/api/client";
import { useUser } from "@/components/contexts/User";
import AgreementForm from "@/components/forms/contracts/agreement-form";
import { useDocumentUploader } from "@/hooks/use-document-uploader";

// Update the i18n object to include dialog titles for different contract types
const i18n = {
  en: {
    titles: {
      create: "Create Agreement",
      sign: "Sign Agreement",
      reject: "Reject Agreement",
      jobContract: "Create Job Contract",
      serviceRates: "Create Service Rates Agreement",
      customContract: "Upload Custom Contract",
    },
    descriptions: {
      create: "Create a new agreement",
      sign: "Are you sure you want to sign this agreement?",
      reject: "Are you sure you want to reject this agreement?",
      jobContract: "Create an employment contract",
      serviceRates: "Create a service rates agreement",
      customContract: "Upload a custom contract document",
    },
    actions: {
      create: "Create",
      sign: "Sign",
      reject: "Reject",
      jobContract: "Create Job Contract",
      serviceRates: "Create Service Rates Agreement",
      customContract: "Upload Custom Contract",
    },
    messages: {
      created: "Agreement created successfully",
      signed: "Agreement signed successfully",
      rejected: "Agreement rejected successfully",
      failedCreate: "Failed to create agreement: ",
      failedSign: "Failed to sign agreement: ",
      failedReject: "Failed to reject agreement: ",
      documensoRedirect:
        "You will be redirected to Documenso to sign this agreement.",
      unavailable: "This action is not available for this agreement.",
    },
  },
};

export type AgreementStruct = NonNullable<
  RouterOutputs["contracts"]["get"]["agreements"]
>[number];

export interface BaseAgreementFormProps {
  // Will be extended for specific form types
  contractId?: string;
}

// Update the RouterOutputs type union
type AgreementCreateResult = RouterOutputs["contracts"]["agreements"]["create"];
type AgreementGenerateResult =
  RouterOutputs["contracts"]["agreements"]["generate"];
type AgreementLinkResult =
  RouterOutputs["contracts"]["agreements"]["linkDocumensoAgreement"];
type AgreementResult =
  | AgreementCreateResult
  | AgreementGenerateResult
  | AgreementLinkResult;
type CreateAgreementFormValues = AgreementFormValues;

// Update the CreateAgreementProps interface
export interface CreateAgreementProps {
  onCreate?: (
    values: CreateAgreementFormValues,
  ) => void | Promise<AgreementResult>;
  onSuccess?: (agreement: AgreementResult) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
  contractId?: string;
  contract?: RouterOutputs["contracts"]["get"];
  children?: React.ReactNode;
}

export function CreateAgreement({
  onCreate,
  onSuccess,
  onError,
  contractId,
  contract,
  children,
}: CreateAgreementProps) {
  const utils = api.useUtils();

  const effectiveContractId = contract?.id ?? contractId;

  // Initialize document uploader with the contracts bucket
  const {
    uploadDocument,
    isUploading: isUploadingDocument,
    error: uploadError,
  } = useDocumentUploader({
    bucketName: "contracts",
    folderPath: "agreements",
  });

  const createDocumentMutation = api.documents.create.useMutation();

  // Create mutation for custom document uploads
  const createAgreementMutation = api.contracts.agreements.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.contracts.getMany.invalidate();
        await utils.contracts.get.invalidate();
        toast.success(i18n.en.messages.created);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCreate + error.message);
      }
    },
  });

  // Generate mutation for preset agreement types
  const generateAgreementMutation =
    api.contracts.agreements.generate.useMutation({
      onSuccess: async (result) => {
        if (onSuccess) {
          await onSuccess(result);
        } else {
          await utils.contracts.getMany.invalidate();
          await utils.contracts.get.invalidate();
          toast.success(i18n.en.messages.created);
        }
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else {
          toast.error(i18n.en.messages.failedCreate + error.message);
        }
      },
    });

  const linkAgreementMutation =
    api.contracts.agreements.linkDocumensoAgreement.useMutation({
      onSuccess: async (result) => {
        if (onSuccess) {
          await onSuccess(result);
        } else {
          await utils.contracts.getMany.invalidate();
          await utils.contracts.get.invalidate();
          toast.success(i18n.en.messages.created);
        }
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else {
          toast.error(i18n.en.messages.failedCreate + error.message);
        }
      },
    });

  const handleSubmit = useCallback(
    async (values: AgreementFormValues) => {
      if (onCreate) {
        await onCreate(values);
      } else {
        if (!effectiveContractId) {
          throw new Error("Contract ID is required for agreement creation");
        }

        try {
          // Ensure signers array is properly formed
          if (!values.signers || values.signers.length === 0) {
            throw new Error("At least one signer is required for an agreement");
          }

          if (values.agreement.documensoId) {
            // Case 1: We have a Documenso ID for external document
            await linkAgreementMutation.mutateAsync({
              contractId: effectiveContractId,
              documensoId: values.agreement.documensoId,
              signers: values.signers,
              expiresAt: values.expiresAt,
            });
          } else if (values.agreement.generate) {
            // Case 2: For preset types (job or service rates), use the generate mutation
            const type =
              contract?.type === ContractType.EMPLOYMENT
                ? "job-position"
                : "service-rate";

            await generateAgreementMutation.mutateAsync({
              contractId: effectiveContractId,
              type,
              signers: values.signers,
              expiresAt:
                values.expiresAt ??
                new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            });
          } else if (
            values.agreement.document &&
            values.agreement.document.length > 0
          ) {
            // case 3: Check if we have a new document to upload (File object)
            const file = values.agreement.document[0];

            if (file) {
              // Upload the document to Supabase
              const uploadResult = await uploadDocument(file);

              console.log("uploadResult", uploadResult);

              if (!uploadResult) {
                throw new Error("Failed to upload document");
              }

              // Create a new document in the database
              const document = await createDocumentMutation.mutateAsync({
                name: uploadResult.fileName,
                type: "CONTRACT",
                url: uploadResult.url,
                size: uploadResult.fileSize,
              });

              await createAgreementMutation.mutateAsync({
                contractId: effectiveContractId,
                documentId: document.id,
                signers: values.signers,
                expiresAt: values.expiresAt,
              });
            }
          } else {
            // Case 4: No valid agreement creation method was determined
            throw new Error(
              "Invalid agreement creation flow. Must provide a document, documensoId, or use a generation template.",
            );
          }
        } catch (error) {
          captureException(error, {
            level: "error",
            extra: {
              contractId: effectiveContractId,
              contractType: contract?.type,
            },
          });

          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";
          toast.error(i18n.en.messages.failedCreate + errorMessage);
          throw error;
        }
      }
    },
    [
      createDocumentMutation,
      createAgreementMutation,
      generateAgreementMutation,
      linkAgreementMutation,
      effectiveContractId,
      onCreate,
      contract?.type,
      uploadDocument,
    ],
  );

  // Determine dialog title and description based on contract type
  const { title, description, action } = useMemo(() => {
    let title = i18n.en.titles.create;
    let description = i18n.en.descriptions.create;
    let action = i18n.en.actions.create;

    if (contract?.type === ContractType.EMPLOYMENT) {
      title = i18n.en.titles.jobContract;
      description = i18n.en.descriptions.jobContract;
      action = i18n.en.actions.jobContract;
    } else if (contract?.type === ContractType.SERVICE_RATE) {
      title = i18n.en.titles.serviceRates;
      description = i18n.en.descriptions.serviceRates;
      action = i18n.en.actions.serviceRates;
    } else {
      title = i18n.en.titles.customContract;
      description = i18n.en.descriptions.customContract;
      action = i18n.en.actions.customContract;
    }

    return {
      title,
      description,
      action,
    };
  }, [contract?.type]);

  // TODO: Add signers to the form - get provider
  const provider = contract?.provider;
  const lastAgreement =
    contract?.agreements?.[contract.agreements.length - 1] ?? undefined;
  const signers = lastAgreement?.signatures?.map((signature) =>
    signature.person.id === provider?.person.id
      ? {
          id: provider.id,
          role: "provider" as const,
        }
      : {
          id: signature.person.id,
          role:
            signature.person.role === PersonRole.CLIENT
              ? "organization"
              : ("internal" as const),
        },
  ) as { id: string; role: "organization" | "provider" | "internal" }[];

  return (
    <DialogForm
      title={title}
      description={description}
      action={action}
      Component={AgreementForm}
      defaultValues={{
        contractId: effectiveContractId,
        signers,
      }}
      contractId={effectiveContractId ?? ""}
      contractType={contract?.type ?? ContractType.OTHER}
      organizationId={contract?.organizationId ?? undefined}
      onSubmit={handleSubmit}
      disabled={isUploadingDocument}
    >
      {children ?? (
        <Button variant="outline" disabled={isUploadingDocument}>
          {isUploadingDocument ? "Uploading..." : action}
        </Button>
      )}
    </DialogForm>
  );
}

export interface SignAgreementProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  agreement?: AgreementStruct;
  signature?: NonNullable<AgreementStruct["signatures"]>[number];
  onSign?: (agreementId: string) => void | Promise<void>;
  onSuccess?: () => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function SignAgreement({
  agreement,
  signature,
  onSign,
  onSuccess,
  onError,
  variant = "outline",
  ...props
}: PropsWithChildren<SignAgreementProps>) {
  const user = useUser();
  const utils = api.useUtils();
  const signAgreementMutation = api.contracts.signatures.sign.useMutation({
    onSuccess: async () => {
      if (onSuccess) {
        await onSuccess();
      } else {
        await utils.contracts.get.invalidate();
        await utils.contracts.getMany.invalidate();
        toast.success(i18n.en.messages.signed);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedSign + error.message);
      }
    },
  });

  const onAction = useCallback(async () => {
    if (!signature?.id) return;

    try {
      if (onSign) {
        await onSign(signature.id);
      } else {
        await signAgreementMutation.mutateAsync({
          signatureId: signature.id,
        });
      }
    } catch (error) {
      captureException(error, {
        level: "error",
        extra: { signatureId: signature.id },
      });

      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(i18n.en.messages.failedSign + errorMessage);
    }
  }, [signAgreementMutation, signature, onSign]);

  if (user.id !== signature?.person.id) {
    return null;
  }

  if (signature?.documensoToken) {
    return (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="primary" size="sm">
            Sign
          </Button>
        </DialogTrigger>
        <DialogContent className="flex min-h-[90dvh] w-full flex-col gap-2">
          <DialogHeader>
            <DialogTitle>Sign Document</DialogTitle>
            <DialogDescription>
              Please sign the document to complete the agreement.
            </DialogDescription>
          </DialogHeader>
          <EmbedSignDocument
            className="size-full flex-1"
            token={signature.documensoToken}
            name={`${signature.person.firstName} ${signature.person.lastName}`}
            onDocumentCompleted={onAction}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <DialogConfirmation
      title={i18n.en.titles.sign}
      description={i18n.en.descriptions.sign}
      action={i18n.en.actions.sign}
      onOpenChange={props.onOpenChange}
      open={props.open}
      variant={variant}
      disabled={signAgreementMutation.isPending}
      onClick={onAction}
      {...props}
    >
      {props.children ?? (
        <Button
          variant="primary"
          size="sm"
          disabled={signAgreementMutation.isPending}
        >
          {i18n.en.actions.sign}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export interface RejectAgreementProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  agreement?: AgreementStruct;
  signature?: NonNullable<AgreementStruct["signatures"]>[number];
  onReject?: (agreementId: string) => void | Promise<void>;
  onSuccess?: () => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<any>) => void | Promise<void>;
}

export function RejectAgreement({
  agreement,
  signature,
  onReject,
  onSuccess,
  onError,
  variant = "destructive",
  ...props
}: PropsWithChildren<RejectAgreementProps>) {
  const user = useUser();
  const utils = api.useUtils();
  const rejectAgreementMutation = api.contracts.signatures.reject.useMutation({
    onSuccess: async () => {
      if (onSuccess) {
        await onSuccess();
      } else {
        await utils.contracts.get.invalidate();
        await utils.contracts.getMany.invalidate();
        toast.success(i18n.en.messages.rejected);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedReject + error.message);
      }
    },
  });

  const onAction = useCallback(async () => {
    if (!signature?.id) return;

    try {
      if (onReject) {
        await onReject(signature.id);
      } else {
        await rejectAgreementMutation.mutateAsync({
          signatureId: signature.id,
        });
      }
    } catch (error) {
      captureException(error, {
        level: "error",
        extra: { signatureId: signature.id },
      });

      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(i18n.en.messages.failedReject + errorMessage);
    }
  }, [rejectAgreementMutation, signature, onReject]);

  if (user.id !== signature?.person.id || !agreement || !signature) {
    return null;
  }

  return (
    <DialogConfirmation
      title={i18n.en.titles.reject}
      description={i18n.en.descriptions.reject}
      action={i18n.en.actions.reject}
      onOpenChange={props.onOpenChange}
      open={props.open}
      variant={variant}
      disabled={rejectAgreementMutation.isPending}
      onClick={onAction}
      {...props}
    >
      {props.children ?? (
        <Button
          variant="destructive"
          size="sm"
          disabled={rejectAgreementMutation.isPending}
        >
          {i18n.en.actions.reject}
        </Button>
      )}
    </DialogConfirmation>
  );
}
