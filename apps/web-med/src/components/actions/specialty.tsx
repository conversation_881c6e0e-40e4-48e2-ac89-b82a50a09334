"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  SpecialtyFormProps,
  SpecialtyFormValues,
} from "@/components/forms/SpecialtyForm";

import { api } from "@/api/client";
import SpecialtyForm from "@/components/forms/SpecialtyForm";

const i18n = {
  en: {
    unknownSpecialty: "Unknown Specialty",
    titles: {
      add: "Add A Specialty",
      update: "Update The Specialty",
      delete: "Delete The Specialty",
    },
    descriptions: {
      add: "Add a new specialty.",
      update: "Update an existing specialty.",
      delete: "Are you sure you want to delete this specialty?",
    },
    actions: {
      label: "Actions",
      add: "Add Specialty",
      update: "Update",
      delete: "Delete",
      open: "Open Specialty",
      selectSpecialty: "Select Specialty",
    },
    messages: {
      created: "Specialty created successfully.",
      updated: "Specialty updated successfully.",
      deleted: "Specialty deleted successfully.",
      failedCreate: "Failed to create specialty: ",
      failedUpdate: "Failed to update specialty: ",
      failedDelete: "Failed to delete specialty: ",
    },
  },
  links: {
    specialties: "/app/specialties",
    specialty: "/app/specialties/[id]",
  },
};

export type CoreSpecialtyStruct = RouterOutputs["specialties"]["get"];
export type SpecialtyStruct = Pick<
  CoreSpecialtyStruct,
  "id" | "name" | "description"
> & {
  providers?: { id: string }[];
  jobs?: { id: string }[];
  shifts?: { id: string }[];
  experiences?: { id: string }[];
};

export interface BaseSpecialtyFormProps
  extends Omit<
    DialogFormProps<SpecialtyFormProps, SpecialtyFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  providerId?: string;
  jobId?: string;
  shiftId?: string;
  experienceId?: string;
  defaultValues?: Partial<SpecialtyFormValues>;
}

export interface AddSpecialtyProps extends BaseSpecialtyFormProps {
  onAdd?: (
    specialty: SpecialtyFormValues,
  ) => void | Promise<RouterOutputs["specialties"]["create"]>;
  onSuccess?: (
    specialty: RouterOutputs["specialties"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddSpecialty({
  onAdd,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<AddSpecialtyProps>) {
  const utils = api.useUtils();
  const createSpecialtyMutation = api.specialties.create.useMutation({
    onSuccess: async (result) => {
      await utils.specialties.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.created);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCreate + error.message);
      }
    },
  });

  return (
    <DialogForm<SpecialtyFormProps, SpecialtyFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.actions.add}
      type="add"
      {...props}
      Component={SpecialtyForm}
      onSubmit={useCallback<NonNullable<SpecialtyFormProps["onSubmit"]>>(
        async (values: SpecialtyFormValues) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            await createSpecialtyMutation.mutateAsync({
              name: values.name,
              description: values.description,
              providerId: values.providerId,
              jobId: values.jobId,
              shiftId: values.shiftId,
              experienceId: values.experienceId,
            });
          }
        },
        [createSpecialtyMutation, onAdd],
      )}
    />
  );
}

export interface UpdateSpecialtyProps extends BaseSpecialtyFormProps {
  specialty?: SpecialtyStruct;
  onUpdate?: (
    specialty: SpecialtyFormValues,
  ) => void | Promise<RouterOutputs["specialties"]["update"]>;
  onSuccess?: (
    specialty: RouterOutputs["specialties"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateSpecialty({
  specialty,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateSpecialtyProps>) {
  const utils = api.useUtils();
  const updateSpecialtyMutation = api.specialties.update.useMutation({
    onSuccess: async (result) => {
      await utils.specialties.get.invalidate({ id: specialty?.id });
      await utils.specialties.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.updated);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedUpdate + error.message);
      }
    },
  });

  return (
    <DialogForm<SpecialtyFormProps, SpecialtyFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={SpecialtyForm}
      defaultValues={{
        name: specialty?.name,
        description: specialty?.description ?? "",
        providerId: specialty?.providers?.[0]?.id,
        jobId: specialty?.jobs?.[0]?.id,
        shiftId: specialty?.shifts?.[0]?.id,
        experienceId: specialty?.experiences?.[0]?.id,
        ...defaultValues,
      }}
      onSubmit={useCallback<NonNullable<SpecialtyFormProps["onSubmit"]>>(
        async (values: SpecialtyFormValues) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateSpecialtyMutation.mutateAsync({
              id: specialty?.id ?? "",
              data: {
                name: values.name,
                description: values.description,
              },
            });
          }
        },
        [updateSpecialtyMutation, specialty, onUpdate],
      )}
    />
  );
}

export interface DeleteSpecialtyProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  specialty?: SpecialtyStruct;
  reroute?: boolean;
  onDelete?: (
    specialty: RouterInputs["specialties"]["delete"],
  ) => void | Promise<RouterOutputs["specialties"]["delete"]>;
  onSuccess?: (
    specialty: RouterOutputs["specialties"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteSpecialty({
  onDelete,
  onSuccess,
  onError,
  specialty,
  reroute,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteSpecialtyProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteSpecialtyMutation = api.specialties.delete.useMutation({
    onSuccess: async (result) => {
      await utils.specialties.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.specialties);
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteSpecialtyMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: specialty?.id ?? "" });
        } else {
          await deleteSpecialtyMutation.mutateAsync({
            id: specialty?.id ?? "",
          });
        }
      }, [deleteSpecialtyMutation, specialty, onDelete])}
    />
  );
}

export function SpecialtyMenu({
  loading = false,
  specialty,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    specialty?: SpecialtyStruct;
    onUpdate?: UpdateSpecialtyProps["onUpdate"];
    onDelete?: DeleteSpecialtyProps["onDelete"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.specialty.replace("[id]", specialty?.id ?? ""));
  }, [router, specialty?.id]);

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="ghost"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuItem onSelect={handleOpen}>
          {i18n.en.actions.open}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <UpdateSpecialty
          onUpdate={onUpdate}
          onOpenChange={handleDialogOpenChange}
          specialty={specialty}
        >
          <DropdownMenuItem onSelect={handleItemSelect}>
            {i18n.en.actions.update}
          </DropdownMenuItem>
        </UpdateSpecialty>
        <DropdownMenuSeparator />
        <DeleteSpecialty
          onDelete={onDelete}
          onOpenChange={handleDialogOpenChange}
          specialty={specialty}
        >
          <DropdownMenuItem
            onSelect={handleItemSelect}
            className="text-red-600 dark:text-red-400"
          >
            {i18n.en.actions.delete}
          </DropdownMenuItem>
        </DeleteSpecialty>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
