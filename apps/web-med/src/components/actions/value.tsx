"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { MoreVerticalIcon, PlusCircleIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { SelectorProps } from "@axa/ui/selectors/Selector";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import { Selector } from "@axa/ui/selectors/Selector";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterOutputs, ValueType } from "@/api";
import type {
  ValueFormProps,
  ValueFormValues,
} from "@/components/forms/ValueForm";

import { api } from "@/api/client";
import ValueForm from "@/components/forms/ValueForm";

const i18n = {
  en: {
    titles: {
      add: "Add A Value",
      update: "Update The Value",
      delete: "Delete The Value",
    },
    descriptions: {
      add: "Add a new value to your facility.",
      update: "Update an existing value.",
      delete: "Are you sure you want to delete this value?",
    },
    actions: {
      label: "Actions",
      add: "Add Value",
      update: "Update",
      delete: "Delete",
      selectValue: "Select Value",
    },
    messages: {
      created: "Value created successfully.",
      updated: "Value updated successfully.",
      deleted: "Value deleted successfully.",
      failedCreate: "Failed to create value: ",
      failedUpdate: "Failed to update value: ",
      failedDelete: "Failed to delete value: ",
    },
  },
  links: {
    values: "/app/values",
  },
};

export type CoreValueStruct = RouterOutputs["values"]["get"];
export type ValueStruct = Pick<CoreValueStruct, "id" | "value" | "type"> & {
  organization?: {
    id: string;
    name: string;
    avatar: string;
  };
};

export interface BaseValueFormProps
  extends Omit<
    DialogFormProps<ValueFormProps, ValueFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  facilityId?: string;
  scope?: ValueType;
  defaultValues?: Partial<ValueFormValues>;
}

export interface AddValueProps extends BaseValueFormProps {
  onCreate?: (
    value: ValueFormValues,
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    value: RouterOutputs["values"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddValue({
  onCreate,
  onSuccess,
  onError,
  title = i18n.en.titles.add,
  description = i18n.en.descriptions.add,
  label = i18n.en.actions.add,
  scope,
  ...props
}: PropsWithChildren<AddValueProps>) {
  const createValueMutation = api.values.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      toast.success(i18n.en.messages.created);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });
  return (
    <DialogForm<ValueFormProps, ValueFormValues>
      title={title}
      description={description}
      label={label}
      type="add"
      {...props}
      scope={scope}
      Component={ValueForm}
      onSubmit={useCallback<NonNullable<ValueFormProps["onSubmit"]>>(
        async (values) => {
          if (onCreate) {
            await onCreate(values);
          } else {
            await createValueMutation.mutateAsync({
              value: values.value,
              type: values.type,
            });
          }
        },
        [createValueMutation, onCreate],
      )}
    />
  );
}

export interface UpdateValueProps extends BaseValueFormProps {
  value?: ValueStruct;
  onUpdate?: (
    value: ValueFormValues,
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    value: RouterOutputs["values"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateValue({
  value,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  scope,
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateValueProps>) {
  const updateValueMutation = api.values.update.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      toast.success(i18n.en.messages.updated);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<ValueFormProps, ValueFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      scope={scope}
      {...props}
      Component={ValueForm}
      defaultValues={{
        id: value?.id,
        value: value?.value,
        type: value?.type!,
        ...defaultValues,
      }}
      onSubmit={useCallback<NonNullable<ValueFormProps["onSubmit"]>>(
        async (values) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateValueMutation.mutateAsync({
              id: values.id ?? "",
              data: {
                value: values.value,
                type: values.type,
              },
            });
          }
        },
        [updateValueMutation, onUpdate],
      )}
    />
  );
}

export interface DeleteValueProps
  extends Omit<
    DialogConfirmationProps,
    "onClick" | "onError" | "onSuccess" | "value"
  > {
  value?: ValueStruct;
  onDelete?: (
    value: RouterOutputs["values"]["delete"],
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    value: RouterOutputs["values"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteValue({
  onDelete,
  onSuccess,
  onError,
  value,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteValueProps>) {
  const deleteValueMutation = api.values.delete.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      {...props}
      disabled={deleteValueMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: value?.id ?? "" });
        } else {
          await deleteValueMutation.mutateAsync({
            id: value?.id ?? "",
          });
        }
      }, [deleteValueMutation, value, onDelete])}
    />
  );
}

export function ValueMenu({
  loading = false,
  value,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    value?: ValueStruct;
    onUpdate?: UpdateValueProps["onUpdate"];
    onDelete?: DeleteValueProps["onDelete"];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateValue
            onUpdate={onUpdate}
            onOpenChange={handleDialogOpenChange}
            value={value}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateValue>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteValue
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            value={value}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteValue>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface SearchValueProps
  extends Pick<
    SelectorProps<RouterOutputs["values"]["getMany"]["items"][number]>,
    | "onSelect"
    | "defaultValue"
    | "children"
    | "variant"
    | "className"
    | "loading"
  > {
  type?: ValueType;
  useDialog?: boolean;
}

export function SearchValue({
  loading = false,
  useDialog = true,
  defaultValue,
  onSelect,
  variant = "ghost",
  className = "size-full",
  children,
  type,
}: SearchValueProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useDebounceValue<string>(defaultValue ?? "", 500);
  const values = api.values.getMany.useQuery(
    {
      query,
      pageNumber: 0,
      pageSize: 5,
      type,
    },
    {
      enabled: open,
    },
  );

  return (
    <Selector<RouterOutputs["values"]["getMany"]["items"][number]>
      loading={loading || values.isLoading}
      useDialog={useDialog}
      data={values.data?.items ?? []}
      open={open}
      onOpenChange={setOpen}
      onSelect={onSelect}
      value={query}
      onValueChange={setQuery}
      variant={variant}
      className={className}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectValue}</span>
        </>
      )}
    </Selector>
  );
}
