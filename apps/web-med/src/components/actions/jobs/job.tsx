"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Check,
  Clock,
  MoreVerticalIcon,
  TrashIcon,
  XCircle,
} from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { But<PERSON> } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  JobPostFormProps,
  JobPostFormValues,
} from "@/components/forms/JobPostForm";
import type { OrganizationJobPostFormProps } from "@/components/forms/OrgJobPost";

import { JobPostStatus } from "@/api";
import { api } from "@/api/client";
import { OrganizationJobPostForm } from "@/components/forms/OrgJobPost";

const i18n = {
  en: {
    titles: {
      add: "Add Job Post",
      update: "Update Job Post",
      delete: "Delete Job Post",
      publish: "Publish Job Post",
      unpublish: "Unpublish Job Post",
      cancel: "Cancel Job Post",
    },
    descriptions: {
      add: "Add a new job post to your organization.",
      update: "Update an existing job post.",
      delete: "Are you sure you want to delete this job?",
      publish: "Are you sure you want to publish this job post?",
      unpublish: "Are you sure you want to unpublish this job post?",
      cancel: "Are you sure you want to cancel this job post?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
      selectJob: "Select Job",
      publish: "Publish",
      unpublish: "Unpublish",
      cancel: "Cancel",
    },
    messages: {
      created: "Job Post created successfully.",
      updated: "Job Post updated successfully.",
      deleted: "Job Post deleted successfully.",
      published: "Job Post published successfully.",
      unpublished: "Job Post unpublished successfully.",
      cancelled: "Job Post cancelled successfully.",
      failedCreate: "Failed to create job: ",
      failedUpdate: "Failed to update job: ",
      failedDelete: "Failed to delete job: ",
      failedPublish: "Failed to publish job: ",
      failedUnpublish: "Failed to unpublish job: ",
      failedCancel: "Failed to cancel job: ",
    },
  },
  links: {
    jobs: "/app/jobs",
    job: "/app/jobs/:id",
  },
};

export type CoreJobStruct = RouterOutputs["jobs"]["get"];
export type JobStruct = Pick<
  CoreJobStruct,
  "id" | "summary" | "scope" | "role" | "status" | "type"
> & {
  organization?: Pick<
    NonNullable<CoreJobStruct["organization"]>,
    "id" | "name"
  >;
};

export interface BaseJobFormProps
  extends Omit<
    DialogFormProps<JobPostFormProps, JobPostFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  showOrganization?: boolean;
  defaultValues?: Partial<JobPostFormValues>;
}

export interface AddJobPostProps extends BaseJobFormProps {
  onAdd?: (
    job: JobPostFormValues,
  ) => void | Promise<RouterOutputs["jobs"]["create"]>;
  onSuccess?: (job: RouterOutputs["jobs"]["create"]) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
  redirectToJob?: boolean;
}

export function AddJobPost({
  onAdd,
  onSuccess,
  onError,
  redirectToJob = true,
  ...props
}: PropsWithChildren<AddJobPostProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const createJobPostMutation = api.jobs.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.jobs.getMany.invalidate();
        toast.success(i18n.en.messages.created);
        if (redirectToJob) {
          router.push(i18n.links.job.replace(":id", result.id));
        }
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCreate + error.message);
      }
    },
  });

  return (
    <DialogForm<OrganizationJobPostFormProps, JobPostFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.titles.add}
      type="add"
      {...props}
      showOrganization
      Component={OrganizationJobPostForm}
      onSubmit={useCallback<NonNullable<JobPostFormProps["onSubmit"]>>(
        async (values) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            await createJobPostMutation.mutateAsync({
              organizationId: values.organizationId,
              summary: values.summary,
              scope: values.description,
              role: values.role,
              type: values.type,
            });
          }
        },
        [createJobPostMutation, onAdd],
      )}
    />
  );
}

export interface UpdateJobPostProps extends BaseJobFormProps {
  job?: JobStruct;
  onUpdate?: (
    job: JobPostFormValues,
  ) => void | Promise<RouterOutputs["jobs"]["update"]>;
  onSuccess?: (job: RouterOutputs["jobs"]["update"]) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateJobPost({
  job,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateJobPostProps>) {
  const utils = api.useUtils();
  const updateJobPostMutation = api.jobs.update.useMutation({
    onSuccess: async (result) => {
      await utils.jobs.get.invalidate({
        id: job?.id ?? "",
      });
      await utils.jobs.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.updated);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedUpdate + error.message);
      }
    },
  });

  return (
    <DialogForm<OrganizationJobPostFormProps, JobPostFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={OrganizationJobPostForm}
      defaultValues={{
        organizationId: job?.organization?.id,
        summary: job?.summary,
        description: job?.scope,
        role: job?.role,
        type: job?.type,
        ...defaultValues,
      }}
      onSubmit={useCallback<NonNullable<JobPostFormProps["onSubmit"]>>(
        async (values) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateJobPostMutation.mutateAsync({
              id: job?.id ?? "",
              data: {
                summary: values.summary,
                scope: values.description,
                role: values.role,
              },
            });
          }
        },
        [updateJobPostMutation, job, onUpdate],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export interface DeleteJobPostProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  job?: JobStruct;
  reroute?: boolean;
  onDelete?: (
    job: RouterInputs["jobs"]["delete"],
  ) => void | Promise<RouterOutputs["jobs"]["delete"]>;
  onSuccess?: (job: RouterOutputs["jobs"]["delete"]) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteJobPost({
  job,
  reroute,
  onDelete,
  onSuccess,
  onError,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteJobPostProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteJobPostMutation = api.jobs.delete.useMutation({
    onSuccess: async (result) => {
      await utils.jobs.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.jobs);
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteJobPostMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: job?.id ?? "" });
        } else {
          await deleteJobPostMutation.mutateAsync({ id: job?.id ?? "" });
        }
      }, [deleteJobPostMutation, job, onDelete])}
    >
      {props.children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

// New job status actions

export interface PublishJobPostProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  job?: JobStruct;
  onPublish?: (
    job: RouterInputs["jobs"]["organization"]["publish"],
  ) => void | Promise<RouterOutputs["jobs"]["organization"]["publish"]>;
  onSuccess?: (
    job: RouterOutputs["jobs"]["organization"]["publish"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function PublishJobPost({
  job,
  onPublish,
  onSuccess,
  onError,
  variant = "primary",
  ...props
}: PropsWithChildren<PublishJobPostProps>) {
  const utils = api.useUtils();
  const publishJobPostMutation = api.jobs.organization.publish.useMutation({
    onSuccess: async (result) => {
      await utils.jobs.getMany.invalidate();
      await utils.jobs.get.invalidate({ id: job?.id ?? "" });
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.published);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedPublish + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.publish}
      description={i18n.en.descriptions.publish}
      action={i18n.en.actions.publish}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={publishJobPostMutation.isPending || job?.status !== "DRAFT"}
      variant={variant}
      onClick={useCallback(async () => {
        if (onPublish) {
          await onPublish({ id: job?.id ?? "" });
        } else {
          await publishJobPostMutation.mutateAsync({ id: job?.id ?? "" });
        }
      }, [publishJobPostMutation, job, onPublish])}
    >
      {props.children ?? (
        <Button variant={variant} className="flex items-center gap-1.5">
          <Check size="16" />
          {i18n.en.actions.publish}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export interface UnpublishJobPostProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  job?: JobStruct;
  onUnpublish?: (
    job: RouterInputs["jobs"]["organization"]["unpublish"],
  ) => void | Promise<RouterOutputs["jobs"]["organization"]["unpublish"]>;
  onSuccess?: (
    job: RouterOutputs["jobs"]["organization"]["unpublish"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UnpublishJobPost({
  job,
  onUnpublish,
  onSuccess,
  onError,
  variant = "outline",
  ...props
}: PropsWithChildren<UnpublishJobPostProps>) {
  const utils = api.useUtils();
  const unpublishJobPostMutation = api.jobs.organization.unpublish.useMutation({
    onSuccess: async (result) => {
      await utils.jobs.getMany.invalidate();
      await utils.jobs.get.invalidate({ id: job?.id ?? "" });
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.unpublished);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedUnpublish + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.unpublish}
      description={i18n.en.descriptions.unpublish}
      action={i18n.en.actions.unpublish}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={
        unpublishJobPostMutation.isPending || job?.status !== "PUBLISHED"
      }
      variant={variant}
      onClick={useCallback(async () => {
        if (onUnpublish) {
          await onUnpublish({ id: job?.id ?? "" });
        } else {
          await unpublishJobPostMutation.mutateAsync({ id: job?.id ?? "" });
        }
      }, [unpublishJobPostMutation, job, onUnpublish])}
    >
      {props.children ?? (
        <Button variant={variant} className="flex items-center gap-1.5">
          <Clock size="16" />
          {i18n.en.actions.unpublish}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export interface CloseJobPostProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  job?: JobStruct;
  onClose?: (
    job: RouterInputs["jobs"]["organization"]["cancel"],
  ) => void | Promise<RouterOutputs["jobs"]["organization"]["cancel"]>;
  onSuccess?: (
    job: RouterOutputs["jobs"]["organization"]["cancel"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function CloseJobPost({
  job,
  onClose,
  onSuccess,
  onError,
  variant = "destructive",
  ...props
}: PropsWithChildren<CloseJobPostProps>) {
  const utils = api.useUtils();
  const closeJobPostMutation = api.jobs.organization.cancel.useMutation({
    onSuccess: async (result) => {
      await utils.jobs.getMany.invalidate();
      await utils.jobs.get.invalidate({ id: job?.id ?? "" });
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.cancelled);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCancel + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.cancel}
      description={i18n.en.descriptions.cancel}
      action={i18n.en.actions.cancel}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={
        closeJobPostMutation.isPending ||
        !["DRAFT", "PUBLISHED", "CLOSED"].includes(job?.status ?? "")
      }
      variant={variant}
      onClick={useCallback(async () => {
        if (onClose) {
          await onClose({ id: job?.id ?? "" });
        } else {
          await closeJobPostMutation.mutateAsync({ id: job?.id ?? "" });
        }
      }, [closeJobPostMutation, job, onClose])}
    >
      {props.children ?? (
        <Button variant={variant} className="flex items-center gap-1.5">
          <XCircle size="16" />
          {i18n.en.actions.cancel}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function JobPostMenu({
  loading,
  job,
  onUpdate,
  onPublish,
  onUnpublish,
  onClose,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    job?: JobStruct;
    onUpdate?: UpdateJobPostProps["onUpdate"];
    onDelete?: DeleteJobPostProps["onDelete"];
    onPublish?: PublishJobPostProps["onPublish"];
    onUnpublish?: UnpublishJobPostProps["onUnpublish"];
    onClose?: CloseJobPostProps["onClose"];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            disabled={loading}
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateJobPost
            showOrganization
            onOpenChange={handleDialogOpenChange}
            job={job}
            onUpdate={onUpdate}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateJobPost>

          {job?.status === JobPostStatus.DRAFT && (
            <PublishJobPost
              onOpenChange={handleDialogOpenChange}
              job={job}
              onPublish={onPublish}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.publish}
              </DropdownMenuItem>
            </PublishJobPost>
          )}

          {job?.status === JobPostStatus.PUBLISHED && (
            <UnpublishJobPost
              onOpenChange={handleDialogOpenChange}
              job={job}
              onUnpublish={onUnpublish}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.unpublish}
              </DropdownMenuItem>
            </UnpublishJobPost>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {[
            JobPostStatus.DRAFT,
            JobPostStatus.PUBLISHED,
            JobPostStatus.FILLED,
            // @ts-expect-error ts(2365)
          ].includes(job?.status ?? "") && (
            <CloseJobPost
              onOpenChange={handleDialogOpenChange}
              job={job}
              onClose={onClose}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.cancel}
              </DropdownMenuItem>
            </CloseJobPost>
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
