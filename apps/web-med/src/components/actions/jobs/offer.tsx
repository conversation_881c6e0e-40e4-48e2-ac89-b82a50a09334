"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterError, RouterInputs, RouterOutputs } from "@/api";
import type {
  OfferFormProps,
  OfferFormValues,
} from "@/components/forms/OfferForm";

import { OfferStatus } from "@/api";
import { api } from "@/api/client";
import OfferForm from "@/components/forms/OfferForm";

const i18n = {
  en: {
    titles: {
      add: "Add An Offer",
      update: "Update The Offer",
      delete: "Delete The Offer",
      send: "Send Offer",
      accept: "Accept The Offer",
      reject: "Reject The Offer",
      withdraw: "Withdraw The Offer",
    },
    descriptions: {
      add: "Add a new offer.",
      update: "Update an existing offer.",
      delete: "Are you sure you want to delete this offer?",
      accept: "Accept this offer.",
      reject: "Reject this offer.",
      send: "Send this offer.",
      withdraw: "Are you sure you want to withdraw this offer?",
    },
    actions: {
      label: "Actions",
      send: "Send Offer",
      add: "Add Offer",
      update: "Update",
      delete: "Delete",
      accept: "Accept",
      reject: "Reject",
      withdraw: "Withdraw",
      open: "Open Offer",
      selectOffer: "Select Offer",
    },
    messages: {
      created: "Offer created successfully.",
      updated: "Offer updated successfully.",
      deleted: "Offer deleted successfully.",
      accepted: "Offer accepted successfully.",
      rejected: "Offer rejected successfully.",
      withdrawn: "Offer withdrawn successfully.",
      failedCreate: "Failed to create offer: ",
      failedUpdate: "Failed to update offer: ",
      failedDelete: "Failed to delete offer: ",
      failedAccept: "Failed to accept offer: ",
      failedReject: "Failed to reject offer: ",
      failedWithdraw: "Failed to withdraw offer: ",
    },
  },
  links: {
    offers: "/app/offers/[id]",
  },
};

export type CoreOfferStruct = RouterOutputs["offers"]["get"];
export type OfferStruct = Pick<
  CoreOfferStruct,
  "id" | "notes" | "status" | "expiresAt" | "createdAt" | "updatedAt"
> & {
  provider?: Pick<NonNullable<CoreOfferStruct["provider"]>, "id" | "status">;
  job?: Pick<NonNullable<CoreOfferStruct["job"]>, "id" | "status">;
  organization?: Pick<NonNullable<CoreOfferStruct["organization"]>, "id">;
};

export interface BaseOfferFormProps
  extends Omit<
    DialogFormProps<OfferFormProps, OfferFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  providerId?: string;
  jobId?: string;
  organizationId?: string;
  defaultValues?: Partial<OfferFormValues>;
}

export interface AddOfferProps extends BaseOfferFormProps {
  onAdd?: (offer: OfferFormValues) => void | Promise<void>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["create"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function AddOffer({
  onAdd,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<AddOfferProps>) {
  const utils = api.useUtils();
  const createOfferMutation = api.offers.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      await utils.offers.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<OfferFormProps, OfferFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.actions.add}
      type="add"
      {...props}
      Component={OfferForm}
      onSubmit={useCallback(
        async (values: OfferFormValues) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            await createOfferMutation.mutateAsync({
              notes: values.notes,
              expiresAt: values.expiresAt,
              status: OfferStatus.PENDING,
              jobId: values.jobId,
              providerId: values.providerId,
            });
          }
        },
        [createOfferMutation, onAdd],
      )}
    />
  );
}

export interface SendOfferProps extends BaseOfferFormProps {
  onSend?: (offer: OfferFormValues) => void | Promise<void>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["create"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
  job?: Pick<NonNullable<RouterOutputs["jobs"]["get"]>, "id">;
  provider?: Pick<NonNullable<RouterOutputs["providers"]["get"]>, "id">;
}

export function SendOffer({
  onSend,
  onSuccess,
  onError,
  job,
  provider,
  ...props
}: PropsWithChildren<SendOfferProps>) {
  return (
    <AddOffer
      title={i18n.en.titles.send}
      description={i18n.en.descriptions.send}
      label={i18n.en.actions.send}
      onSuccess={onSuccess}
      onError={onError}
      onAdd={useCallback(
        async (values: OfferFormValues) => {
          if (onSend) {
            await onSend({
              ...values,
              jobId: job?.id ?? "",
              providerId: provider?.id ?? "",
            });
          }
        },
        [onSend, job?.id, provider?.id],
      )}
      {...props}
    />
  );
}

export interface UpdateOfferProps extends BaseOfferFormProps {
  offer?: OfferStruct;
  onUpdate?: (offer: OfferFormValues) => void | Promise<void>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["update"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function UpdateOffer({
  offer,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateOfferProps>) {
  const utils = api.useUtils();
  const updateOfferMutation = api.offers.update.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      await utils.offers.get.invalidate({ id: offer?.id });
      await utils.offers.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<OfferFormProps, OfferFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={OfferForm}
      defaultValues={{
        notes: offer?.notes ?? "",
        expiresAt: offer?.expiresAt ?? undefined,
        jobId: offer?.job?.id,
        providerId: offer?.provider?.id,
        ...defaultValues,
      }}
      onSubmit={useCallback(
        async (values: OfferFormValues) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateOfferMutation.mutateAsync({
              id: offer?.id ?? "",
              data: {
                notes: values.notes,
                expiresAt: values.expiresAt,
              },
            });
          }
        },
        [updateOfferMutation, offer, onUpdate],
      )}
    />
  );
}

export interface WithdrawOfferProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  onWithdraw?: (
    offer: RouterInputs["offers"]["organization"]["withdraw"],
  ) =>
    | void
    | Promise<void>
    | Promise<NonNullable<RouterOutputs["offers"]["organization"]["withdraw"]>>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["organization"]["withdraw"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
  offer?: Pick<OfferStruct, "id">;
}

export function WithdrawOffer({
  onWithdraw,
  onSuccess,
  onError,
  offer,
  ...props
}: PropsWithChildren<WithdrawOfferProps>) {
  const utils = api.useUtils();
  const withdrawOfferMutation = api.offers.organization.withdraw.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.offers.get.invalidate({ id: offer?.id });
        await utils.offers.getMany.invalidate();
        toast.success(i18n.en.messages.withdrawn);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedWithdraw + error.message);
      }
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.withdraw}
      description={i18n.en.descriptions.withdraw}
      label={i18n.en.actions.withdraw}
      type="button"
      {...props}
      onClick={useCallback(async () => {
        if (onWithdraw) {
          await onWithdraw({
            id: offer?.id ?? "",
          });
        } else {
          await withdrawOfferMutation.mutateAsync({
            id: offer?.id ?? "",
          });
        }
      }, [onWithdraw, offer?.id, withdrawOfferMutation])}
    />
  );
}

export interface DeleteOfferProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  offer?: OfferStruct;
  onDelete?: (offer: RouterOutputs["offers"]["delete"]) => void | Promise<void>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function DeleteOffer({
  onDelete,
  onSuccess,
  onError,
  offer,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteOfferProps>) {
  const utils = api.useUtils();
  const deleteOfferMutation = api.offers.delete.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        await utils.offers.getMany.invalidate();
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      {...props}
      disabled={deleteOfferMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: offer?.id ?? "" });
        } else {
          await deleteOfferMutation.mutateAsync({
            id: offer?.id ?? "",
          });
        }
      }, [deleteOfferMutation, offer, onDelete])}
    />
  );
}

export interface UpdateOfferStatusProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  offer?: OfferStruct;
  status: OfferStatus;
  onUpdate?: (
    offer: RouterInputs["offers"]["update"],
  ) => void | Promise<void> | Promise<RouterOutputs["offers"]["update"]>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["update"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function UpdateOfferStatus({
  status,
  onSuccess,
  onError,
  onUpdate,
  offer,
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateOfferStatusProps>) {
  const utils = api.useUtils();
  const updateStatusMutation = api.offers.update.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      await utils.offers.get.invalidate({ id: offer?.id });
      await utils.offers.getMany.invalidate();
      toast.success(
        status === OfferStatus.ACCEPTED
          ? i18n.en.messages.accepted
          : status === OfferStatus.REJECTED
            ? i18n.en.messages.rejected
            : i18n.en.messages.withdrawn,
      );
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(
        (status === OfferStatus.ACCEPTED
          ? i18n.en.messages.failedAccept
          : status === OfferStatus.REJECTED
            ? i18n.en.messages.failedReject
            : i18n.en.messages.failedWithdraw) + error.message,
      );
    },
  });

  return (
    <DialogConfirmation
      title={
        status === OfferStatus.ACCEPTED
          ? i18n.en.titles.accept
          : status === OfferStatus.REJECTED
            ? i18n.en.titles.reject
            : i18n.en.titles.withdraw
      }
      description={
        status === OfferStatus.ACCEPTED
          ? i18n.en.descriptions.accept
          : status === OfferStatus.REJECTED
            ? i18n.en.descriptions.reject
            : i18n.en.descriptions.withdraw
      }
      {...props}
      disabled={updateStatusMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onUpdate) {
          await onUpdate({
            id: offer?.id ?? "",
            data: {
              status,
            },
          });
        } else if (status === OfferStatus.ACCEPTED) {
          await updateStatusMutation.mutateAsync({
            id: offer?.id ?? "",
            data: {
              status,
            },
          });
        } else if (status === OfferStatus.REJECTED) {
          await updateStatusMutation.mutateAsync({
            id: offer?.id ?? "",
            data: {
              status: OfferStatus.REJECTED,
            },
          });
        } else {
          await updateStatusMutation.mutateAsync({
            id: offer?.id ?? "",
            data: {
              status: OfferStatus.WITHDRAWN,
            },
          });
        }
      }, [status, updateStatusMutation, offer?.id, onUpdate])}
    />
  );
}

export interface AcceptOfferProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  offer?: OfferStruct;
  onAccept?: (
    offer: RouterInputs["offers"]["provider"]["accept"],
  ) =>
    | void
    | Promise<void>
    | Promise<RouterOutputs["offers"]["provider"]["accept"]>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["provider"]["accept"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function AcceptOffer({
  offer,
  onAccept,
  ...props
}: PropsWithChildren<AcceptOfferProps>) {
  return (
    <UpdateOfferStatus
      status={OfferStatus.ACCEPTED}
      offer={offer}
      onUpdate={onAccept}
      {...props}
    />
  );
}

export interface RejectOfferProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  offer?: OfferStruct;
  onReject?: (
    offer: RouterInputs["offers"]["provider"]["reject"],
  ) =>
    | void
    | Promise<void>
    | Promise<RouterOutputs["offers"]["provider"]["reject"]>;
  onSuccess?: (
    offer: RouterOutputs["offers"]["provider"]["reject"],
  ) => void | Promise<void>;
  onError?: (error: RouterError) => void | Promise<void>;
}

export function RejectOffer({
  offer,
  onReject,
  ...props
}: PropsWithChildren<RejectOfferProps>) {
  return (
    <UpdateOfferStatus
      status={OfferStatus.REJECTED}
      offer={offer}
      onUpdate={onReject}
      {...props}
    />
  );
}

export function OfferMenu({
  loading = false,
  offer,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    offer?: OfferStruct;
    onUpdate?: UpdateOfferProps["onUpdate"];
    onDelete?: DeleteOfferProps["onDelete"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.offers.replace("[id]", offer?.id ?? ""));
  }, [router, offer?.id]);

  const isPending = offer?.status === OfferStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onSelect={handleOpen}>
            {i18n.en.actions.open}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <UpdateOffer
            onUpdate={onUpdate}
            onOpenChange={handleDialogOpenChange}
            offer={offer}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateOffer>
          {isPending && (
            <>
              <AcceptOffer onOpenChange={handleDialogOpenChange} offer={offer}>
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.accept}
                </DropdownMenuItem>
              </AcceptOffer>
              <RejectOffer onOpenChange={handleDialogOpenChange} offer={offer}>
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.reject}
                </DropdownMenuItem>
              </RejectOffer>
              <WithdrawOffer
                onOpenChange={handleDialogOpenChange}
                offer={offer}
              >
                <DropdownMenuItem
                  onSelect={handleItemSelect}
                  className="text-red-600 dark:text-red-400"
                >
                  {i18n.en.actions.withdraw}
                </DropdownMenuItem>
              </WithdrawOffer>
            </>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteOffer
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            offer={offer}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              className="text-red-600 dark:text-red-400"
            >
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteOffer>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Provider-specific actions
export function ProviderOfferMenu({
  loading = false,
  offer,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    offer?: OfferStruct;
    onDelete?: DeleteOfferProps["onDelete"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.offers.replace("[id]", offer?.id ?? ""));
  }, [router, offer?.id]);

  const isPending = offer?.status === OfferStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onSelect={handleOpen}>
            {i18n.en.actions.open}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {isPending && (
            <>
              <AcceptOffer onOpenChange={handleDialogOpenChange} offer={offer}>
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.accept}
                </DropdownMenuItem>
              </AcceptOffer>
              <RejectOffer onOpenChange={handleDialogOpenChange} offer={offer}>
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.reject}
                </DropdownMenuItem>
              </RejectOffer>
            </>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteOffer
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            offer={offer}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              className="text-red-600 dark:text-red-400"
            >
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteOffer>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Organization-specific actions
export function OrganizationOfferMenu({
  loading = false,
  offer,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    offer?: OfferStruct;
    onUpdate?: UpdateOfferProps["onUpdate"];
    onDelete?: DeleteOfferProps["onDelete"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.offers.replace("[id]", offer?.id ?? ""));
  }, [router, offer?.id]);

  const isPending = offer?.status === OfferStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuItem onSelect={handleOpen}>
            {i18n.en.actions.open}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <UpdateOffer
            onUpdate={onUpdate}
            onOpenChange={handleDialogOpenChange}
            offer={offer}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateOffer>
          {isPending && (
            <WithdrawOffer onOpenChange={handleDialogOpenChange} offer={offer}>
              <DropdownMenuItem
                onSelect={handleItemSelect}
                className="text-red-600 dark:text-red-400"
              >
                {i18n.en.actions.withdraw}
              </DropdownMenuItem>
            </WithdrawOffer>
          )}
        </DropdownMenuGroup>
        {/* <DropdownMenuSeparator /> */}
        {/* <DropdownMenuGroup>
          <DeleteOffer
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            offer={offer}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              className="text-red-600 dark:text-red-400"
            >
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteOffer>
        </DropdownMenuGroup> */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
