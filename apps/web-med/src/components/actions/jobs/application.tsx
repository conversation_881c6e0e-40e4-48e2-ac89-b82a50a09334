"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  ApplicationFormProps,
  ApplicationFormValues,
} from "@/components/forms/ApplicationForm";

import { ApplicationStatus } from "@/api";
import { api } from "@/api/client";
import ApplicationForm from "@/components/forms/ApplicationForm";

import { useUser } from "../../contexts/User";

const i18n = {
  en: {
    unknownApplication: "Unknown Application",
    titles: {
      apply: "Apply for Job",
      delete: "Delete Application",
      review: "Review Application",
      accept: "Accept Application",
      reject: "Reject Application",
      withdraw: "Withdraw Application",
    },
    descriptions: {
      apply: "Submit your application for this job.",
      delete: "Are you sure you want to delete this application?",
      review: "Mark this application as under review.",
      accept: "Accept this application.",
      reject: "Reject this application.",
      withdraw: "Withdraw your application from consideration.",
    },
    actions: {
      apply: "Apply",
      open: "Open Application",
      delete: "Delete Application",
      review: "Review Application",
      accept: "Accept Application",
      reject: "Reject Application",
      withdraw: "Withdraw Application",
      selectApplication: "Select Application",
    },
    messages: {
      applied: "Application submitted successfully",
      deleted: "Application deleted successfully",
      reviewed: "Application is under review",
      accepted: "Application accepted successfully",
      rejected: "Application rejected successfully",
      withdrawn: "Application withdrawn successfully",
      failedApply: "Failed to submit application: ",
      failedDelete: "Failed to delete application: ",
      failedReview: "Failed to review application: ",
      failedAccept: "Failed to accept application: ",
      failedReject: "Failed to reject application: ",
      failedWithdraw: "Failed to withdraw application: ",
    },
  },
  links: {
    applications: "/app/applications/[id]",
  },
};

export type CoreApplicationStruct = RouterOutputs["applications"]["get"];
export type ApplicationStruct = Pick<
  RouterOutputs["applications"]["getMany"]["items"][number],
  "id" | "status" | "createdAt" | "updatedAt"
> & {
  provider?: {
    id: string;
    person?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  };
  job?: {
    id: string;
    summary: string;
  };
};

export interface BaseApplicationFormProps
  extends Omit<
    DialogFormProps<ApplicationFormProps, ApplicationFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  providerId?: string;
  jobId?: string;
  defaultValues?: Partial<ApplicationFormValues>;
}

export interface ApplyForJobProps extends BaseApplicationFormProps {
  onApply?: (
    application: ApplicationFormValues,
  ) => void | Promise<RouterOutputs["applications"]["provider"]["create"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["provider"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ApplyForJob({
  onApply,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<ApplyForJobProps>) {
  const utils = api.useUtils();
  const applyMutation = api.applications.provider.create.useMutation({
    onSuccess: async (result) => {
      await utils.applications.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.applied);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedApply + error.message);
      }
    },
  });

  return (
    <DialogForm<ApplicationFormProps, ApplicationFormValues>
      title={i18n.en.titles.apply}
      description={i18n.en.descriptions.apply}
      label={i18n.en.actions.apply}
      type="add"
      {...props}
      Component={ApplicationForm}
      onSubmit={useCallback(
        async (values: ApplicationFormValues) => {
          if (onApply) {
            await onApply(values);
          } else {
            await applyMutation.mutateAsync({
              jobId: values.jobId,
              notes: values.message,
            });
          }
        },
        [applyMutation, onApply],
      )}
    />
  );
}

export interface DeleteApplicationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  application?: ApplicationStruct;
  reroute?: boolean;
  onDelete?: (
    application: RouterInputs["applications"]["delete"],
  ) => void | Promise<void> | Promise<RouterOutputs["applications"]["delete"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteApplication({
  onDelete,
  onSuccess,
  onError,
  application,
  reroute,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteApplicationProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteApplicationMutation = api.applications.delete.useMutation({
    onSuccess: async (result) => {
      await utils.applications.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.applications.replace("[id]", ""));
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteApplicationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: application?.id ?? "" });
        } else {
          await deleteApplicationMutation.mutateAsync({
            id: application?.id ?? "",
          });
        }
      }, [deleteApplicationMutation, application, onDelete])}
    />
  );
}

// Provider-specific actions
export interface WithdrawApplicationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  application?: ApplicationStruct;
  onWithdraw?: (
    application: RouterInputs["applications"]["provider"]["withdraw"],
  ) =>
    | void
    | Promise<void>
    | Promise<RouterOutputs["applications"]["provider"]["withdraw"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["provider"]["withdraw"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function WithdrawApplication({
  onWithdraw,
  onSuccess,
  onError,
  application,
  variant = "outline",
  ...props
}: PropsWithChildren<WithdrawApplicationProps>) {
  const utils = api.useUtils();
  const withdrawApplicationMutation =
    api.applications.provider.withdraw.useMutation({
      onSuccess: async (result) => {
        await utils.applications.get.invalidate({ id: application?.id });
        await utils.applications.getMany.invalidate();
        if (onSuccess) {
          await onSuccess(result);
        } else {
          toast.success(i18n.en.messages.withdrawn);
        }
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else {
          toast.error(i18n.en.messages.failedWithdraw + error.message);
        }
      },
    });

  return (
    <DialogConfirmation
      title={i18n.en.titles.withdraw}
      description={i18n.en.descriptions.withdraw}
      action={i18n.en.actions.withdraw}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={withdrawApplicationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onWithdraw) {
          await onWithdraw({
            id: application?.id ?? "",
          });
        } else {
          await withdrawApplicationMutation.mutateAsync({
            id: application?.id ?? "",
          });
        }
      }, [withdrawApplicationMutation, application, onWithdraw])}
    />
  );
}

// Organization-specific actions
export interface ReviewApplicationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  application?: ApplicationStruct;
  onReview?: (
    application: RouterInputs["applications"]["update"],
  ) => void | Promise<void> | Promise<RouterOutputs["applications"]["update"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function ReviewApplication({
  onReview,
  onSuccess,
  onError,
  application,
  variant = "outline",
  ...props
}: PropsWithChildren<ReviewApplicationProps>) {
  const utils = api.useUtils();
  const reviewApplicationMutation = api.applications.update.useMutation({
    onSuccess: async (result) => {
      await utils.applications.get.invalidate({ id: application?.id });
      await utils.applications.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.reviewed);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedReview + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.review}
      description={i18n.en.descriptions.review}
      action={i18n.en.actions.review}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={reviewApplicationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onReview) {
          await onReview({
            id: application?.id ?? "",
            data: {
              status: ApplicationStatus.PENDING,
            },
          });
        } else {
          await reviewApplicationMutation.mutateAsync({
            id: application?.id ?? "",
            data: {
              status: ApplicationStatus.PENDING,
            },
          });
        }
      }, [reviewApplicationMutation, application, onReview])}
    />
  );
}

export interface AcceptApplicationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  application?: Pick<ApplicationStruct, "id">;
  onAccept?: (
    application: RouterInputs["applications"]["organization"]["approve"],
  ) =>
    | void
    | Promise<void>
    | Promise<RouterOutputs["applications"]["organization"]["approve"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["organization"]["approve"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AcceptApplication({
  onAccept,
  onSuccess,
  onError,
  application,
  variant = "outline",
  ...props
}: PropsWithChildren<AcceptApplicationProps>) {
  const utils = api.useUtils();
  const acceptApplicationMutation =
    api.applications.organization.approve.useMutation({
      onSuccess: async (result) => {
        await utils.applications.get.invalidate({ id: application?.id });
        await utils.applications.getMany.invalidate();
        if (onSuccess) {
          await onSuccess(result);
        } else {
          toast.success(i18n.en.messages.accepted);
        }
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else {
          toast.error(i18n.en.messages.failedAccept + error.message);
        }
      },
    });

  return (
    <DialogConfirmation
      title={i18n.en.titles.accept}
      description={i18n.en.descriptions.accept}
      action={i18n.en.actions.accept}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={acceptApplicationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onAccept) {
          await onAccept({
            id: application?.id ?? "",
          });
        } else {
          await acceptApplicationMutation.mutateAsync({
            id: application?.id ?? "",
          });
        }
      }, [acceptApplicationMutation, application, onAccept])}
    />
  );
}

export interface RejectApplicationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  application?: Pick<ApplicationStruct, "id">;
  onReject?: (
    application: RouterInputs["applications"]["organization"]["reject"],
  ) =>
    | void
    | Promise<void>
    | Promise<RouterOutputs["applications"]["organization"]["reject"]>;
  onSuccess?: (
    application: RouterOutputs["applications"]["organization"]["reject"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function RejectApplication({
  onReject,
  onSuccess,
  onError,
  application,
  variant = "destructive",
  ...props
}: PropsWithChildren<RejectApplicationProps>) {
  const utils = api.useUtils();
  const rejectApplicationMutation =
    api.applications.organization.reject.useMutation({
      onSuccess: async (result) => {
        await utils.applications.get.invalidate({ id: application?.id });
        await utils.applications.getMany.invalidate();
        if (onSuccess) {
          await onSuccess(result);
        } else {
          toast.success(i18n.en.messages.rejected);
        }
      },
      onError: async (error) => {
        if (onError) {
          await onError(error);
        } else {
          toast.error(i18n.en.messages.failedReject + error.message);
        }
      },
    });

  return (
    <DialogConfirmation
      title={i18n.en.titles.reject}
      description={i18n.en.descriptions.reject}
      action={i18n.en.actions.reject}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={rejectApplicationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onReject) {
          await onReject({
            id: application?.id ?? "",
          });
        } else {
          await rejectApplicationMutation.mutateAsync({
            id: application?.id ?? "",
          });
        }
      }, [rejectApplicationMutation, application, onReject])}
    />
  );
}

export function ApplicationMenu({
  loading = false,
  application,
  onDelete,
  onReview,
  onAccept,
  onReject,
  onWithdraw,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    application?: ApplicationStruct;
    onDelete?: DeleteApplicationProps["onDelete"];
    onReview?: ReviewApplicationProps["onReview"];
    onAccept?: AcceptApplicationProps["onAccept"];
    onReject?: RejectApplicationProps["onReject"];
    onWithdraw?: WithdrawApplicationProps["onWithdraw"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.applications.replace("[id]", application?.id ?? ""));
  }, [router, application?.id]);

  const isPending = application?.status === ApplicationStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="ghost"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuItem onSelect={handleOpen}>
          {i18n.en.actions.open}
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        {/* Organization actions */}
        {isPending && (
          <ReviewApplication
            onReview={onReview}
            onOpenChange={handleDialogOpenChange}
            application={application}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.review}
            </DropdownMenuItem>
          </ReviewApplication>
        )}

        {isPending && (
          <>
            <AcceptApplication
              onAccept={onAccept}
              onOpenChange={handleDialogOpenChange}
              application={application}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.accept}
              </DropdownMenuItem>
            </AcceptApplication>

            <RejectApplication
              onReject={onReject}
              onOpenChange={handleDialogOpenChange}
              application={application}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.reject}
              </DropdownMenuItem>
            </RejectApplication>
          </>
        )}

        {/* Provider actions */}
        {isPending && (
          <WithdrawApplication
            onWithdraw={onWithdraw}
            onOpenChange={handleDialogOpenChange}
            application={application}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.withdraw}
            </DropdownMenuItem>
          </WithdrawApplication>
        )}

        <DropdownMenuSeparator />
        <DeleteApplication
          onDelete={onDelete}
          onOpenChange={handleDialogOpenChange}
          application={application}
        >
          <DropdownMenuItem
            onSelect={handleItemSelect}
            className="text-red-600 dark:text-red-400"
          >
            {i18n.en.actions.delete}
          </DropdownMenuItem>
        </DeleteApplication>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function ProviderApplicationMenu({
  loading = false,
  application,
  onWithdraw,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    application?: ApplicationStruct;
    onDelete?: DeleteApplicationProps["onDelete"];
    onWithdraw?: WithdrawApplicationProps["onWithdraw"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.applications.replace("[id]", application?.id ?? ""));
  }, [router, application?.id]);

  const isPending = application?.status === ApplicationStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="ghost"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuItem onSelect={handleOpen}>
          {i18n.en.actions.open}
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        {/* Provider actions */}
        {isPending && (
          <WithdrawApplication
            onWithdraw={onWithdraw}
            onOpenChange={handleDialogOpenChange}
            application={application}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.withdraw}
            </DropdownMenuItem>
          </WithdrawApplication>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function OrganizationApplicationMenu({
  loading = false,
  application,
  onDelete,
  onReview,
  onAccept,
  onReject,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    application?: ApplicationStruct;
    onDelete?: DeleteApplicationProps["onDelete"];
    onReview?: ReviewApplicationProps["onReview"];
    onAccept?: AcceptApplicationProps["onAccept"];
    onReject?: RejectApplicationProps["onReject"];
  } & ButtonProps
>) {
  const { isInternal } = useUser();
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.applications.replace("[id]", application?.id ?? ""));
  }, [router, application?.id]);

  const isPending = application?.status === ApplicationStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="ghost"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuItem onSelect={handleOpen} asChild>
            <Link
              href={i18n.links.applications.replace(
                ":id",
                application?.id ?? "",
              )}
            >
              View Application
            </Link>
          </DropdownMenuItem>

          {isPending ? (
            <>
              <DropdownMenuSeparator />
              <AcceptApplication
                onAccept={onAccept}
                onOpenChange={handleDialogOpenChange}
                application={application}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.accept}
                </DropdownMenuItem>
              </AcceptApplication>
              <RejectApplication
                onReject={onReject}
                onOpenChange={handleDialogOpenChange}
                application={application}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  <span className="text-red-600 dark:text-red-400">
                    {i18n.en.actions.reject}
                  </span>
                </DropdownMenuItem>
              </RejectApplication>
            </>
          ) : (
            <>
              <DropdownMenuSeparator />
              <ReviewApplication
                onReview={onReview}
                onOpenChange={handleDialogOpenChange}
                application={application}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.review}
                </DropdownMenuItem>
              </ReviewApplication>
            </>
          )}
        </DropdownMenuGroup>

        {/* {isInternal && (
          <>
            <DropdownMenuSeparator />
            <DeleteApplication
              onDelete={onDelete}
              onOpenChange={handleDialogOpenChange}
              application={application}
            >
              <DropdownMenuItem
                onSelect={handleItemSelect}
                className="text-red-600 dark:text-red-400"
              >
                {i18n.en.actions.delete}
              </DropdownMenuItem>
            </DeleteApplication>
          </>
        )} */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
