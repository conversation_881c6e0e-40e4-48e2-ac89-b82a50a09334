"use client";

import type { TRPCClientErrorLike } from "@trpc/react-query";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs, UserRole } from "@/api";
import type {
  OrganizationUserFormProps,
  UserFormProps,
  UserFormValues,
} from "@/components/forms/OrgUser";
import type { DialogConfirmationProps } from "@/ui/shared/DialogConfirmation";

import { api } from "@/api/client";
import { OrganizationUserForm } from "@/components/forms/OrgUser";
import DialogConfirmation from "@/ui/shared/DialogConfirmation";

const i18n = {
  en: {
    titles: {
      update: "Update User",
      delete: "Delete User",
    },
    descriptions: {
      delete: "Are you sure you want to delete this user?",
    },
    actions: {
      label: "Actions",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      updated: "User updated successfully.",
      failedUpdate: "Failed to update user: ",
      deleted: "User deleted successfully.",
      failedDelete: "Failed to delete user: ",
    },
  },
  links: {
    user: "/app/admin/users",
  },
};

export type UserStruct =
  | RouterOutputs["user"]["get"]
  | RouterOutputs["user"]["getMany"]["items"][number];

export type UpdateUserProps = Omit<
  DialogFormProps<OrganizationUserFormProps, UserFormValues>,
  "Component" | "onSubmit"
> & {
  user?: UserStruct;
  showOrganization?: boolean;
  defaultValues?: OrganizationUserFormProps["defaultValues"];
  onUpdate?: (
    user: UserFormValues & { id: string },
  ) => void | Promise<void> | Promise<RouterOutputs["user"]["update"]>;
};

export function UpdateUser({
  user,
  showOrganization = true,
  ...props
}: PropsWithChildren<UpdateUserProps>) {
  const utils = api.useUtils();
  const updateUserMutation = api.user.update.useMutation({
    onSuccess: async () => {
      await utils.user.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<UpdateUserProps, UserFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationUserForm}
      showOrganization={showOrganization}
      defaultValues={{
        ...props.defaultValues,
        userId: user?.id ?? "",
        organizationId: user?.organization?.id ?? "",
      }}
      onSubmit={useCallback(
        async (values: UserFormValues) => {
          await updateUserMutation.mutateAsync({
            id: user?.id ?? "",
            role: values.role,
            organizationId: values.organizationId,
          });
        },
        [updateUserMutation, user?.id],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export interface DeleteUserProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  user?: RouterOutputs["user"]["get"];
  reroute?: boolean;
  onDelete?: (
    user: RouterInputs["user"]["delete"],
  ) => void | Promise<RouterOutputs["user"]["delete"]>;
  onSuccess?: (user: RouterOutputs["user"]["delete"]) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}
export function DeleteUser({
  onDelete,
  onSuccess,
  onError,
  user,
  reroute,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteUserProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteUserMutation = api.user.delete.useMutation({
    onSuccess: async (result) => {
      await utils.user.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.user);
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteUserMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: user?.id ?? "" });
        } else {
          await deleteUserMutation.mutateAsync({
            id: user?.id ?? "",
          });
        }
      }, [deleteUserMutation, user, onDelete])}
    />
  );
}

export function UserMenu({
  loading = false,
  user,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    user?: UserStruct;
    onUpdate?: UpdateUserProps["onUpdate"];
    onDelete?: DeleteUserProps["onDelete"];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateUser
            user={user}
            onOpenChange={handleDialogOpenChange}
            onUpdate={onUpdate}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateUser>
          <DropdownMenuSeparator />
          <DeleteUser
            onOpenChange={handleDialogOpenChange}
            user={user}
            onDelete={onDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteUser>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
