"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { MoreVerticalIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, QualificationType, RouterOutputs } from "@/api";
import type {
  QualificationFormProps,
  QualificationFormValues,
} from "@/components/forms/QualificationForm";

import { QualificationStatus } from "@/api";
import { api } from "@/api/client";
import QualificationForm from "@/components/forms/QualificationForm";

import { useUser } from "../contexts/User";

const i18n = {
  en: {
    titles: {
      add: "Add A Qualification",
      update: "Update The Qualification",
      delete: "Delete The Qualification",
      approve: "Approve The Qualification",
      reject: "Reject The Qualification",
    },
    descriptions: {
      add: "Add a new qualification.",
      update: "Update an existing qualification.",
      delete: "Are you sure you want to delete this qualification?",
      approve: "Approve this qualification.",
      reject: "Reject this qualification.",
    },
    actions: {
      label: "Actions",
      add: "Add Qualification",
      update: "Update",
      delete: "Delete",
      approve: "Approve",
      reject: "Reject",
      selectQualification: "Select Qualification",
    },
    messages: {
      created: "Qualification created successfully.",
      updated: "Qualification updated successfully.",
      deleted: "Qualification deleted successfully.",
      approved: "Qualification approved successfully.",
      rejected: "Qualification rejected successfully.",
      failedCreate: "Failed to create qualification: ",
      failedUpdate: "Failed to update qualification: ",
      failedDelete: "Failed to delete qualification: ",
      failedApprove: "Failed to approve qualification: ",
      failedReject: "Failed to reject qualification: ",
    },
  },
  links: {
    qualifications: "/app/qualifications",
  },
};

export type CoreQualificationStruct = RouterOutputs["qualifications"]["get"];
export type QualificationStruct = Pick<
  CoreQualificationStruct,
  | "id"
  | "name"
  | "type"
  | "institution"
  | "identifier"
  | "state"
  | "country"
  | "startDate"
  | "endDate"
  | "status"
> & {
  provider?: Pick<NonNullable<CoreQualificationStruct["provider"]>, "id">;
};

export interface BaseQualificationFormProps
  extends Omit<
    DialogFormProps<QualificationFormProps, QualificationFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  providerId?: string;
  defaultValues?: Partial<QualificationFormValues>;
}

export interface AddQualificationProps extends BaseQualificationFormProps {
  onAdd?: (
    qualification: QualificationFormValues,
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    qualification: RouterOutputs["qualifications"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddQualification({
  onAdd,
  onSuccess,
  onError,
  ...props
}: PropsWithChildren<AddQualificationProps>) {
  const createQualificationMutation = api.qualifications.create.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      toast.success(i18n.en.messages.created);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<QualificationFormProps, QualificationFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.actions.add}
      type="add"
      {...props}
      Component={QualificationForm}
      onSubmit={useCallback<NonNullable<QualificationFormProps["onSubmit"]>>(
        async (values: QualificationFormValues) => {
          if (onAdd) {
            await onAdd(values);
          } else {
            await createQualificationMutation.mutateAsync({
              providerId: values.providerId ?? "",
              name: values.name,
              type: values.type as QualificationType,
              institution: values.institution,
              identifier: values.identifier,
              state: values.state,
              country: values.country,
              startDate: values.startDate,
              endDate: values.endDate,
              status: values.status as QualificationStatus,
            });
          }
        },
        [createQualificationMutation, onAdd],
      )}
    />
  );
}

export interface UpdateQualificationProps extends BaseQualificationFormProps {
  qualification?: QualificationStruct;
  onUpdate?: (
    qualification: QualificationFormValues,
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    qualification: RouterOutputs["qualifications"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateQualification({
  qualification,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  children,
  ...props
}: PropsWithChildren<UpdateQualificationProps>) {
  const updateQualificationMutation = api.qualifications.update.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      toast.success(i18n.en.messages.updated);
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<QualificationFormProps, QualificationFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={QualificationForm}
      defaultValues={{
        providerId: qualification?.provider?.id,
        name: qualification?.name,
        type: qualification?.type,
        institution: qualification?.institution ?? "",
        identifier: qualification?.identifier ?? "",
        state: qualification?.state ?? "",
        country: qualification?.country ?? "",
        startDate: qualification?.startDate ?? undefined,
        endDate: qualification?.endDate ?? undefined,
        status: qualification?.status,
        ...defaultValues,
      }}
      onSubmit={useCallback<NonNullable<QualificationFormProps["onSubmit"]>>(
        async (values: QualificationFormValues) => {
          if (onUpdate) {
            await onUpdate(values);
          } else {
            await updateQualificationMutation.mutateAsync({
              id: qualification?.id ?? "",
              data: {
                name: values.name,
                type: values.type as QualificationType,
                institution: values.institution,
                identifier: values.identifier,
                state: values.state,
                country: values.country,
                startDate: values.startDate,
                endDate: values.endDate,
                status: values.status as QualificationStatus,
              },
            });
          }
        },
        [updateQualificationMutation, qualification, onUpdate],
      )}
    >
      {children}
    </DialogForm>
  );
}

export interface DeleteQualificationProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  qualification?: QualificationStruct;
  onDelete?: (
    qualification: RouterOutputs["qualifications"]["delete"],
  ) => void | Promise<void> | Promise<{ id: string }>;
  onSuccess?: (
    qualification: RouterOutputs["qualifications"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteQualification({
  onDelete,
  onSuccess,
  onError,
  qualification,
  variant = "destructive",
  children,
  ...props
}: PropsWithChildren<DeleteQualificationProps>) {
  const deleteQualificationMutation = api.qualifications.delete.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      {...props}
      disabled={deleteQualificationMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: qualification?.id ?? "" });
        } else {
          await deleteQualificationMutation.mutateAsync({
            id: qualification?.id ?? "",
          });
        }
      }, [deleteQualificationMutation, qualification, onDelete])}
    >
      {children}
    </DialogConfirmation>
  );
}

export interface UpdateQualificationStatusProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  qualification?: QualificationStruct;
  status: QualificationStatus;
  onSuccess?: (
    qualification: RouterOutputs["qualifications"]["updateStatus"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateQualificationStatus({
  status,
  onSuccess,
  onError,
  qualification,
  variant = "outline",
  ...props
}: PropsWithChildren<UpdateQualificationStatusProps>) {
  const updateStatusMutation = api.qualifications.updateStatus.useMutation({
    onSuccess: async (result) => {
      if (onSuccess) {
        await onSuccess(result);
      }
      toast.success(
        status === QualificationStatus.APPROVED
          ? i18n.en.messages.approved
          : i18n.en.messages.rejected,
      );
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      }
      toast.error(
        (status === QualificationStatus.APPROVED
          ? i18n.en.messages.failedApprove
          : i18n.en.messages.failedReject) + error.message,
      );
    },
  });

  return (
    <DialogConfirmation
      title={
        status === QualificationStatus.APPROVED
          ? i18n.en.titles.approve
          : i18n.en.titles.reject
      }
      description={
        status === QualificationStatus.APPROVED
          ? i18n.en.descriptions.approve
          : i18n.en.descriptions.reject
      }
      {...props}
      disabled={updateStatusMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        await updateStatusMutation.mutateAsync({
          id: qualification?.id ?? "",
          status,
        });
      }, [updateStatusMutation, qualification, status])}
    />
  );
}

export function QualificationMenu({
  loading = false,
  qualification,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    qualification?: QualificationStruct;
    onUpdate?: UpdateQualificationProps["onUpdate"];
    onDelete?: DeleteQualificationProps["onDelete"];
  } & ButtonProps
>) {
  const { isInternal } = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const isPending = qualification?.status === QualificationStatus.PENDING;

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateQualification
            onUpdate={onUpdate}
            onOpenChange={handleDialogOpenChange}
            qualification={qualification}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateQualification>
          {isPending && isInternal && (
            <>
              <DropdownMenuSeparator />
              <UpdateQualificationStatus
                onOpenChange={handleDialogOpenChange}
                qualification={qualification}
                status={QualificationStatus.APPROVED}
                action={i18n.en.actions.approve}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.approve}
                </DropdownMenuItem>
              </UpdateQualificationStatus>
              <UpdateQualificationStatus
                onOpenChange={handleDialogOpenChange}
                qualification={qualification}
                status={QualificationStatus.REJECTED}
                variant="destructive"
                action={i18n.en.actions.reject}
              >
                <DropdownMenuItem
                  onSelect={handleItemSelect}
                  className="text-red-600 dark:text-red-400"
                >
                  {i18n.en.actions.reject}
                </DropdownMenuItem>
              </UpdateQualificationStatus>
            </>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteQualification
            onDelete={onDelete}
            onOpenChange={handleDialogOpenChange}
            qualification={qualification}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              className="text-red-600 dark:text-red-400"
            >
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteQualification>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
