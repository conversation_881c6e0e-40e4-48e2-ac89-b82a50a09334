"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, PlusCircleIcon, Star } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { SelectorProps } from "@axa/ui/selectors/Selector";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import { Selector } from "@axa/ui/selectors/Selector";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterInputs, RouterOutputs } from "@/api";
import type {
  ReviewFormProps,
  ReviewFormValues,
} from "@/components/forms/ReviewForm";

import { api } from "@/api/client";
import ReviewForm from "@/components/forms/ReviewForm";

const i18n = {
  en: {
    unknownReview: "Unknown Review",
    titles: {
      add: "Add A Review",
      update: "Update The Review",
      delete: "Delete The Review",
    },
    descriptions: {
      add: "Review the provider for this shift.",
      update: "Update an existing review for this shift.",
      delete: "Are you sure you want to delete this review?",
    },
    actions: {
      label: "Actions",
      add: "Rate Provider",
      update: "Update Review",
      delete: "Delete Review",
      open: "Open Review",
      selectReview: "Select Review",
    },
    messages: {
      created: "Review created successfully. Thank you for your feedback!",
      updated: "Review updated successfully. Thank you for your feedback!",
      deleted: "Review deleted successfully.",
      failedCreate: "Failed to create review: ",
      failedUpdate: "Failed to update review: ",
      failedDelete: "Failed to delete review: ",
    },
  },
  links: {
    reviews: "/app/reviews",
    review: "/app/reviews/[id]",
  },
};

export type CoreReviewStruct = RouterOutputs["reviews"]["get"];
export type ReviewStruct = Pick<
  CoreReviewStruct,
  "id" | "rating" | "comment"
> & {
  organization?: Pick<
    NonNullable<CoreReviewStruct["organization"]>,
    "id" | "name"
  >;
  provider?: Pick<NonNullable<CoreReviewStruct["provider"]>, "id">;
  shift?: Pick<NonNullable<CoreReviewStruct["shift"]>, "id" | "summary">;
};

export interface BaseReviewFormProps
  extends Omit<
    DialogFormProps<ReviewFormProps, ReviewFormValues>,
    "Component" | "onSubmit" | "onError"
  > {
  organizationId?: string;
  providerId?: string;
  shiftId?: string;
  defaultValues?: Partial<ReviewFormValues>;
}

export interface AddReviewProps extends BaseReviewFormProps {
  onAdd?: (
    review: RouterInputs["reviews"]["create"],
  ) => void | Promise<RouterOutputs["reviews"]["create"]>;
  onSuccess?: (
    review: RouterOutputs["reviews"]["create"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function AddReview({
  onAdd,
  onSuccess,
  onError,
  children,
  ...props
}: PropsWithChildren<AddReviewProps>) {
  const utils = api.useUtils();
  const createReviewMutation = api.reviews.create.useMutation({
    onSuccess: async (result) => {
      await utils.reviews.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.created);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedCreate + error.message);
      }
    },
  });

  return (
    <DialogForm<ReviewFormProps, ReviewFormValues>
      title={i18n.en.titles.add}
      description={i18n.en.descriptions.add}
      label={i18n.en.actions.add}
      type="add"
      {...props}
      Component={ReviewForm}
      onSubmit={useCallback<NonNullable<ReviewFormProps["onSubmit"]>>(
        async (values) => {
          if (onAdd) {
            await onAdd({
              provider: values.providerId ?? "",
              shift: values.shiftId ?? "",
              rating: values.rating,
              comment: values.comment,
            });
          } else {
            await createReviewMutation.mutateAsync({
              provider: values.providerId ?? "",
              shift: values.shiftId ?? "",
              rating: values.rating,
              comment: values.comment,
            });
          }
        },
        [createReviewMutation, onAdd],
      )}
    >
      {children ?? (
        <Button variant="outline" className="flex w-full items-center gap-2">
          <Star className="size-4" />
          {i18n.en.actions.add}
        </Button>
      )}
    </DialogForm>
  );
}

export interface UpdateReviewProps extends BaseReviewFormProps {
  review?: ReviewStruct;
  onUpdate?: (
    review: RouterInputs["reviews"]["update"],
  ) => void | Promise<RouterOutputs["reviews"]["update"]>;
  onSuccess?: (
    review: RouterOutputs["reviews"]["update"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function UpdateReview({
  review,
  onUpdate,
  onSuccess,
  onError,
  defaultValues = {},
  variant = "outline",
  children,
  ...props
}: PropsWithChildren<UpdateReviewProps>) {
  const utils = api.useUtils();
  const updateReviewMutation = api.reviews.update.useMutation({
    onSuccess: async (result) => {
      await utils.reviews.get.invalidate({ id: review?.id });
      await utils.reviews.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        toast.success(i18n.en.messages.updated);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedUpdate + error.message);
      }
    },
  });

  return (
    <DialogForm<ReviewFormProps, ReviewFormValues>
      title={i18n.en.titles.update}
      description={i18n.en.descriptions.update}
      label={i18n.en.actions.update}
      variant={variant}
      type="update"
      {...props}
      Component={ReviewForm}
      defaultValues={{
        organizationId: review?.organization?.id,
        providerId: review?.provider?.id,
        shiftId: review?.shift?.id,
        comment: review?.comment ?? "",
        rating: review?.rating,
        ...defaultValues,
      }}
      onSubmit={useCallback<NonNullable<ReviewFormProps["onSubmit"]>>(
        async (values) => {
          if (onUpdate) {
            await onUpdate({
              id: review?.id ?? "",
              data: {
                rating: values.rating,
                comment: values.comment,
              },
            });
          } else {
            await updateReviewMutation.mutateAsync({
              id: review?.id ?? "",
              data: {
                rating: values.rating,
                comment: values.comment,
              },
            });
          }
        },
        [updateReviewMutation, review, onUpdate],
      )}
    >
      {children ?? (
        <Button variant="outline" className="flex w-full items-center gap-2">
          <Star className="size-4" />
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export interface DeleteReviewProps
  extends Omit<DialogConfirmationProps, "onClick" | "onError" | "onSuccess"> {
  review?: ReviewStruct;
  reroute?: boolean;
  onDelete?: (
    review: RouterInputs["reviews"]["delete"],
  ) => void | Promise<RouterOutputs["reviews"]["delete"]>;
  onSuccess?: (
    review: RouterOutputs["reviews"]["delete"],
  ) => void | Promise<void>;
  onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
}

export function DeleteReview({
  onDelete,
  onSuccess,
  onError,
  review,
  reroute,
  variant = "destructive",
  ...props
}: PropsWithChildren<DeleteReviewProps>) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteReviewMutation = api.reviews.delete.useMutation({
    onSuccess: async (result) => {
      await utils.reviews.getMany.invalidate();
      if (onSuccess) {
        await onSuccess(result);
      } else {
        if (reroute) {
          router.replace(i18n.links.reviews);
        }
        toast.success(i18n.en.messages.deleted);
      }
    },
    onError: async (error) => {
      if (onError) {
        await onError(error);
      } else {
        toast.error(i18n.en.messages.failedDelete + error.message);
      }
    },
  });

  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={props.onOpenChange}
      open={props.open}
      {...props}
      disabled={deleteReviewMutation.isPending}
      variant={variant}
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete({ id: review?.id ?? "" });
        } else {
          await deleteReviewMutation.mutateAsync({
            id: review?.id ?? "",
          });
        }
      }, [deleteReviewMutation, review, onDelete])}
    />
  );
}

export function ReviewMenu({
  loading = false,
  review,
  onUpdate,
  onDelete,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    review?: ReviewStruct;
    onUpdate?: UpdateReviewProps["onUpdate"];
    onDelete?: DeleteReviewProps["onDelete"];
  } & ButtonProps
>) {
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  const handleOpen = useCallback(() => {
    router.push(i18n.links.review.replace("[id]", review?.id ?? ""));
  }, [router, review?.id]);

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="ghost"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuItem onSelect={handleOpen}>
          {i18n.en.actions.open}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <UpdateReview
          onUpdate={onUpdate}
          onOpenChange={handleDialogOpenChange}
          review={review}
        >
          <DropdownMenuItem onSelect={handleItemSelect}>
            {i18n.en.actions.update}
          </DropdownMenuItem>
        </UpdateReview>
        <DropdownMenuSeparator />
        <DeleteReview
          onDelete={onDelete}
          onOpenChange={handleDialogOpenChange}
          review={review}
        >
          <DropdownMenuItem
            onSelect={handleItemSelect}
            className="text-red-600 dark:text-red-400"
          >
            {i18n.en.actions.delete}
          </DropdownMenuItem>
        </DeleteReview>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface SearchReviewProps
  extends Pick<
    SelectorProps<RouterOutputs["reviews"]["getMany"]["items"][number]>,
    | "onSelect"
    | "defaultValue"
    | "children"
    | "variant"
    | "className"
    | "loading"
    | "pending"
  > {
  useDialog?: boolean;
  // filter by relations
  organizationId?: string;
  providerId?: string;
  shiftId?: string;
}

export function SearchReview({
  loading = false,
  pending = false,
  useDialog = true,
  defaultValue,
  onSelect,
  variant = "ghost",
  className = "size-full",
  children,
  // filters by relations
  organizationId,
  providerId,
  shiftId,
}: SearchReviewProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useDebounceValue<string>(defaultValue ?? "", 500);
  const reviews = api.reviews.getMany.useQuery(
    {
      query,
      pageNumber: 0,
      pageSize: 5,
      organizationId,
      providerId,
      shiftId,
      include: {
        shift: true,
        provider: true,
      },
    },
    {
      enabled: open,
    },
  );

  const handleRenderValue = useCallback(
    (review: RouterOutputs["reviews"]["getMany"]["items"][number]) => {
      if (review.shift) {
        return `Review for ${review.shift.summary || "Shift"}`;
      }
      return `Review ${review.id || i18n.en.unknownReview}`;
    },
    [],
  );

  const handleRenderItem = useCallback(
    (review: RouterOutputs["reviews"]["getMany"]["items"][number]) => {
      return (
        <div className="flex flex-col gap-1">
          <div className="font-medium">
            {review.shift
              ? `Review for ${review.shift.summary || "Shift"}`
              : `Review ${review.id}`}
          </div>
          {review.comment && (
            <div className="text-sm text-muted-foreground">
              {review.comment.length > 50
                ? `${review.comment.substring(0, 50)}...`
                : review.comment}
            </div>
          )}
        </div>
      );
    },
    [],
  );

  return (
    <Selector<RouterOutputs["reviews"]["getMany"]["items"][number]>
      loading={loading || reviews.isLoading}
      pending={pending}
      useDialog={useDialog}
      data={reviews.data?.items ?? []}
      open={open}
      onOpenChange={setOpen}
      onSelect={onSelect}
      value={query}
      onValueChange={setQuery}
      variant={variant}
      className={className}
      renderValue={handleRenderValue}
      renderItem={handleRenderItem}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectReview}</span>
        </>
      )}
    </Selector>
  );
}
