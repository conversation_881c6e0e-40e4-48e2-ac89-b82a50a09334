import type { NextRequest } from "next/server";

import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { captureException } from "@sentry/nextjs";
import { TRPCError } from "@trpc/server";
import { fetchRequestHandler } from "@trpc/server/adapters/fetch";
import { ipAddress } from "@vercel/functions";

import { appRouter, createTRPCContext } from "@axa/api-medical";
import { rateLimit } from "@axa/lib/upstash";

const setCorsHeaders = (res: Response) => {
  res.headers.set("Access-Control-Allow-Origin", "*");
  res.headers.set("Access-Control-Request-Method", "*");
  res.headers.set("Access-Control-Allow-Methods", "OPTIONS, GET, POST");
  res.headers.set("Access-Control-Allow-Headers", "*");
};

export const OPTIONS = () => {
  const response = new Response(null, {
    status: 204,
  });
  setCorsHeaders(response);
  return response;
};

const handler = async (req: NextRequest) => {
  try {
    const id = ipAddress(req) ?? "anonymous";

    const { success, limit, remaining } = await rateLimit(id);

    if (!success) {
      const error = new TRPCError({
        code: "TOO_MANY_REQUESTS",
        cause: "Rate limit exceeded",
        message: JSON.stringify({
          limit,
          remaining,
        }),
      });
      captureException(error);
      return new NextResponse(
        JSON.stringify({
          id: null,
          error,
        }),
        { status: 429 },
      );
    }
  } catch (e) {
    console.error(e);
  }

  const response = await fetchRequestHandler({
    endpoint: "/api/trpc",
    router: appRouter,
    req,
    createContext: async () =>
      await createTRPCContext({
        headers: req.headers,
        auth: await auth(),
      }),
    onError({ error, path }) {
      captureException(error);
      console.error(`>>> tRPC Error on '${path}'`, error);
    },
  });

  setCorsHeaders(response);
  return response;
};

export { handler as GET, handler as POST };
