"use client";

import { useState } from "react";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { httpBatchStreamLink, httpLink, loggerLink } from "@trpc/client";
import { createTRPCReact } from "@trpc/react-query";
import <PERSON><PERSON><PERSON><PERSON> from "superjson";

import type { AppRouter } from "@axa/api-medical";

const VERCEL_URL = process.env.VERCEL_URL;
const NODE_ENV = process.env.NODE_ENV;
const PORT = process.env.PORT ?? 3002;

const getBaseUrl = () => {
  if (typeof window !== "undefined") return window.location.origin;
  if (VERCEL_URL) return `https://${VERCEL_URL}`;

  return `http://localhost:${PORT}`;
};

export const getRPCUrl = () => {
  return getBaseUrl() + "/api/trpc";
};

export const api = createTRPCReact<AppRouter>();

export const createQueryCache = (() => {
  let cache: QueryCache | undefined;

  return () => {
    return (cache ??= new QueryCache({}));
  };
})();
export const getQueryCache = createQueryCache;

export const createQueryClient = (() => {
  let client: QueryClient | undefined;

  return () => {
    return (client ??= new QueryClient({
      queryCache: getQueryCache(),
      defaultOptions: {
        queries: {
          // With SSR, we usually want to set some default staleTime
          // above 0 to avoid refetching immediately on the client
          staleTime: 30 * 1000,
        },
      },
    }));
  };
})();
export const getQueryClient = createQueryClient;

export const createTRPCClient = (() => {
  let client: ReturnType<typeof api.createClient> | undefined;

  return (
    url = getRPCUrl(),
    options: {
      type: "http" | "batch";
    } = {
      type: "batch",
    },
  ) => {
    client ??= api.createClient({
      links: [
        loggerLink({
          enabled: (op) =>
            NODE_ENV === "development" ||
            (op.direction === "down" && op.result instanceof Error),
        }),
        options.type === "http"
          ? httpLink({
              transformer: SuperJSON,
              url,
              headers() {
                const headers = new Headers();
                headers.set("x-trpc-source", "nextjs-react");
                return headers;
              },
            })
          : httpBatchStreamLink({
              transformer: SuperJSON,
              url,
              headers() {
                const headers = new Headers();
                headers.set("x-trpc-source", "nextjs-react");
                return headers;
              },
            }),
      ],
    });

    return client;
  };
})();

export function TRPCReactProvider(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient();
  const [trpcClient] = useState(createTRPCClient);

  return (
    <QueryClientProvider client={queryClient}>
      <api.Provider client={trpcClient} queryClient={queryClient}>
        {props.children}
      </api.Provider>
    </QueryClientProvider>
  );
}
