import type { TRPCClientError, TRPCClientErrorLike } from "@trpc/client";
import type { inferReactQueryProcedureOptions } from "@trpc/react-query";

import type { AppRouter } from "@axa/api-medical";

export type { RouterInputs, RouterOutputs, AppRouter } from "@axa/api-medical";

export type RouterQueryOptions = inferReactQueryProcedureOptions<AppRouter>;
export type RouterError =
  | TRPCClientError<AppRouter>
  | TRPCClientErrorLike<AppRouter>
  | Error
  | null;

export * from "@axa/api-medical/constants/enums";
export * from "@axa/api-medical/constants/actions";
