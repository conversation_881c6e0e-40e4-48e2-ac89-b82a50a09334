import type { <PERSON>ada<PERSON>, Viewport } from "next";

import { DM_Serif_Display } from "next/font/google";
import { cookies } from "next/headers";
import { GoogleAnalytics } from "@next/third-parties/google";
import { GeistMono } from "geist/font/mono";
import { GeistSans } from "geist/font/sans";

import { cn } from "@axa/ui/lib";
import { ThemeProvider } from "@axa/ui/primitives/theme";
import { Toaster } from "@axa/ui/primitives/toast";

import RootProvider from "./_root";

import "./globals.css";

import { BASE_URL, companyInfo, DEVELOPMENT_URL } from "@axa/constants/med";

import { env } from "@/env";

const dm_serif_display = DM_Serif_Display({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dm_serif_display",
  weight: "400",
});

export const metadata: Metadata = {
  metadataBase: new URL(
    env.VERCEL_ENV === "production" ? BASE_URL : DEVELOPMENT_URL,
  ),
  title: {
    absolute: companyInfo.name,
    template: `%s | ${companyInfo.name}`,
  },
  description: companyInfo.slogan,
  openGraph: {
    title: {
      absolute: companyInfo.name,
      template: `%s | ${companyInfo.name}`,
    },
    description: companyInfo.slogan,
    url: BASE_URL,
    siteName: companyInfo.name,
  },
  twitter: {
    card: "summary_large_image",
    site: companyInfo.social.twitter_handle,
    creator: companyInfo.social.twitter_handle,
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const cookieStore = await cookies();
  const analyticsConsent = cookieStore.get("analytics-consent");
  const theme = cookieStore.get("theme");
  return (
    <html
      lang="en"
      suppressHydrationWarning={env.NODE_ENV === "production"}
      className={cn(theme?.value === "dark" && "dark")}
      style={{ colorScheme: theme?.value ?? "light" }}
    >
      <body
        className={cn(
          "min-h-dvh bg-background font-sans text-foreground antialiased",
          dm_serif_display.variable,
          GeistSans.variable,
          GeistMono.variable,
        )}
      >
        <RootProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            forcedTheme={theme?.value}
            enableSystem
          >
            {props.children}
            <Toaster closeButton richColors expand position="bottom-center" />
          </ThemeProvider>
        </RootProvider>
        {analyticsConsent?.value === "true" && (
          <GoogleAnalytics gaId="G-XGBN070810" />
        )}
      </body>
    </html>
  );
}
