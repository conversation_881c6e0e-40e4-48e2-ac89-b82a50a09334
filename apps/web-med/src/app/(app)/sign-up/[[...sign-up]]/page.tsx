import type { Metadata } from "next";

import Image from "next/image";
import { SignUp } from "@clerk/nextjs";

export const metadata: Metadata = {
  title: "Sign Up",
};

const i18n = {
  links: {
    signUp: "/sign-up",
    signIn: "/sign-in",
    app: "/app",
  },
};

export default function SignUpPage() {
  return (
    <main className="flex h-dvh w-full items-center justify-center">
      <div className="size-full max-h-full min-h-full md:grid md:grid-cols-[1fr_auto]">
        <div className="flex h-dvh w-full flex-col items-center justify-center gap-4 p-8">
          <SignUp
            path={i18n.links.signUp}
            signInUrl={i18n.links.signIn}
            fallbackRedirectUrl={i18n.links.app}
          />
        </div>

        <div className="hidden max-h-screen bg-muted object-contain lg:inline">
          <Image
            src="/images/sign-up.png"
            priority
            alt="Image"
            width="1080"
            height="1080"
            className="aspect-square size-full object-cover object-[20%_80%]"
          />
        </div>
      </div>
    </main>
  );
}
