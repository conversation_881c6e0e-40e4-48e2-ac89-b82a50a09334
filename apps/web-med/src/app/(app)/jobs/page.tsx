import type { Metada<PERSON> } from "next";

import JobBoard from "@/www/public/job-board";

export const metadata: Metadata = {
  title: "Job Board",
  description: "Find the perfect job for you",
};

export default function JobsPage() {
  // TODO: get ip location and create search params in middleware
  return (
    <div className="mx-auto flex h-full max-w-screen-lg flex-col gap-4">
      <JobBoard />
    </div>
  );
}
