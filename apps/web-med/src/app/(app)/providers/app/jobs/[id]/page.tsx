import type { Metada<PERSON> } from "next";

import { use } from "react";

import { api } from "@/api/server";
import Job from "@/www/providers/job-listing";

export default function JobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  return <Job id={id} />;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const job = await api.jobs.metadata({ id });

  return {
    title: job.title,
    description: job.description,
  };
}
