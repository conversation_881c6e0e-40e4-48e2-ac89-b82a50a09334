import type { Metadata } from "next";

import { use } from "react";

import Application from "@/www/providers/application";

export const metadata: Metadata = {
  title: "Application Details",
  description: "AXA Med Provider Application Details",
};

export default function ApplicationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  return <Application id={id} />;
}
