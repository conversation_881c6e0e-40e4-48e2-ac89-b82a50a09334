import { Suspense } from "react";

import { api } from "@/api/server";
import AppLayout from "@/components/layouts/AppLayout";
import ProvidersLayout from "@/components/layouts/ProviderLayout";

export default function ProvidersRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = api.user.me();
  return (
    <Suspense>
      <AppLayout user={user}>
        <ProvidersLayout>{children}</ProvidersLayout>
      </AppLayout>
    </Suspense>
  );
}
