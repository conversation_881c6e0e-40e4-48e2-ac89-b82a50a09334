import type { Metada<PERSON> } from "next";

import { use } from "react";

import { api } from "@/api/server";
import JobView from "@/www/organizations/job";

export default function JobPage({
  params,
}: {
  params: Promise<{ job: string }>;
}) {
  const { job } = use(params);
  return <JobView id={job} />;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ job: string }>;
}): Promise<Metadata> {
  const { job } = await params;
  const jobPost = await api.jobs.metadata({ id: job });

  return {
    title: jobPost.title,
    description: jobPost.description,
  };
}
