import { Suspense } from "react";

import { api } from "@/api/server";
import AppLayout from "@/components/layouts/AppLayout";
import OrganizationLayout from "@/components/layouts/OrganizationLayout";

export default function MedicalAppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = api.user.me();
  // const notifications = api.user.notifications.getMany({ read: false });
  return (
    <Suspense>
      <AppLayout user={user}>
        <OrganizationLayout>{children}</OrganizationLayout>
      </AppLayout>
    </Suspense>
  );
}
