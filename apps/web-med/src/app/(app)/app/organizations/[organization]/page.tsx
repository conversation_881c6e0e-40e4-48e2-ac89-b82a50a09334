import type { Metada<PERSON> } from "next";

import { use } from "react";

import { api } from "@/api/server";
import OrganizationView from "@/www/organizations/organization";

export default function OrganizationPage({
  params,
}: {
  params: Promise<{ organization: string }>;
}) {
  const { organization } = use(params);
  return <OrganizationView id={organization} />;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ organization: string }>;
}): Promise<Metadata> {
  const { organization } = await params;
  const metadata = await api.organizations.getMetadata({
    id: organization,
  });

  return {
    title: metadata.title,
    description: metadata.description,
  };
}
