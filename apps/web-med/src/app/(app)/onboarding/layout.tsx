import { Suspense } from "react";

import { api } from "@/api/server";
import AppLayout from "@/components/layouts/AppLayout";
import OnboardingLayout from "@/components/layouts/OnboardingLayout";

export default function OnboardingLayoutRoot({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = api.user.me();
  return (
    <Suspense fallback={null}>
      <AppLayout user={user}>
        <OnboardingLayout>{children}</OnboardingLayout>
      </AppLayout>
    </Suspense>
  );
}
