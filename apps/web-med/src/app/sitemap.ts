import type { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  return [
    {
      url: "https://med.axapro.com",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 1,
    },
    // {
    //   url: "https://med.axapro.com/jobs",
    //   lastModified: new Date("2024-11-05"),
    //   changeFrequency: "yearly",
    //   priority: 0.8,
    // },
    {
      url: "https://med.axapro.com/providers",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 0.8,
    },
    {
      url: "https://med.axapro.com/organizations",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 0.8,
    },
    {
      url: "https://med.axapro.com/contact-us",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 0.5,
    },
    {
      url: "https://med.axapro.com/privacy-policy",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 0.5,
    },
    {
      url: "https://med.axapro.com/terms-of-use",
      lastModified: new Date("2024-11-05"),
      changeFrequency: "yearly",
      priority: 0.5,
    },
  ];
}
