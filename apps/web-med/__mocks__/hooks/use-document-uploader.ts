export const useDocumentUploader = () => ({
  uploadDocument: async (file: File) => {
    // Simulate a successful upload after a short delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      url: `https://example.com/documents/${file.name}`,
      path: `documents/${file.name}`,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    };
  },
  isUploading: false,
  progress: 100,
  error: null,
});
