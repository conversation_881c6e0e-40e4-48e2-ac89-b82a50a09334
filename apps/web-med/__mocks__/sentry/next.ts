import { fn } from "storybook/test";

export const captureException = fn();
export const captureMessage = fn();
export const captureEvent = fn();
export const addBreadcrumb = fn();
export const trpcMiddleware = fn(({ next, ...args }) => {
  return next({
    ...args,
    ctx: {
      ...args.ctx,
      options: {
        ...args.ctx.options,
      },
    },
  });
});

export default {
  captureException,
  captureMessage,
  captureEvent,
  trpcMiddleware,
};
