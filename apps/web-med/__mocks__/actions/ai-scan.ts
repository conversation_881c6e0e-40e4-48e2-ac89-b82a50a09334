import { fn } from "@storybook/test";

import type { ResumeScanResult } from "@axa/api-medical/actions/ai-scan";

const data = {
  error: null,
  resume: {
    firstName: "Serita",
    lastName: "Scullark",
    email: "<EMAIL>",
    phoneNumber: "+13476818968",
    address: "Pomona, NY, US 10970",
    specialties: [
      {
        name: "Nursing",
        description: "The practice of caring for the sick and injured",
      },
      {
        name: "Nurse Practitioner",
        description: "A nurse who provides primary care to patients",
      },
      {
        name: "Emergency Medicine",
        description: "The practice of caring for the sick and injured",
      },
      {
        name: "Ambulatory Surgery",
        description: "The practice of caring for the sick and injured",
      },
      {
        name: "Chronic Disease Management",
        description: "The practice of caring for the sick and injured",
      },
    ],
    professionalTitle: "Nurse Practitioner",
    spokenLanguages: ["English"],
    yearsOfExperience: 9,
    qualifications: [
      {
        name: "Master's of Science in Nursing",
        type: "DEGREE",
        institution: "William Paterson University",
        identifier: "",
        startDate: "2021-12",
        endDate: "2023-12",
        state: "NJ",
        country: "US",
      },
      {
        name: "Bachelor's of Science in Nursing",
        type: "DEGREE",
        institution: "Chamberlain University",
        identifier: "",
        startDate: "2014-12",
        endDate: "2018-12",
        state: "NJ",
        country: "US",
      },
      {
        name: "Associate's Degree in Nursing",
        type: "DEGREE",
        institution: "Monroe College",
        identifier: "",
        startDate: "2012-12",
        endDate: "2014-12",
        state: "NY",
        country: "US",
      },
      {
        name: "New Jersey State AG NP-C License",
        type: "LICENSE",
        institution: "New Jersey State Board of Nursing",
        identifier: "",
        startDate: "2023-01",
        endDate: "2028-01",
        state: "NJ",
        country: "US",
      },
      {
        name: "New Jersey State Registered Nurse License",
        type: "LICENSE",
        institution: "New Jersey State Board of Nursing",
        identifier: "",
        startDate: "2015-12",
        endDate: "2028-12",
        state: "NJ",
        country: "US",
      },
      {
        name: "New York State Registered Nurse License",
        type: "LICENSE",
        institution: "New York State Education Department",
        identifier: "",
        startDate: "2015-12",
        endDate: "2028-12",
        state: "NY",
        country: "US",
      },
      {
        name: "ACLS Certification",
        type: "CERTIFICATE",
        institution: "American Heart Association",
        identifier: "",
        startDate: "2023-01",
        endDate: "2026-01",
        state: "",
        country: "US",
      },
      {
        name: "BLS Certification",
        type: "CERTIFICATE",
        institution: "American Heart Association",
        identifier: "",
        startDate: "2023-01",
        endDate: "2026-01",
        state: "",
        country: "US",
      },
      {
        name: "PALS Certification",
        type: "CERTIFICATE",
        institution: "American Heart Association",
        identifier: "",
        startDate: "2023-01",
        endDate: "2026-01",
        state: "",
        country: "US",
      },
      {
        name: "TNC Certification",
        type: "CERTIFICATE",
        institution: "American Nurses Credentialing Center",
        identifier: "",
        startDate: "2023-01",
        endDate: "2026-01",
        state: "",
        country: "US",
      },
    ],
    experience: [
      {
        role: "Nurse Practitioner Student",
        description:
          "Utilized evidence-based practice to assess, diagnose, and provide treatment to a diverse population; ordered and interpreted tests and prescribed medications; collaborated with healthcare providers for optimal patient care.",
        specialties: ["Nursing", "Nurse Practitioner"],
        company: "RWJBarnabas Health",
        startDate: "2023-09",
        endDate: "2023-12",
        location: "West Orange, NJ, US",
      },
      {
        role: "Nurse Practitioner Student",
        description:
          "Provided comprehensive primary care, including preventive care and chronic disease management; performed gynecological exams and educated patients on disease prevention.",
        specialties: ["Nursing", "Nurse Practitioner"],
        company: "NAAL Medical and Wellness Center",
        startDate: "2023-01",
        endDate: "2023-08",
        location: "East Orange, NJ, US",
      },
      {
        role: "Registered Nurse",
        description:
          "Administered medications for pain management; managed patients’ airways and provided emergency interventions; educated patients and families on post-op care.",
        specialties: ["Nursing", "Post-Anesthesia Care Unit"],
        company: "Northern Westchester Hospital",
        startDate: "2023-12",
        endDate: "2023-10",
        location: "Mt. Kisco, NY, US",
      },
      {
        role: "Registered Nurse",
        description:
          "Admitted patients for pre-op; monitored post-op recovery and assisted in minor procedures; provided discharge instructions.",
        specialties: ["Nursing", "Ambulatory Surgery"],
        company: "Phelps Hospital",
        startDate: "2022-10",
        endDate: "2023-12",
        location: "Sleepy Hollow, NY, US",
      },
      {
        role: "Registered Nurse",
        description:
          "Provided emergency care including administering critical medications and monitoring patients in an emergency department setting; triaged patients as needed.",
        specialties: ["Nursing", "Emergency Medicine"],
        company: "Montefiore Medical Center",
        startDate: "2018-02",
        endDate: "2023-12",
        location: "Bronx, NY, US",
      },
      {
        role: "Registered Nurse",
        description:
          "Provided emergency care, including triaging patients and administering critical care measures in the emergency department; performed various medical procedures and assessments.",
        specialties: ["Nursing", "Emergency Medicine"],
        company: "St. Barnabas Hospital",
        startDate: "2015-11",
        endDate: "2018-02",
        location: "Bronx, NY, US",
      },
    ],
  },
} satisfies ResumeScanResult;

export const scanResume = fn(() => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), 2000);
  });
});
