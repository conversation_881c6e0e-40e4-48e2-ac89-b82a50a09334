<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 728.79 707.09">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        filter: url(#drop-shadow-1);
      }

      .cls-3 {
        fill: url(#Dégradé_sans_nom_7);
      }

      .cls-4 {
        fill: url(#Dégradé_sans_nom_7-2);
      }

      .cls-5 {
        fill: url(#Dégradé_sans_nom_7-4);
      }

      .cls-6 {
        fill: url(#Dégradé_sans_nom_7-3);
      }

      .cls-7 {
        fill: url(#Dégradé_sans_nom_7-6);
      }

      .cls-8 {
        fill: url(#Dégradé_sans_nom_7-5);
      }

      .cls-9 {
        fill: url(#Dégradé_sans_nom_10);
      }

      .cls-10 {
        fill: url(#Dégradé_sans_nom_10-3);
      }

      .cls-11 {
        fill: url(#Dégradé_sans_nom_10-2);
      }
    </style>
    <filter id="drop-shadow-1" x="-330.16" y="-329.99" width="1390" height="1368" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="109.94"/>
      <feFlood flood-color="#0e3030" flood-opacity=".1"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="Dégradé_sans_nom_10" data-name="Dégradé sans nom 10" x1="433.57" y1="805.77" x2="647.7" y2="501.23" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#39ead9"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_10-2" data-name="Dégradé sans nom 10" x1="616.91" y1="37.13" x2="306.35" y2="320.41" xlink:href="#Dégradé_sans_nom_10"/>
    <linearGradient id="Dégradé_sans_nom_7" data-name="Dégradé sans nom 7" x1="298.93" y1="489.2" x2="307.13" y2="478.21" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#30d5c8"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_7-2" data-name="Dégradé sans nom 7" x1="301.34" y1="508.34" x2="306.68" y2="501.2" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-3" data-name="Dégradé sans nom 7" x1="300.18" y1="532.33" x2="307.78" y2="522.15" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-4" data-name="Dégradé sans nom 7" x1="305.61" y1="482.64" x2="295.41" y2="490" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-5" data-name="Dégradé sans nom 7" x1="306.78" y1="525.95" x2="296.25" y2="533.56" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-6" data-name="Dégradé sans nom 7" x1="304.86" y1="508.84" x2="300.69" y2="511.85" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_10-3" data-name="Dégradé sans nom 10" x1="281.88" y1="370.39" x2="-7.84" y2="634.92" xlink:href="#Dégradé_sans_nom_10"/>
  </defs>
  <g id="Calque_1">
    <g>
      <g class="cls-2">
        <path class="cls-9" d="M615.85,325.9l-220.09,381.2h186.15c112.91,0,183.48-122.23,127.02-220.01l-93.07-161.18Z"/>
        <path class="cls-11" d="M502.56,207.71c0,34.56-8.79,69.99-28.11,103.42l-132.73,229.92-2.64,4.69-23.53,40.71-69.66,120.64c10.37-18.98,19.17-37.23,26.49-54.75,1.58-3.77,3.12-7.51,4.51-11.21,2.79-7.07,5.31-14.04,7.59-20.85,1.91-5.64,3.63-11.21,5.2-16.67.29-1.03.59-2.05.88-3.08,1.57-5.61,3-11.14,4.25-16.56.84-3.59,1.61-7.11,2.27-10.63.15-.62.29-1.25.4-1.87,1.06-5.42,1.98-10.77,2.71-16.01.55-3.7,1.03-7.37,1.39-10.96.33-2.9.59-5.75.81-8.58.37-4.8.66-9.53.77-14.14.11-2.93.14-5.83.14-8.72,0-2.05-.04-4.1-.07-6.12-.04-2.13-.11-4.21-.22-6.3-.04-1.98-.15-3.92-.33-5.86,0-.26,0-.48-.04-.7-.4-5.9-1.03-11.65-1.83-17.26-.22-1.8-.51-3.56-.81-5.31-.37-2.23-.73-4.4-1.17-6.56-.62-3.33-1.32-6.63-2.13-9.86-.04-.37-.11-.7-.22-1.03-.4-1.61-.81-3.19-1.21-4.8,0-.07,0-.15-.04-.18-.48-1.72-.92-3.41-1.39-5.09-1.03-3.52-2.09-6.96-3.23-10.33-.59-1.69-1.17-3.33-1.76-4.98-.59-1.61-1.17-3.19-1.76-4.76-.07-.15-.11-.26-.15-.37-1.87-4.8-3.85-9.45-5.97-13.96-.59-1.28-1.21-2.57-1.79-3.81-.26-.48-.48-.92-.7-1.36-.62-1.28-1.25-2.57-1.94-3.85-2.35-4.54-4.8-8.94-7.36-13.19-.66-1.14-1.32-2.23-2.02-3.33-1.94-3.12-3.92-6.16-5.94-9.12-1.14-1.69-2.31-3.34-3.48-4.95-.73-1.03-1.47-2.05-2.2-3-.66-.95-1.36-1.87-2.09-2.79-2.16-2.86-4.36-5.64-6.6-8.35-55-68.27-64.09-162.63-20.38-238.68l38.66-66.95L294.16,0h.66c121.12,0,207.75,99.38,207.75,207.71Z"/>
        <path class="cls-3" d="M302.66,494.06c-.44-5.9-1.06-11.65-1.83-17.26.81,5.61,1.43,11.36,1.83,17.26Z"/>
        <path class="cls-4" d="M303.32,511.17c-.03-3.56-.13-7.08-.29-10.55.16,3.47.26,6.98.29,10.55Z"/>
        <path class="cls-6" d="M303.17,521.76c-.11,4.65-.37,9.34-.77,14.14.37-4.8.66-9.53.77-14.14Z"/>
        <path class="cls-5" d="M302.66,494.06c-.44-5.9-1.06-11.65-1.83-17.26.81,5.61,1.43,11.36,1.83,17.26Z"/>
        <path class="cls-8" d="M302.4,535.91c.37-4.8.66-9.53.77-14.14-.11,4.65-.37,9.34-.77,14.14Z"/>
        <path class="cls-7" d="M303.32,511.17v1.87c0-2.05-.04-4.1-.07-6.12.04,1.43.07,2.82.07,4.25Z"/>
        <path class="cls-10" d="M303.32,513.04c0,2.9-.04,5.79-.14,8.72-.11,4.62-.4,9.34-.77,14.14-.22,2.82-.48,5.68-.81,8.58-.37,3.59-.84,7.26-1.39,10.96-.73,5.24-1.65,10.59-2.71,16.01-.11.62-.26,1.25-.4,1.87-.66,3.52-1.43,7.04-2.27,10.63-1.25,5.42-2.68,10.96-4.25,16.56-.29,1.03-.59,2.05-.88,3.08-1.58,5.46-3.3,11.03-5.2,16.67-2.27,6.82-4.8,13.78-7.59,20.85-1.39,3.7-2.93,7.44-4.51,11.21-7.33,17.52-16.12,35.77-26.49,54.75h-90c-27.85,0-54.64-7.44-77.95-20.85-23.31-13.49-43.13-32.94-57.06-57.06-27.85-48.23-27.85-107.67,0-155.93l115.65-200.31,89.96-155.82c-43.72,76.04-34.63,170.4,20.38,238.68,2.24,2.71,4.43,5.5,6.6,8.35.73.92,1.43,1.83,2.09,2.79.73.95,1.47,1.98,2.2,3,1.17,1.61,2.35,3.26,3.48,4.95,2.01,2.97,3.99,6.01,5.94,9.12.7,1.1,1.36,2.2,2.02,3.33,2.56,4.25,5.02,8.65,7.36,13.19.66,1.28,1.32,2.57,1.94,3.85.22.44.44.88.7,1.36.59,1.25,1.21,2.53,1.79,3.81,2.13,4.51,4.1,9.16,5.97,13.96.04.11.07.22.15.37.59,1.58,1.17,3.15,1.76,4.76.59,1.65,1.17,3.3,1.76,4.98,1.14,3.37,2.2,6.82,3.23,10.33.48,1.69.92,3.37,1.39,5.09.04.04.04.11.04.18.4,1.61.81,3.19,1.21,4.8.11.33.18.66.22,1.03.81,3.23,1.5,6.52,2.13,9.86.44,2.16.81,4.32,1.17,6.56.29,1.76.55,3.56.81,5.31.77,5.61,1.39,11.36,1.83,17.26.04.22.04.44.04.7.15,1.94.26,3.88.33,5.86.07,2.09.15,4.18.22,6.3.04,2.02.07,4.07.07,6.12Z"/>
      </g>
      <line class="cls-1" x1="226.52" y1="117.13" x2="265.18" y2="50.17"/>
    </g>
  </g>
</svg>