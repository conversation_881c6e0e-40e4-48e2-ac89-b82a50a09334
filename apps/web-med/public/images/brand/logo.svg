<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1720.09 516.92">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        filter: url(#drop-shadow-1);
      }

      .cls-3 {
        fill: url(#Dégradé_sans_nom_7);
      }

      .cls-4 {
        fill: url(#Dégradé_sans_nom_7-2);
      }

      .cls-5 {
        fill: url(#Dégradé_sans_nom_7-4);
      }

      .cls-6 {
        fill: url(#Dégradé_sans_nom_7-3);
      }

      .cls-7 {
        fill: url(#Dégradé_sans_nom_7-6);
      }

      .cls-8 {
        fill: url(#Dégradé_sans_nom_7-5);
      }

      .cls-9 {
        fill: url(#Dégradé_sans_nom_10);
      }

      .cls-10 {
        <!-- fill: #1c353d; -->
        fill: #ffffff;
      }

      .cls-11 {
        fill: url(#Dégradé_sans_nom_10-3);
      }

      .cls-12 {
        fill: url(#Dégradé_sans_nom_10-2);
      }
    </style>
    <filter id="drop-shadow-1" x="-241.63" y="-241.12" width="1017" height="1000" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="80.37"/>
      <feFlood flood-color="#0e3030" flood-opacity=".1"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="Dégradé_sans_nom_10" data-name="Dégradé sans nom 10" x1="316.96" y1="589.06" x2="473.5" y2="366.42" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#39ead9"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_10-2" data-name="Dégradé sans nom 10" x1="450.99" y1="27.14" x2="223.95" y2="234.23" xlink:href="#Dégradé_sans_nom_10"/>
    <linearGradient id="Dégradé_sans_nom_7" data-name="Dégradé sans nom 7" x1="218.53" y1="357.63" x2="224.53" y2="349.59" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#30d5c8"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_7-2" data-name="Dégradé sans nom 7" x1="220.29" y1="371.62" x2="224.19" y2="366.4" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-3" data-name="Dégradé sans nom 7" x1="219.44" y1="389.16" x2="225" y2="381.72" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-4" data-name="Dégradé sans nom 7" x1="223.42" y1="352.83" x2="215.96" y2="358.21" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-5" data-name="Dégradé sans nom 7" x1="224.27" y1="384.49" x2="216.57" y2="390.06" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-6" data-name="Dégradé sans nom 7" x1="222.87" y1="371.99" x2="219.82" y2="374.19" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_10-3" data-name="Dégradé sans nom 10" x1="206.07" y1="270.77" x2="-5.73" y2="464.15" xlink:href="#Dégradé_sans_nom_10"/>
  </defs>
  <g id="Calque_1">
    <g>
      <g>
        <path class="cls-10" d="M920.22,302.99h-160.59l-28.47,56.19h-64.92l140.84-265.74h66.44l140.84,265.74h-65.68l-28.47-56.19ZM894.79,253.64l-55.05-108.2-55.05,108.2h110.09Z"/>
        <path class="cls-10" d="M1229.23,224.03l122.24,135.15h-74.41l-85.8-94.15-85.8,94.15h-70.61l121.86-134.39-119.96-131.35h74.41l83.9,91.87,83.14-91.87h70.99l-119.96,130.6Z"/>
        <path class="cls-10" d="M1625.94,302.99h-160.59l-28.47,56.19h-64.92l140.84-265.74h66.44l140.84,265.74h-65.68l-28.47-56.19ZM1600.5,253.64l-55.05-108.2-55.05,108.2h110.09Z"/>
      </g>
      <g class="cls-2">
        <path class="cls-9" d="M450.21,238.24l-160.9,278.67h136.08c82.54,0,134.13-89.36,92.85-160.84l-68.04-117.83Z"/>
        <path class="cls-12" d="M367.4,151.84c0,25.26-6.43,51.17-20.55,75.6l-97.03,168.08-1.93,3.43-17.2,29.76-50.93,88.19c7.58-13.88,14.01-27.22,19.37-40.02,1.15-2.76,2.28-5.49,3.3-8.2,2.04-5.17,3.89-10.26,5.55-15.24,1.39-4.13,2.65-8.2,3.8-12.19.21-.75.43-1.5.64-2.25,1.15-4.1,2.2-8.14,3.11-12.11.62-2.63,1.18-5.2,1.66-7.77.11-.46.21-.91.29-1.37.78-3.96,1.45-7.88,1.98-11.71.4-2.71.75-5.38,1.02-8.01.24-2.12.43-4.21.59-6.27.27-3.51.48-6.97.56-10.34.08-2.14.11-4.26.11-6.38,0-1.5-.03-3-.05-4.47-.03-1.55-.08-3.08-.16-4.61-.03-1.45-.11-2.87-.24-4.29,0-.19,0-.35-.03-.51-.29-4.31-.75-8.52-1.34-12.62-.16-1.31-.38-2.6-.59-3.89-.27-1.63-.54-3.21-.86-4.8-.46-2.44-.96-4.85-1.55-7.21-.03-.27-.08-.51-.16-.75-.29-1.18-.59-2.33-.88-3.51,0-.05,0-.11-.03-.13-.35-1.26-.67-2.49-1.02-3.72-.75-2.57-1.53-5.09-2.36-7.55-.43-1.23-.86-2.44-1.29-3.64-.43-1.18-.86-2.33-1.29-3.48-.05-.11-.08-.19-.11-.27-1.37-3.51-2.81-6.91-4.37-10.21-.43-.94-.88-1.88-1.31-2.79-.19-.35-.35-.67-.51-.99-.46-.94-.91-1.88-1.42-2.81-1.71-3.32-3.51-6.54-5.38-9.64-.48-.83-.97-1.63-1.47-2.44-1.42-2.28-2.87-4.5-4.34-6.67-.83-1.23-1.69-2.44-2.55-3.62-.54-.75-1.07-1.5-1.61-2.2-.48-.7-.99-1.37-1.53-2.04-1.58-2.09-3.19-4.13-4.82-6.11-40.21-49.91-46.85-118.89-14.9-174.48l28.26-48.95L215.04,0h.48c88.54,0,151.87,72.65,151.87,151.84Z"/>
        <path class="cls-3" d="M221.26,361.18c-.32-4.31-.78-8.52-1.34-12.62.59,4.1,1.05,8.3,1.34,12.62Z"/>
        <path class="cls-4" d="M221.74,373.69c-.02-2.6-.09-5.17-.21-7.71.12,2.54.19,5.11.21,7.71Z"/>
        <path class="cls-6" d="M221.63,381.43c-.08,3.4-.27,6.83-.56,10.34.27-3.51.48-6.97.56-10.34Z"/>
        <path class="cls-5" d="M221.26,361.18c-.32-4.31-.78-8.52-1.34-12.62.59,4.1,1.05,8.3,1.34,12.62Z"/>
        <path class="cls-8" d="M221.07,391.77c.27-3.51.48-6.97.56-10.34-.08,3.4-.27,6.83-.56,10.34Z"/>
        <path class="cls-7" d="M221.74,373.69v1.37c0-1.5-.03-3-.05-4.47.03,1.04.05,2.06.05,3.11Z"/>
        <path class="cls-11" d="M221.74,375.06c0,2.12-.03,4.23-.11,6.38-.08,3.37-.3,6.83-.56,10.34-.16,2.06-.35,4.15-.59,6.27-.27,2.63-.62,5.3-1.02,8.01-.54,3.83-1.21,7.74-1.98,11.71-.08.46-.19.91-.29,1.37-.48,2.57-1.05,5.14-1.66,7.77-.91,3.96-1.96,8.01-3.11,12.11-.21.75-.43,1.5-.64,2.25-1.15,3.99-2.41,8.06-3.8,12.19-1.66,4.98-3.51,10.07-5.55,15.24-1.02,2.71-2.14,5.44-3.3,8.2-5.36,12.81-11.79,26.15-19.37,40.02h-65.79c-20.36,0-39.94-5.44-56.98-15.24-17.04-9.86-31.53-24.08-41.71-41.71-20.36-35.26-20.36-78.71,0-113.99l84.55-146.43,65.77-113.91c-31.96,55.59-25.32,124.57,14.9,174.48,1.64,1.98,3.24,4.02,4.82,6.11.54.67,1.04,1.34,1.53,2.04.54.7,1.07,1.45,1.61,2.2.86,1.18,1.71,2.38,2.55,3.62,1.47,2.17,2.92,4.39,4.34,6.67.51.8.99,1.61,1.47,2.44,1.87,3.11,3.67,6.32,5.38,9.64.48.94.96,1.88,1.42,2.81.16.32.32.64.51.99.43.91.88,1.85,1.31,2.79,1.55,3.3,3,6.7,4.37,10.21.03.08.05.16.11.27.43,1.15.86,2.3,1.29,3.48.43,1.21.86,2.41,1.29,3.64.83,2.46,1.61,4.98,2.36,7.55.35,1.23.67,2.46,1.02,3.72.03.03.03.08.03.13.3,1.18.59,2.33.88,3.51.08.24.13.48.16.75.59,2.36,1.1,4.77,1.55,7.21.32,1.58.59,3.16.86,4.8.21,1.29.4,2.6.59,3.89.56,4.1,1.02,8.3,1.34,12.62.03.16.03.32.03.51.11,1.42.19,2.84.24,4.29.05,1.53.11,3.05.16,4.61.03,1.47.05,2.97.05,4.47Z"/>
      </g>
      <g>
        <path class="cls-10" d="M1129.95,439.06v52.17h-8.94v-40.02l-24.82,30.11h-.52l-24.81-30.11v40.02h-8.65v-52.17h9.69l24.22,29.88,24.22-29.88h9.61Z"/>
        <path class="cls-10" d="M1225.92,483.4v7.83h-51.94v-52.17h51.2v7.83h-42.33v13.93h35.62v7.82h-35.62v14.76h43.07Z"/>
        <path class="cls-10" d="M1324.12,465.14c0,16.09-12.37,26.08-31.75,26.08h-26.45v-52.17h26.45c19.38,0,31.75,9.99,31.75,26.08ZM1314.95,465.14c0-12.07-9.54-18.26-23.25-18.26h-16.84v36.51h16.84c13.71,0,23.25-6.18,23.25-18.26Z"/>
      </g>
      <line class="cls-1" x1="165.59" y1="85.62" x2="193.86" y2="36.68"/>
    </g>
  </g>
</svg>