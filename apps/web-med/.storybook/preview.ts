import type { Preview } from "@storybook/nextjs-vite";

import config from "@axa/storybook-config/preview-msw";

import { ThemeDecorator } from "./decorators/Theme";
import { TRPCDecorator } from "./decorators/TRPC";
import { UserDecorator } from "./decorators/User";

export const decorators = [ThemeDecorator, TRPCDecorator, UserDecorator];

export const parameters = {
  nextjs: {
    appDirectory: true,
    navigation: {
      pathname: "/",
    },
  },
};

export default {
  ...config,
  initialGlobals: {
    ...config.initialGlobals,
    user: "ADMIN",
  },
  globalTypes: {
    ...config.globalTypes,
    user: {
      name: "User",
      description: "Global user for components",
      defaultValue: "ADMIN",
      toolbar: {
        icon: "user",
        items: ["ADMIN", "BILLING", "INTERNAL", "PROVIDER", "CLIENT", "NONE"],
      },
    },
  },
} satisfies Preview;
