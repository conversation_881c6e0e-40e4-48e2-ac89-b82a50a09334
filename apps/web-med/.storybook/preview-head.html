<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  rel="preload"
  href="/fonts/geist-sans/Geist-Variable.woff2"
  as="font"
  type="font/woff2"
  crossorigin
/>
<link
  rel="preload"
  href="/fonts/geist-mono/Geist-Variable.woff2"
  as="font"
  type="font/woff2"
  crossorigin
/>
<link
  href="https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&display=swap"
  rel="stylesheet"
/>
<style>
  @font-face {
    font-family: "Geist Sans";
    src: url("/fonts/geist-sans/Geist-Variable.woff2");
  }
  @font-face {
    font-family: "Geist Mono";
    src: url("/fonts/geist-mono/Geist-Variable.woff2");
  }
  .dm-serif-display-regular {
    font-family: "DM Serif Display", serif;
    font-weight: 400;
    font-style: normal;
  }
  .dm-serif-display-regular-italic {
    font-family: "DM Serif Display", serif;
    font-weight: 400;
    font-style: italic;
  }
  :root {
    --font-dm_serif_display: "DM Serif Display", serif;
    --font-geist-sans: "Geist Sans", sans-serif;
    --font-geist-mono: "Geist Mono", monospace;
  }
</style>
