import type { PropsWithChildren } from "react";

import React from "react";
import { createTRPCQueryUtils } from "@trpc/react-query";

import {
  api,
  createQueryClient,
  createTRPCClient,
  getRPCUrl,
  TRPCReactProvider,
} from "../../src/api/client";

export { createQueryClient, createTR<PERSON>Client } from "../../src/api/client";

export const trpcContext = createTRPCQueryUtils({
  client: createTRPCClient(getRPCUrl(), { type: "http" }),
  queryClient: createQueryClient(),
});

type RPCContext = ReturnType<(typeof api)["useUtils"]>;
// Hack to be able to access trpcContext
const ActOnRPCContext = ({
  callback,
  children,
}: PropsWithChildren<{
  callback: (trpcContext: RPCContext) => void;
}>) => {
  const trpcContext = api.useUtils();
  callback(trpcContext);
  return <>{children}</>;
};

export const withTrpcContext =
  (callback: (context: RPCContext) => void) => (Story: React.FC) => (
    <ActOnRPCContext callback={callback}>
      <Story />
    </ActOnRPCContext>
  );

export const TRPCDecorator = (Story: React.FC) => (
  <TRPCReactProvider>
    <Story />
  </TRPCReactProvider>
);
