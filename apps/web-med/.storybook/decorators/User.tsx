import React from "react";
import { useGlobals } from "storybook/preview-api";

import type { UserContextType } from "@/components/contexts/User";

import { ProviderContext } from "@/components/contexts/Provider";
import { UserContext } from "@/components/contexts/User";

const primaryOrganization = {
  id: "1",
  name: "AXA Med",
  avatar: "/placeholder.svg",
};

const person: Pick<
  UserContextType,
  "id" | "firstName" | "lastName" | "email" | "phone" | "avatar"
> = {
  id: "1",
  firstName: "Gabriel",
  lastName: "Rashad",
  email: "<EMAIL>",
  phone: "**********",
  avatar: "/placeholder.svg",
};

const providerPerson = {
  id: "2",
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "**********",
  avatar: "/placeholder.svg",
  provider: {
    id: "2",
    accountId: "**********",
    backgroundCheckStatus: "PENDING",
    i9VerificationStatus: "PENDING",
    identityVerificationStatus: "PENDING",
    status: "ACTIVE",
  } as UserContextType["provider"],
};

const clientPerson = {
  id: "3",
  firstName: "Jane",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "**********",
  avatar: "/placeholder.svg",
};

// TODO: switch when global state is set
const user = {
  loading: false,
  initialized: true,
  signOut: async () => {
    return Promise.resolve();
  },
  refetch: async () => {
    return Promise.resolve();
  },
  error: null,
};

type UserType =
  | "ADMIN"
  | "BILLING"
  | "INTERNAL"
  | "PROVIDER"
  | "CLIENT"
  | "NONE";

function getUser(type: UserType): UserContextType {
  switch (type) {
    default:
    case "NONE":
      return {
        ...user,
        role: null,
        organization: null,
      };
    case "ADMIN":
      return {
        ...user,
        ...person,
        mode: "ORGANIZATION",
        role: "ADMIN",
        isAdmin: true,
        isBilling: true,
        isInternal: true,
        isProvider: false,
        isClient: false,
        organization: primaryOrganization,
      };
    case "BILLING":
      return {
        ...user,
        ...person,
        mode: "ORGANIZATION",
        role: "BILLING",
        isAdmin: false,
        isBilling: true,
        isInternal: true,
        isProvider: false,
        isClient: false,
        organization: primaryOrganization,
      };
    case "INTERNAL":
      return {
        ...user,
        ...person,
        mode: "ORGANIZATION",
        role: "INTERNAL",
        isAdmin: false,
        isBilling: false,
        isInternal: true,
        isProvider: false,
        isClient: false,
        organization: primaryOrganization,
      };
    case "PROVIDER":
      return {
        ...user,
        ...providerPerson,
        mode: "PROVIDER",
        role: "PROVIDER",
        isAdmin: false,
        isBilling: false,
        isInternal: false,
        isProvider: true,
        isClient: false,
        organization: null,
      };
    case "CLIENT":
      return {
        ...user,
        ...clientPerson,
        mode: "ORGANIZATION",
        role: "CLIENT",
        isAdmin: false,
        isBilling: false,
        isInternal: false,
        isProvider: false,
        isClient: true,
        organization: {
          id: "2",
          name: "Northwell",
          avatar: "/placeholder.svg",
        },
      };
  }
}

const provider = {
  id: "1",
  title: "Physician",
  specialties: [
    {
      id: "1",
      name: "Cardiology",
    },
    {
      id: "2",
      name: "Pediatrics",
    },
    {
      id: "3",
      name: "Family Medicine",
    },
    {
      id: "4",
      name: "Internal Medicine",
    },
  ],
  address: {
    address: "123 Page Ave",
    city: "Staten Island",
    state: "NY",
    zip: "10308",
    country: "USA",
    latitude: 40.712776,
    longitude: -74.005974,
  },
};
export function UserDecorator(Story: React.FC) {
  const [variables] = useGlobals();

  return (
    <UserContext.Provider value={getUser(variables.user as UserType)}>
      <ProviderContext.Provider value={provider}>
        <Story />
      </ProviderContext.Provider>
    </UserContext.Provider>
  );
}
