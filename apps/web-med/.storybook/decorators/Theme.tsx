import type { FunctionComponent } from "react";

import React, { useEffect } from "react";

import { ThemeProvider, useTheme } from "@/ui/primitives/theme";

import "@/ui/styles/index.css";

import type { StoryContext } from "@storybook/react";

export function ThemeSwitcher({ theme }: { theme: string }) {
  const { setTheme } = useTheme();

  useEffect(() => {
    setTheme(theme);
  }, [theme, setTheme]);

  return null;
}

export function ThemeDecorator(
  Story: FunctionComponent,
  context: StoryContext,
) {
  const theme = context.globals.theme || "light";
  return (
    <ThemeProvider attribute="class" defaultTheme={theme}>
      <ThemeSwitcher theme={theme} />
      <Story />
    </ThemeProvider>
  );
}
