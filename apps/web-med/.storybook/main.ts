import path from "path";
import { fileURLToPath } from "url";

import { mergeConfig } from "vitest/config";

import type { StorybookConfig } from "@axa/storybook-config/nextjs-vite";
import config from "@axa/storybook-config/nextjs-vite";

const root = fileURLToPath(
  new URL("../../../package.json", import.meta.url),
).slice(0, -"package.json".length);
const base = fileURLToPath(new URL("../package.json", import.meta.url)).slice(
  0,
  -"package.json".length,
);

export default {
  ...config,
  viteFinal: (sbConfig, options) => {
    return mergeConfig(config.viteFinal(sbConfig, options), {
      define: {
        "process.env.PUBLIC_URL": JSON.stringify("http://localhost:6010"),
      },
      optimizeDeps: {
        // exclude: ["@sentry/nextjs"],
        include: [
          "@axa/constants/*.ts",
          "@axa/lib/**/*.ts",
          "@axa/database-medical/**/*.ts > .prisma/client/medical/**/*.ts",
          "@axa/database-medical/**/*.ts",
          "@axa/api-medical/**/*.ts",
          "@axa/ui/**/*.tsx",
          "@axa/ui/**/*.ts",
        ],
        needsInterop: [".prisma/client/medical/**/*.js"],
        force: true,
      },
      resolve: {
        preserveSymlinks: true,
        // external: ["@sentry/nextjs"],
        alias: {
          "@/ui/": path.join(root, "packages/ui/src/"),
          "@/": path.join(base, "./src/"),
          "@axa/database-medical": path.join(
            root,
            "packages/db-medical/prisma/",
          ),
          "@axa/api-medical": path.join(root, "packages/api-medical/src/"),
          "@axa/ui": path.join(root, "packages/ui/src/"),
          "@axa/lib": path.join(root, "packages/lib/src/"),
          "@axa/constants": path.join(root, "packages/constants/src/"),
          "@prisma/client": path.join(
            root,
            "node_modules/.prisma/client/medical/",
          ),
          ".prisma/client/medical": path.join(
            root,
            "node_modules/.prisma/client/medical/index.js",
          ),
          // mocks
          "@sentry/nextjs": path.resolve(base, "__mocks__/sentry/next.ts"),
          "@/hooks/use-document-uploader": path.resolve(
            base,
            "__mocks__/hooks/use-document-uploader.ts",
          ),
          "@axa/api-medical/actions/ai-scan": path.resolve(
            base,
            "__mocks__/actions/ai-scan.ts",
          ),
          "@axa/api-medical/actions/general": path.resolve(
            base,
            "__mocks__/actions/general.ts",
          ),
        },
      },
    } satisfies typeof sbConfig);
  },
} satisfies StorybookConfig;
