import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  ContractStatus,
  ContractType,
  OrganizationClass,
  OrganizationStatus,
  OrganizationType,
  ScheduleType,
} from "@/api";

const createMockOrganization =
  (): RouterOutputs["organizations"]["get"]["organization"] => ({
    id: faker.string.uuid(),
    name: faker.company.name(),
    path: faker.number.int().toString(),
    avatar: faker.image.avatar(),
    type: faker.helpers.enumValue(OrganizationType),
    class: faker.helpers.enumValue(OrganizationClass),
    status: faker.helpers.enumValue(OrganizationStatus),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    approvedAt: faker.date.past(),
    rejectedAt: null,
    phone: faker.phone.number(),
    email: faker.internet.email(),
    parent: null,
    manager: undefined,
    billingFrequency: "<PERSON>ONTH<PERSON><PERSON>",
    basePercentage: faker.number.int({ min: 5, max: 20 }),
    assistPercentage: faker.number.int({ min: 1, max: 10 }),
    score: faker.number.float({ min: 100, max: 500, precision: 0.1 }),
    address: {
      id: faker.string.uuid(),
      formatted: faker.address.streetAddress(),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
      country: faker.address.countryCode(),
      timeZone: faker.address.timeZone(),
    },
    schedule: {
      id: faker.string.uuid(),
      name: faker.lorem.sentence(2),
      type: faker.helpers.enumValue(ScheduleType),
      blocks: [
        {
          id: faker.string.uuid(),
          dayOfWeek: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.number.int({ min: 6 * 60, max: 12 * 60 }),
          endTime: faker.number.int({ min: 12 * 60, max: 18 * 60 }),
        },
      ],
      startsAt: null,
      endsAt: null,
    },
    contracts: [
      {
        id: faker.string.uuid(),
        title: faker.lorem.sentence(2),
        type: faker.helpers.enumValue(ContractType),
        status: faker.helpers.enumValue(ContractStatus),
        createdAt: faker.date.past(),
      } as Omit<
        NonNullable<
          NonNullable<
            RouterOutputs["organizations"]["get"]["organization"]
          >["contracts"]
        >,
        "where"
      >[number],
    ],
  });

const createMockMember = () => ({
  id: faker.string.uuid(),
  role: "OWNER",
  user: {
    id: faker.string.uuid(),
    avatar: faker.image.avatar(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
  },
});

const createMockInvite = () => ({
  id: faker.string.uuid(),
  emailAddress: faker.internet.email(),
  role: "org:member",
  status: "pending",
  createdAt: Date.now(),
  updatedAt: Date.now(),
});

export const mockOrganizationQuery: RouterOutputs["organizations"]["get"] = {
  organization: createMockOrganization(),
  accounts: [
    {
      id: "acc_1",
      name: "City Healthcare Network",
      path: "city-healthcare-network",
      avatar: faker.image.avatar(),
      type: OrganizationType.CLIENT,
      status: OrganizationStatus.ACTIVE,
      createdAt: new Date("2024-01-01"),
      addressId: null,
      scheduleId: null,
      managerId: "mgr_1",
    },
  ],
};

export const mockOrganization = createMockOrganization();
export const mockMembers = Array.from({ length: 3 }, createMockMember);
export const mockInvites = Array.from({ length: 2 }, createMockInvite);
