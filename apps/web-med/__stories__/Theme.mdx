import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Theme" />

# AXA Professionals Theming System

## Overview

Our theming system is built on a set of consistent design tokens that power the entire application. This approach ensures visual consistency, accessibility compliance, and seamless dark mode support across all components.

### Key Principles

1. **Semantic Naming**: Colors are named by their semantic purpose (e.g., `primary`, `destructive`) rather than their visual appearance.
2. **Foreground/Background Pairs**: Each background color has a corresponding foreground color (e.g., `primary`/`primary-foreground`) for optimal contrast.
3. **Dark Mode Support**: All colors have light and dark mode variants that automatically switch when the `.dark` class is applied.
4. **Accessibility First**: All color combinations meet WCAG AA contrast requirements (4.5:1 for normal text, 3:1 for large text).

### Usage Conventions

- **Component Styling**: Always use semantic color tokens instead of hardcoded values.
- **Custom Components**: Follow the `bg-{token}` and `text-{token}-foreground` pattern.
- **Hover/Focus States**: Use opacity modifiers (e.g., `hover:bg-primary/90`) rather than different colors.
- **Gradients**: When using gradients, ensure both colors are from the same palette family.

### Implementation

Our theme is implemented using:

- Tailwind CSS for utility classes
- CSS variables for token values
- A theme provider component for runtime switching

# Theme Colors

## Light Mode

### Base Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 bg-background rounded-lg shadow-md">
  {/* Primary Colors */}
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-primary rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-primary-foreground py-8">
        <code className="text-sm">bg-primary</code><br/>
        <code className="text-sm">text-primary-foreground</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-secondary shadow-md">
    <div className="py-8 text-center text-secondary-foreground">
      <code className="text-sm text-secondary-foreground">bg-secondary</code>
      <br />
      <code className="text-sm text-secondary-foreground">
        text-secondary-foreground
      </code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-accent shadow-md">
    <div className="py-8 text-center text-accent-foreground">
      <code className="text-sm">bg-accent</code>
      <br />
      <code className="text-sm">text-accent-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-destructive shadow-md">
    <div className="py-8 text-center text-destructive-foreground">
      <code className="text-sm">bg-destructive</code>
      <br />
      <code className="text-sm">text-destructive-foreground</code>
    </div>
  </div>
</div>

{/* Background Colors */}

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg border bg-background shadow-md">
    <div className="py-8 text-center text-foreground">
      <code className="text-sm">bg-background</code>
      <br />
      <code className="text-sm">text-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-muted shadow-md">
    <div className="py-8 text-center text-muted-foreground">
      <code className="text-sm">bg-muted</code>
      <br />
      <code className="text-sm">text-muted-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg border bg-card shadow-md">
    <div className="py-8 text-center text-card-foreground">
      <code className="text-sm">bg-card</code>
      <br />
      <code className="text-sm">text-card-foreground</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-popover rounded-lg shadow-md border flex items-center justify-center">
      <div className="text-center text-popover-foreground py-8">
        <code className="text-sm">bg-popover</code><br/>
        <code className="text-sm">text-popover-foreground</code>
      </div>
    </div>
  </div>
</div>

### Sidebar Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 bg-background rounded-lg shadow-md">
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-sidebar rounded-lg shadow-md border flex items-center justify-center">
      <div className="text-center text-sidebar-foreground py-8">
        <code className="text-sm">sidebar-background</code><br/>
        <code className="text-sm">sidebar-foreground</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-sidebar-primary shadow-md">
    <div className="py-8 text-center text-sidebar-primary-foreground">
      <code className="text-sm">sidebar-primary</code>
      <br />
      <code className="text-sm">sidebar-primary-foreground</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-sidebar-accent rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-sidebar-accent-foreground py-8">
        <code className="text-sm">sidebar-accent</code><br/>
        <code className="text-sm">sidebar-accent-foreground</code>
      </div>
    </div>
  </div>
</div>

### Chart Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 bg-background rounded-lg shadow-md">
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-chart-1 rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-white py-8">
        <code className="text-sm">chart-1</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-2 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-2</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-3 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-3</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-4 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-4</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-chart-5 rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-white py-8">
        <code className="text-sm">chart-5</code>
      </div>
    </div>
  </div>
</div>

## Dark Mode

### Base Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 dark p-4 rounded-lg shadow-md bg-background">
  {/* Primary Colors */}
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-primary rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-primary-foreground py-8">
        <code className="text-sm">bg-primary</code><br/>
        <code className="text-sm">text-primary-foreground</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-secondary shadow-md">
    <div className="py-8 text-center text-secondary-foreground">
      <code className="text-sm text-secondary-foreground">bg-secondary</code>
      <br />
      <code className="text-sm text-secondary-foreground">
        text-secondary-foreground
      </code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-accent shadow-md">
    <div className="py-8 text-center text-accent-foreground">
      <code className="text-sm">bg-accent</code>
      <br />
      <code className="text-sm">text-accent-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-destructive shadow-md">
    <div className="py-8 text-center text-destructive-foreground">
      <code className="text-sm">bg-destructive</code>
      <br />
      <code className="text-sm">text-destructive-foreground</code>
    </div>
  </div>
</div>

{/* Background Colors */}

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg border bg-background shadow-md">
    <div className="py-8 text-center text-foreground">
      <code className="text-sm">bg-background</code>
      <br />
      <code className="text-sm">text-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-muted shadow-md">
    <div className="py-8 text-center text-muted-foreground">
      <code className="text-sm">bg-muted</code>
      <br />
      <code className="text-sm">text-muted-foreground</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg border bg-card shadow-md">
    <div className="py-8 text-center text-card-foreground">
      <code className="text-sm">bg-card</code>
      <br />
      <code className="text-sm">text-card-foreground</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-popover rounded-lg shadow-md border flex items-center justify-center">
      <div className="text-center text-popover-foreground py-8">
        <code className="text-sm">bg-popover</code><br/>
        <code className="text-sm">text-popover-foreground</code>
      </div>
    </div>
  </div>
</div>

### Sidebar Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 bg-background rounded-lg shadow-md dark">
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-sidebar rounded-lg shadow-md border flex items-center justify-center">
      <div className="text-center text-sidebar-foreground py-8">
        <code className="text-sm">sidebar-background</code><br/>
        <code className="text-sm">sidebar-foreground</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-sidebar-primary shadow-md">
    <div className="py-8 text-center text-sidebar-primary-foreground">
      <code className="text-sm">sidebar-primary</code>
      <br />
      <code className="text-sm">sidebar-primary-foreground</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-sidebar-accent rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-sidebar-accent-foreground py-8">
        <code className="text-sm">sidebar-accent</code><br/>
        <code className="text-sm">sidebar-accent-foreground</code>
      </div>
    </div>
  </div>
</div>

### Chart Colors

<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 bg-background rounded-lg shadow-md dark">
  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-chart-1 rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-white py-8">
        <code className="text-sm">chart-1</code>
      </div>
    </div>
  </div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-2 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-2</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-3 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-3</code>
    </div>
  </div>
</div>

<div className="flex flex-col items-center justify-center p-1 py-4">
  <div className="flex w-full items-center justify-center rounded-lg bg-chart-4 shadow-md">
    <div className="py-8 text-center text-white">
      <code className="text-sm">chart-4</code>
    </div>
  </div>
</div>

  <div className="flex flex-col justify-center items-center p-1 py-4">
    <div className="w-full bg-chart-5 rounded-lg shadow-md flex items-center justify-center">
      <div className="text-center text-white py-8">
        <code className="text-sm">chart-5</code>
      </div>
    </div>
  </div>
</div>

---

## Detailed Usage Guide

### Core Color Tokens

| Token            | Purpose                 | Usage Guidelines                                                                                        |
| ---------------- | ----------------------- | ------------------------------------------------------------------------------------------------------- |
| **Primary**      | Main brand color (teal) | Use for primary actions, key UI elements, and brand emphasis. Limit usage to maintain visual hierarchy. |
| **Secondary**    | Complementary blue-teal | Use for secondary actions, highlights, and complementary elements to primary.                           |
| **Accent**       | Subtle cool gray        | Use for subtle emphasis, borders, and background variations.                                            |
| **Destructive**  | Bright red              | Use exclusively for destructive actions, errors, and critical alerts.                                   |
| **Background**   | Very light cool gray    | Main page background. Provides a neutral canvas for content.                                            |
| **Muted**        | Light cool gray         | Use for secondary content, disabled states, and subtle UI elements.                                     |
| **Card/Popover** | Pure white              | Use for elevated surfaces like cards, dialogs, and popovers.                                            |

### Specialized Color Groups

#### Sidebar Colors

Our sidebar has its own color palette to create visual separation from the main content area:

- `sidebar-background`: Deep teal-gray for the sidebar background
- `sidebar-primary`: Brighter teal used for active/selected items
- `sidebar-accent`: Lighter teal used for hover states and subtle emphasis

#### Chart Colors

For data visualizations, use our dedicated chart color palette:

- These colors include warm accents (oranges, reds) to contrast with our cool primary palette
- They maintain consistent contrast in both light and dark modes
- Use them in sequence (chart-1, chart-2, etc.) for multi-series charts

### Accessibility Considerations

All color combinations in our theme meet WCAG AA standards:

- Text on backgrounds maintains at least 4.5:1 contrast ratio
- UI elements and graphics maintain at least 3:1 contrast ratio
- Focus states are visible with at least 3:1 contrast against adjacent colors

### Dark Mode Implementation

Our dark mode is implemented using the `.dark` class on the `<html>` element. This triggers CSS variable changes that:

- Invert the luminosity of most colors while preserving hue
- Maintain or improve contrast ratios
- Reduce eye strain with deeper, less bright cool colors

### Extending the Theme

When extending the theme:

1. Define new colors as CSS variables in both light and dark modes
2. Follow the naming convention: `--{component}-{purpose}`
3. Add corresponding Tailwind utility classes
4. Document the new colors in this guide

### Code Examples

#### Component Variants with Theme Tokens

Our component library uses theme tokens consistently across variants. The Badge component is an excellent example of how to implement variants using our theme tokens:

```tsx
// Badge component variants using theme tokens
const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        muted:
          "border-transparent bg-muted text-muted-foreground hover:bg-muted/80",
        accent:
          "border-transparent bg-accent text-accent-foreground hover:bg-accent/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

// Usage examples:
function BadgeExamples() {
  return (
    <div className="flex gap-2">
      <Badge>Default (Primary)</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="muted">Muted</Badge>
      <Badge variant="accent">Accent</Badge>
      <Badge variant="destructive">Destructive</Badge>
      <Badge variant="outline">Outline</Badge>
    </div>
  );
}
```

This approach ensures:

- Consistent styling across all instances of the component
- Automatic dark mode support through theme tokens
- Accessibility compliance with proper contrast ratios
- Easy theme extension by adding new variants

#### Creating Custom Variants

When creating custom variants for components, follow this pattern:

```tsx
// Adding a "success" variant to the Badge component
const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        // Existing variants...
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        // ...

        // New custom variant
        success:
          "bg-success text-success-foreground hover:bg-success/80 border-transparent shadow",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

// Don't forget to add the new tokens to your theme
// In your CSS variables:
// --success: 160 84% 39%;  // A green that complements our teal palette
// --success-foreground: 0 0% 100%;
```

#### Basic Component Styling

```tsx
// Good: Using semantic color tokens
function SubmitButton() {
  return (
    <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
      Submit
    </Button>
  );
}

// Bad: Using hardcoded colors
function IncorrectButton() {
  return (
    <Button className="bg-[#00A0B0] text-white hover:bg-[#008A99]">
      Submit
    </Button>
  );
}
```

#### Dark Mode Support

```tsx
// This component will automatically adapt to dark mode
function Card({ children }) {
  return (
    <div className="rounded-lg bg-card p-4 text-card-foreground shadow">
      {children}
    </div>
  );
}
```

#### State-Based Styling

Use theme tokens for different states to maintain consistency:

```tsx
// Define status styles outside the component for better performance
const STATUS_STYLES = {
  info: "bg-primary/10 text-primary border-primary/20",
  success: "bg-success/10 text-success border-success/20",
  warning: "bg-secondary/10 text-secondary border-secondary/20",
  error: "bg-destructive/10 text-destructive border-destructive/20",
};

// Alert component with different states
function Alert({ status = "info", children }) {
  return (
    <div className={`rounded border p-4 ${STATUS_STYLES[status]}`}>
      {children}
    </div>
  );
}
```

#### Combining with Other Design Patterns

Our theme tokens work seamlessly with other design patterns:

```tsx
// Combining theme tokens with responsive design
function ResponsiveCard({ children }) {
  return (
    <div className="rounded-md border border-accent/10 bg-card p-2 text-card-foreground shadow-sm md:rounded-lg md:p-4 md:shadow lg:p-6">
      {children}
    </div>
  );
}

// Define variant mapping outside the component
function getStatusVariant(status) {
  switch (status) {
    case "active":
      return "default"; // primary
    case "pending":
      return "secondary";
    case "inactive":
      return "muted";
    case "error":
      return "destructive";
    default:
      return "outline";
  }
}

// Combining theme tokens with conditional rendering
function StatusBadge({ status }) {
  const variant = getStatusVariant(status);
  return <Badge variant={variant}>{status}</Badge>;
}
```

By following these guidelines and using our theme tokens consistently, you'll create a cohesive, accessible, and visually appealing user experience across the AXA Professionals platform.
