import type { <PERSON>a, StoryObj } from "@storybook/react";

import { PayoutStatus } from "@/api";
import PreviewPayout from "@/components/shared/PreviewPayout";

const meta: Meta<typeof PreviewPayout> = {
  title: "Components/Shared/PreviewPayout",
  component: PreviewPayout,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof PreviewPayout>;

const basePayout = {
  id: "1",
  amount: 500,
  status: PayoutStatus.PENDING,
  date: new Date("2023-07-15"),
  organization: {
    name: "City General Hospital",
  },
};

export const Default: Story = {
  args: {
    payout: basePayout,
  },
};

export const WithShift: Story = {
  args: {
    payout: {
      ...basePayout,
      shift: {
        summary: "Emergency Room Shift",
        startDateTime: new Date("2023-07-15T09:00:00"),
        endDateTime: new Date("2023-07-15T17:00:00"),
      },
    },
  },
};

export const Completed: Story = {
  args: {
    payout: {
      ...basePayout,
      status: PayoutStatus.COMPLETED,
    },
  },
};

export const Failed: Story = {
  args: {
    payout: {
      ...basePayout,
      status: PayoutStatus.FAILED,
    },
  },
};
