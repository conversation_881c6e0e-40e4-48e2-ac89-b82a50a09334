import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { ContractStatusType } from "@/components/shared/ContractStatus";

import { ContractStatus } from "@/components/shared/ContractStatus";

const meta: Meta<typeof ContractStatus> = {
  title: "Components/Shared/ContractStatus",
  component: ContractStatus,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ContractStatus>;

const statuses: ContractStatusType[] = ["Pending", "Signed", "Completed"];

export const AllStatuses: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      {statuses.map((status) => (
        <ContractStatus key={status} status={status} />
      ))}
    </div>
  ),
};

export const Pending: Story = {
  args: {
    status: "Pending",
  },
};

export const Signed: Story = {
  args: {
    status: "Signed",
  },
};

export const Completed: Story = {
  args: {
    status: "Completed",
  },
};
