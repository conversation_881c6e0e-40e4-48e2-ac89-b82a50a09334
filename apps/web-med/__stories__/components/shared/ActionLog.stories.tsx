import type { <PERSON>a, StoryObj } from "@storybook/react";

import ActionLog from "@/components/shared/actions/ActionLog";

const meta = {
  title: "Components/Shared/ActionLog",
  component: ActionLog,
  parameters: {
    layout: "padded",
    viewport: {
      defaultViewport: "desktop",
      viewports: {
        mobile: {
          name: "Mobile",
          styles: {
            width: "375px",
            height: "600px",
          },
        },
        tablet: {
          name: "Tablet",
          styles: {
            width: "768px",
            height: "800px",
          },
        },
        desktop: {
          name: "Desktop",
          styles: {
            width: "1024px",
            height: "800px",
          },
        },
      },
    },
  },
  args: {
    title: "Activity Log",
  },
  decorators: [
    (Story) => (
      <div className="h-[600px] w-full max-w-[800px]">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ActionLog>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleActions = [
  {
    id: "1",
    type: "CONTRACT_CREATED",
    metadata: {
      contractId: "contract-123",
      title: "Provider Agreement",
    },
    createdAt: new Date("2024-01-30T10:00:00Z"),
    actor: {
      id: "user-1",
      firstName: "John",
      lastName: "Doe",
      avatar: null,
    },
  },
  {
    id: "2",
    type: "CONTRACT_SIGNED",
    metadata: {
      contractId: "contract-123",
      signatureId: "sig-456",
    },
    createdAt: new Date("2024-01-30T10:05:00Z"),
    actor: {
      id: "user-2",
      firstName: "Jane",
      lastName: "Smith",
      avatar: null,
    },
  },
  {
    id: "3",
    type: "PROVIDER_VERIFIED",
    metadata: {
      providerId: "provider-789",
      verificationId: "ver-123",
    },
    createdAt: new Date("2024-01-30T10:10:00Z"),
    actor: {
      id: "user-3",
      firstName: "Admin",
      lastName: "User",
      avatar: null,
    },
  },
  {
    id: "4",
    type: "SHIFT_COMPLETED",
    metadata: {
      shiftId: "shift-456",
      hours: 8,
    },
    createdAt: new Date("2024-01-30T10:15:00Z"),
    actor: {
      id: "user-4",
      firstName: "Sarah",
      lastName: "Johnson",
      avatar: null,
    },
  },
  {
    id: "5",
    type: "REVIEW_SUBMITTED",
    metadata: {
      reviewId: "review-789",
      rating: 5,
    },
    createdAt: new Date("2024-01-30T10:20:00Z"),
    actor: {
      id: "user-5",
      firstName: "Michael",
      lastName: "Brown",
      avatar: null,
    },
  },
];

export const Empty: Story = {
  args: {
    actions: [],
  },
};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const WithActions: Story = {
  args: {
    actions: sampleActions,
  },
};

export const WithError: Story = {
  args: {
    error: new Error("Failed to load activity log"),
  },
};

export const WithLongHistory: Story = {
  args: {
    actions: Array.from({ length: 20 }).map((_, i) => {
      const base = sampleActions[i % sampleActions.length]!;
      return {
        ...base,
        id: `action-${i}`,
        createdAt: new Date(Date.now() - (20 - i) * 60000),
      };
    }),
  },
};

export const ResponsiveMobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile",
    },
  },
  decorators: [
    (Story) => (
      <div className="h-[500px] w-full">
        <Story />
      </div>
    ),
  ],
  args: {
    actions: sampleActions.slice(0, 3),
  },
};

export const ResponsiveTablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
  decorators: [
    (Story) => (
      <div className="h-[600px] w-full">
        <Story />
      </div>
    ),
  ],
  args: {
    actions: sampleActions,
  },
};

export const ResponsiveDesktop: Story = {
  parameters: {
    viewport: {
      defaultViewport: "desktop",
    },
  },
  args: {
    actions: sampleActions,
  },
};
