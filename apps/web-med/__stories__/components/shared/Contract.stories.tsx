import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { ContractStatusType } from "@/components/shared/ContractStatus";

import { Contract } from "@/components/shared/Contract";

const meta: Meta<typeof Contract> = {
  title: "Components/Shared/Contract",
  component: Contract,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof Contract>;

const baseContract = {
  id: "1",
  title: "Provider Service Agreement",
  facility: "City General Hospital",
  date: "2023-07-01",
  status: "Pending" as ContractStatusType,
  description:
    "This contract outlines the terms and conditions for providing medical services at City General Hospital.",
  signees: [
    { name: "Dr. <PERSON>", role: "Provider", status: "Pending" as const },
    {
      name: "<PERSON>",
      role: "Hospital Administrator",
      status: "Signed" as const,
      signedAt: "2023-06-15",
    },
  ],
};

export const Pending: Story = {
  args: {
    contract: baseContract,
  },
};

export const Signed: Story = {
  args: {
    contract: {
      ...baseContract,
      status: "Signed" as ContractStatusType,
      signees: baseContract.signees.map((s) => ({
        ...s,
        status: "Signed" as const,
        signedAt: "2023-06-20",
      })),
    },
  },
};

export const Completed: Story = {
  args: {
    contract: {
      ...baseContract,
      status: "Completed" as ContractStatusType,
      signees: baseContract.signees.map((s) => ({
        ...s,
        status: "Signed" as const,
        signedAt: "2023-06-20",
      })),
    },
  },
};

export const WithDeclinedSignee: Story = {
  args: {
    contract: {
      ...baseContract,
      signees: [
        ...baseContract.signees,
        {
          name: "Alice Johnson",
          role: "Legal Advisor",
          status: "Declined" as const,
        },
      ],
    },
  },
};
