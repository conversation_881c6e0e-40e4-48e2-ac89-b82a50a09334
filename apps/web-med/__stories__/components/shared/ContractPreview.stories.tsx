import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ContractType, SignatureStatus } from "@/api";
import { ContractPreview } from "@/components/shared/ContractPreview";

const meta: Meta<typeof ContractPreview> = {
  title: "Components/Shared/ContractPreview",
  component: ContractPreview,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ContractPreview>;

const baseContract = {
  id: "1",
  title: "Provider Service Agreement",
  type: ContractType.EMPLOYMENT,
  signatureStatus: SignatureStatus.PENDING,
  documensoId: "DOC-12345",
};

export const Pending: Story = {
  args: {
    contract: baseContract,
  },
};

export const Signed: Story = {
  args: {
    contract: {
      ...baseContract,
      signatureStatus: SignatureStatus.SIGNED,
      signedAt: new Date("2023-06-15"),
      signedBy: "<PERSON><PERSON> <PERSON>",
    },
  },
};

export const Rejected: Story = {
  args: {
    contract: {
      ...baseContract,
      signatureStatus: SignatureStatus.REJECTED,
    },
  },
};

export const OrganizationContract: Story = {
  args: {
    contract: {
      ...baseContract,
      type: ContractType.SERVICE_AGREEMENT,
      title: "Facility Partnership Agreement",
    },
  },
};
