import type { Meta, StoryObj } from "@storybook/react";

import { ApplicationStatus } from "@/api";
import ApplicationStatusBadge from "@/components/common/ApplicationStatus";

const meta = {
  title: "Components/Common/ApplicationStatus",
  component: ApplicationStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ApplicationStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: ApplicationStatus.PENDING,
  },
};

export const Accepted: Story = {
  args: {
    status: ApplicationStatus.ACCEPTED,
  },
};

export const Rejected: Story = {
  args: {
    status: ApplicationStatus.REJECTED,
  },
};

export const Withdrawn: Story = {
  args: {
    status: ApplicationStatus.WITHDRAWN,
  },
};

export const Loading: Story = {
  args: {
    status: ApplicationStatus.PENDING,
    loading: true,
  },
};
