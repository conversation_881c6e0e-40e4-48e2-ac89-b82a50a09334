import type { Meta, StoryObj } from "@storybook/react";

import { PaymentType } from "@/api";
import PaymentTypeBadge from "@/components/common/PaymentType";

const meta = {
  title: "Components/Common/PaymentType",
  component: PaymentTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof PaymentTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Hourly: Story = {
  args: {
    type: PaymentType.HOURLY,
  },
};

export const Fixed: Story = {
  args: {
    type: PaymentType.FIXED,
  },
};

export const Loading: Story = {
  args: {
    type: PaymentType.HOURLY,
    loading: true,
  },
};
