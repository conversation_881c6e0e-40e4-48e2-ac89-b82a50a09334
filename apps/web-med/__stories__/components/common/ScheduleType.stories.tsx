import type { Meta, StoryObj } from "@storybook/react";

import { ScheduleType } from "@/api";
import ScheduleTypeBadge from "@/components/common/ScheduleType";

const meta = {
  title: "Components/Common/ScheduleType",
  component: ScheduleTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ScheduleTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Provider: Story = {
  args: {
    type: ScheduleType.PROVIDER,
  },
};

export const Organization: Story = {
  args: {
    type: ScheduleType.ORGANIZATION,
  },
};

export const Shift: Story = {
  args: {
    type: ScheduleType.SHIFT,
  },
};

export const Loading: Story = {
  args: {
    type: ScheduleType.PROVIDER,
    loading: true,
  },
};
