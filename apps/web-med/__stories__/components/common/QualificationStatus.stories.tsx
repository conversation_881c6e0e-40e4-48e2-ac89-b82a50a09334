import type { Meta, StoryObj } from "@storybook/react";

import { QualificationStatus } from "@/api";
import QualificationStatusBadge from "@/components/common/QualificationStatus";

const meta = {
  title: "Components/Common/QualificationStatus",
  component: QualificationStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof QualificationStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    type: QualificationStatus.PENDING,
  },
};

export const Approved: Story = {
  args: {
    type: QualificationStatus.APPROVED,
  },
};

export const Rejected: Story = {
  args: {
    type: QualificationStatus.REJECTED,
  },
};

export const Expired: Story = {
  args: {
    type: QualificationStatus.EXPIRED,
  },
};

export const Loading: Story = {
  args: {
    type: QualificationStatus.PENDING,
    loading: true,
  },
};
