import type { Meta, StoryObj } from "@storybook/react";

import { FacilityType } from "@/api";
import FacilityTypeBadge from "@/components/common/FacilityType";

const meta = {
  title: "Components/Common/FacilityType",
  component: FacilityTypeBadge,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof FacilityTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Campus: Story = {
  args: {
    type: FacilityType.CAMPUS,
  },
};

export const Hospital: Story = {
  args: {
    type: FacilityType.HOSPITAL,
  },
};

export const Clinic: Story = {
  args: {
    type: FacilityType.CLINIC,
  },
};

export const Office: Story = {
  args: {
    type: FacilityType.OFFICE,
  },
};

export const Pharmacy: Story = {
  args: {
    type: FacilityType.PHARMACY,
  },
};

export const Lab: Story = {
  args: {
    type: FacilityType.LAB,
  },
};

export const Imaging: Story = {
  args: {
    type: FacilityType.IMAGING,
  },
};

export const Rehabilitation: Story = {
  args: {
    type: FacilityType.REHABILITATION,
  },
};

export const Other: Story = {
  args: {
    type: FacilityType.OTHER,
  },
};

export const WithoutIcon: Story = {
  args: {
    type: FacilityType.HOSPITAL,
    showIcon: false,
  },
};

export const AllTypes: Story = {
  args: {
    type: FacilityType.HOSPITAL,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex flex-wrap items-center gap-4">
        <FacilityTypeBadge type={FacilityType.CAMPUS} />
        <FacilityTypeBadge type={FacilityType.HOSPITAL} />
        <FacilityTypeBadge type={FacilityType.CLINIC} />
        <FacilityTypeBadge type={FacilityType.OFFICE} />
        <FacilityTypeBadge type={FacilityType.PHARMACY} />
        <FacilityTypeBadge type={FacilityType.LAB} />
        <FacilityTypeBadge type={FacilityType.IMAGING} />
        <FacilityTypeBadge type={FacilityType.REHABILITATION} />
        <FacilityTypeBadge type={FacilityType.OTHER} />
      </div>
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="flex flex-wrap items-center gap-4">
          <FacilityTypeBadge type={FacilityType.CAMPUS} />
          <FacilityTypeBadge type={FacilityType.HOSPITAL} />
          <FacilityTypeBadge type={FacilityType.CLINIC} />
          <FacilityTypeBadge type={FacilityType.OFFICE} />
          <FacilityTypeBadge type={FacilityType.PHARMACY} />
          <FacilityTypeBadge type={FacilityType.LAB} />
          <FacilityTypeBadge type={FacilityType.IMAGING} />
          <FacilityTypeBadge type={FacilityType.REHABILITATION} />
          <FacilityTypeBadge type={FacilityType.OTHER} />
        </div>
      </div>
    </div>
  ),
};
