import type { Meta, StoryObj } from "@storybook/react";

import { DepartmentType } from "@/api";
import DepartmentTypeBadge from "@/components/common/DepartmentType";

const meta = {
  title: "Components/Common/DepartmentType",
  component: DepartmentTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof DepartmentTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Center: Story = {
  args: {
    type: DepartmentType.CENTER,
  },
};

export const Institute: Story = {
  args: {
    type: DepartmentType.INSTITUTE,
  },
};

export const Department: Story = {
  args: {
    type: DepartmentType.DEPARTMENT,
  },
};

export const Ward: Story = {
  args: {
    type: DepartmentType.WARD,
  },
};

export const Unit: Story = {
  args: {
    type: DepartmentType.UNIT,
  },
};

export const Room: Story = {
  args: {
    type: DepartmentType.ROOM,
  },
};

export const Other: Story = {
  args: {
    type: DepartmentType.OTHER,
  },
};

export const Loading: Story = {
  args: {
    type: DepartmentType.CENTER,
    loading: true,
  },
};
