import type { Meta, StoryObj } from "@storybook/react";

import { ContractStatus } from "@/api";
import ContractStatusBadge from "@/components/common/ContractStatus";

const meta = {
  title: "Components/Common/ContractStatus",
  component: ContractStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ContractStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: ContractStatus.PENDING,
  },
};

export const Signed: Story = {
  args: {
    status: ContractStatus.SIGNED,
  },
};

export const Rejected: Story = {
  args: {
    status: ContractStatus.REJECTED,
  },
};

export const Draft: Story = {
  args: {
    status: ContractStatus.DRAFT,
  },
};

export const Expired: Story = {
  args: {
    status: ContractStatus.EXPIRED,
  },
};

export const Loading: Story = {
  args: {
    status: ContractStatus.PENDING,
    loading: true,
  },
};
