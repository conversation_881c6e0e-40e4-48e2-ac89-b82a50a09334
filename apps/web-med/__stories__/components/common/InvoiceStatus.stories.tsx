import type { Meta, StoryObj } from "@storybook/react";

import { InvoiceStatus } from "@/api";
import InvoiceStatusBadge from "@/components/common/InvoiceStatus";

const meta = {
  title: "Components/Common/InvoiceStatus",
  component: InvoiceStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof InvoiceStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Draft: Story = {
  args: {
    type: InvoiceStatus.DRAFT,
  },
};

export const Open: Story = {
  args: {
    type: InvoiceStatus.OPEN,
  },
};

export const Paid: Story = {
  args: {
    type: InvoiceStatus.PAID,
  },
};

export const Due: Story = {
  args: {
    type: InvoiceStatus.DUE,
  },
};

export const Void: Story = {
  args: {
    type: InvoiceStatus.VOID,
  },
};

export const Loading: Story = {
  args: {
    type: InvoiceStatus.DRAFT,
    loading: true,
  },
};
