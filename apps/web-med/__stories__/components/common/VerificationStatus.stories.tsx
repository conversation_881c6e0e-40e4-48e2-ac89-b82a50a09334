import type { Meta, StoryObj } from "@storybook/react";

import { VerificationStatus } from "@/api";
import VerificationStatusBadge from "@/components/common/VerificationStatus";

const meta = {
  title: "Components/Common/VerificationStatus",
  component: VerificationStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof VerificationStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    type: VerificationStatus.PENDING,
  },
};

export const Approved: Story = {
  args: {
    type: VerificationStatus.APPROVED,
  },
};

export const Rejected: Story = {
  args: {
    type: VerificationStatus.REJECTED,
  },
};

export const Loading: Story = {
  args: {
    type: VerificationStatus.PENDING,
    loading: true,
  },
};
