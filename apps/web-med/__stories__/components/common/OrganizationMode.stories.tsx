import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationMode } from "@/api";
import OrganizationModeBadge from "@/components/common/OrganizationMode";

const meta = {
  title: "Components/Common/OrganizationMode",
  component: OrganizationModeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof OrganizationModeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Independent: Story = {
  args: {
    type: OrganizationMode.INDEPENDENT,
  },
};

export const Assisted: Story = {
  args: {
    type: OrganizationMode.ASSISTED,
  },
};

export const Loading: Story = {
  args: {
    type: OrganizationMode.INDEPENDENT,
    loading: true,
  },
};
