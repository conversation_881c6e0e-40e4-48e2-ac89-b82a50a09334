import type { Meta, StoryObj } from "@storybook/react";

import { JobPostStatus } from "@/api";
import JobStatus from "@/components/common/JobStatus";

const meta = {
  title: "Components/Common/JobStatus",
  component: JobStatus,
  tags: ["autodocs"],
} satisfies Meta<typeof JobStatus>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Draft: Story = {
  args: {
    status: JobPostStatus.DRAFT,
  },
};

export const Published: Story = {
  args: {
    status: JobPostStatus.PUBLISHED,
  },
};

export const Filled: Story = {
  args: {
    status: JobPostStatus.FILLED,
  },
};

export const Completed: Story = {
  args: {
    status: JobPostStatus.COMPLETED,
  },
};

export const Cancelled: Story = {
  args: {
    status: JobPostStatus.CANCELLED,
  },
};

export const Expired: Story = {
  args: {
    status: JobPostStatus.EXPIRED,
  },
};

export const Loading: Story = {
  args: {
    status: JobPostStatus.PUBLISHED,
    loading: true,
  },
};
