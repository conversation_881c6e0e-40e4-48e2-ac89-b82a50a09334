import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationBillingType } from "@/api";
import OrganizationBillingTypeBadge from "@/components/common/OrganizationBillingType";

const meta = {
  title: "Components/Common/OrganizationBillingType",
  component: OrganizationBillingTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof OrganizationBillingTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Invoice: Story = {
  args: {
    type: OrganizationBillingType.INVOICE,
  },
};

export const Charge: Story = {
  args: {
    type: OrganizationBillingType.CHARGE,
  },
};

export const None: Story = {
  args: {
    type: OrganizationBillingType.NONE,
  },
};

export const Loading: Story = {
  args: {
    type: OrganizationBillingType.INVOICE,
    loading: true,
  },
};
