import type { Meta, StoryObj } from "@storybook/react";

import { JobPostPriority } from "@/api";
import JobPriority from "@/components/common/JobPriority";

const meta = {
  title: "Components/Common/JobPriority",
  component: JobPriority,
  tags: ["autodocs"],
} satisfies Meta<typeof JobPriority>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Low: Story = {
  args: {
    priority: JobPostPriority.LOW,
  },
};

export const Medium: Story = {
  args: {
    priority: JobPostPriority.MEDIUM,
  },
};

export const High: Story = {
  args: {
    priority: JobPostPriority.HIGH,
  },
};

export const Loading: Story = {
  args: {
    priority: JobPostPriority.MEDIUM,
    loading: true,
  },
};
