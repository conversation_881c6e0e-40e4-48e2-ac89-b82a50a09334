import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationType } from "@/api";
import OrganizationTypeBadge from "@/components/common/OrganizationType";

const meta = {
  title: "Components/Common/OrganizationType",
  component: OrganizationTypeBadge,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Account: Story = {
  args: {
    type: OrganizationType.ACCOUNT,
  },
};

export const Client: Story = {
  args: {
    type: OrganizationType.CLIENT,
  },
};

export const Internal: Story = {
  args: {
    type: OrganizationType.INTERNAL,
  },
};

export const AllTypes: Story = {
  args: {
    type: OrganizationType.ACCOUNT,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <OrganizationTypeBadge type={OrganizationType.ACCOUNT} />
        <OrganizationTypeBadge type={OrganizationType.CLIENT} />
        <OrganizationTypeBadge type={OrganizationType.INTERNAL} />
      </div>
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="flex items-center gap-4">
          <OrganizationTypeBadge type={OrganizationType.ACCOUNT} />
          <OrganizationTypeBadge type={OrganizationType.CLIENT} />
          <OrganizationTypeBadge type={OrganizationType.INTERNAL} />
        </div>
      </div>
    </div>
  ),
};
