import type { Meta, StoryObj } from "@storybook/react";

import { PayoutStatus } from "@/api";
import PayoutStatusBadge from "@/components/common/PayoutStatus";

const meta = {
  title: "Components/Common/PayoutStatus",
  component: PayoutStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof PayoutStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: PayoutStatus.PENDING,
  },
};

export const Processing: Story = {
  args: {
    status: PayoutStatus.PROCESSING,
  },
};

export const Cancelled: Story = {
  args: {
    status: PayoutStatus.CANCELLED,
  },
};

export const Completed: Story = {
  args: {
    status: PayoutStatus.COMPLETED,
  },
};

export const Failed: Story = {
  args: {
    status: PayoutStatus.FAILED,
  },
};

export const Loading: Story = {
  args: {
    status: PayoutStatus.PENDING,
    loading: true,
  },
};
