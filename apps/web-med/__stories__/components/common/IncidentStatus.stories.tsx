import type { Meta, StoryObj } from "@storybook/react";

import { IncidentStatus } from "@/api";
import IncidentStatusBadge from "@/components/common/IncidentStatus";

const meta = {
  title: "Components/Common/IncidentStatus",
  component: IncidentStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof IncidentStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Open: Story = {
  args: {
    type: IncidentStatus.OPEN,
  },
};

export const InProgress: Story = {
  args: {
    type: IncidentStatus.IN_PROGRESS,
  },
};

export const Resolved: Story = {
  args: {
    type: IncidentStatus.RESOLVED,
  },
};

export const Loading: Story = {
  args: {
    type: IncidentStatus.OPEN,
    loading: true,
  },
};
