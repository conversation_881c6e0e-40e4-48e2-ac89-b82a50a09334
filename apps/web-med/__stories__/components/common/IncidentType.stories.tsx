import type { Meta, StoryObj } from "@storybook/react";

import { IncidentType } from "@/api";
import IncidentTypeBadge from "@/components/common/IncidentType";

const meta = {
  title: "Components/Common/IncidentType",
  component: IncidentTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof IncidentTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Safety: Story = {
  args: {
    type: IncidentType.SAFETY,
  },
};

export const Health: Story = {
  args: {
    type: IncidentType.HEALTH,
  },
};

export const Environment: Story = {
  args: {
    type: IncidentType.ENVIRONMENT,
  },
};

export const Other: Story = {
  args: {
    type: IncidentType.OTHER,
  },
};

export const Loading: Story = {
  args: {
    type: IncidentType.SAFETY,
    loading: true,
  },
};
