import type { Meta, StoryObj } from "@storybook/react";

import { PersonRole } from "@/api";
import PersonRoleBadge from "@/components/common/PersonRole";

const meta = {
  title: "Components/Common/PersonRole",
  component: PersonRoleBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof PersonRoleBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Admin: Story = {
  args: {
    type: PersonRole.ADMIN,
  },
};

export const Billing: Story = {
  args: {
    type: PersonRole.BILLING,
  },
};

export const Internal: Story = {
  args: {
    type: PersonRole.INTERNAL,
  },
};

export const Client: Story = {
  args: {
    type: PersonRole.CLIENT,
  },
};

export const Provider: Story = {
  args: {
    type: PersonRole.PROVIDER,
  },
};

export const User: Story = {
  args: {
    type: PersonRole.USER,
  },
};

export const None: Story = {
  args: {
    type: PersonRole.NONE,
  },
};

export const Loading: Story = {
  args: {
    type: PersonRole.ADMIN,
    loading: true,
  },
};
