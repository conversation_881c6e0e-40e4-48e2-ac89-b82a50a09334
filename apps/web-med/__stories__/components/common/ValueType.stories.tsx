import type { Meta, StoryObj } from "@storybook/react";

import { ValueType } from "@/api";
import ValueTypeBadge from "@/components/common/ValueType";

const meta = {
  title: "Components/Common/ValueType",
  component: ValueTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ValueTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Tag: Story = {
  args: {
    type: ValueType.TAG,
  },
};

export const Action: Story = {
  args: {
    type: ValueType.ACTION,
  },
};

export const MedicalRole: Story = {
  args: {
    type: ValueType.MEDICAL_ROLE,
  },
};

export const Contact: Story = {
  args: {
    type: ValueType.CONTACT,
  },
};

export const Expense: Story = {
  args: {
    type: ValueType.EXPENSE,
  },
};

export const Loading: Story = {
  args: {
    type: ValueType.TAG,
    loading: true,
  },
};
