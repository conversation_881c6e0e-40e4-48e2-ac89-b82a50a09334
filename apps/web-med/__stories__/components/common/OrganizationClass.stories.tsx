import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationClass } from "@/api";
import OrganizationClassBadge from "@/components/common/OrganizationClass";

const meta = {
  title: "Components/Common/OrganizationClass",
  component: OrganizationClassBadge,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationClassBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Nonprofit: Story = {
  args: {
    class: OrganizationClass.NONPROFIT,
  },
};

export const Government: Story = {
  args: {
    class: OrganizationClass.GOVERNMENT,
  },
};

export const Private: Story = {
  args: {
    class: OrganizationClass.PRIVATE,
  },
};

export const AllClasses: Story = {
  args: {
    class: OrganizationClass.PRIVATE,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <OrganizationClassBadge class={OrganizationClass.NONPROFIT} />
        <OrganizationClassBadge class={OrganizationClass.GOVERNMENT} />
        <OrganizationClassBadge class={OrganizationClass.PRIVATE} />
      </div>
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="flex items-center gap-4">
          <OrganizationClassBadge class={OrganizationClass.NONPROFIT} />
          <OrganizationClassBadge class={OrganizationClass.GOVERNMENT} />
          <OrganizationClassBadge class={OrganizationClass.PRIVATE} />
        </div>
      </div>
    </div>
  ),
};
