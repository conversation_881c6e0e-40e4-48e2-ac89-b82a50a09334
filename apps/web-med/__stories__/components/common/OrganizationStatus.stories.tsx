import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationStatus } from "@/api";
import OrganizationStatusBadge from "@/components/common/OrganizationStatus";

const meta = {
  title: "Components/Common/OrganizationStatus",
  component: OrganizationStatusBadge,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: OrganizationStatus.PENDING,
  },
};

export const Active: Story = {
  args: {
    status: OrganizationStatus.ACTIVE,
  },
};

export const Inactive: Story = {
  args: {
    status: OrganizationStatus.INACTIVE,
  },
};

export const Rejected: Story = {
  args: {
    status: OrganizationStatus.REJECTED,
  },
};

export const Suspended: Story = {
  args: {
    status: OrganizationStatus.SUSPENDED,
  },
};

export const AllStatuses: Story = {
  args: {
    status: OrganizationStatus.ACTIVE,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <OrganizationStatusBadge status={OrganizationStatus.PENDING} />
        <OrganizationStatusBadge status={OrganizationStatus.ACTIVE} />
        <OrganizationStatusBadge status={OrganizationStatus.INACTIVE} />
        <OrganizationStatusBadge status={OrganizationStatus.REJECTED} />
        <OrganizationStatusBadge status={OrganizationStatus.SUSPENDED} />
      </div>
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="flex items-center gap-4">
          <OrganizationStatusBadge status={OrganizationStatus.PENDING} />
          <OrganizationStatusBadge status={OrganizationStatus.ACTIVE} />
          <OrganizationStatusBadge status={OrganizationStatus.INACTIVE} />
          <OrganizationStatusBadge status={OrganizationStatus.REJECTED} />
          <OrganizationStatusBadge status={OrganizationStatus.SUSPENDED} />
        </div>
      </div>
    </div>
  ),
};
