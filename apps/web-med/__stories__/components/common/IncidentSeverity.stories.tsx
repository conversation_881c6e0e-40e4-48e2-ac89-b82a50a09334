import type { Meta, StoryObj } from "@storybook/react";

import { IncidentSeverity } from "@/api";
import IncidentSeverityBadge from "@/components/common/IncidentSeverity";

const meta = {
  title: "Components/Common/IncidentSeverity",
  component: IncidentSeverityBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof IncidentSeverityBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Minor: Story = {
  args: {
    type: IncidentSeverity.MINOR,
  },
};

export const Major: Story = {
  args: {
    type: IncidentSeverity.MAJOR,
  },
};

export const Critical: Story = {
  args: {
    type: IncidentSeverity.CRITICAL,
  },
};

export const Loading: Story = {
  args: {
    type: IncidentSeverity.MINOR,
    loading: true,
  },
};
