import type { Meta, StoryObj } from "@storybook/react";

import { ProviderStatus } from "@/api";
import ProviderStatusBadge from "@/components/common/ProviderStatus";

const meta = {
  title: "Components/Common/ProviderStatus",
  component: ProviderStatusBadge,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ProviderStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: ProviderStatus.PENDING,
  },
};

export const Active: Story = {
  args: {
    status: ProviderStatus.ACTIVE,
  },
};

export const Inactive: Story = {
  args: {
    status: ProviderStatus.INACTIVE,
  },
};

export const Suspended: Story = {
  args: {
    status: ProviderStatus.SUSPENDED,
  },
};

export const Rejected: Story = {
  args: {
    status: ProviderStatus.REJECTED,
  },
};

export const AllStatuses: Story = {
  args: {
    status: ProviderStatus.ACTIVE,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <ProviderStatusBadge status={ProviderStatus.PENDING} />
        <ProviderStatusBadge status={ProviderStatus.ACTIVE} />
        <ProviderStatusBadge status={ProviderStatus.INACTIVE} />
        <ProviderStatusBadge status={ProviderStatus.SUSPENDED} />
        <ProviderStatusBadge status={ProviderStatus.REJECTED} />
      </div>
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="flex items-center gap-4">
          <ProviderStatusBadge status={ProviderStatus.PENDING} />
          <ProviderStatusBadge status={ProviderStatus.ACTIVE} />
          <ProviderStatusBadge status={ProviderStatus.INACTIVE} />
          <ProviderStatusBadge status={ProviderStatus.SUSPENDED} />
          <ProviderStatusBadge status={ProviderStatus.REJECTED} />
        </div>
      </div>
    </div>
  ),
};
