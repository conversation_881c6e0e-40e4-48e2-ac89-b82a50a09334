import type { Meta, StoryObj } from "@storybook/react";

import { PayoutType } from "@/api";
import PayoutTypeBadge from "@/components/common/PayoutType";

const meta = {
  title: "Components/Common/PayoutType",
  component: PayoutTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof PayoutTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Platform: Story = {
  args: {
    type: PayoutType.PLATFORM,
  },
};

export const External: Story = {
  args: {
    type: PayoutType.EXTERNAL,
  },
};

export const Loading: Story = {
  args: {
    type: PayoutType.PLATFORM,
    loading: true,
  },
};
