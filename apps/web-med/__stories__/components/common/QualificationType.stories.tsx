import type { Meta, StoryObj } from "@storybook/react";

import { QualificationType } from "@/api";
import QualificationTypeBadge from "@/components/common/QualificationType";

const meta = {
  title: "Components/Common/QualificationType",
  component: QualificationTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof QualificationTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Degree: Story = {
  args: {
    type: QualificationType.DEGREE,
  },
};

export const License: Story = {
  args: {
    type: QualificationType.LICENSE,
  },
};

export const Certificate: Story = {
  args: {
    type: QualificationType.CERTIFICATE,
  },
};

export const Other: Story = {
  args: {
    type: QualificationType.OTHER,
  },
};

export const Loading: Story = {
  args: {
    type: QualificationType.DEGREE,
    loading: true,
  },
};
