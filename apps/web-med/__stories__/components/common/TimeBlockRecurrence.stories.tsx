import type { Meta, StoryObj } from "@storybook/react";

import { TimeBlockRecurrence } from "@/api";
import TimeBlockRecurrenceBadge from "@/components/common/TimeBlockRecurrence";

const meta = {
  title: "Components/Common/TimeBlockRecurrence",
  component: TimeBlockRecurrenceBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof TimeBlockRecurrenceBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Weekly: Story = {
  args: {
    type: TimeBlockRecurrence.WEEKLY,
  },
};

export const Biweekly: Story = {
  args: {
    type: TimeBlockRecurrence.BIWEEKLY,
  },
};

export const Monthly: Story = {
  args: {
    type: TimeBlockRecurrence.MONTHLY,
  },
};

export const Quarterly: Story = {
  args: {
    type: TimeBlockRecurrence.QUARTERLY,
  },
};

export const Yearly: Story = {
  args: {
    type: TimeBlockRecurrence.YEARLY,
  },
};

export const Loading: Story = {
  args: {
    type: TimeBlockRecurrence.WEEKLY,
    loading: true,
  },
};
