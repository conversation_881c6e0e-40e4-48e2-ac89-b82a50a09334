import type { Meta, StoryObj } from "@storybook/react";

import { JobPostMode } from "@/api";
import JobPostModeBadge from "@/components/common/JobPostMode";

const meta = {
  title: "Components/Common/JobPostMode",
  component: JobPostModeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof JobPostModeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Independent: Story = {
  args: {
    type: JobPostMode.INDEPENDENT,
  },
};

export const Assisted: Story = {
  args: {
    type: JobPostMode.ASSISTED,
  },
};

export const Loading: Story = {
  args: {
    type: JobPostMode.INDEPENDENT,
    loading: true,
  },
};
