import type { Meta, StoryObj } from "@storybook/react";

import { TimeBlockType } from "@/api";
import TimeBlockTypeBadge from "@/components/common/TimeBlockType";

const meta = {
  title: "Components/Common/TimeBlockType",
  component: TimeBlockTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof TimeBlockTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Availability: Story = {
  args: {
    type: TimeBlockType.AVAILABILITY,
  },
};

export const TimeOff: Story = {
  args: {
    type: TimeBlockType.TIME_OFF,
  },
};

export const Shift: Story = {
  args: {
    type: TimeBlockType.SHIFT,
  },
};

export const Loading: Story = {
  args: {
    type: TimeBlockType.AVAILABILITY,
    loading: true,
  },
};
