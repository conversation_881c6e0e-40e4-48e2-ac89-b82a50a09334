import type { Meta, StoryObj } from "@storybook/react";

import { SignatureStatus } from "@/api";
import SignatureStatusBadge from "@/components/common/SignatureStatus";

const meta = {
  title: "Components/Common/SignatureStatus",
  component: SignatureStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof SignatureStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    type: SignatureStatus.PENDING,
  },
};

export const Signed: Story = {
  args: {
    type: SignatureStatus.SIGNED,
  },
};

export const Rejected: Story = {
  args: {
    type: SignatureStatus.REJECTED,
  },
};

export const Loading: Story = {
  args: {
    type: SignatureStatus.PENDING,
    loading: true,
  },
};
