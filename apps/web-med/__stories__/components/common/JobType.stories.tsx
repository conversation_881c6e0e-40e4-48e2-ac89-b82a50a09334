import type { Meta, StoryObj } from "@storybook/react";

import { JobPostType } from "@/api";
import JobType from "@/components/common/JobType";

const meta = {
  title: "Components/Common/JobType",
  component: JobType,
  tags: ["autodocs"],
} satisfies Meta<typeof JobType>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Permanent: Story = {
  args: {
    type: JobPostType.PERMANENT,
  },
};

export const Temporary: Story = {
  args: {
    type: JobPostType.TEMPORARY,
  },
};

export const PerDiem: Story = {
  args: {
    type: JobPostType.PER_DIEM,
  },
};

export const Loading: Story = {
  args: {
    type: JobPostType.PERMANENT,
    loading: true,
  },
};
