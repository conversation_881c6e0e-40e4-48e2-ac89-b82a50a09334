import type { <PERSON>a, StoryObj } from "@storybook/react";

import { OfferStatus } from "@/api";
import OfferStatusType from "@/components/common/OfferStatus";

const meta = {
  title: "Components/Common/OfferStatus",
  component: OfferStatusType,
  tags: ["autodocs"],
} satisfies Meta<typeof OfferStatusType>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: OfferStatus.PENDING,
  },
};

export const Accepted: Story = {
  args: {
    status: OfferStatus.ACCEPTED,
  },
};

export const Rejected: Story = {
  args: {
    status: OfferStatus.REJECTED,
  },
};

export const Withdrawn: Story = {
  args: {
    status: OfferStatus.WITHDRAWN,
  },
};

export const Loading: Story = {
  args: {
    status: OfferStatus.PENDING,
    loading: true,
  },
};
