import type { Meta, StoryObj } from "@storybook/react";

import { ShiftStatus } from "@/api";
import ShiftStatusBadge from "@/components/common/ShiftStatus";

const meta = {
  title: "Components/Common/ShiftStatus",
  component: ShiftStatusBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ShiftStatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    status: ShiftStatus.PENDING,
  },
};

export const Confirmed: Story = {
  args: {
    status: ShiftStatus.CONFIRMED,
  },
};

export const Active: Story = {
  args: {
    status: ShiftStatus.ACTIVE,
  },
};

export const Completed: Story = {
  args: {
    status: ShiftStatus.COMPLETED,
  },
};

export const Cancelled: Story = {
  args: {
    status: ShiftStatus.CANCELLED,
  },
};

export const Approved: Story = {
  args: {
    status: ShiftStatus.APPROVED,
  },
};

export const Loading: Story = {
  args: {
    status: ShiftStatus.PENDING,
    loading: true,
  },
};
