import type { Meta, StoryObj } from "@storybook/react";

import { ContractType } from "@/api";
import ContractTypeBadge from "@/components/common/ContractType";

const meta = {
  title: "Components/Common/ContractType",
  component: ContractTypeBadge,
  tags: ["autodocs"],
} satisfies Meta<typeof ContractTypeBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Employment: Story = {
  args: {
    type: ContractType.EMPLOYMENT,
  },
};

export const ServiceAgreement: Story = {
  args: {
    type: ContractType.SERVICE_AGREEMENT,
  },
};

export const ServiceRate: Story = {
  args: {
    type: ContractType.SERVICE_RATE,
  },
};

export const Loading: Story = {
  args: {
    type: ContractType.EMPLOYMENT,
    loading: true,
  },
};
