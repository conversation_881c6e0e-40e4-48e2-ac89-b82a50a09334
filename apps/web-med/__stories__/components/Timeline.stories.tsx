import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Timeline } from "@/components/Timeline";

const meta = {
  title: "Components/Timeline",
  component: Timeline,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Timeline>;

export default meta;
type Story = StoryObj<typeof meta>;

const DEMO_STEPS = [
  { key: "step1", label: "Step 1", date: new Date("2024-01-01") },
  { key: "step2", label: "Step 2", date: new Date("2024-01-15") },
  { key: "step3", label: "Step 3", date: new Date("2024-02-01") },
  { key: "step4", label: "Step 4", date: new Date("2024-02-15") },
];

export const Default: Story = {
  args: {
    steps: DEMO_STEPS,
    currentStepIndex: 1,
  },
};

export const WithEvents: Story = {
  args: {
    steps: DEMO_STEPS,
    currentStepIndex: 1,
    events: [
      {
        type: "Error Event",
        date: new Date("2024-01-10"),
        variant: "error",
      },
      {
        type: "Warning Event",
        date: new Date("2024-02-05"),
        variant: "warning",
      },
    ],
  },
};

export const Loading: Story = {
  args: {
    steps: DEMO_STEPS,
    currentStepIndex: 1,
    loading: true,
  },
};

export const Terminated: Story = {
  args: {
    steps: DEMO_STEPS,
    currentStepIndex: 1,
    isTerminated: true,
  },
};

export const CustomContent: Story = {
  args: {
    steps: DEMO_STEPS,
    currentStepIndex: 1,
    renderStepContent: ({ step, status }) => (
      <div className="absolute -bottom-12 left-1/2 -translate-x-1/2">
        <div className="rounded-md bg-background p-2 shadow-sm">
          <div className="font-medium">{step.label}</div>
          <div className="text-xs text-muted-foreground">Status: {status}</div>
        </div>
      </div>
    ),
  },
};
