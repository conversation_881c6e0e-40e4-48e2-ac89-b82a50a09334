import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobListing } from "@/components/jobs/JobListing";

const meta: Meta<typeof JobListing> = {
  title: "Components/Jobs/JobListing",
  component: JobListing,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobListing>;

export const Default: Story = {
  args: {
    id: 1,
    title: "Registered Nurse - ICU",
    company: "City General Hospital",
    location: "New York, NY",
    category: "Nursing",
    description:
      "Seeking experienced RN for our Intensive Care Unit. Must have 3+ years of ICU experience.",
    postedDate: "2023-06-01",
    schedule: {
      totalHours: 40,
      startTime: "07:00",
    },
    hourlyRate: 45,
    positionType: "Full-time",
    onApply: (id) => console.log(`Applied to job with id: ${id}`),
  },
};

export const PartTime: Story = {
  args: {
    ...Default.args,
    id: 2,
    title: "Pediatric Nurse - Part Time",
    company: "Children's Health Center",
    category: "Nursing",
    description:
      "Looking for a compassionate pediatric nurse for part-time shifts.",
    schedule: {
      totalHours: 20,
      startTime: "09:00",
    },
    hourlyRate: 40,
    positionType: "Part-time",
  },
};

export const PerDiem: Story = {
  args: {
    ...Default.args,
    id: 3,
    title: "Emergency Room Physician - Per Diem",
    company: "Metro Emergency Hospital",
    category: "Physician",
    description: "Experienced ER physician needed for per diem shifts.",
    schedule: {
      totalHours: 12,
      startTime: "19:00",
    },
    hourlyRate: 150,
    positionType: "Per Diem",
  },
};
