import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobApplicationCard } from "@/components/jobs/JobApplicationCard";

const meta: Meta<typeof JobApplicationCard> = {
  title: "Components/Jobs/JobApplicationCard",
  component: JobApplicationCard,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobApplicationCard>;

const baseApplication = {
  id: "1",
  submittedAt: new Date("2023-05-15"),
  jobPost: {
    summary: "ICU Nurse",
    startDate: new Date("2023-07-01"),
    endDate: new Date("2023-12-31"),
    payRate: 50,
    payType: "HOURLY" as const,
    location: {
      name: "Memorial Hospital",
    },
  },
};

export const Pending: Story = {
  args: {
    application: {
      ...baseApplication,
      status: "PENDING",
    },
  },
};

export const UnderReview: Story = {
  args: {
    application: {
      ...baseApplication,
      status: "UNDER_REVIEW",
      reviewedAt: new Date("2023-05-20"),
    },
  },
};

export const Accepted: Story = {
  args: {
    application: {
      ...baseApplication,
      status: "ACCEPTED",
      reviewedAt: new Date("2023-05-25"),
      notes: "Congratulations! Your application has been accepted.",
    },
  },
};

export const Rejected: Story = {
  args: {
    application: {
      ...baseApplication,
      status: "REJECTED",
      reviewedAt: new Date("2023-05-25"),
      notes:
        "We regret to inform you that your application was not successful.",
    },
  },
};

export const Withdrawn: Story = {
  args: {
    application: {
      ...baseApplication,
      status: "WITHDRAWN",
      notes: "Application withdrawn by the candidate.",
    },
  },
};
