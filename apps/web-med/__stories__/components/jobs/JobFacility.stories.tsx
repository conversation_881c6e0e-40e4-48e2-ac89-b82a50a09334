import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobFacility } from "@/components/jobs/JobFacility";

const meta: Meta<typeof JobFacility> = {
  title: "Components/Jobs/JobFacility",
  component: JobFacility,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobFacility>;

const mockFacility = {
  id: "loc1",
  name: "Main Campus",
  type: "HOSPITAL",
  description: "Main hospital campus",
  address: {
    formatted: "123 Medical Dr, Cityville, ST 12345",
    latitude: 40.7128,
    longitude: -74.006,
    timeZone: "America/New_York",
  },
  department: {
    id: "dept1",
    name: "Emergency Department",
  },
};

const mockContact = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
};

export const Default: Story = {
  args: {
    facility: mockFacility,
    contact: mockContact,
    onUpdate: async (data) => {
      console.log("Updating facility", data);
    },
  },
};

export const WithoutDepartment: Story = {
  args: {
    ...Default.args,
    facility: {
      ...mockFacility,
      department: undefined,
    },
  },
};

export const LongAddress: Story = {
  args: {
    ...Default.args,
    facility: {
      ...mockFacility,
      address: {
        ...mockFacility.address,
        formatted:
          "123 Very Long Street Name, Suite 1000, Big City With A Long Name, State With A Long Name 12345-6789, Country With A Very Long Name",
      },
    },
  },
};
