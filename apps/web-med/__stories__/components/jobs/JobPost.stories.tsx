import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { JobPost } from "@/components/jobs/JobPost";

const meta: Meta<typeof JobPost> = {
  title: "Components/Jobs/JobPost",
  component: JobPost,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobPost>;

const mockJobData = {
  id: "1",
  externalId: null,
  poNumber: null,
  summary: "Emergency Room Physician",
  description:
    "We are seeking an experienced Emergency Room Physician for night shift coverage. The ideal candidate will be comfortable with high-pressure situations and a diverse range of medical emergencies.",
  type: "FULL_TIME",
  schedule: [
    { day: "Monday", startTime: "9:00 AM", endTime: "5:00 PM" },
    { day: "Tuesday", startTime: "9:00 AM", endTime: "5:00 PM" },
    { day: "Wednesday", startTime: "9:00 AM", endTime: "5:00 PM" },
    { day: "Thursday", startTime: "9:00 AM", endTime: "5:00 PM" },
    { day: "Friday", startTime: "9:00 AM", endTime: "5:00 PM" },
  ],
  paymentType: "HOURLY",
  paymentRate: 150,
  startDate: new Date("2024-09-28T19:00:00"), // 7:00 PM
  endDate: new Date("2024-09-29T07:00:00"), // 7:00 AM next day
  organization: {
    id: "org1",
    name: "City General Hospital",
    avatar: "/placeholder.svg",
  },
  location: {
    id: "loc1",
    name: "Main Campus",
    type: "HOSPITAL",
    description: "Main hospital campus",
    address: {
      formatted: "123 Medical Dr, Cityville, ST 12345",
      latitude: 40.7128,
      longitude: -74.006,
      timeZone: "America/New_York",
    },
    department: {
      id: "dept1",
      name: "Emergency Department",
    },
  },
  contacts: [
    {
      name: "Gabriel Rashad",
      email: "<EMAIL>",
      phone: "+****************",
    },
  ],
  applications: [
    {
      id: "app1",
      provider: {
        person: {
          firstName: "Jane",
          lastName: "Smith",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Emergency Medicine" }],
        yearsOfExperience: 5,
      },
    },
    {
      id: "app2",
      provider: {
        person: {
          firstName: "John",
          lastName: "Doe",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Internal Medicine" }],
        yearsOfExperience: 3,
      },
    },
  ],
  offers: [
    {
      id: "offer1",
      provider: {
        person: {
          firstName: "Alice",
          lastName: "Johnson",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Emergency Medicine" }],
        yearsOfExperience: 7,
      },
    },
  ],
};

const mockProspects = [
  {
    id: "prospect1",
    person: {
      firstName: "Bob",
      lastName: "Williams",
      avatar: "/placeholder.svg",
    },
    specialties: [{ name: "Pediatrics" }],
    yearsOfExperience: 4,
  },
  {
    id: "prospect2",
    person: {
      firstName: "Carol",
      lastName: "Brown",
      avatar: "/placeholder.svg",
    },
    specialties: [{ name: "Emergency Medicine" }],
    yearsOfExperience: 6,
  },
];

export const Default: Story = {
  args: {
    job: mockJobData,
    loading: false,
    onUpdate: async (id, data) => {
      console.log("Updating job", id, data);
    },
    onSearchProspects: async (query, specialty, experience) => {
      console.log("Searching prospects", query, specialty, experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return mockProspects;
    },
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
};
