import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { JobSchedule } from "@/components/jobs/JobSchedule";

const meta: Meta<typeof JobSchedule> = {
  title: "Components/Jobs/JobSchedule",
  component: JobSchedule,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobSchedule>;

export const FullTime: Story = {
  args: {
    type: "FULL_TIME",
    schedule: [
      { day: "Monday", startTime: "9:00 AM", endTime: "5:00 PM" },
      { day: "Tuesday", startTime: "9:00 AM", endTime: "5:00 PM" },
      { day: "Wednesday", startTime: "9:00 AM", endTime: "5:00 PM" },
      { day: "Thursday", startTime: "9:00 AM", endTime: "5:00 PM" },
      { day: "Friday", startTime: "9:00 AM", endTime: "5:00 PM" },
    ],
  },
};

export const PartTime: Story = {
  args: {
    type: "PART_TIME",
    schedule: [
      { day: "Monday", startTime: "9:00 AM", endTime: "2:00 PM" },
      { day: "Wednesday", startTime: "9:00 AM", endTime: "2:00 PM" },
      { day: "Friday", startTime: "9:00 AM", endTime: "2:00 PM" },
    ],
    hoursPerWeek: 15,
  },
};

export const PerDiem: Story = {
  args: {
    type: "PER_DIEM",
    schedule: [
      { day: "Saturday", startTime: "10:00 AM", endTime: "6:00 PM" },
      { day: "Sunday", startTime: "10:00 AM", endTime: "6:00 PM" },
    ],
    hoursPerWeek: 16,
  },
};
