import type { <PERSON>a, StoryObj } from "@storybook/react";

import { JobOfferCard } from "@/components/jobs/JobOfferCard";

const meta: Meta<typeof JobOfferCard> = {
  title: "Components/Jobs/JobOfferCard",
  component: JobOfferCard,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobOfferCard>;

const baseOffer = {
  id: "1",
  details: "We are excited to offer you this position...",
  jobPost: {
    summary: "Emergency Room Nurse",
    startDate: new Date("2023-07-01"),
    endDate: new Date("2023-09-30"),
    payRate: 45,
    payType: "HOURLY" as const,
    location: {
      name: "City General Hospital",
    },
  },
};

export const Pending: Story = {
  args: {
    offer: {
      ...baseOffer,
      status: "PENDING",
      expiresAt: new Date("2023-06-15"),
    },
  },
};

export const Accepted: Story = {
  args: {
    offer: {
      ...baseOffer,
      status: "ACCEPTED",
    },
  },
};

export const Rejected: Story = {
  args: {
    offer: {
      ...baseOffer,
      status: "REJECTED",
    },
  },
};

export const Withdrawn: Story = {
  args: {
    offer: {
      ...baseOffer,
      status: "WITHDRAWN",
    },
  },
};
