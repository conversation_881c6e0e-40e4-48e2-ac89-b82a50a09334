import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobCandidate } from "@/components/jobs/JobCandidate";

const meta: Meta<typeof JobCandidate> = {
  title: "Components/Jobs/JobCandidate",
  component: JobCandidate,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobCandidate>;

export const Default: Story = {
  args: {
    name: "Dr. <PERSON>",
    avatar: "/placeholder.svg",
    specialty: "Emergency Medicine",
    yearsOfExperience: 5,
    onViewProfile: () => console.log("View profile clicked"),
    onMakeOffer: () => console.log("Make offer clicked"),
  },
};
