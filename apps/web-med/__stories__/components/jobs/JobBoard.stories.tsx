import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type {
  DayOfWeek,
  Job,
  JobCategory,
  PositionType,
} from "@/components/jobs/JobBoard";

import JobBoard from "@/components/jobs/JobBoard";

const meta: Meta<typeof JobBoard> = {
  title: "Components/Jobs/JobBoard",
  component: JobBoard,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof JobBoard>;

const jobCategories: JobCategory[] = [
  "All",
  "Nursing",
  "Physician",
  "Specialist",
  "Administrative",
];

const daysOfWeek: DayOfWeek[] = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const positionTypes: PositionType[] = ["Full-time", "Part-time", "Per Diem"];

const allLocations = [
  "New York, NY",
  "Los Angeles, CA",
  "Chicago, IL",
  "Houston, TX",
  "Phoenix, AZ",
  "Philadelphia, PA",
  "San Antonio, TX",
  "San Diego, CA",
  "Dallas, TX",
  "San Jose, CA",
];

const jobs: Job[] = [
  {
    id: 1,
    title: "Registered Nurse - ICU",
    company: "City General Hospital",
    location: "New York, NY",
    category: "Nursing",
    description:
      "Seeking experienced RN for our Intensive Care Unit. Must have 3+ years of ICU experience.",
    postedDate: "2023-06-01",
    schedule: {
      days: ["Monday", "Tuesday", "Wednesday", "Thursday"],
      totalHours: 40,
      startTime: "07:00",
    },
    hourlyRate: 45,
    positionType: "Full-time",
  },
  {
    id: 2,
    title: "Family Medicine Physician",
    company: "Hometown Health Clinic",
    location: "Chicago, IL",
    category: "Physician",
    description:
      "Join our growing family practice. Board certification required.",
    postedDate: "2023-05-28",
    schedule: {
      days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      totalHours: 45,
      startTime: "08:00",
    },
    hourlyRate: 100,
    positionType: "Full-time",
  },
  {
    id: 3,
    title: "Cardiologist",
    company: "Heart Health Center",
    location: "Los Angeles, CA",
    category: "Specialist",
    description:
      "Seeking board-certified cardiologist to join our team of heart specialists.",
    postedDate: "2023-05-25",
    schedule: {
      days: ["Monday", "Wednesday", "Friday"],
      totalHours: 36,
      startTime: "09:00",
    },
    hourlyRate: 150,
    positionType: "Part-time",
  },
  {
    id: 4,
    title: "Medical Office Manager",
    company: "Wellness Medical Group",
    location: "Houston, TX",
    category: "Administrative",
    description:
      "Experienced office manager needed to oversee daily operations of our multi-physician practice.",
    postedDate: "2023-06-02",
    schedule: {
      days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      totalHours: 40,
      startTime: "08:30",
    },
    hourlyRate: 35,
    positionType: "Full-time",
  },
  {
    id: 5,
    title: "Emergency Room Nurse",
    company: "Metro Emergency Hospital",
    location: "Miami, FL",
    category: "Nursing",
    description:
      "Fast-paced ER seeking skilled nurses. Must be able to work nights and weekends.",
    postedDate: "2023-05-30",
    schedule: {
      days: ["Thursday", "Friday", "Saturday", "Sunday"],
      totalHours: 36,
      startTime: "19:00",
    },
    hourlyRate: 50,
    positionType: "Per Diem",
  },
];

export const Default: Story = {
  args: {
    jobCategories,
    daysOfWeek,
    positionTypes,
    allLocations,
    jobs,
  },
};

export const NoJobs: Story = {
  args: {
    ...Default.args,
    jobs: [],
  },
};
