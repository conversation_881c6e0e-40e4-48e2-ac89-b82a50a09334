import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { JobProspects } from "@/components/jobs/JobProspects";

const meta: Meta<typeof JobProspects> = {
  title: "Components/Jobs/JobProspects",
  component: JobProspects,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobProspects>;

const mockJob = {
  id: "1",
  applications: [
    {
      id: "app1",
      provider: {
        person: {
          firstName: "<PERSON>",
          lastName: "<PERSON>",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Emergency Medicine" }],
        yearsOfExperience: 5,
      },
    },
    {
      id: "app2",
      provider: {
        person: {
          firstName: "<PERSON>",
          lastName: "Doe",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Internal Medicine" }],
        yearsOfExperience: 3,
      },
    },
  ],
  offers: [
    {
      id: "offer1",
      provider: {
        person: {
          firstName: "<PERSON>",
          lastName: "<PERSON>",
          avatar: "/placeholder.svg",
        },
        specialties: [{ name: "Emergency Medicine" }],
        yearsOfExperience: 7,
      },
    },
  ],
};

const mockProspects = [
  {
    id: "prospect1",
    person: {
      firstName: "Bob",
      lastName: "Williams",
      avatar: "/placeholder.svg",
    },
    specialties: [{ name: "Pediatrics" }],
    yearsOfExperience: 4,
  },
  {
    id: "prospect2",
    person: {
      firstName: "Carol",
      lastName: "Brown",
      avatar: "/placeholder.svg",
    },
    specialties: [{ name: "Emergency Medicine" }],
    yearsOfExperience: 6,
  },
];

export const Default: Story = {
  args: {
    job: mockJob as any,
    onSearchProspects: async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return mockProspects;
    },
  },
};

export const EmptyResults: Story = {
  args: {
    ...Default.args,
    onSearchProspects: async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return [];
    },
  },
};
