import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import PaymentMethodsList, {
  PaymentMethod,
  PaymentMethodSkeleton,
} from "@/components/widgets/stripe/PaymentMethods";

const meta = {
  title: "Components/Widgets/Stripe/PaymentMethods",
  component: PaymentMethodsList,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof PaymentMethodsList>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock payment methods
const mockMethods: RouterOutputs["billing"]["customers"]["methods"] = [
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
  },
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "mastercard",
    last4: "8888",
    expMonth: 3,
    expYear: 2026,
  },
  {
    id: faker.string.uuid(),
    type: "us_bank_account",
    brand: undefined,
    last4: "6789",
    expMonth: undefined,
    expYear: undefined,
    // Additional bank account properties
    bankName: "Chase Bank",
    accountType: "checking",
    accountHolderType: "individual",
  },
];

// Mock organization
const mockOrganization = {
  id: faker.string.uuid(),
  name: faker.company.name(),
};

// Mock mutations
const mockCreateSetupIntent = {
  mutateAsync: async () => ({ clientSecret: "mock_client_secret" }),
  isPending: false,
} as any;

const mockRemovePaymentMethod = {
  mutateAsync: async () => ({}),
  isPending: false,
} as any;

// Stories for PaymentMethodsList
export const Default: Story = {
  args: {
    methods: mockMethods,
    organization: mockOrganization as any,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: new Error("Failed to load payment methods"),
  },
};

export const Empty: Story = {
  args: {
    methods: [],
    organization: mockOrganization as any,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const RemovingPaymentMethod: Story = {
  args: {
    methods: mockMethods,
    organization: mockOrganization as any,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: {
      ...mockRemovePaymentMethod,
      isPending: true,
    },
  },
};

// Stories for individual PaymentMethod component
export const CreditCard: StoryObj<typeof PaymentMethod> = {
  render: (args) => <PaymentMethod {...args} />,
  args: {
    method: mockMethods[0],
    onRemove: () => {},
  },
};

export const BankAccount: StoryObj<typeof PaymentMethod> = {
  render: (args) => <PaymentMethod {...args} />,
  args: {
    method: {
      id: faker.string.uuid(),
      type: "us_bank_account",
      brand: undefined,
      last4: "6789",
      expMonth: undefined,
      expYear: undefined,
      bankName: "Chase Bank",
      accountType: "checking",
      accountHolderType: "individual",
    },
    onRemove: () => {},
  },
};

export const BusinessBankAccount: StoryObj<typeof PaymentMethod> = {
  render: (args) => <PaymentMethod {...args} />,
  args: {
    method: {
      id: faker.string.uuid(),
      type: "us_bank_account",
      brand: undefined,
      last4: "9876",
      expMonth: undefined,
      expYear: undefined,
      bankName: "Bank of America",
      accountType: "savings",
      accountHolderType: "company",
    },
    onRemove: () => {},
  },
};

export const PaymentMethodSkeletonStory: StoryObj<
  typeof PaymentMethodSkeleton
> = {
  render: () => <PaymentMethodSkeleton />,
};
