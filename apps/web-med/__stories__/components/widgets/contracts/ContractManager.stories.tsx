import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import { ContractStatus, ContractType } from "@/api";
import { trpcMsw } from "@/api/mock";
import { ContractManager } from "@/components/widgets/contracts/manager/Manager";

import { contract, thread } from "./data";

const meta = {
  title: "Components/Widgets/Contracts/ContractManager",
  component: ContractManager,
  parameters: {
    layout: "centered",
    nextjs: { appDirectory: true },
    msw: {
      handlers: [
        // trpcMsw.contracts.get.query(({ input }) => {
        //   return contract;
        // }),
        // trpcMsw.threads.get.query(({ input }) => {
        //   return thread;
        // }),
      ],
    },
  },
} satisfies Meta<typeof ContractManager>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic example
export const Basic: Story = {
  args: {
    loading: false,
    error: null,
  },
};

export const WithAgreements: Story = {
  args: {
    loading: false,
    error: null,
    contract,
  },
};
