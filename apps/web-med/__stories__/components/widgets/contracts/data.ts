import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  AgreementStatus,
  ContractStatus,
  ContractType,
  SignatureStatus,
} from "@/api";

import {
  createMockMessage,
  createMockThread,
} from "../../../pages/organizations/contract/data";

// Set a seed for reproducible results
faker.seed(123);

// Factory function for creating mock persons (providers, etc.)
export function createMockPerson(overrides = {}) {
  return {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    title: faker.helpers.arrayElement(["Dr.", "Mr.", "Ms.", "Mrs."]),
    avatar: faker.helpers.maybe(() => faker.image.avatar()),
    ...overrides,
  };
}

// Factory function for creating mock organizations
export function createMockOrganization(overrides = {}) {
  return {
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: faker.helpers.maybe(() => faker.image.avatar()),
    ...overrides,
  };
}

// Factory function for creating mock signatures
export function createMockSignature(overrides = {}) {
  const status =
    overrides.status ??
    faker.helpers.arrayElement(Object.values(SignatureStatus));
  const createdAt = faker.date.recent({ days: 30 }).toISOString();

  return {
    id: faker.string.uuid(),
    status,
    createdAt,
    signedAt:
      status === "SIGNED"
        ? faker.date.between({ from: createdAt, to: new Date() }).toISOString()
        : null,
    rejectedAt:
      status === "REJECTED"
        ? faker.date.between({ from: createdAt, to: new Date() }).toISOString()
        : null,
    documensoToken: faker.helpers.maybe(() => faker.string.alphanumeric(20)),
    person: overrides.person || createMockPerson(),
    ...overrides,
  };
}

// Factory function for creating mock agreements
export function createMockAgreement(overrides = {}) {
  const status =
    overrides.status ||
    faker.helpers.arrayElement(Object.values(AgreementStatus));
  const createdAt = faker.date.recent({ days: 60 }).toISOString();

  return {
    id: faker.string.uuid(),
    status,
    documensoId: faker.string.uuid(),
    documensoUrl: `https://example.com/documents/${faker.string.alphanumeric(10)}.pdf`,
    createdAt,
    expiresAt: faker.helpers.maybe(() => faker.date.future().toISOString()),
    signedAt:
      status === AgreementStatus.SIGNED
        ? faker.date.between({ from: createdAt, to: new Date() }).toISOString()
        : null,
    rejectedAt:
      status === AgreementStatus.REJECTED
        ? faker.date.between({ from: createdAt, to: new Date() }).toISOString()
        : null,
    signatures:
      overrides.signatures ||
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        createMockSignature(),
      ),
    ...overrides,
  };
}

// Factory function for creating mock jobs
export function createMockJob(overrides = {}) {
  return {
    id: faker.string.uuid(),
    status: "ACTIVE",
    summary: faker.person.jobTitle(),
    scope: faker.person.jobArea(),
    ...overrides,
  };
}

// Factory function for creating mock positions
export function createMockPosition(overrides = {}) {
  return {
    id: faker.string.uuid(),
    summary: `${faker.person.jobTitle()} - ${faker.person.jobArea()}`,
    status: "ACTIVE",
    provider: overrides.provider || {
      id: faker.string.uuid(),
      title: faker.helpers.arrayElement(["Dr.", "Mr.", "Ms."]),
      person: createMockPerson(),
    },
    job: overrides.job || createMockJob(),
    ...overrides,
  };
}

// Factory function for creating mock contracts
export function createMockContract(
  overrides = {},
): RouterOutputs["contracts"]["get"] {
  const createdAt = faker.date.recent({ days: 90 }).toISOString();

  return {
    id: faker.string.uuid(),
    title: `${faker.helpers.arrayElement(["Employment", "Consulting", "Temporary"])} Contract - ${faker.person.jobTitle()}`,
    type: faker.helpers.arrayElement(Object.values(ContractType)),
    status: faker.helpers.arrayElement(Object.values(ContractStatus)),
    createdAt,
    updatedAt: faker.date
      .between({ from: createdAt, to: new Date() })
      .toISOString(),
    deletedAt: null,
    expiresAt: faker.date.future().toISOString(),
    provider: overrides.provider || {
      id: faker.string.uuid(),
      title: faker.helpers.arrayElement(["Dr.", "Mr.", "Ms."]),
      person: createMockPerson(),
    },
    organization: overrides.organization || createMockOrganization(),
    position: overrides.position || createMockPosition(),
    agreements:
      overrides.agreements ||
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        createMockAgreement(),
      ),
    ...overrides,
  };
}

// Create specific user types for stories
export const users = {
  provider: createMockPerson({
    id: "provider-1",
    firstName: "John",
    lastName: "Smith",
    title: "Dr.",
  }),
  organization: createMockOrganization({
    id: "org-1",
    name: "General Hospital",
  }),
  admin: createMockPerson({
    id: "admin-1",
    firstName: "Admin",
    lastName: "User",
    title: "Ms.",
  }),
  currentUser: createMockPerson({
    id: "current-user",
    firstName: "Jane",
    lastName: "Doe",
    title: "Dr.",
  }),
};

// Generate specific contract variants for stories
export const contract = createMockContract({
  id: "contract-1",
  title: "Employment Contract - Senior Nurse Practitioner",
  type: ContractType.EMPLOYMENT,
  status: ContractStatus.PENDING,
  provider: {
    id: users.provider.id,
    title: users.provider.title,
    person: {
      id: users.provider.id,
      firstName: users.provider.firstName,
      lastName: users.provider.lastName,
      avatar: null,
    },
  },
  organization: {
    id: users.organization.id,
    name: users.organization.name,
    avatar: null,
  },
});

// Contract with no agreements
export const contractNoAgreements = createMockContract({
  id: "contract-2",
  agreements: [],
});

// Finalized contract
export const finalizedContract = createMockContract({
  id: "contract-3",
  status: ContractStatus.ACTIVE,
  agreements: [
    createMockAgreement({
      status: AgreementStatus.SIGNED,
    }),
  ],
});

export const thread = createMockThread({
  id: "thread-1",
  messages: [createMockMessage()],
});
