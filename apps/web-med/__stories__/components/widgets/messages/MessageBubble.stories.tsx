import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { action } from "storybook/actions";

import type { Message } from "@/hooks/use-messenger";

import MessageBubble from "@/components/widgets/messages/message-bubble";

import { editedMessage, messages, users } from "./data";

const meta = {
  title: "Components/Widgets/Messages/MessageBubble",
  component: MessageBubble,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof MessageBubble>;

export default meta;
type Story = StoryObj<typeof meta>;

// Add orderBy property to messages
const enhanceMessage = (message: any): Message => ({
  ...message,
  orderBy: {} as never,
  createdAt: new Date(message.createdAt),
  updatedAt: new Date(message.updatedAt),
  deletedAt: message.deletedAt ? new Date(message.deletedAt) : null,
});

// Agent message (not current user)
export const AgentMessage: Story = {
  args: {
    message: enhanceMessage(messages[0]),
    isCurrentUser: false,
  },
};

// User message (current user)
export const UserMessage: Story = {
  args: {
    message: enhanceMessage(messages[1]),
    isCurrentUser: true,
  },
};

// Doctor message
export const DoctorMessage: Story = {
  args: {
    message: enhanceMessage(messages[4]),
    isCurrentUser: false,
  },
};

// Long message
export const LongMessage: Story = {
  args: {
    message: enhanceMessage({
      ...messages[0],
      content:
        "This is a very long message that should wrap to multiple lines. It demonstrates how the message bubble handles long content and ensures that the text is readable and properly formatted within the constraints of the UI. Long messages are common in chat applications, especially when users are explaining complex situations or providing detailed information.",
    }),
    isCurrentUser: false,
  },
};

// Message with edited content
export const EditedMessage: Story = {
  args: {
    message: enhanceMessage(editedMessage),
    isCurrentUser: true,
  },
};

// Editable message with dropdown
export const EditableMessage: Story = {
  args: {
    message: enhanceMessage(messages[1]),
    isCurrentUser: true,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};
