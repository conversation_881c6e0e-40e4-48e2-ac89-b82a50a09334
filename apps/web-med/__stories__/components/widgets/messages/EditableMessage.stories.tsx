import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { action } from "storybook/actions";

import type { Message } from "@/hooks/use-messenger";

import MessageBubble from "@/components/widgets/messages/message-bubble";

const meta: Meta<typeof MessageBubble> = {
  title: "Components/Widgets/Messages/EditableMessage",
  component: MessageBubble,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    editable: {
      control: "boolean",
      description: "Whether the message can be edited/deleted",
      defaultValue: true,
    },
    isCurrentUser: {
      control: "boolean",
      description: "Whether the message is from the current user",
      defaultValue: true,
    },
    onEdit: { action: "edit" },
    onDelete: { action: "delete" },
  },
};

export default meta;
type Story = StoryObj<typeof MessageBubble>;

// Base message data
const baseMessage: Message = {
  id: "msg-1",
  content: "Hello, I have a question about my upcoming appointment.",
  createdAt: new Date("2025-04-04T10:05:00Z"),
  updatedAt: new Date("2025-04-04T10:05:00Z"),
  deletedAt: null,
  authorId: "user-123",
  threadId: "thread-1",
  author: {
    id: "user-123",
    firstName: "John",
    lastName: "Doe",
    avatar: null,
  },
  orderBy: {} as never,
};

// Current user's editable message
export const CurrentUserEditable: Story = {
  args: {
    message: baseMessage,
    isCurrentUser: true,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};

// Current user's non-editable message
export const CurrentUserNonEditable: Story = {
  args: {
    message: baseMessage,
    isCurrentUser: true,
    editable: false,
  },
};

// Other user's message that is editable (e.g., by a moderator)
export const OtherUserEditable: Story = {
  args: {
    message: {
      ...baseMessage,
      authorId: "other-user",
      author: {
        id: "other-user",
        firstName: "Jane",
        lastName: "Smith",
        avatar: null,
      },
    },
    isCurrentUser: false,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};

// Edited message with edit timestamp
export const EditedMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      content:
        "Hello, I have a question about my upcoming appointment. I updated this message.",
      updatedAt: new Date("2025-04-04T10:15:00Z"), // 10 minutes later
    },
    isCurrentUser: true,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};

// Message with a long content to test wrapping
export const LongMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      content:
        "This is a much longer message that should wrap to multiple lines. I'm writing this to test how the message bubble handles longer content and whether the dropdown menu still appears in the correct position. The dropdown should still be easily accessible and the message should look good with this amount of text.",
    },
    isCurrentUser: true,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};

// Message from a provider with role information
export const ProviderMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      authorId: "provider-123",
      author: {
        id: "provider-123",
        firstName: "Dr. Michael",
        lastName: "Smith",
        avatar: null,
      },
    },
    isCurrentUser: false,
    editable: true,
    onEdit: action("Edit message"),
    onDelete: action("Delete message"),
  },
};
