import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import { action } from "storybook/actions";

import { cn } from "@axa/ui/lib";

import type { Message } from "@/hooks/use-messenger";

import MessageBubble, {
  MessageHeader,
} from "@/components/widgets/messages/message-bubble";

// Create a simplified type for our story messages
interface StoryMessage {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  author:
    | {
        id: string;
        firstName?: string;
        lastName?: string;
        avatar?: string | null;
        title?: string;
        role?: string;
      }
    | null
    | undefined;
  authorId: string;
  threadId: string;
  deletedAt: string | null;
  orderBy: never;
}

// Generate message data using Faker
const generateMessage = (
  role: "CLIENT" | "STAFF" | "PROVIDER" = "CLIENT",
  isEdited = false,
  customContent?: string,
): StoryMessage => {
  const authorId = faker.string.uuid();
  const createdAt = faker.date.recent({ days: 3 }).toISOString();
  const updatedAt = isEdited
    ? faker.date.between({ from: createdAt, to: new Date() }).toISOString()
    : createdAt;

  let title: string | undefined;
  let firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  if (role === "PROVIDER") {
    title = faker.helpers.arrayElement([
      "Cardiologist",
      "Neurologist",
      "Primary Care Physician",
      "Dermatologist",
    ]);
    firstName = `Dr. ${firstName}`;
  }

  return {
    id: faker.string.uuid(),
    content: customContent ?? faker.lorem.paragraph(),
    createdAt,
    updatedAt,
    author: {
      id: authorId,
      firstName,
      lastName,
      avatar: null,
      title,
      role,
    },
    authorId,
    threadId: "thread-1",
    deletedAt: null,
    orderBy: {} as never,
  };
};

// Generate message groups
const generateMessageGroup = (
  count: number,
  role: "CLIENT" | "STAFF" | "PROVIDER" = "CLIENT",
  customContents?: string[],
): StoryMessage[] => {
  return Array.from({ length: count }).map((_, index) => {
    const content =
      customContents && index < customContents.length
        ? customContents[index]
        : undefined;
    return generateMessage(role, index === 1, content);
  });
};

// Create a MessageGroup component to demonstrate the usage
function MessageGroup({
  messages = [],
  isCurrentUser = false,
  editable = false,
}: {
  messages: StoryMessage[];
  isCurrentUser: boolean;
  editable?: boolean;
}) {
  const align = isCurrentUser ? "right" : "left";

  // Handle empty messages array
  if (messages.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        No messages to display
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-1">
      {/* Show header only for the first message */}
      {messages[0]?.author && (
        <MessageHeader author={messages[0].author} align={align} />
      )}

      {/* Show all messages without headers */}
      <div
        className={cn(
          "ml-14 flex flex-col gap-1",
          align === "right" && "ml-0 mr-14",
        )}
      >
        {messages.map((message, index) => (
          <MessageBubble
            key={message.id}
            message={
              {
                ...message,
                orderBy: {} as never,
                author: message.author
                  ? {
                      id: message.author.id,
                      firstName: message.author.firstName ?? "",
                      lastName: message.author.lastName ?? "",
                      avatar: message.author.avatar ?? null,
                    }
                  : undefined,
                createdAt: new Date(message.createdAt),
                updatedAt: new Date(message.updatedAt),
                deletedAt: message.deletedAt
                  ? new Date(message.deletedAt)
                  : null,
              } as Message
            }
            isCurrentUser={isCurrentUser}
            showHeader={false}
            isFirstInGroup={index === 0}
            editable={editable}
            onEdit={action("Edit message")}
            onDelete={action("Delete message")}
          />
        ))}
      </div>
    </div>
  );
}

const meta = {
  title: "Components/Messages/MessageGroup",
  component: MessageGroup,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof MessageGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

// User message group
export const UserMessageGroup: Story = {
  args: {
    messages: generateMessageGroup(3, "CLIENT", [
      "Hello, I have a question about my appointment.",
      "I'm wondering if we can reschedule it to next Friday?",
      "I have a conflict with the current time.",
    ]),
    isCurrentUser: true,
    editable: true,
  },
};

// Agent message group
export const AgentMessageGroup: Story = {
  args: {
    messages: generateMessageGroup(3, "STAFF", [
      "Hi there! How can I help you today?",
      "I can check your appointment details for you.",
      "What day was your appointment scheduled for?",
    ]),
    isCurrentUser: false,
    editable: false,
  },
};

// Doctor message group
export const DoctorMessageGroup: Story = {
  args: {
    messages: generateMessageGroup(3, "PROVIDER", [
      "Based on your symptoms, I recommend scheduling a follow-up.",
      "I have availability on Monday at 2pm or Wednesday at 10am.",
      "Would either of those times work for you?",
    ]),
    isCurrentUser: false,
    editable: true,
  },
};

// Mixed edited and non-edited messages
export const MixedEditedMessages: Story = {
  args: {
    messages: generateMessageGroup(3, "CLIENT", [
      "Hello, I need to reschedule my appointment.",
      "I need to reschedule my appointment for next week.", // This one will be marked as edited
      "Please let me know what times are available.",
    ]),
    isCurrentUser: true,
    editable: true,
  },
};

// Empty message group
export const EmptyMessageGroup: Story = {
  args: {
    messages: [],
    isCurrentUser: true,
    editable: false,
  },
};
