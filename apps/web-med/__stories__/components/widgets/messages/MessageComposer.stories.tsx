import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useState } from "react";
import { action } from "storybook/actions";

import { Card } from "@axa/ui/primitives/card";

import { MessageComposer } from "@/components/widgets/messages/message-composer";

const meta: Meta<typeof MessageComposer> = {
  title: "Components/Widgets/Messages/MessageComposer",
  component: MessageComposer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <Card className="w-[600px] p-4">
        <Story />
      </Card>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof MessageComposer>;

// Basic message composer
export const Default: Story = {
  args: {
    onSubmit: action("submit"),
    onContentChange: action("content-change"),
  },
};

// Message composer with initial content
export const WithInitialContent: Story = {
  args: {
    initialContent: "Hello, I have a question about my appointment.",
    onSubmit: action("submit"),
    onContentChange: action("content-change"),
  },
};

// Message composer in submitting state
export const Submitting: Story = {
  args: {
    isSubmitting: true,
    onSubmit: action("submit"),
    onContentChange: action("content-change"),
  },
};

// Message composer with custom placeholder
export const CustomPlaceholder: Story = {
  args: {
    placeholder: "Type your message here...",
    onSubmit: action("submit"),
    onContentChange: action("content-change"),
  },
};

// Interactive message composer with state
export const Interactive: Story = {
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = (content: string) => {
      setIsSubmitting(true);
      action("submit")(content);

      // Simulate API call
      setTimeout(() => {
        setIsSubmitting(false);
      }, 1500);
    };

    return (
      <MessageComposer
        isSubmitting={isSubmitting}
        onSubmit={handleSubmit}
        onContentChange={action("content-change")}
      />
    );
  },
};

// Message composer with disabled Enter key submission
export const DisabledEnterSubmit: Story = {
  args: {
    submitOnEnter: false,
    onSubmit: action("submit"),
    onContentChange: action("content-change"),
  },
};
