import type { Meta, StoryObj } from "@storybook/react";

import { useState } from "react";
import { action } from "storybook/actions";

import MessagesManager from "@/components/widgets/messages/messages-manager";

import { generateLongConversation, messages } from "./data";

const meta = {
  title: "Components/Widgets/Messages/MessagesManager",
  component: MessagesManager,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "600px" }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof MessagesManager>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic example with default height
export const Default: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    return (
      <MessagesManager
        messages={messages}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        onSubmit={() => {
          action("submit")(content);
          setContent("");
        }}
        title="Messages"
      />
    );
  },
};

// Example with custom height
export const CustomHeight: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    return (
      <MessagesManager
        messages={messages}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        onSubmit={() => {
          action("submit")(content);
          setContent("");
        }}
        title="Messages with Custom Height"
        height="400px"
      />
    );
  },
};

// Example with many messages to demonstrate scrolling
export const LongConversation: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    const longConversation = generateLongConversation(20);

    return (
      <MessagesManager
        messages={longConversation}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        onSubmit={() => {
          action("submit")(content);
          setContent("");
        }}
        title="Long Conversation"
        height="600px"
      />
    );
  },
};

// Example with loading state
export const Loading: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    return (
      <MessagesManager
        messages={[]}
        loading={true}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        title="Loading Messages"
      />
    );
  },
};

// Example with empty state
export const Empty: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    return (
      <MessagesManager
        messages={[]}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        title="No Messages"
      />
    );
  },
};

// Example with max height
export const WithMaxHeight: Story = {
  render: function Render() {
    const [content, setContent] = useState("");
    return (
      <MessagesManager
        messages={generateLongConversation(10)}
        currentUserId="current-user"
        content={content}
        setContent={setContent}
        onSubmit={() => {
          action("submit")(content);
          setContent("");
        }}
        title="Messages with Max Height"
        height="auto"
        maxHeight="400px"
      />
    );
  },
};
