import type { Message } from "@/components/widgets/messages/types";

// Sample users
export const users = {
  agent: {
    id: "agent",
    title: "Nurse",
    firstName: "Support",
    lastName: "Agent",
    avatar: null,
  },
  currentUser: {
    id: "current-user",
    title: "Patient",
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    avatar: null,
  },
  doctor: {
    id: "doctor",
    title: "Doctor",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    avatar: null,
  },
  admin: {
    id: "admin",
    title: "Administrator",
    firstName: "Admin",
    lastName: "User",
    avatar: null,
  },
};

// Sample messages
export const messages: Message[] = [
  {
    id: "1",
    content: "Hello! How can I help you today?",
    threadId: "thread-1",
    authorId: users.agent.id,
    author: users.agent,
    createdAt: new Date("2024-01-30T10:00:00Z").toISOString(),
    updatedAt: new Date("2024-01-30T10:00:00Z").toISOString(),
    deletedAt: null,
  },
  {
    id: "2",
    content: "I have a question about my appointment next week.",
    threadId: "thread-1",
    authorId: users.currentUser.id,
    author: users.currentUser,
    createdAt: new Date("2024-01-30T10:05:00Z").toISOString(),
    updatedAt: new Date("2024-01-30T10:05:00Z").toISOString(),
    deletedAt: null,
  },
  {
    id: "3",
    content:
      "Of course, I'd be happy to help with that. What would you like to know?",
    threadId: "thread-1",
    authorId: users.agent.id,
    author: users.agent,
    createdAt: new Date("2024-01-30T10:07:00Z").toISOString(),
    updatedAt: new Date("2024-01-30T10:07:00Z").toISOString(),
    deletedAt: null,
  },
  {
    id: "4",
    content: "I need to reschedule. Is that possible?",
    threadId: "thread-1",
    authorId: users.currentUser.id,
    author: users.currentUser,
    createdAt: new Date("2024-01-30T10:10:00Z").toISOString(),
    updatedAt: new Date("2024-01-30T10:10:00Z").toISOString(),
    deletedAt: null,
  },
  {
    id: "5",
    content:
      "Yes, that's definitely possible. Let me check the available slots for next week.",
    threadId: "thread-1",
    authorId: users.doctor.id,
    author: users.doctor,
    createdAt: new Date("2024-01-30T10:15:00Z").toISOString(),
    updatedAt: new Date("2024-01-30T10:15:00Z").toISOString(),
    deletedAt: null,
  },
];

// Edited message example
export const editedMessage: Message = {
  ...messages[3],
  id: "6",
  content:
    "I need to reschedule my appointment. Is that possible? I have a conflict on that day.",
  updatedAt: new Date("2024-01-30T10:12:00Z").toISOString(),
};

// Generate a long conversation for testing scrolling
export function generateLongConversation(count = 20): Message[] {
  const result: Message[] = [];
  const userIds = [users.currentUser.id, users.agent.id, users.doctor.id];
  const userMap = {
    [users.currentUser.id]: users.currentUser,
    [users.agent.id]: users.agent,
    [users.doctor.id]: users.doctor,
  };

  for (let i = 0; i < count; i++) {
    const userId = userIds[i % userIds.length];
    const baseTime = new Date("2024-01-30T10:00:00Z");
    baseTime.setMinutes(baseTime.getMinutes() + i * 5);

    result.push({
      id: `long-${i}`,
      content: `This is message #${i + 1} in a long conversation. It demonstrates how the chat handles many messages.`,
      threadId: "thread-1",
      authorId: userId,
      author: userMap[userId],
      createdAt: baseTime.toISOString(),
      updatedAt: baseTime.toISOString(),
      deletedAt: null,
    });
  }

  return result;
}

// Mock handlers
export const mockHandlers = {
  onSubmit: async (content: string) => {
    console.log("Message submitted:", content);
    return Promise.resolve();
  },
  onUpdateMessage: async (messageId: string, content: string) => {
    console.log("Message updated:", messageId, content);
    return Promise.resolve();
  },
  onDeleteMessage: async (messageId: string) => {
    console.log("Message deleted:", messageId);
    return Promise.resolve();
  },
  canModifyMessage: (authorId: string) => {
    return authorId === users.currentUser.id;
  },
  setContent: (content: string) => {
    console.log("Content set:", content);
  },
};
