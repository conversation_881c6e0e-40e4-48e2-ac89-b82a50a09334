import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useState } from "react";

import Messages from "@/components/widgets/messages/messages-manager";

import {
  generateLongConversation,
  messages,
  mockHandlers,
  users,
} from "./data";

const meta = {
  title: "Components/Widgets/Messages/Messages",
  component: Messages,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Messages>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story with default messages
export const Default: Story = {
  args: {
    messages: messages,
    currentUserId: users.currentUser.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    onUpdateMessage: mockHandlers.onUpdateMessage,
    onDeleteMessage: mockHandlers.onDeleteMessage,
    canModifyMessage: mockHandlers.canModifyMessage,
    title: "Messages",
  },
};

// Story with loading state
export const Loading: Story = {
  args: {
    loading: true,
    messages: [],
    currentUserId: users.currentUser.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    title: "Messages",
  },
};

// Story with empty state
export const Empty: Story = {
  args: {
    messages: [],
    currentUserId: users.currentUser.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    title: "Messages",
  },
};

// Story with error state
export const error: Story = {
  args: {
    error: new Error("Failed to load messages"),
    messages: [],
    currentUserId: users.currentUser.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    title: "Messages",
  },
};

// Story with many messages to test scrolling
export const LongConversation: Story = {
  args: {
    messages: generateLongConversation(30),
    currentUserId: users.currentUser.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    onUpdateMessage: mockHandlers.onUpdateMessage,
    onDeleteMessage: mockHandlers.onDeleteMessage,
    canModifyMessage: mockHandlers.canModifyMessage,
    title: "Messages",
  },
};

// Story with admin view (can modify all messages)
export const AdminView: Story = {
  args: {
    messages: messages,
    currentUserId: users.admin.id,
    content: "",
    setContent: mockHandlers.setContent,
    onSubmit: mockHandlers.onSubmit,
    onUpdateMessage: mockHandlers.onUpdateMessage,
    onDeleteMessage: mockHandlers.onDeleteMessage,
    canModifyMessage: () => true, // Admin can modify all messages
    title: "Messages (Admin View)",
  },
};

// Interactive story with state management
export const Interactive: Story = {
  render: (args) => {
    const [content, setContent] = useState("");
    const [currentMessages, setCurrentMessages] = useState([...messages]);

    const handleSubmit = async (newContent: string) => {
      if (!newContent.trim()) return;

      const newMessage = {
        id: `new-${Date.now()}`,
        content: newContent,
        threadId: "thread-1",
        authorId: users.currentUser.id,
        author: users.currentUser,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        deletedAt: null,
      };

      setCurrentMessages([...currentMessages, newMessage]);
      setContent("");
      return Promise.resolve();
    };

    const handleUpdateMessage = async (
      messageId: string,
      newContent: string,
    ) => {
      setCurrentMessages(
        currentMessages.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                content: newContent,
                updatedAt: new Date().toISOString(),
              }
            : msg,
        ),
      );
      return Promise.resolve();
    };

    const handleDeleteMessage = async (messageId: string) => {
      setCurrentMessages(currentMessages.filter((msg) => msg.id !== messageId));
      return Promise.resolve();
    };

    return (
      <Messages
        {...args}
        messages={currentMessages}
        content={content}
        setContent={setContent}
        onSubmit={handleSubmit}
        onUpdateMessage={handleUpdateMessage}
        onDeleteMessage={handleDeleteMessage}
      />
    );
  },
  args: {
    currentUserId: users.currentUser.id,
    canModifyMessage: (authorId: string) => authorId === users.currentUser.id,
    title: "Interactive Messages",
  },
};
