import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { action } from "storybook/actions";

import { ReferralCard } from "@/components/widgets/referrals/shared/ReferralCard";
import { mockReferrals, mockHandlers } from "./data";

const meta: Meta<typeof ReferralCard> = {
  title: "Components/Widgets/Referrals/ReferralCard",
  component: ReferralCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="w-[400px]">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic states
export const Invited: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "invited")!,
    onCopyLink: action("copy-link"),
    onViewDetails: action("view-details"),
    onResendInvite: action("resend-invite"),
  },
};

export const SignedUp: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "signed_up")!,
    onCopyLink: action("copy-link"),
    onViewDetails: action("view-details"),
  },
};

export const Approved: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "approved")!,
    onCopyLink: action("copy-link"),
    onViewDetails: action("view-details"),
  },
};

export const Rejected: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "rejected")!,
    onCopyLink: action("copy-link"),
    onViewDetails: action("view-details"),
  },
};

export const Expired: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "expired")!,
    onCopyLink: action("copy-link"),
    onViewDetails: action("view-details"),
  },
};

// Without actions
export const WithoutActions: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "approved")!,
    showActions: false,
  },
};

// Interactive example
export const Interactive: Story = {
  args: {
    referral: mockReferrals.find(r => r.status === "signed_up")!,
    onCopyLink: mockHandlers.onCopyLink,
    onViewDetails: mockHandlers.onViewDetails,
    onResendInvite: mockHandlers.onResendInvite,
  },
};

// Grid showcase
export const AllStates: Story = {
  render: () => (
    <div className="grid gap-4 max-w-4xl">
      <div className="grid md:grid-cols-2 gap-4">
        {mockReferrals.slice(0, 4).map((referral) => (
          <ReferralCard
            key={referral.id}
            referral={referral}
            onCopyLink={action("copy-link")}
            onViewDetails={action("view-details")}
            onResendInvite={action("resend-invite")}
          />
        ))}
      </div>
    </div>
  ),
  parameters: {
    layout: "padded",
  },
};
