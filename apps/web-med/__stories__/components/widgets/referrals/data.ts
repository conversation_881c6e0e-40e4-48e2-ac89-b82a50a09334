import type {
  Referral,
  ReferralStats,
} from "@/components/widgets/referrals/shared/types";

// Simple mock referrals
export const mockReferrals: Referral[] = [
  {
    id: "1",
    invitedBy: {
      id: "provider-1",
      firstName: "Dr. <PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      avatar: null,
      specialty: "Cardiology",
    },
    invitedUser: {
      id: "user-1",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      avatar: null,
      specialty: "Family Medicine",
    },
    status: "approved",
    payoutStatus: "completed",
    payoutAmount: 5,
    inviteLink: "https://app.example.com/invite/abc123",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-17T09:15:00Z",
    signedUpAt: "2024-01-16T14:30:00Z",
    approvedAt: "2024-01-17T09:15:00Z",
  },
  {
    id: "2",
    invitedBy: {
      id: "provider-2",
      firstName: "<PERSON><PERSON> <PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      avatar: null,
      specialty: "Dermatology",
    },
    invitedUser: undefined,
    status: "invited",
    payoutStatus: undefined,
    payoutAmount: 5,
    inviteLink: "https://app.example.com/invite/def456",
    createdAt: "2024-01-20T16:45:00Z",
    updatedAt: "2024-01-20T16:45:00Z",
    signedUpAt: undefined,
    approvedAt: undefined,
  },
  {
    id: "3",
    invitedBy: {
      id: "provider-1",
      firstName: "Dr. Sarah",
      lastName: "Johnson",
      email: "<EMAIL>",
      avatar: null,
      specialty: "Cardiology",
    },
    invitedUser: {
      id: "user-3",
      firstName: "Emily",
      lastName: "Davis",
      email: "<EMAIL>",
      avatar: null,
      specialty: "Pediatrics",
    },
    status: "signed_up",
    payoutStatus: undefined,
    payoutAmount: 5,
    inviteLink: "https://app.example.com/invite/ghi789",
    createdAt: "2024-01-18T11:20:00Z",
    updatedAt: "2024-01-19T08:45:00Z",
    signedUpAt: "2024-01-19T08:45:00Z",
    approvedAt: undefined,
  },
];

// Simple mock stats
export const mockStats: ReferralStats = {
  totalInvited: 3,
  totalSignedUp: 2,
  totalApproved: 1,
  totalPayoutAmount: 5,
  pendingPayouts: 0,
  completedPayouts: 1,
};
