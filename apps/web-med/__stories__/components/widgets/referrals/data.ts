import { faker } from "@faker-js/faker";

import type { 
  Refer<PERSON>, 
  ReferralUser, 
  ReferralStats, 
  ReferralStatus, 
  PayoutStatus 
} from "@/components/widgets/referrals/shared/types";

// Set seed for consistent data
faker.seed(123);

// Mock users factory
export function createMockUser(overrides: Partial<ReferralUser> = {}): ReferralUser {
  return {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    avatar: faker.helpers.maybe(() => faker.image.avatar(), { probability: 0.6 }),
    title: faker.helpers.arrayElement(["Dr.", "Mr.", "Ms.", "Mrs."]),
    specialty: faker.helpers.arrayElement([
      "Cardiology",
      "Emergency Medicine",
      "Family Medicine",
      "Internal Medicine",
      "Pediatrics",
      "Surgery",
      "Anesthesiology",
      "Radiology",
      "Psychiatry",
      "Dermatology",
    ]),
    experience: faker.number.int({ min: 1, max: 25 }),
    location: `${faker.location.city()}, ${faker.location.state({ abbreviated: true })}`,
    ...overrides,
  };
}

// Mock referral factory
export function createMockReferral(overrides: Partial<Referral> = {}): Referral {
  const status: ReferralStatus = faker.helpers.arrayElement([
    "invited",
    "signed_up", 
    "approved",
    "rejected",
    "expired"
  ]);
  
  const payoutStatus: PayoutStatus | undefined = 
    status === "approved" 
      ? faker.helpers.arrayElement(["pending", "processing", "completed", "failed"])
      : status === "signed_up"
      ? "pending"
      : undefined;

  const createdAt = faker.date.recent({ days: 30 });
  const signedUpAt = status !== "invited" ? faker.date.between({ from: createdAt, to: new Date() }) : undefined;
  const approvedAt = status === "approved" ? faker.date.between({ from: signedUpAt || createdAt, to: new Date() }) : undefined;
  const rejectedAt = status === "rejected" ? faker.date.between({ from: signedUpAt || createdAt, to: new Date() }) : undefined;
  const expiredAt = status === "expired" ? faker.date.between({ from: createdAt, to: new Date() }) : undefined;

  return {
    id: faker.string.uuid(),
    inviteLink: `https://app.axaprofessionals.com/signup?ref=${faker.string.alphanumeric(12)}`,
    invitedBy: createMockUser(),
    invitedUser: status !== "invited" ? createMockUser() : undefined,
    status,
    payoutStatus,
    payoutAmount: 5,
    createdAt: createdAt.toISOString(),
    updatedAt: faker.date.between({ from: createdAt, to: new Date() }).toISOString(),
    signedUpAt: signedUpAt?.toISOString(),
    approvedAt: approvedAt?.toISOString(),
    rejectedAt: rejectedAt?.toISOString(),
    expiredAt: expiredAt?.toISOString(),
    payoutProcessedAt: payoutStatus === "completed" 
      ? faker.date.between({ from: approvedAt || createdAt, to: new Date() }).toISOString()
      : undefined,
    notes: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.3 }),
    ...overrides,
  };
}

// Predefined mock users for consistent stories
export const mockUsers = {
  drSmith: createMockUser({
    id: "user-1",
    firstName: "Sarah",
    lastName: "Smith",
    email: "<EMAIL>",
    title: "Dr.",
    specialty: "Cardiology",
    experience: 8,
    location: "New York, NY",
    avatar: "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face",
  }),
  drJohnson: createMockUser({
    id: "user-2",
    firstName: "Michael",
    lastName: "Johnson",
    email: "<EMAIL>",
    title: "Dr.",
    specialty: "Emergency Medicine",
    experience: 12,
    location: "Los Angeles, CA",
    avatar: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face",
  }),
  nurseBrown: createMockUser({
    id: "user-3",
    firstName: "Emily",
    lastName: "Brown",
    email: "<EMAIL>",
    title: "Ms.",
    specialty: "Pediatrics",
    experience: 5,
    location: "Chicago, IL",
  }),
  drWilson: createMockUser({
    id: "user-4",
    firstName: "David",
    lastName: "Wilson",
    email: "<EMAIL>",
    title: "Dr.",
    specialty: "Surgery",
    experience: 15,
    location: "Houston, TX",
  }),
};

// Mock referrals with different states
export const mockReferrals: Referral[] = [
  // Recent successful referral
  createMockReferral({
    id: "ref-1",
    invitedBy: mockUsers.drSmith,
    invitedUser: mockUsers.drJohnson,
    status: "approved",
    payoutStatus: "completed",
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
    signedUpAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    approvedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    payoutProcessedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
  }),

  // Pending approval
  createMockReferral({
    id: "ref-2",
    invitedBy: mockUsers.drSmith,
    invitedUser: mockUsers.nurseBrown,
    status: "signed_up",
    payoutStatus: "pending",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    signedUpAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  }),

  // Just invited
  createMockReferral({
    id: "ref-3",
    invitedBy: mockUsers.drJohnson,
    status: "invited",
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
  }),

  // Rejected referral
  createMockReferral({
    id: "ref-4",
    invitedBy: mockUsers.drWilson,
    invitedUser: createMockUser({ firstName: "Jane", lastName: "Doe" }),
    status: "rejected",
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    signedUpAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
    rejectedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
    notes: "Credentials could not be verified",
  }),

  // Expired invitation
  createMockReferral({
    id: "ref-5",
    invitedBy: mockUsers.nurseBrown,
    status: "expired",
    createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString(),
    expiredAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  }),
];

// Generate additional random referrals
export const additionalReferrals = Array.from({ length: 15 }, () => createMockReferral());

// All referrals combined
export const allReferrals = [...mockReferrals, ...additionalReferrals];

// Mock stats
export const mockStats: ReferralStats = {
  totalInvited: allReferrals.length,
  totalSignedUp: allReferrals.filter(r => ["signed_up", "approved", "rejected"].includes(r.status)).length,
  totalApproved: allReferrals.filter(r => r.status === "approved").length,
  totalRejected: allReferrals.filter(r => r.status === "rejected").length,
  totalExpired: allReferrals.filter(r => r.status === "expired").length,
  totalPayoutAmount: allReferrals.filter(r => r.status === "approved").length * 5,
  pendingPayouts: allReferrals.filter(r => r.payoutStatus === "pending").length,
  completedPayouts: allReferrals.filter(r => r.payoutStatus === "completed").length,
};

// Provider-specific data (for provider stories)
export const providerReferrals = allReferrals.filter(r => r.invitedBy.id === mockUsers.drSmith.id);
export const providerStats: ReferralStats = {
  totalInvited: providerReferrals.length,
  totalSignedUp: providerReferrals.filter(r => ["signed_up", "approved", "rejected"].includes(r.status)).length,
  totalApproved: providerReferrals.filter(r => r.status === "approved").length,
  totalRejected: providerReferrals.filter(r => r.status === "rejected").length,
  totalExpired: providerReferrals.filter(r => r.status === "expired").length,
  totalPayoutAmount: providerReferrals.filter(r => r.status === "approved").length * 5,
  pendingPayouts: providerReferrals.filter(r => r.payoutStatus === "pending").length,
  completedPayouts: providerReferrals.filter(r => r.payoutStatus === "completed").length,
};

// Mock handlers for stories
export const mockHandlers = {
  onSendInvite: async (data: any) => {
    console.log("Sending invite:", data);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
  },
  onApproveReferral: (referralId: string) => {
    console.log("Approving referral:", referralId);
  },
  onRejectReferral: (referralId: string) => {
    console.log("Rejecting referral:", referralId);
  },
  onProcessPayout: (referralId: string) => {
    console.log("Processing payout:", referralId);
  },
  onCopyLink: (link: string) => {
    console.log("Copying link:", link);
  },
  onViewDetails: (referral: Referral) => {
    console.log("Viewing details:", referral);
  },
  onResendInvite: (referral: Referral) => {
    console.log("Resending invite:", referral);
  },
  onExportData: () => {
    console.log("Exporting data");
  },
};
