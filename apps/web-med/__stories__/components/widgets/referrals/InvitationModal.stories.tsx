import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";
import { action } from "storybook/actions";
import { But<PERSON> } from "@axa/ui/primitives/button";

import { InvitationModal } from "@/components/widgets/referrals/provider/InvitationModal";
import { mockHandlers } from "./data";

const meta: Meta<typeof InvitationModal> = {
  title: "Components/Widgets/Referrals/Provider/InvitationModal",
  component: InvitationModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Interactive wrapper component
function InteractiveWrapper({ 
  isLoading = false, 
  generatedLink,
  onSendInvite = mockHandlers.onSendInvite 
}: {
  isLoading?: boolean;
  generatedLink?: string;
  onSendInvite?: any;
}) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setOpen(true)}>Open Invitation Modal</Button>
      <InvitationModal
        open={open}
        onOpenChange={setOpen}
        onSendInvite={onSendInvite}
        isLoading={isLoading}
        generatedLink={generatedLink}
      />
    </>
  );
}

export const Default: Story = {
  render: () => <InteractiveWrapper />,
};

export const Loading: Story = {
  render: () => <InteractiveWrapper isLoading={true} />,
};

export const WithGeneratedLink: Story = {
  render: () => (
    <InteractiveWrapper 
      generatedLink="https://app.axaprofessionals.com/signup?ref=abc123def456"
    />
  ),
};

export const SlowSubmission: Story = {
  render: () => (
    <InteractiveWrapper 
      onSendInvite={async (data: any) => {
        action("send-invite")(data);
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
      }}
    />
  ),
};

// Always open variants for design review
export const AlwaysOpenForm: Story = {
  args: {
    open: true,
    onOpenChange: action("open-change"),
    onSendInvite: action("send-invite"),
  },
};

export const AlwaysOpenLoading: Story = {
  args: {
    open: true,
    onOpenChange: action("open-change"),
    onSendInvite: action("send-invite"),
    isLoading: true,
  },
};

export const AlwaysOpenSuccess: Story = {
  render: () => {
    // This would normally be controlled by the component's internal state
    // For story purposes, we'll simulate the success state
    const [step, setStep] = useState<"form" | "success">("success");
    
    return (
      <InvitationModal
        open={true}
        onOpenChange={action("open-change")}
        onSendInvite={action("send-invite")}
        generatedLink="https://app.axaprofessionals.com/signup?ref=story123"
      />
    );
  },
};
