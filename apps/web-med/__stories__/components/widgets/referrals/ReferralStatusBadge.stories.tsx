import type { <PERSON>a, StoryObj } from "@storybook/react";

import { ReferralStatusBadge, PayoutStatusBadge } from "@/components/widgets/referrals/shared/ReferralStatusBadge";

const meta: Meta<typeof ReferralStatusBadge> = {
  title: "Components/Widgets/Referrals/ReferralStatusBadge",
  component: ReferralStatusBadge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    status: {
      control: "select",
      options: ["invited", "signed_up", "approved", "rejected", "expired"],
    },
    showIcon: {
      control: "boolean",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Referral Status Badge Stories
export const Invited: Story = {
  args: {
    status: "invited",
    showIcon: true,
  },
};

export const SignedUp: Story = {
  args: {
    status: "signed_up",
    showIcon: true,
  },
};

export const Approved: Story = {
  args: {
    status: "approved",
    showIcon: true,
  },
};

export const Rejected: Story = {
  args: {
    status: "rejected",
    showIcon: true,
  },
};

export const Expired: Story = {
  args: {
    status: "expired",
    showIcon: true,
  },
};

export const WithoutIcon: Story = {
  args: {
    status: "approved",
    showIcon: false,
  },
};

// All statuses showcase
export const AllStatuses: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      <ReferralStatusBadge status="invited" />
      <ReferralStatusBadge status="signed_up" />
      <ReferralStatusBadge status="approved" />
      <ReferralStatusBadge status="rejected" />
      <ReferralStatusBadge status="expired" />
    </div>
  ),
};

// Payout Status Badge Stories
const PayoutMeta: Meta<typeof PayoutStatusBadge> = {
  title: "Components/Widgets/Referrals/PayoutStatusBadge",
  component: PayoutStatusBadge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    status: {
      control: "select",
      options: ["pending", "processing", "completed", "failed"],
    },
    showIcon: {
      control: "boolean",
    },
  },
};

export const PayoutPending: StoryObj<typeof PayoutStatusBadge> = {
  args: {
    status: "pending",
    showIcon: true,
  },
};

export const PayoutProcessing: StoryObj<typeof PayoutStatusBadge> = {
  args: {
    status: "processing",
    showIcon: true,
  },
};

export const PayoutCompleted: StoryObj<typeof PayoutStatusBadge> = {
  args: {
    status: "completed",
    showIcon: true,
  },
};

export const PayoutFailed: StoryObj<typeof PayoutStatusBadge> = {
  args: {
    status: "failed",
    showIcon: true,
  },
};

export const AllPayoutStatuses: StoryObj<typeof PayoutStatusBadge> = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      <PayoutStatusBadge status="pending" />
      <PayoutStatusBadge status="processing" />
      <PayoutStatusBadge status="completed" />
      <PayoutStatusBadge status="failed" />
    </div>
  ),
};

// Combined showcase
export const StatusCombinations: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-2">Referral Statuses</h3>
        <div className="flex flex-wrap gap-2">
          <ReferralStatusBadge status="invited" />
          <ReferralStatusBadge status="signed_up" />
          <ReferralStatusBadge status="approved" />
          <ReferralStatusBadge status="rejected" />
          <ReferralStatusBadge status="expired" />
        </div>
      </div>
      <div>
        <h3 className="text-sm font-medium mb-2">Payout Statuses</h3>
        <div className="flex flex-wrap gap-2">
          <PayoutStatusBadge status="pending" />
          <PayoutStatusBadge status="processing" />
          <PayoutStatusBadge status="completed" />
          <PayoutStatusBadge status="failed" />
        </div>
      </div>
      <div>
        <h3 className="text-sm font-medium mb-2">Common Combinations</h3>
        <div className="space-y-2">
          <div className="flex gap-2">
            <ReferralStatusBadge status="signed_up" />
            <PayoutStatusBadge status="pending" />
          </div>
          <div className="flex gap-2">
            <ReferralStatusBadge status="approved" />
            <PayoutStatusBadge status="processing" />
          </div>
          <div className="flex gap-2">
            <ReferralStatusBadge status="approved" />
            <PayoutStatusBadge status="completed" />
          </div>
        </div>
      </div>
    </div>
  ),
};
