import type { <PERSON>a, StoryObj } from "@storybook/react";
import { action } from "storybook/actions";

import { ReferralDashboard } from "@/components/widgets/referrals/admin/ReferralDashboard";
import { allReferrals, mockStats, mockHandlers } from "./data";

const meta: Meta<typeof ReferralDashboard> = {
  title: "Components/Widgets/Referrals/Admin/ReferralDashboard",
  component: ReferralDashboard,
  parameters: {
    layout: "fullscreen",
    viewport: {
      defaultViewport: "desktop",
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    referrals: allReferrals,
    stats: mockStats,
    onApproveReferral: action("approve-referral"),
    onRejectReferral: action("reject-referral"),
    onProcessPayout: action("process-payout"),
    onExportData: action("export-data"),
  },
};

export const Loading: Story = {
  args: {
    referrals: [],
    stats: mockStats,
    isLoading: true,
    onApproveReferral: action("approve-referral"),
    onRejectReferral: action("reject-referral"),
    onProcessPayout: action("process-payout"),
    onExportData: action("export-data"),
  },
};

export const EmptyState: Story = {
  args: {
    referrals: [],
    stats: {
      totalInvited: 0,
      totalSignedUp: 0,
      totalApproved: 0,
      totalRejected: 0,
      totalExpired: 0,
      totalPayoutAmount: 0,
      pendingPayouts: 0,
      completedPayouts: 0,
    },
    onApproveReferral: action("approve-referral"),
    onRejectReferral: action("reject-referral"),
    onProcessPayout: action("process-payout"),
    onExportData: action("export-data"),
  },
};

export const HighVolume: Story = {
  args: {
    referrals: allReferrals,
    stats: {
      totalInvited: 2500,
      totalSignedUp: 1800,
      totalApproved: 1350,
      totalRejected: 250,
      totalExpired: 200,
      totalPayoutAmount: 6750,
      pendingPayouts: 125,
      completedPayouts: 1225,
    },
    onApproveReferral: action("approve-referral"),
    onRejectReferral: action("reject-referral"),
    onProcessPayout: action("process-payout"),
    onExportData: action("export-data"),
  },
};

export const Interactive: Story = {
  args: {
    referrals: allReferrals,
    stats: mockStats,
    onApproveReferral: mockHandlers.onApproveReferral,
    onRejectReferral: mockHandlers.onRejectReferral,
    onProcessPayout: mockHandlers.onProcessPayout,
    onExportData: mockHandlers.onExportData,
  },
};
