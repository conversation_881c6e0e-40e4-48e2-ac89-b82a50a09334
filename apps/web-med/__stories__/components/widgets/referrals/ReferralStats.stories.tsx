import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ReferralStats } from "@/components/widgets/referrals/shared/ReferralStats";
import { mockStats, providerStats } from "./data";

const meta: Meta<typeof ReferralStats> = {
  title: "Components/Widgets/Referrals/ReferralStats",
  component: ReferralStats,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    stats: mockStats,
  },
};

export const Compact: Story = {
  args: {
    stats: mockStats,
    variant: "compact",
  },
};

export const ProviderStats: Story = {
  args: {
    stats: providerStats,
  },
};

export const EmptyStats: Story = {
  args: {
    stats: {
      totalInvited: 0,
      totalSignedUp: 0,
      totalApproved: 0,
      totalRejected: 0,
      totalExpired: 0,
      totalPayoutAmount: 0,
      pendingPayouts: 0,
      completedPayouts: 0,
    },
  },
};

export const HighVolumeStats: Story = {
  args: {
    stats: {
      totalInvited: 1250,
      totalSignedUp: 890,
      totalApproved: 675,
      totalRejected: 125,
      totalExpired: 90,
      totalPayoutAmount: 3375,
      pendingPayouts: 45,
      completedPayouts: 630,
    },
  },
};

export const CompactComparison: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Default View</h3>
        <ReferralStats stats={mockStats} />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-4">Compact View</h3>
        <ReferralStats stats={mockStats} variant="compact" />
      </div>
    </div>
  ),
};
