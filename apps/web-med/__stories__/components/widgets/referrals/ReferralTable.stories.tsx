import type { Meta, StoryObj } from "@storybook/react";
import { action } from "storybook/actions";

import { ReferralTable } from "@/components/widgets/referrals/admin/ReferralTable";
import { allReferrals, mockReferrals, mockHandlers } from "./data";

const meta: Meta<typeof ReferralTable> = {
  title: "Components/Widgets/Referrals/Admin/ReferralTable",
  component: ReferralTable,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    referrals: mockReferrals,
    onApprove: action("approve"),
    onReject: action("reject"),
    onProcessPayout: action("process-payout"),
    onViewDetails: action("view-details"),
  },
};

export const WithActions: Story = {
  args: {
    referrals: mockReferrals.filter(r => r.status === "signed_up"),
    showActions: true,
    onApprove: action("approve"),
    onReject: action("reject"),
    onViewDetails: action("view-details"),
  },
};

export const WithPayoutActions: Story = {
  args: {
    referrals: mockReferrals.filter(r => r.payoutStatus === "pending"),
    showPayoutActions: true,
    onProcessPayout: action("process-payout"),
    onViewDetails: action("view-details"),
  },
};

export const Compact: Story = {
  args: {
    referrals: mockReferrals,
    variant: "compact",
    onViewDetails: action("view-details"),
  },
};

export const Loading: Story = {
  args: {
    referrals: [],
    isLoading: true,
  },
};

export const Empty: Story = {
  args: {
    referrals: [],
  },
};

export const LargeDataset: Story = {
  args: {
    referrals: allReferrals,
    onApprove: action("approve"),
    onReject: action("reject"),
    onProcessPayout: action("process-payout"),
    onViewDetails: action("view-details"),
  },
};

export const PendingApprovals: Story = {
  args: {
    referrals: allReferrals.filter(r => r.status === "signed_up"),
    showActions: true,
    onApprove: action("approve"),
    onReject: action("reject"),
    onViewDetails: action("view-details"),
  },
};

export const PayoutManagement: Story = {
  args: {
    referrals: allReferrals.filter(r => r.payoutStatus),
    showPayoutActions: true,
    onProcessPayout: action("process-payout"),
    onViewDetails: action("view-details"),
  },
};

export const Interactive: Story = {
  args: {
    referrals: mockReferrals,
    showActions: true,
    showPayoutActions: true,
    onApprove: mockHandlers.onApproveReferral,
    onReject: mockHandlers.onRejectReferral,
    onProcessPayout: mockHandlers.onProcessPayout,
    onViewDetails: mockHandlers.onViewDetails,
  },
};
