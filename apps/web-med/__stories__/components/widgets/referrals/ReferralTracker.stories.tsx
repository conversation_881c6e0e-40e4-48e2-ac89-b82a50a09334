import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";
import { action } from "storybook/actions";

import { ReferralTracker } from "@/components/widgets/referrals/provider/ReferralTracker";
import { providerReferrals, providerStats, mockHandlers } from "./data";

const meta: Meta<typeof ReferralTracker> = {
  title: "Components/Widgets/Referrals/Provider/ReferralTracker",
  component: ReferralTracker,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    referrals: providerReferrals,
    stats: providerStats,
    onInviteNew: action("invite-new"),
    onViewDetails: action("view-details"),
    onCopyLink: action("copy-link"),
    onResendInvite: action("resend-invite"),
  },
};

export const Loading: Story = {
  args: {
    referrals: [],
    stats: providerStats,
    isLoading: true,
    onInviteNew: action("invite-new"),
  },
};

export const Empty: Story = {
  args: {
    referrals: [],
    stats: {
      totalInvited: 0,
      totalSignedUp: 0,
      totalApproved: 0,
      totalRejected: 0,
      totalExpired: 0,
      totalPayoutAmount: 0,
      pendingPayouts: 0,
      completedPayouts: 0,
    },
    onInviteNew: action("invite-new"),
  },
};

export const WithSearch: Story = {
  render: () => {
    const [searchQuery, setSearchQuery] = useState("");
    
    const filteredReferrals = providerReferrals.filter(referral => {
      if (!searchQuery) return true;
      
      const searchLower = searchQuery.toLowerCase();
      return (
        referral.invitedBy.firstName.toLowerCase().includes(searchLower) ||
        referral.invitedBy.lastName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.firstName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.lastName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.email.toLowerCase().includes(searchLower)
      );
    });

    return (
      <ReferralTracker
        referrals={filteredReferrals}
        stats={providerStats}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onInviteNew={action("invite-new")}
        onViewDetails={action("view-details")}
        onCopyLink={action("copy-link")}
        onResendInvite={action("resend-invite")}
      />
    );
  },
};

export const Interactive: Story = {
  render: () => {
    const [searchQuery, setSearchQuery] = useState("");
    
    const filteredReferrals = providerReferrals.filter(referral => {
      if (!searchQuery) return true;
      
      const searchLower = searchQuery.toLowerCase();
      return (
        referral.invitedBy.firstName.toLowerCase().includes(searchLower) ||
        referral.invitedBy.lastName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.firstName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.lastName.toLowerCase().includes(searchLower) ||
        referral.invitedUser?.email.toLowerCase().includes(searchLower)
      );
    });

    return (
      <ReferralTracker
        referrals={filteredReferrals}
        stats={providerStats}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onInviteNew={mockHandlers.onSendInvite}
        onViewDetails={mockHandlers.onViewDetails}
        onCopyLink={mockHandlers.onCopyLink}
        onResendInvite={mockHandlers.onResendInvite}
      />
    );
  },
};
