import type { <PERSON>a, StoryObj } from "@storybook/react";
import { action } from "storybook/actions";

import { ReferralIncentiveExplanation } from "@/components/widgets/referrals/provider/ReferralIncentiveExplanation";

const meta: Meta<typeof ReferralIncentiveExplanation> = {
  title: "Components/Widgets/Referrals/Provider/ReferralIncentiveExplanation",
  component: ReferralIncentiveExplanation,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onGetStarted: action("get-started"),
  },
};

export const Compact: Story = {
  args: {
    variant: "compact",
    onGetStarted: action("get-started"),
  },
};

export const WithoutAction: Story = {
  args: {
    // No onGetStarted handler - button will be disabled
  },
};

export const VariantComparison: Story = {
  render: () => (
    <div className="space-y-12">
      <div>
        <h2 className="text-xl font-semibold mb-6">Full Version</h2>
        <ReferralIncentiveExplanation 
          variant="full"
          onGetStarted={action("get-started-full")}
        />
      </div>
      <div>
        <h2 className="text-xl font-semibold mb-6">Compact Version</h2>
        <ReferralIncentiveExplanation 
          variant="compact"
          onGetStarted={action("get-started-compact")}
        />
      </div>
    </div>
  ),
};
