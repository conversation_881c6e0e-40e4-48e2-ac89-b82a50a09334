// apps/web-med/src/components/widgets/facility-generator/Banner.stories.tsx
import type { Meta, StoryObj } from "@storybook/react";

import { But<PERSON> } from "@axa/ui/primitives/button"; // For demo child

import { Banner } from "@/components/widgets/facility-generator/Banner";

const meta: Meta<typeof Banner> = {
  title: "Components/Widgets/FacilityGenerator/Banner", // Updated title to reflect location
  component: Banner,
  parameters: {
    layout: "centered",
  },
  argTypes: {
    title: { control: "text" },
    description: { control: "text" },
  },
  args: {
    title: "AI-Powered Facility Search",
    description:
      "Discover departments efficiently with our intelligent search system",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithAction: Story = {
  args: {
    children: <Button>Perform Action</Button>,
  },
};

export const LongText: Story = {
  args: {
    title:
      "This is a Banner Component with a Particularly Long Title That Might Wrap",
    description:
      "This description is also quite long to demonstrate how the text wraps within the banner component's layout, especially on smaller screens or when contained within a narrower parent element. It keeps going and going.",
    children: <Button>Action</Button>,
  },
};
