import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { fn } from "storybook/test";

import LoaderButton from "@/components/widgets/facility-generator/LoaderButton";

const meta: Meta<typeof LoaderButton> = {
  title: "Components/Widgets/FacilityGenerator/LoaderButton", // Updated title to reflect location
  component: LoaderButton,
  parameters: {
    layout: "centered",
  },
  argTypes: {
    isLoading: { control: "boolean" },
    disabled: { control: "boolean" },
    children: { control: "text" },
    onClick: { action: "clicked" }, // Use Storybook actions addon
  },
  args: {
    isLoading: false,
    disabled: false,
    children: "Click Me",
    onClick: fn(), // Mock function
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Loading: Story = {
  args: {
    isLoading: true,
    children: "Loading...",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: "Cannot Click",
  },
};

export const LoadingAndDisabled: Story = {
  args: {
    isLoading: true,
    disabled: true, // Usually isLoading implies disabled, but testing the prop
    children: "Submitting...",
  },
};
