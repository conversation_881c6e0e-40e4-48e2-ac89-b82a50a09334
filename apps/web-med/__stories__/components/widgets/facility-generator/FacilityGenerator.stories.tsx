import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";

import { faker } from "@faker-js/faker"; // For generating mock data
import { fn } from "storybook/test"; // For mocking functions

import { toast } from "@axa/ui/primitives/toast"; // To show success messages like in the component

import type {
  DepartmentType,
  RouterOutputs,
} from "@/components/widgets/facility-generator";
import type { GeneratedDepartment } from "@/components/widgets/facility-generator/GeneratedDepartmentsList";

// Import types from the widget index
import FacilityGenerator from "@/components/widgets/facility-generator/FacilityGenerator";

// Mock facility data structure based on RouterOutputs["locations"]["get"]
// Adjusted according to inferred API output structure from linter errors
const mockFacility: RouterOutputs["locations"]["get"] = {
  id: faker.string.uuid(),
  createdAt: faker.date.past(),
  updatedAt: faker.date.recent(),
  name: faker.company.name(),
  description: faker.lorem.sentence(),
  type: "HOSPITAL",
  address: {
    id: faker.string.uuid(),
    formatted: `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state({ abbreviated: true })} ${faker.location.zipCode()}`,
    timeZone: faker.location.timeZone(),
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  },
  organization: {
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: null,
  },
  departments: [],
  buildings: [],
  contacts: [],
  documents: [],
  actions: [],
};

// Mock department data structure that generateFacility would return
const mockGeneratedDepartments: GeneratedDepartment[] = Array.from(
  { length: 5 },
  () => ({
    name: faker.commerce.department() + " Department",
    description: faker.lorem.sentence(),
    type: faker.helpers.arrayElement([
      "DEPARTMENT",
      "CENTER",
      "WARD",
      "UNIT",
    ]) as string,
  }),
);

const mockGeneratedFacilityResult = JSON.stringify({
  buildings: [
    {
      // Assuming a building structure might exist
      departments: mockGeneratedDepartments,
    },
  ],
});

// --- Mock Handlers ---
const mockOnGenerateClick = fn().mockImplementation(async () => {
  console.log("Story: onGenerateClick triggered");
  toast.info("Mock: Generating departments...");
  // We can't easily change args from inside the mock handler itself for state simulation.
  // Use Controls addon or play functions for realistic state changes.
});

const mockOnAddSingle = fn().mockImplementation(async (department) => {
  console.log("Story: onAddSingle triggered with:", department);
  toast.success(`Mock: Adding ${department.name}`);
  await new Promise((resolve) => setTimeout(resolve, 500));
});

const mockOnAddAll = fn().mockImplementation(async () => {
  console.log("Story: onAddAll triggered");
  toast.success("Mock: Adding all departments...");
  await new Promise((resolve) => setTimeout(resolve, 1500));
});

const mockOnCancel = fn().mockImplementation(() => {
  console.log("Story: onCancel triggered");
  toast.info("Mock: Canceling add operation");
  // Need Controls/play function to set generatedData back to null.
});

const meta: Meta<typeof FacilityGenerator> = {
  title: "Components/Widgets/FacilityGenerator/FacilityGenerator",
  component: FacilityGenerator,
  parameters: {
    layout: "centered",
  },
  // Define args based on the *new* FacilityGeneratorProps
  args: {
    loading: false,
    error: null,
    facility: mockFacility,
    isGenerating: false,
    isSubmitting: false,
    generatedData: null, // Default state: no data generated yet
    isAddingDepartment: false,
    onGenerateClick: mockOnGenerateClick,
    onAddSingle: mockOnAddSingle,
    onAddAll: mockOnAddAll,
    onCancel: mockOnCancel,
  },
  // Define argTypes for controls if needed (optional)
  argTypes: {
    loading: { control: "boolean" },
    isGenerating: { control: "boolean" },
    isSubmitting: { control: "boolean" },
    isAddingDepartment: { control: "boolean" },
    generatedData: { control: "object" }, // Allow editing mock data
    facility: { control: "object" },
    error: { control: "object" },
    // Actions are automatically inferred by Storybook if args have `fn()`
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// --- Stories ---

export const Default: Story = {
  args: {
    // Uses default args: ready to generate, no generated data
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    facility: undefined, // Facility data wouldn't be available yet
    generatedData: null, // Ensure other props are consistent with loading state
  },
};

export const ErrorLoading: Story = {
  args: {
    loading: false,
    facility: undefined,
    generatedData: null,
    error: {
      message: "Failed to load initial facility data.",
    } as any, // Basic error object
  },
};

// Simulate state where user clicked "Search Facility"
export const Generating: Story = {
  args: {
    isGenerating: true, // Shows loader on the search button
  },
};

// Simulate state after generation is complete
export const WithGeneratedData: Story = {
  args: {
    generatedData: { departments: mockGeneratedDepartments },
  },
};

// Simulate state when user clicks "Add" on a single item
export const AddingSingleDepartment: Story = {
  args: {
    generatedData: { departments: mockGeneratedDepartments },
    isAddingDepartment: true, // Disables the "Add" buttons in the list
  },
};

// Simulate state when user clicks "Add Departments" (for all)
export const AddingAllDepartments: Story = {
  args: {
    generatedData: { departments: mockGeneratedDepartments },
    isSubmitting: true, // Disables the "Add All" and "Cancel" buttons
    isAddingDepartment: false, // Or true? Depends if you disable single adds too
  },
};
