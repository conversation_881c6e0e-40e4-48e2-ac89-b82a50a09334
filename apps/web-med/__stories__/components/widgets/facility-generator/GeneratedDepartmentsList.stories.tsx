// apps/web-med/__stories__/components/widgets/facility-generator/GeneratedDepartmentsList.stories.tsx
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import { fn } from "storybook/test";

import type { DepartmentType } from "@/api"; // Import if needed for casting

import type { GeneratedDepartment } from "@/components/widgets/facility-generator/GeneratedDepartmentsList";

import GeneratedDepartmentsList from "@/components/widgets/facility-generator/GeneratedDepartmentsList";

// Mock data using the GeneratedDepartment type
const mockDepartments: GeneratedDepartment[] = Array.from(
  { length: 6 },
  () => ({
    name: faker.commerce.department() + " Unit",
    description: faker.lorem.sentence(10),
    // Ensure type matches what's expected if validation occurs (casting may be needed)
    type: faker.helpers.arrayElement([
      "DEPARTMENT",
      "CENTER",
      "WARD",
      "<PERSON><PERSON>",
      "RO<PERSON>",
      "OTHER",
    ]) as string, // Keep as string based on GeneratedDepartment type
  }),
);

const meta: Meta<typeof GeneratedDepartmentsList> = {
  // Naming convention: Widgets/ContainingWidget/ComponentName
  title: "Components/Widgets/FacilityGenerator/GeneratedDepartmentsList",
  component: GeneratedDepartmentsList,
  parameters: {
    layout: "padded", // Use padded layout as it's a list section
  },
  argTypes: {
    departments: { control: "object" }, // Allow editing mock data in controls
    onAddSingle: { action: "addSingle" },
    onAddAll: { action: "addAll" },
    onCancel: { action: "cancel" },
    isAddingAll: { control: "boolean" },
    isAddingSingle: { control: "boolean" },
  },
  args: {
    departments: mockDepartments,
    onAddSingle: fn(),
    onAddAll: fn(),
    onCancel: fn(),
    isAddingAll: false,
    isAddingSingle: false,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {}, // Uses default args
};

export const EmptyList: Story = {
  args: {
    departments: [], // Pass empty array
  },
};

export const AddingAllState: Story = {
  args: {
    isAddingAll: true, // Simulate "Add All" button being processed
  },
};

export const AddingSingleState: Story = {
  args: {
    isAddingSingle: true, // Simulate individual "Add" buttons being processed
  },
};

export const FewerItems: Story = {
  args: {
    departments: mockDepartments.slice(0, 2), // Show with fewer items
  },
};
