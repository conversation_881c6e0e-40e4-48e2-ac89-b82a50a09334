import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import type { RouterOutputs } from "@/api";

import {
  OrganizationBillingFrequency,
  OrganizationClass,
  OrganizationStatus,
  OrganizationType,
} from "@/api";
import OrganizationBillingForm from "@/components/forms/OrganizationBillingForm";

const meta = {
  title: "Components/Forms/OrganizationBilling",
  component: OrganizationBillingForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationBillingForm>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock organization data
const mockOrganization = {
  id: "org-123",
  name: "Test Organization",
  type: OrganizationType.CLIENT,
  class: OrganizationClass.NONPROFIT,
  status: OrganizationStatus.ACTIVE,
  createdAt: new Date(),
  updatedAt: new Date(),
  billingFrequency: OrganizationBillingFrequency.MONTHLY,
  basePercentage: 100,
  assistPercentage: 0,
  cancellationFeePercentage: 0,
} satisfies Pick<
  NonNullable<RouterOutputs["organizations"]["get"]["organization"]>,
  | "id"
  | "name"
  | "type"
  | "class"
  | "status"
  | "createdAt"
  | "updatedAt"
  | "balance"
  | "billingFrequency"
  | "assistPercentage"
  | "basePercentage"
  | "cancellationFeePercentage"
>;

export const Default: Story = {
  args: {
    organization: mockOrganization,
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out base rate field", async () => {
      const baseRateInput = canvas.getByLabelText("Base Rate Percentage");
      await userEvent.clear(baseRateInput);
      await userEvent.type(baseRateInput, "1");
    });

    await step("Fill out assist rate field", async () => {
      const assistRateInput = canvas.getByLabelText("Assist Rate Percentage");
      await userEvent.clear(assistRateInput);
      await userEvent.type(assistRateInput, "1");
    });

    await step("Fill out cancellation fee field", async () => {
      const cancellationFeeInput = canvas.getByLabelText(
        "Cancellation Fee Percentage",
      );
      await userEvent.clear(cancellationFeeInput);
      await userEvent.type(cancellationFeeInput, "1");
    });

    await step("Fill out billing frequency field", async () => {
      const billingFrequencyInput = canvas.getByLabelText("Billing Frequency");
      await userEvent.clear(billingFrequencyInput);
      await userEvent.type(billingFrequencyInput, "MONTHLY");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Continue" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button text changes to indicate submission
      const submittingButton = canvas.getByRole("button", {
        name: "Setting up...",
      });
      expect(submittingButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    organization: mockOrganization,
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const Loading: Story = {
  args: {
    organization: mockOrganization,
    loading: true,
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};
