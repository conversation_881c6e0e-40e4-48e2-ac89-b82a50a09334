import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import MessageForm from "@/components/forms/MessageForm";

const meta: Meta<typeof MessageForm> = {
  title: "Components/Forms/MessageForm",
  component: MessageForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof MessageForm>;

export const Default: Story = {
  args: {
    onSubmit: (content: string) => {
      console.log("Message submitted:", content);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Type a message
    await userEvent.type(
      canvas.getByPlaceholderText(/type your message/i),
      "Hello, this is a test message",
    );

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /send/i }));

    // Assert that the input is cleared after submission
    await expect(canvas.getByPlaceholderText(/type your message/i)).toHaveValue(
      "",
    );
  },
};
