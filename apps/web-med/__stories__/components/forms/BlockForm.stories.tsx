import type { Meta, StoryObj } from "@storybook/react";

import { useState } from "react";

import { Card } from "@axa/ui/primitives/card";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";
import BlockForm from "@/components/forms/BlockForm";

// Define the form values type to match the component
type FormMode = "exact" | "recurring" | "span";
interface BlockFormValues {
  mode: FormMode;
  type: TimeBlockType;
  startTime: string;
  endTime: string;
  exactDate?: Date;
  dayOfWeek?: number;
  recurrence?: TimeBlockRecurrence;
  startDate?: Date;
  endDate?: Date;
}

const meta: Meta<typeof BlockForm> = {
  title: "Components/Forms/BlockForm",
  component: BlockForm,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof BlockForm>;

// Helper component to wrap the BlockForm and display submitted values
const BlockFormTester = ({
  defaultValues,
  initialMode,
}: {
  defaultValues?: Partial<BlockFormValues>;
  initialMode?: FormMode;
}) => {
  const [submittedValues, setSubmittedValues] =
    useState<BlockFormValues | null>(null);

  const handleSubmit = (values: BlockFormValues) => {
    setSubmittedValues(values);
  };

  return (
    <div className="flex w-[600px] flex-col gap-6">
      <Card className="p-6">
        <h2 className="mb-4 text-lg font-medium">Block Form</h2>
        <BlockForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={() => console.log("Cancelled")}
          initialMode={initialMode}
        />
      </Card>

      {submittedValues && (
        <Card className="bg-muted/10 p-6">
          <h3 className="text-md mb-2 font-medium">Submitted Values:</h3>
          <div className="mb-4 grid grid-cols-2 gap-2 text-sm">
            <div className="font-medium">Mode:</div>
            <div>{submittedValues.mode}</div>

            {submittedValues.dayOfWeek !== undefined && (
              <>
                <div className="font-medium">Day of Week:</div>
                <div>
                  {submittedValues.dayOfWeek} -
                  {submittedValues.dayOfWeek >= 0 &&
                  submittedValues.dayOfWeek <= 6
                    ? [
                        "Monday",
                        "Tuesday",
                        "Wednesday",
                        "Thursday",
                        "Friday",
                        "Saturday",
                        "Sunday",
                      ][submittedValues.dayOfWeek]
                    : "Invalid day"}
                </div>
              </>
            )}
          </div>
          <pre className="overflow-auto rounded bg-background p-3 text-xs">
            {JSON.stringify(submittedValues, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export const Default: Story = {
  render: () => <BlockFormTester />,
};

export const ExactDateMode: Story = {
  render: () => (
    <BlockFormTester
      initialMode="exact"
      defaultValues={{
        mode: "exact",
        type: TimeBlockType.SHIFT,
        startTime: "09:00",
        endTime: "17:00",
        exactDate: new Date(),
      }}
    />
  ),
};

export const RecurringMode: Story = {
  render: () => (
    <BlockFormTester
      initialMode="recurring"
      defaultValues={{
        mode: "recurring",
        type: TimeBlockType.SHIFT,
        dayOfWeek: 2, // Wednesday
        startTime: "09:00",
        endTime: "17:00",
        recurrence: TimeBlockRecurrence.WEEKLY,
      }}
    />
  ),
};

export const DateRangeMode: Story = {
  render: () => (
    <BlockFormTester
      initialMode="span"
      defaultValues={{
        mode: "span",
        type: TimeBlockType.SHIFT,
        startTime: "09:00",
        endTime: "17:00",
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      }}
    />
  ),
};

export const DifferentBlockTypes: Story = {
  render: () => (
    <div className="flex flex-col gap-8">
      <h2 className="text-xl font-bold">Different Block Types</h2>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card className="p-4">
          <h3 className="text-md mb-2 font-medium">Work Shift</h3>
          <BlockFormTester
            defaultValues={{
              mode: "recurring",
              type: TimeBlockType.SHIFT,
              startTime: "09:00",
              endTime: "17:00",
            }}
          />
        </Card>

        <Card className="p-4">
          <h3 className="text-md mb-2 font-medium">Available Time</h3>
          <BlockFormTester
            defaultValues={{
              mode: "recurring",
              type: TimeBlockType.AVAILABILITY,
              startTime: "09:00",
              endTime: "17:00",
            }}
          />
        </Card>

        <Card className="p-4">
          <h3 className="text-md mb-2 font-medium">Time Off</h3>
          <BlockFormTester
            defaultValues={{
              mode: "recurring",
              type: TimeBlockType.TIME_OFF,
              startTime: "09:00",
              endTime: "17:00",
            }}
          />
        </Card>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};

export const RecurrenceOptions: Story = {
  render: () => (
    <div className="flex flex-col gap-8">
      <h2 className="text-xl font-bold">Recurrence Options</h2>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[
          { label: "Weekly", value: TimeBlockRecurrence.WEEKLY },
          { label: "Bi-Weekly", value: TimeBlockRecurrence.BIWEEKLY },
          { label: "Monthly", value: TimeBlockRecurrence.MONTHLY },
          { label: "Quarterly", value: TimeBlockRecurrence.QUARTERLY },
          { label: "Yearly", value: TimeBlockRecurrence.YEARLY },
        ].map((option) => (
          <Card key={option.value} className="p-4">
            <h3 className="text-md mb-2 font-medium">{option.label}</h3>
            <BlockFormTester
              initialMode="recurring"
              defaultValues={{
                mode: "recurring",
                type: TimeBlockType.SHIFT,
                dayOfWeek: 1, // Tuesday
                startTime: "09:00",
                endTime: "17:00",
                recurrence: option.value,
              }}
            />
          </Card>
        ))}
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};
