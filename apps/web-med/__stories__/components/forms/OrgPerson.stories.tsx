import type { <PERSON>a, StoryObj } from "@storybook/react";

import { OrganizationPersonForm } from "@/components/forms/OrgPerson";

const meta = {
  title: "Components/Forms/OrganizationPerson",
  component: OrganizationPersonForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationPersonForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValues: {
      organizationId: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
  },
};

export const WithOrganization: Story = {
  args: {
    showOrganization: true,
    defaultValues: {
      organizationId: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
  },
};

export const WithDefaultValues: Story = {
  args: {
    showOrganization: true,
    defaultValues: {
      organizationId: "org-123",
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "1234567890",
    },
  },
};

export const WithPerson: Story = {
  args: {
    personId: "person-123",
    defaultValues: {
      organizationId: "",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      phone: "9876543210",
    },
  },
};

export const Closed: Story = {
  args: {
    open: false,
    showOrganization: true,
    defaultValues: {
      organizationId: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
  },
};
