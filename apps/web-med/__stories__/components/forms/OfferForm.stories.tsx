import type { Meta, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import OfferForm from "@/components/forms/OfferForm";

const meta: Meta<typeof OfferForm> = {
  title: "Components/Forms/Offer",
  component: OfferForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof OfferForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      notes: "",
      providerId: "",
      jobId: "",
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Notes/i),
      "This is an offer for a nursing position at our hospital.",
    );

    // Type in the provider search field
    await userEvent.type(
      canvas.getByPlaceholderText(/Search providers/i),
      "provider-123",
    );

    // Type in the job search field
    await userEvent.type(
      canvas.getByPlaceholderText(/Search jobs/i),
      "job-456",
    );

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Submit/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      notes: "This is a pre-filled offer for a nursing position.",
      providerId: "provider-123",
      jobId: "job-456",
      expiresAt: new Date("2023-12-31"),
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};

export const HideProvider: Story = {
  args: {
    defaultValues: {
      notes: "This is an offer with provider selection hidden.",
      jobId: "job-456",
    },
    showProvider: false,
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};

export const HideJob: Story = {
  args: {
    defaultValues: {
      notes: "This is an offer with job selection hidden.",
      providerId: "provider-123",
    },
    showJob: false,
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
