import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { JobPostType } from "@/api";
import { OrganizationJobPostForm } from "@/components/forms/OrgJobPost";

import { medicalRoles, organizations } from "../selectors/mocks";

const meta: Meta<typeof OrganizationJobPostForm> = {
  title: "Components/Forms/OrganizationJobPost",
  component: OrganizationJobPostForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [organizations, medicalRoles],
    },
  },
};

export default meta;
type Story = StoryObj<typeof OrganizationJobPostForm>;

export const Default: Story = {
  args: {
    open: true,
    defaultValues: {
      summary: "",
      description: "",
      type: JobPostType.PER_DIEM,
      organizationId: "",
    },
    showOrganization: true,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Summary/i),
      "Emergency Room Physician",
    );

    await userEvent.type(
      canvas.getByLabelText(/Description/i),
      "We are seeking an experienced Emergency Room Physician for our hospital.",
    );

    await userEvent.type(canvas.getByLabelText(/Medical Role/i), "Physician");

    await userEvent.type(canvas.getByLabelText(/Organization/i), "org-12345");

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Submit/i }));
  },
};

export const WithDefaultValues: Story = {
  args: {
    open: true,
    defaultValues: {
      summary: "Pediatric Nurse",
      description:
        "Seeking a compassionate pediatric nurse for our children's ward.",
      type: JobPostType.TEMPORARY,
      organizationId: "org-67890",
    },
    showOrganization: true,
  },
};

export const HideOrganization: Story = {
  args: {
    open: true,
    defaultValues: {
      summary: "Cardiologist",
      description: "Looking for an experienced cardiologist to join our team.",
      type: JobPostType.PERMANENT,
      organizationId: "org-54321",
    },
    showOrganization: false,
  },
};

export const Loading: Story = {
  args: {
    open: true,
    defaultValues: {
      summary: "Radiologist",
      description: "Seeking a radiologist for our imaging department.",
      type: JobPostType.PER_DIEM,
      organizationId: "org-98765",
    },
    loading: true,
  },
};
