import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { ValueType } from "@/api";
import ValueForm from "@/components/forms/ValueForm";

const meta = {
  title: "Components/Forms/Value",
  component: ValueForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ValueForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Select type
    await step("Select value type", async () => {
      const typeSelect = canvas.getByText("Select the type");
      await userEvent.click(typeSelect);

      // Wait for the dropdown to appear and select Tag
      const tagOption = canvas.getByText("Tag");
      await userEvent.click(tagOption);
    });

    // Fill out value
    await step("Fill out value field", async () => {
      const valueInput = canvas.getByPlaceholderText("Enter the value");
      await userEvent.clear(valueInput);
      await userEvent.type(valueInput, "Emergency");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      type: ValueType.MEDICAL_ROLE,
      value: "Nurse Practitioner",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const ExpenseValue: Story = {
  args: {
    defaultValues: {
      type: ValueType.EXPENSE,
      value: "Travel",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const ContactValue: Story = {
  args: {
    defaultValues: {
      type: ValueType.CONTACT,
      value: "Email",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const CustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Save Value
        </button>
      </div>
    ),
  },
};
