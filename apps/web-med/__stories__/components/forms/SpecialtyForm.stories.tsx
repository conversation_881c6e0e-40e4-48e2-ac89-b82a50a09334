import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import SpecialtyForm from "@/components/forms/SpecialtyForm";

const meta = {
  title: "Components/Forms/Specialty",
  component: SpecialtyForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof SpecialtyForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out specialty name", async () => {
      const nameInput = canvas.getByPlaceholderText("Enter specialty name");
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, "Cardiology");
    });

    await step("Fill out specialty description", async () => {
      const descriptionInput = canvas.getByPlaceholderText(
        "Enter specialty description",
      );
      await userEvent.clear(descriptionInput);
      await userEvent.type(
        descriptionInput,
        "Specializing in diagnosis and treatment of heart disorders",
      );
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      name: "Neurology",
      description: "Specializing in disorders of the nervous system",
      providerId: "provider-123",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const JobSpecialty: Story = {
  args: {
    defaultValues: {
      name: "Pediatrics",
      description: "Care for infants, children, and adolescents",
      jobId: "job-456",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const CustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Add Specialty
        </button>
      </div>
    ),
  },
};
