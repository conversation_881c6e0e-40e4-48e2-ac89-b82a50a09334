import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { DepartmentType } from "@/api";
import DepartmentForm, {
  DepartmentFormSubmitButton,
} from "@/components/forms/DepartmentForm";

const meta: Meta<typeof DepartmentForm> = {
  title: "Components/Forms/Department",
  component: DepartmentForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof DepartmentForm>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/department name/i),
      "Cardiology",
    );
    await userEvent.type(
      canvas.getByLabelText(/description/i),
      "Department specializing in heart-related issues",
    );

    // Select department type
    await userEvent.click(canvas.getByLabelText(/ward/i));

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert that the form was submitted
    // await expect(canvas.getByText(/form submitted/i)).toBeInTheDocument();
  },
};

export const WithDefaultValues: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "Neurology",
      description: "Department focusing on nervous system disorders",
      type: DepartmentType.DEPARTMENT,
    },
  },
};

export const WithRoomType: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "Operating Room 1",
      description: "Main operating room for surgical procedures",
      type: DepartmentType.ROOM,
    },
  },
};

export const WithOtherType: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "Mobile Clinic",
      description: "Traveling healthcare unit for remote areas",
      type: DepartmentType.OTHER,
    },
  },
};

export const WithCustomSubmitButton: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  render: (args) => (
    <DepartmentForm {...args}>
      <div className="flex w-full justify-center">
        <DepartmentFormSubmitButton className="bg-blue-500 text-white">
          Create Department
        </DepartmentFormSubmitButton>
      </div>
    </DepartmentForm>
  ),
};
