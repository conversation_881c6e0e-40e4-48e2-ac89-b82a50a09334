import type { Meta, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { OrganizationMemberForm } from "@/components/forms/OrgMember";

import { organizations, people } from "../selectors/mocks";

const meta = {
  title: "Components/Forms/OrganizationMember",
  component: OrganizationMemberForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [organizations, people],
    },
  },
} satisfies Meta<typeof OrganizationMemberForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValues: {},
  },
};

export const WithOrganization: Story = {
  args: {
    showOrganization: true,
    defaultValues: {},
  },
};

export const WithDefaultValues: Story = {
  args: {
    showOrganization: true,
    personId: "person-123",
    organizationId: "org-456",
    defaultValues: {
      role: "ADMIN",
      organizationId: "org-456",
    },
  },
};

export const WithPerson: Story = {
  args: {
    personId: "person-123",
    defaultValues: {
      role: "MEMBER",
    },
  },
};

export const Closed: Story = {
  args: {
    showOrganization: true,
    defaultValues: {},
  },
};
