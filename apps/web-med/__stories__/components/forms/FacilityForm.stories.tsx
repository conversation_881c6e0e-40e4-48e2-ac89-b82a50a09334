import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, waitFor, within } from "storybook/test";

import FacilityForm, {
  FacilityFormSubmitButton,
} from "@/components/forms/FacilityForm";

const meta: Meta<typeof FacilityForm> = {
  title: "Components/Forms/FacilityForm",
  component: FacilityForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof FacilityForm>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/facility name/i),
      "Central Hospital",
    );
    await userEvent.type(
      canvas.getByLabelText(/description/i),
      "A state-of-the-art medical facility",
    );

    // Simulate address autocomplete selection
    const addressInput = canvas.getByPlaceholderText(/type an address/i);
    await userEvent.type(addressInput, "123 Healthy Way, Oceanside");

    // Wait for and select an autocomplete option
    await waitFor(async () => {
      const autocompleteOption = await canvas.findByText(
        "123 Healthy Way, Oceanside, NY, USA",
      );
      await userEvent.keyboard("{ArrowDown}");
      await userEvent.keyboard("{Enter}");
      // await userEvent.click(autocompleteOption);
    });

    // Select facility type using the radio cards
    await userEvent.click(canvas.getByLabelText(/campus/i));

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert that the form was submitted
    // await waitFor(() => {
    //   expect(canvas.getByText(/form submitted/i)).toBeInTheDocument();
    // });
  },
};

export const WithDefaultValues: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "City Clinic",
      description: "A local clinic serving the community",
      address: "456 Care Lane, Townsville, TS 67890",
      type: "CLINIC",
    },
  },
};

export const WithOtherType: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "Mobile Health Unit",
      description: "A mobile healthcare facility",
      address: "Various locations",
      type: "OTHER",
    },
  },
};

export const WithCustomSubmitButton: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  render: (args) => (
    <FacilityForm {...args}>
      <div className="flex w-full justify-center">
        <FacilityFormSubmitButton className="bg-blue-500 text-white">
          Create Facility
        </FacilityFormSubmitButton>
      </div>
    </FacilityForm>
  ),
};

export const WithLabType: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "Central Research Laboratory",
      description: "A state-of-the-art research facility",
      address: "789 Science Blvd, Techville, TV 54321",
      type: "LAB",
    },
  },
};

export const WithPharmacyType: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
    defaultValues: {
      name: "City Center Pharmacy",
      description: "A full-service pharmacy in the heart of the city",
      address: "789 Main St, Downtown, DT 54321",
      type: "PHARMACY",
    },
  },
};
