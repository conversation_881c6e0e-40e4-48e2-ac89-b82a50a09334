import type { Meta, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { OrganizationFacilityForm } from "@/components/forms/OrgFacility";

import { organizations } from "../selectors/mocks";

const meta = {
  title: "Components/Forms/OrganizationFacility",
  component: OrganizationFacilityForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [organizations],
    },
  },
} satisfies Meta<typeof OrganizationFacilityForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValues: {},
  },
};

export const WithOrganization: Story = {
  args: {
    showOrganization: true,
    defaultValues: {},
  },
};

export const WithDefaultValues: Story = {
  args: {
    showOrganization: true,
    defaultValues: {
      name: "General Hospital",
      type: "HOSPITAL" as any,
      organizationId: "org-123",
    },
  },
};

export const Compact: Story = {
  args: {
    isCompact: true,
    defaultValues: {
      name: "Local Clinic",
      type: "CLINIC" as any,
    },
  },
};

export const WithLocation: Story = {
  args: {
    locationId: "location-456",
    defaultValues: {
      name: "Downtown Medical Center",
      type: "HOSPITAL" as any,
    },
  },
};
