import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, waitFor, within } from "storybook/test";

import { QualificationType } from "@/api";
import QualificationForm from "@/components/forms/QualificationForm";

const meta: Meta<typeof QualificationForm> = {
  title: "Components/Forms/Qualification",
  tags: ["forms"],
  component: QualificationForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof QualificationForm>;

export const Default: Story = {
  args: {
    onSubmit: (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const body = within(document.body);
    const canvas = within(canvasElement);

    await userEvent.type(
      canvas.getByLabelText(/Name/),
      "Bachelor of Science in Nursing",
    );
    // Fill out the form
    await userEvent.click(canvas.getByRole("combobox"));
    await waitFor(async () => {
      await expect(
        body.getByRole("option", { name: "Degree" }),
      ).toBeInTheDocument();
    });
    await userEvent.selectOptions(
      body.getByRole("option", { name: "Degree" }),
      "Degree",
    );
    await userEvent.type(
      canvas.getByLabelText(/Institution/i),
      "State University",
    );

    await userEvent.type(
      canvas.getByLabelText(/Start date|Issue date/i),
      "2020-05-15",
    );
    await userEvent.type(
      canvas.getByLabelText(/End date|Expiration date/i),
      "2025-05-15",
    );

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert that the form was submitted
    await expect(canvas.getByText(/form submitted/i)).toBeInTheDocument();
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      type: QualificationType.LICENSE,
      name: "Registered Nurse License",
      institution: "State Board of Nursing",
      startDate: new Date("2022-01-01"),
      endDate: new Date("2024-01-01"),
    },
    onSubmit: (values) => {
      console.log(values);
    },
  },
};
