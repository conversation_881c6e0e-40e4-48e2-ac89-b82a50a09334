import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import { expect, userEvent, within } from "storybook/test";

import { ValueType } from "@axa/database-medical";

import { trpcMsw } from "@/api/mock";
import ProviderTitleForm from "@/components/forms/ProviderTitleForm";

const meta = {
  title: "Components/Forms/ProviderTitle",
  component: ProviderTitleForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [
        trpcMsw.values.getMany.query(({ input }) => {
          if (input.type === ValueType.MEDICAL_ROLE) {
            return {
              items: [
                "Cardiologist",
                "Pediatrician",
                "Neurologist",
                "General Practitioner",
                "Orthopedic Surgeon",
              ].map((value, index) => ({
                value,
                id: (index + 1).toString(),
                type: ValueType.MEDICAL_ROLE,
                createdAt: faker.date.past(),
                updatedAt: faker.date.past(),
              })),
              total: 5,
            };
          }
          return { items: [], total: 0 };
        }),
      ],
    },
  },
} satisfies Meta<typeof ProviderTitleForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out professional title", async () => {
      const titleInput = canvas.getByPlaceholderText(
        "e.g. Emergency Room Physician, Registered Nurse",
      );
      await userEvent.clear(titleInput);
      await userEvent.type(titleInput, "Cardiac Surgeon");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Save" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      title: "Registered Nurse",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const EditingTitle: Story = {
  args: {
    defaultValues: {
      title: "Nurse Practitioner",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Edit the existing title
    await step("Edit professional title", async () => {
      const titleInput = canvas.getByDisplayValue("Nurse Practitioner");
      await userEvent.clear(titleInput);
      await userEvent.type(titleInput, "Pediatric Nurse Practitioner");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });
  },
};

export const CustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Update Title
        </button>
      </div>
    ),
  },
};
