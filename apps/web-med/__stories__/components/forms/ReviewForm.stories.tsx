import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import ReviewForm from "@/components/forms/ReviewForm";

const meta = {
  title: "Components/Forms/Review",
  component: ReviewForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ReviewForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out comment field", async () => {
      const commentInput = canvas.getByPlaceholderText(
        "Enter your review comment",
      );
      await userEvent.clear(commentInput);
      await userEvent.type(
        commentInput,
        "The service was excellent and the staff was very professional.",
      );
    });

    await step("Select rating", async () => {
      const ratingSelect = canvas.getByText("Select rating");
      await userEvent.click(ratingSelect);

      // Wait for the dropdown to appear and select 5 stars
      const fiveStarsOption = canvas.getByText("5 Stars");
      await userEvent.click(fiveStarsOption);
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      comment: "The provider was knowledgeable and attentive to my needs.",
      rating: 4,
      providerId: "provider-123",
      organizationId: "org-456",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const LowRating: Story = {
  args: {
    defaultValues: {
      rating: 2,
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form with a negative comment
    await step("Fill out comment field with negative feedback", async () => {
      const commentInput = canvas.getByPlaceholderText(
        "Enter your review comment",
      );
      await userEvent.clear(commentInput);
      await userEvent.type(
        commentInput,
        "The wait time was too long and the staff seemed disorganized.",
      );
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      await userEvent.click(submitButton);
    });
  },
};

export const CustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Post Review
        </button>
      </div>
    ),
  },
};
