import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import JobExperienceForm from "@/components/forms/JobExperienceForm";

const meta: Meta<typeof JobExperienceForm> = {
  title: "Components/Forms/JobExperience",
  component: JobExperienceForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobExperienceForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      role: "",
      company: "",
      startDate: new Date(),
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Role/i),
      "Emergency Room Physician",
    );

    await userEvent.type(
      canvas.getByLabelText(/Company/i),
      "Memorial Hospital",
    );

    await userEvent.type(
      canvas.getByLabelText(/Description/i),
      "Worked in a fast-paced emergency department handling critical care cases and trauma patients.",
    );

    // Note: Date fields are complex components that might be difficult to interact with in tests
    // For a real test, you would need to handle the date picker interactions

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Save/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      role: "Pediatric Nurse",
      company: "Children's Hospital",
      startDate: new Date("2020-01-15"),
      endDate: new Date("2022-06-30"),
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
