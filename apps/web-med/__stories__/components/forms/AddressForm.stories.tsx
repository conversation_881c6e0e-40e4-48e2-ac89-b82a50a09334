import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import AddressForm from "@/components/forms/AddressForm";

const meta: Meta<typeof AddressForm> = {
  title: "Components/Forms/Address",
  component: AddressForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof AddressForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      address: {
        formatted: "",
        country: "",
        state: "",
        city: "",
        street: "",
        postal: "",
        latitude: 0,
        longitude: 0,
      },
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    // Note: Since AddressAutocomplete is a complex component that might involve
    // external API calls, we'll just simulate a click on it
    await userEvent.click(
      canvas.getByPlaceholderText(/Type to search for an address/i),
    );

    // For a real test, you would need to mock the address autocomplete API
    // and simulate selecting an address

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /save/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      address: {
        formatted: "123 Main St, San Francisco, CA 94105, USA",
        country: "USA",
        state: "CA",
        city: "San Francisco",
        street: "123 Main St",
        postal: "94105",
        latitude: 37.7749,
        longitude: -122.4194,
      },
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
