import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import ApplicationForm from "@/components/forms/ApplicationForm";

const meta: Meta<typeof ApplicationForm> = {
  title: "Components/Forms/Application",
  component: ApplicationForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ApplicationForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      jobId: "",
      providerId: "",
      message: "",
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(canvas.getByLabelText(/Job ID/i), "job-123456");

    await userEvent.type(
      canvas.getByLabelText(/Provider ID/i),
      "provider-789012",
    );

    await userEvent.type(
      canvas.getByLabelText(/Message/i),
      "I am very interested in this position and believe my skills are a great match.",
    );

    // Submit the form
    await userEvent.click(
      canvas.getByRole("button", { name: /Submit Application/i }),
    );

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      jobId: "job-123456",
      providerId: "provider-789012",
      message:
        "I am very interested in this position and believe my skills are a great match.",
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
