import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import TimeBlockForm from "@/components/forms/TimeBlockForm";

const meta = {
  title: "Components/Forms/TimeBlock",
  component: TimeBlockForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof TimeBlockForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Submit" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      dayOfWeek: "monday",
      startTime: 9 * 60, // 9:00 AM in minutes
      endTime: 17 * 60, // 5:00 PM in minutes
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const WeekendTimeBlock: Story = {
  args: {
    defaultValues: {
      dayOfWeek: "saturday",
      startTime: 10 * 60, // 10:00 AM in minutes
      endTime: 14 * 60, // 2:00 PM in minutes
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const RecurringTimeBlock: Story = {
  args: {
    defaultValues: {
      dayOfWeek: "wednesday",
      startTime: 13 * 60, // 1:00 PM in minutes
      endTime: 16 * 60, // 4:00 PM in minutes
      recurrence: "weekly",
      startDate: "2023-01-01",
      endDate: "2023-12-31",
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const CustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Save Time Block
        </button>
      </div>
    ),
  },
};
