import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import JobPostForm from "@/components/forms/JobPostForm";

const meta: Meta<typeof JobPostForm> = {
  title: "Components/Forms/JobPost",
  component: JobPostForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof JobPostForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      summary: "",
      description: "",
      type: "FULL_TIME",
      payType: "HOURLY",
      paymentRate: 0,
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/summary/i),
      "Emergency Room Physician",
    );
    await userEvent.type(
      canvas.getByLabelText(/description/i),
      "We are seeking an experienced Emergency Room Physician.",
    );
    await userEvent.selectOptions(canvas.getByLabelText(/type/i), "PART_TIME");
    await userEvent.selectOptions(canvas.getByLabelText(/pay type/i), "HOURLY");
    await userEvent.type(canvas.getByLabelText(/payment rate/i), "150");

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert that the form was submitted
    await expect(canvas.getByText(/form submitted/i)).toBeInTheDocument();
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      summary: "Pediatric Nurse",
      description:
        "Seeking a compassionate pediatric nurse for our children's ward.",
      type: "PART_TIME",
      payType: "HOURLY",
      paymentRate: 45,
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
