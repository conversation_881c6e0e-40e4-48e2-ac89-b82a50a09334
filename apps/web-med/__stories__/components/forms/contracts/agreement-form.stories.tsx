import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import { addMonths } from "date-fns";

import type { RouterOutputs } from "@/api";
import type { AgreementFormValues } from "@/components/forms/contracts/agreement-form";

import { ContractType } from "@/api";
import { trpcMsw } from "@/api/mock";
import AgreementForm from "@/components/forms/contracts/agreement-form";

// Generate consistent IDs for mock data
const providerIds = Array.from(
  { length: 3 },
  (_, _i) => `provider-${faker.string.uuid()}`,
);
const providerPersonIds = Array.from(
  { length: 3 },
  (_, _i) => `provider-person-${faker.string.uuid()}`,
);
const orgRepIds = Array.from(
  { length: 3 },
  (_, _i) => `person-${faker.string.uuid()}`,
);

const mockProviders = [
  ...Array.from({ length: 3 }, (_, _i) => ({
    id: providerIds[_i],
    title: faker.person.jobTitle(),
    person: {
      id: providerPersonIds[_i],
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      avatar: faker.image.avatar(),
      createdAt: new Date("2023-01-15"),
      updatedAt: new Date("2023-01-15"),
      organization: {
        id: `org${_i + 1}`,
        name: faker.company.name(),
        avatar: null,
      },
    },
  })),
];

const organizations = ["org1", "org2", "org3", "org4"];
const mockOrganizationReps = [
  ...Array.from({ length: 3 }, (_, _i) => ({
    id: orgRepIds[_i],
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    avatar: faker.image.avatar(),
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2023-01-15"),
    organization: {
      id: `org-${organizations[_i]}`,
      name: faker.company.name(),
      avatar: null,
    },
  })),
];

const mockOrganizations = [
  {
    id: "1",
    name: "City General Healthcare",
    description: "Leading healthcare provider in the region",
    avatar: "https://example.com/avatar1.jpg",
    type: "HEALTHCARE_PROVIDER",
    status: "ACTIVE",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-03-10"),
    deletedAt: null,
    contacts: [],
    actions: [],
    documents: [],
  },
  {
    id: "2",
    name: "Metro Medical Group",
    description: "Multi-specialty medical practice group",
    avatar: "https://example.com/avatar2.jpg",
    type: "HEALTHCARE_PROVIDER",
    status: "ACTIVE",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-03-10"),
    deletedAt: null,
    contacts: [],
    actions: [],
    documents: [],
  },
  {
    id: "3",
    name: "Coastal Health Systems",
    description: "Integrated healthcare network",
    avatar: null,
    type: "HEALTHCARE_PROVIDER",
    status: "ACTIVE",
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-03-10"),
    deletedAt: null,
    contacts: [],
    actions: [],
    documents: [],
  },
];

// Create a staff member ID for internal signers
const staffMemberId = `staff-${faker.string.uuid()}`;

const meta: Meta<typeof AgreementForm> = {
  title: "Components/Forms/Contracts/AgreementForm",
  component: AgreementForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [
        trpcMsw.providers.getMany.query(({ input }) => {
          console.log(input);
          return {
            items: mockProviders,
            total: 3,
          } as unknown as RouterOutputs["providers"]["getMany"];
        }),
        trpcMsw.people.getMany.query(({ input }) => {
          console.log(input);

          return {
            items: mockOrganizationReps,
            total: 4,
          } as unknown as RouterOutputs["people"]["getMany"];
        }),
        trpcMsw.organizations.getMany.query(() => {
          return {
            items: mockOrganizations,
            total: 3,
          } as unknown as RouterOutputs["organizations"]["getMany"];
        }),
      ],
    },
  },
  args: {
    contractId: "contract-123",
    onSubmit: async (values: AgreementFormValues) => {
      console.log("Form values:", values);
    },
    defaultValues: {
      signers: [],
    },
  },
};

export default meta;

type Story = StoryObj<typeof AgreementForm>;

export const JobContract: Story = {
  args: {
    contractType: ContractType.EMPLOYMENT,
    organizationId: "org-123",
    defaultValues: {
      signers: [],
    },
  },
};

export const ServiceRatesContract: Story = {
  args: {
    contractType: ContractType.SERVICE_RATE,
    organizationId: "org-123",
    defaultValues: {
      signers: [],
    },
  },
};

export const CustomContract: Story = {
  args: {
    contractType: ContractType.OTHER,
    organizationId: "org-123",
    defaultValues: {
      signers: [],
    },
  },
};

export const WithDefaultValues: Story = {
  args: {
    contractType: ContractType.EMPLOYMENT,
    organizationId: "org-123",
    defaultValues: {
      type: "job-position",
      agreement: {
        generate: true,
      },
      expiresAt: addMonths(new Date(), 1), // 1 month from now
      signers: [],
    },
  },
};

export const WithSubmitAction: Story = {
  args: {
    contractType: ContractType.EMPLOYMENT,
    organizationId: "org-123",
    defaultValues: {
      signers: [],
    },
    onSubmit: async (values: AgreementFormValues) => {
      console.log("Form values:", values);
    },
  },
};

export const WithPresetSigners: Story = {
  args: {
    contractType: ContractType.EMPLOYMENT,
    organizationId: "org-123",
    defaultValues: {
      type: "job-position",
      agreement: {
        generate: true,
      },
      expiresAt: addMonths(new Date(), 1), // 1 month from now
      signers: [
        {
          id: providerIds[0] ?? "provider-123", // Use the actual provider person ID
          role: "provider",
        },
        {
          id: orgRepIds[0] ?? "org-rep-fallback-id", // Use the actual organization rep ID
          role: "organization",
        },
      ],
    },
  },
};

export const ServiceRateWithPresetSigners: Story = {
  args: {
    contractType: ContractType.SERVICE_RATE,
    organizationId: "org-123",
    defaultValues: {
      type: "service-rate",
      agreement: {
        generate: true,
      },
      expiresAt: addMonths(new Date(), 1), // 1 month from now
      signers: [
        {
          id: staffMemberId ?? "staff-fallback-id", // Use the staff member ID for internal role
          role: "internal",
        },
        {
          id: orgRepIds[1] ?? "org-rep-fallback-id", // Use a different organization rep
          role: "organization",
        },
      ],
    },
  },
};

export const CustomWithPresetSigners: Story = {
  args: {
    contractType: ContractType.OTHER,
    organizationId: "org-123",
    defaultValues: {
      type: "custom",
      agreement: {
        generate: false,
      },
      expiresAt: addMonths(new Date(), 1), // 1 month from now
      signers: [
        {
          id: providerIds[1] ?? "", // Use a different provider
          role: "provider",
        },
        {
          id: staffMemberId ?? "staff-fallback-id", // Use the staff member for internal
          role: "internal",
        },
        {
          id: orgRepIds[2] ?? "org-rep-fallback-id", // Use a different org rep
          role: "organization",
        },
      ],
    },
  },
};
