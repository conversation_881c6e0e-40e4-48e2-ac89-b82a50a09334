import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { ContractType } from "@/api";
import { trpcMsw } from "@/api/mock";
import CoreContractForm from "@/components/forms/contracts/core";

// Generate consistent IDs for mock data
const providerIds = Array.from(
  { length: 3 },
  (_, _i) => `provider-${faker.string.uuid()}`,
);
const providerPersonIds = Array.from(
  { length: 3 },
  (_, _i) => `provider-person-${faker.string.uuid()}`,
);
const orgRepIds = Array.from(
  { length: 3 },
  (_, _i) => `person-${faker.string.uuid()}`,
);
const staffMemberId = `staff-${faker.string.uuid()}`;

const meta: Meta<typeof CoreContractForm> = {
  title: "Components/Forms/Contracts/CoreContractForm",
  component: CoreContractForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [
        trpcMsw.providers.getMany.query(({ input }) => {
          console.log(input);
          return {
            items: [
              ...Array.from({ length: 3 }, (_, i) => ({
                id: providerIds[i],
                title: faker.person.jobTitle(),
                person: {
                  id: providerPersonIds[i],
                  firstName: faker.person.firstName(),
                  lastName: faker.person.lastName(),
                  email: faker.internet.email(),
                  phone: faker.phone.number(),
                  avatar: faker.image.avatar(),
                  createdAt: new Date("2023-01-15"),
                  updatedAt: new Date("2023-01-15"),
                  organization: {
                    id: `org${i + 1}`,
                    name: faker.company.name(),
                    avatar: null,
                  },
                },
              })),
            ],
            total: 3,
          } as unknown as RouterOutputs["providers"]["getMany"];
        }),
        trpcMsw.people.getMany.query(({ input }) => {
          console.log(input);
          const organizations = ["org1", "org2", "org3", "org4"];
          return {
            items: [
              ...Array.from({ length: 3 }, (_, i) => ({
                id: orgRepIds[i],
                firstName: faker.person.firstName(),
                lastName: faker.person.lastName(),
                email: faker.internet.email(),
                phone: faker.phone.number(),
                avatar: faker.image.avatar(),
                createdAt: new Date("2023-01-15"),
                updatedAt: new Date("2023-01-15"),
                organization: {
                  id: `org-${organizations[i]}`,
                  name: faker.company.name(),
                  avatar: null,
                },
              })),
            ],
            total: 4,
          } as unknown as RouterOutputs["people"]["getMany"];
        }),
        trpcMsw.organizations.getMany.query(() => {
          return {
            items: [
              {
                id: "1",
                name: "City General Healthcare",
                description: "Leading healthcare provider in the region",
                avatar: "https://example.com/avatar1.jpg",
                type: "HEALTHCARE_PROVIDER",
                status: "ACTIVE",
                createdAt: new Date("2024-01-01"),
                updatedAt: new Date("2024-03-10"),
                deletedAt: null,
                contacts: [],
                actions: [],
                documents: [],
              },
              {
                id: "2",
                name: "Metro Medical Group",
                description: "Multi-specialty medical practice group",
                avatar: "https://example.com/avatar2.jpg",
                type: "HEALTHCARE_PROVIDER",
                status: "ACTIVE",
                createdAt: new Date("2024-01-15"),
                updatedAt: new Date("2024-03-10"),
                deletedAt: null,
                contacts: [],
                actions: [],
                documents: [],
              },
              {
                id: "3",
                name: "Coastal Health Systems",
                description: "Integrated healthcare network",
                avatar: null,
                type: "HEALTHCARE_PROVIDER",
                status: "ACTIVE",
                createdAt: new Date("2024-02-01"),
                updatedAt: new Date("2024-03-10"),
                deletedAt: null,
                contacts: [],
                actions: [],
                documents: [],
              },
            ],
            total: 3,
          } as unknown as RouterOutputs["organizations"]["getMany"];
        }),
      ],
    },
  },
};
export default meta;

type Story = StoryObj<typeof CoreContractForm>;

const commonArgs = {
  onSubmit: (v: Record<string, unknown>) => console.log("General Submit", v),
};

export const Default: Story = {
  args: { ...commonArgs },
};

export const EmploymentTab: Story = {
  args: {
    ...commonArgs,
    defaultValues: {
      type: ContractType.EMPLOYMENT,
      title: "Employment Contract",
      organization: "1",
      agreement: {
        generate: true,
      },
      signers: [],
    },
  },
};

export const ServiceRatesTab: Story = {
  args: {
    ...commonArgs,
    defaultValues: {
      type: ContractType.SERVICE_RATE,
      title: "Service Rate Agreement",
      organization: "2",
      agreement: {
        generate: true,
      },
      signers: [],
    },
  },
};

export const WithPresetSigners: Story = {
  args: {
    ...commonArgs,
    defaultValues: {
      type: ContractType.EMPLOYMENT,
      title: "Employment Contract with Preset Signers",
      organization: "1",
      agreement: {
        generate: true,
      },
      signers: [
        {
          id: providerPersonIds[0] ?? "provider-fallback-id",
          role: "provider",
        },
        {
          id: orgRepIds[0] ?? "org-rep-fallback-id",
          role: "organization",
        },
      ],
    },
  },
};

export const ServiceRateWithPresetSigners: Story = {
  args: {
    ...commonArgs,
    defaultValues: {
      type: ContractType.SERVICE_RATE,
      title: "Service Rate with Preset Signers",
      organization: "2",
      agreement: {
        generate: true,
      },
      signers: [
        {
          id: staffMemberId ?? "staff-fallback-id",
          role: "internal",
        },
        {
          id: orgRepIds[1] ?? "org-rep-fallback-id",
          role: "organization",
        },
      ],
    },
  },
};
