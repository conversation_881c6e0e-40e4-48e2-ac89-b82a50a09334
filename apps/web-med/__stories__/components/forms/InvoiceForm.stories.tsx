import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import InvoiceForm from "@/components/forms/InvoiceForm";

const meta: Meta<typeof InvoiceForm> = {
  title: "Components/Forms/Invoice",
  component: InvoiceForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof InvoiceForm>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(canvas.getByLabelText(/ID/i), "INV-2023-12345");

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Save/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const PrefilledID: Story = {
  args: {
    // Note: We can't use defaultValues here as it's not part of the props
    // The component has its own internal defaultValues
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Clear the field and type a new value
    const idField = canvas.getByLabelText(/ID/i);
    await userEvent.clear(idField);
    await userEvent.type(idField, "INV-2023-67890");
  },
};
