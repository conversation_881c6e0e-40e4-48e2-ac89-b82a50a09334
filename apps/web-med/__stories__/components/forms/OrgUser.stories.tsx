import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { UserRole } from "@/components/forms/OrgUser";

import { OrganizationUserForm } from "@/components/forms/OrgUser";

import { organizations } from "../selectors/mocks";

const meta = {
  title: "Components/Forms/OrganizationUser",
  component: OrganizationUserForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [organizations],
    },
  },
} satisfies Meta<typeof OrganizationUserForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    open: true,
    userId: "user-123",
    defaultValues: {},
  },
};

export const WithOrganization: Story = {
  args: {
    open: true,
    showOrganization: true,
    userId: "user-123",
    defaultValues: {
      role: "CLIENT" as UserRole,
    },
  },
};

export const WithDefaultValues: Story = {
  args: {
    open: true,
    showOrganization: true,
    userId: "user-123",
    defaultValues: {
      role: "CLIENT" as User<PERSON>ole,
      organizationId: "org-1",
    },
  },
};

export const AdminRole: Story = {
  args: {
    open: true,
    showOrganization: true,
    userId: "user-456",
    defaultValues: {
      role: "ADMIN" as UserRole,
    },
  },
};

export const Closed: Story = {
  args: {
    open: false,
    showOrganization: true,
    userId: "user-123",
    defaultValues: {},
  },
};
