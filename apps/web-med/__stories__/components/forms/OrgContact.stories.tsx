import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { OrganizationContactForm } from "@/components/forms/OrgContact";

import { organizations, people } from "../selectors/mocks";

const meta = {
  title: "Components/Forms/OrganizationContact",
  component: OrganizationContactForm,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [people, organizations],
    },
  },
} satisfies Meta<typeof OrganizationContactForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    open: true,
    defaultValues: {},
  },
};

export const WithOrganization: Story = {
  args: {
    open: true,
    showOrganization: true,
    defaultValues: {},
  },
};

export const WithDefaultValues: Story = {
  args: {
    open: true,
    showOrganization: true,
    defaultValues: {
      organizationId: "org-1",
      personId: "person-1",
      role: "Email",
      person: {
        id: "person-1",
        firstName: "<PERSON>",
        lastName: "Do<PERSON>",
        email: "<EMAIL>",
      },
    },
  },
};

export const Closed: Story = {
  args: {
    open: false,
    showOrganization: true,
    defaultValues: {},
  },
};
