import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { PaymentType } from "@/api";
import ShiftForm from "@/components/forms/ShiftForm";

const meta: Meta<typeof ShiftForm> = {
  title: "Components/Forms/Shift",
  component: ShiftForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ShiftForm>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};

export const WithDefaultMotion: Story = {
  args: {
    onSubmit: async (values) => {
      console.log(values);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(canvas.getByLabelText(/summary/i), "Night Shift ER");
    await userEvent.type(
      canvas.getByLabelText(/description/i),
      "Emergency Room night shift coverage",
    );
    await userEvent.selectOptions(
      canvas.getByLabelText(/shift type/i),
      "NIGHT",
    );
    await userEvent.selectOptions(
      canvas.getByLabelText(/billing type/i),
      "HOURLY",
    );
    await userEvent.type(canvas.getByLabelText(/billing rate/i), "50");
    await userEvent.type(canvas.getByLabelText(/start date/i), "2023-07-01");
    await userEvent.type(canvas.getByLabelText(/end date/i), "2023-07-02");
    await userEvent.type(canvas.getByLabelText(/start time/i), "22:00");
    await userEvent.type(canvas.getByLabelText(/end time/i), "06:00");

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert that the form was submitted
    await expect(canvas.getByText(/form submitted/i)).toBeInTheDocument();
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      summary: "Day Shift Pediatrics",
      scope: "Daytime shift in pediatrics ward",
      paymentType: PaymentType.HOURLY,
      paymentRate: 40,
      startDate: new Date("2023-07-01"),
      endDate: new Date("2023-07-01"),
      startTime: { hour: 8, minute: 0 },
      endTime: { hour: 16, minute: 0 },
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
