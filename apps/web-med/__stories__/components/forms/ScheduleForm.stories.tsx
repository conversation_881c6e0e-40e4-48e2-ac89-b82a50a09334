import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import ScheduleForm from "@/components/forms/ScheduleForm";

const meta: Meta<typeof ScheduleForm> = {
  title: "Components/Forms/Schedule",
  component: ScheduleForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ScheduleForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      type: "SHIFT",
      name: "",
      timeBlocks: [],
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Schedule Name/i),
      "Weekly Schedule",
    );

    await userEvent.click(
      canvas.getByRole("button", { name: /Add Time Block/i }),
    );

    // Note: TimeBlockField is a complex component with many fields
    // For a real test, you would need to interact with all the fields
    // This is a simplified version

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Submit/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      type: "PROVIDER",
      name: "Doctor's Weekly Schedule",
      timeBlocks: [
        {
          type: "AVAILABILITY",
          hours: 8,
          startDate: new Date("2023-01-01"),
          endDate: new Date("2023-12-31"),
          startsAt: { hour: 9, minute: 0 },
          endsAt: { hour: 17, minute: 0 },
          timeZone: "America/New_York",
          recurrence: "WEEKLY",
        },
        {
          type: "TIME_OFF",
          hours: 24,
          startDate: new Date("2023-07-01"),
          endDate: new Date("2023-07-07"),
          timeZone: "America/New_York",
        },
      ],
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
