import type { Meta, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import DocumentUploadForm from "@/components/forms/DocumentUploadForm";

const meta: Meta<typeof DocumentUploadForm> = {
  title: "Components/Forms/DocumentUpload",
  component: DocumentUploadForm,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="w-full max-w-3xl rounded-lg border p-6 shadow-sm">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof DocumentUploadForm>;

export const Default: Story = {
  args: {
    bucketName: "documents",
    folderPath: "uploads",
    onSubmit: async (values) => {
      console.log("Form submitted:", values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.id = "form-submitted";
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
    onError: (error) => {
      console.error("Form error:", error);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Document Title/i),
      "Medical Report",
    );

    await userEvent.type(
      canvas.getByLabelText(/Description/i),
      "Annual medical examination report with detailed findings and recommendations.",
    );

    // Create a mock file
    const file = new File(["dummy content"], "report.pdf", {
      type: "application/pdf",
    });
    const fileInput = canvas.getByLabelText(/Upload Document/i);

    // Mock the file selection
    await userEvent.upload(fileInput, file);

    // Submit the form
    await userEvent.click(
      canvas.getByRole("button", { name: /Upload Document/i }),
    );

    // Wait for the form submission to complete
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Verify the form was submitted
    const submitMessage = document.getElementById("form-submitted");
    expect(submitMessage).not.toBeNull();
  },
};

export const WithPrefilledData: Story = {
  args: {
    bucketName: "documents",
    folderPath: "medical-records",
    defaultValues: {
      title: "Patient Medical History",
      description:
        "Complete medical history including previous treatments and medications.",
    },
    onSubmit: async (values) => {
      console.log("Form submitted:", values);
    },
  },
};

export const WithCustomSubmitButton: Story = {
  args: {
    bucketName: "documents",
    folderPath: "contracts",
    onSubmit: async (values) => {
      console.log("Form submitted:", values);
    },
  },
  render: (args) => (
    <DocumentUploadForm {...args}>
      <div className="flex justify-end space-x-4">
        <button
          type="button"
          className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary/90"
        >
          Save Document
        </button>
      </div>
    </DocumentUploadForm>
  ),
};
