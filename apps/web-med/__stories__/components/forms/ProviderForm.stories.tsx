import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import ProviderForm from "@/components/forms/ProviderForm";

const meta = {
  title: "Components/Forms/Provider",
  component: ProviderForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ProviderForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out provider name", async () => {
      const nameInput = canvas.getByPlaceholderText("Enter name");
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, "Dr. <PERSON>");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Save" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Custom Submit
        </button>
      </div>
    ),
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // The name field should be empty by default
    await step("Verify empty name field", async () => {
      const nameInput = canvas.getByPlaceholderText("Enter name");
      expect(nameInput).toHaveValue("");
    });

    // Fill out the form
    await step("Fill out provider name", async () => {
      const nameInput = canvas.getByPlaceholderText("Enter name");
      await userEvent.type(nameInput, "Dr. John Doe");
    });

    // Submit the form with custom button
    await step("Submit the form with custom button", async () => {
      const submitButton = canvas.getByRole("button", {
        name: "Custom Submit",
      });
      await userEvent.click(submitButton);
    });
  },
};
