import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { OrganizationClass, OrganizationType } from "@/api";
import OrganizationInformation from "@/components/forms/OrganizationInformationForm";

const meta = {
  title: "Components/Forms/OrganizationInformation",
  component: OrganizationInformation,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationInformation>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out organization name", async () => {
      const nameInput = canvas.getByPlaceholderText(
        "Enter the name of the organization",
      );
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, "Test Medical Center");
    });

    await step("Fill out email address", async () => {
      const emailInput = canvas.getByPlaceholderText("Enter the email address");
      await userEvent.clear(emailInput);
      await userEvent.type(emailInput, "<EMAIL>");
    });

    await step("Fill out phone number", async () => {
      const phoneInput = canvas.getByPlaceholderText("Enter the phone number");
      await userEvent.clear(phoneInput);
      await userEvent.type(phoneInput, "**********");
    });

    await step("Select organization type", async () => {
      let typeSelect: HTMLElement;
      try {
        typeSelect = canvas.getByText("Select the organization type");
      } catch (e) {
        console.log("Type select not found");
        typeSelect = canvas.getByText("Type");
      }
      await userEvent.click(typeSelect);
      const internalOption = canvas.getByText("Internal");
      await userEvent.click(internalOption);
    });

    await step("Select organization class", async () => {
      const classSelect = canvas.getByText("Select the organization class");
      await userEvent.click(classSelect);
      const nonprofitOption = canvas.getByText("Non-Profit");
      await userEvent.click(nonprofitOption);
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save Changes" });
      await userEvent.click(submitButton);
    });
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      name: "General Hospital",
      email: "<EMAIL>",
      phone: "5551234567",
      type: OrganizationType.CLIENT,
      class: OrganizationClass.NONPROFIT,
      address: {
        street: "123 Main St",
        city: "Boston",
        state: "MA",
        postal: "02108",
        country: "USA",
        formatted: "123 Main St, Boston, MA 02108, USA",
        latitude: 42.3601,
        longitude: -71.0589,
      },
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};

export const GovernmentOrganization: Story = {
  args: {
    defaultValues: {
      name: "County Health Department",
      email: "<EMAIL>",
      phone: "**********",
      type: OrganizationType.ACCOUNT,
      class: OrganizationClass.GOVERNMENT,
      address: {
        street: "456 Government Ave",
        city: "Washington",
        state: "DC",
        postal: "20001",
        country: "USA",
        formatted: "456 Government Ave, Washington, DC 20001, USA",
        latitude: 38.9072,
        longitude: -77.0369,
      },
    },
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
};
