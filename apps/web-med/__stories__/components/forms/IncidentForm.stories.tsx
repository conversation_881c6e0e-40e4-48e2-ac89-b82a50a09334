import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import { IncidentSeverity, IncidentStatus, IncidentType } from "@/api";
import IncidentForm from "@/components/forms/IncidentForm";

const meta: Meta<typeof IncidentForm> = {
  title: "Components/Forms/Incident",
  component: IncidentForm,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof IncidentForm>;

export const Default: Story = {
  args: {
    defaultValues: {
      title: "",
      description: "",
      type: IncidentType.SAFETY,
      severity: IncidentSeverity.MINOR,
      status: IncidentStatus.OPEN,
    },
    onSubmit: async (values) => {
      console.log(values);
      // This is for the play function to work
      const submitMessage = document.createElement("div");
      submitMessage.textContent = "Form submitted";
      submitMessage.style.display = "none";
      document.body.appendChild(submitMessage);
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await userEvent.type(
      canvas.getByLabelText(/Title/i),
      "Patient Fall in Room 302",
    );

    await userEvent.type(
      canvas.getByLabelText(/Description/i),
      "Patient fell while attempting to get out of bed without assistance. Minor bruising on left arm.",
    );

    // Select incident type
    const typeCombobox = canvas.getAllByRole("combobox")[0];
    if (typeCombobox) {
      await userEvent.click(typeCombobox);
      const healthOption = canvas.getByRole("option", { name: "Health" });
      if (healthOption) {
        await userEvent.click(healthOption);
      }
    }

    // Select severity
    const severityCombobox = canvas.getAllByRole("combobox")[1];
    if (severityCombobox) {
      await userEvent.click(severityCombobox);
      const majorOption = canvas.getByRole("option", { name: "Major" });
      if (majorOption) {
        await userEvent.click(majorOption);
      }
    }

    // Submit the form
    await userEvent.click(canvas.getByRole("button", { name: /Submit/i }));

    // Assert that the form was submitted
    await expect(document.body.textContent).toContain("Form submitted");
  },
};

export const WithDefaultValues: Story = {
  args: {
    defaultValues: {
      title: "Medication Error in ICU",
      description:
        "Wrong medication administered to patient in ICU. Patient is stable and being monitored.",
      type: IncidentType.HEALTH,
      severity: IncidentSeverity.CRITICAL,
      status: IncidentStatus.IN_PROGRESS,
      organizationId: "org-12345",
      providerId: "provider-67890",
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};

export const SafetyIncident: Story = {
  args: {
    defaultValues: {
      title: "Hazardous Material Spill",
      description:
        "Chemical spill in laboratory area. Area has been cordoned off and cleanup team notified.",
      type: IncidentType.SAFETY,
      severity: IncidentSeverity.MAJOR,
      status: IncidentStatus.OPEN,
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  },
};
