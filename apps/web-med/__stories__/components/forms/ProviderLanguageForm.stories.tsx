import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import ProviderLanguageForm from "@/components/forms/ProviderLanguageForm";

const meta = {
  title: "Components/Forms/ProviderLanguage",
  component: ProviderLanguageForm,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ProviderLanguageForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out language field", async () => {
      const languageInput = canvas.getByPlaceholderText("Enter language name");
      await userEvent.clear(languageInput);
      await userEvent.type(languageInput, "Spanish");
    });

    // Submit the form
    await step("Submit the form", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });

    // Assert form submission was successful
    await step("Verify form submission", async () => {
      // In a real scenario, you might check for a success message
      // Here we're just checking the button is still present after submission
      const submitButton = canvas.getByRole("button", { name: "Save" });
      expect(submitButton).toBeInTheDocument();
    });
  },
};

export const WithCustomActions: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
    children: (
      <div className="flex w-full justify-between">
        <button type="button" className="text-gray-500">
          Cancel
        </button>
        <button
          type="submit"
          className="rounded bg-blue-500 px-4 py-2 text-white"
        >
          Add Language
        </button>
      </div>
    ),
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Fill out the form
    await step("Fill out language field", async () => {
      const languageInput = canvas.getByPlaceholderText("Enter language name");
      await userEvent.clear(languageInput);
      await userEvent.type(languageInput, "French");
    });

    // Submit the form with custom button
    await step("Submit the form with custom button", async () => {
      const submitButton = canvas.getByRole("button", { name: "Add Language" });
      await userEvent.click(submitButton);
    });
  },
};

export const MultipleLanguages: Story = {
  args: {
    onSubmit: async (values) => {
      console.log("Form submitted with values:", values);
      return new Promise((resolve) => setTimeout(resolve, 500));
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // First language submission
    await step("Fill out first language", async () => {
      const languageInput = canvas.getByPlaceholderText("Enter language name");
      await userEvent.clear(languageInput);
      await userEvent.type(languageInput, "German");
    });

    await step("Submit first language", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });

    // Second language submission (simulating form reset)
    await step("Fill out second language", async () => {
      const languageInput = canvas.getByPlaceholderText("Enter language name");
      await userEvent.clear(languageInput);
      await userEvent.type(languageInput, "Mandarin");
    });

    await step("Submit second language", async () => {
      const submitButton = canvas.getByRole("button", { name: "Save" });
      await userEvent.click(submitButton);
    });
  },
};
