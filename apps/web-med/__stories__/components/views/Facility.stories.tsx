import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { FacilityType } from "@/api";

import Facility from "@/components/views/Facility";

const meta: Meta<typeof Facility> = {
  title: "Components/Views/Facility",
  component: Facility,
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    type: {
      control: {
        type: "select",
        options: ["CAMPUS", "HOSPITAL", "CLINIC", "OFFICE", "OTHER"],
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Facility>;

const organization = {
  name: "Northwell Health",
  id: "northwell",
  avatar: "/placeholder.svg",
};

const sampleDepartments = [
  { name: "Cardiology", contact: "Dr. Heart", building: "Building A" },
  { name: "Neurology", contact: "Dr. Brain", building: "Building B" },
  { name: "Orthopedics", contact: "Dr. Bone", building: "Building C" },
];

export const Campus: Story = {
  args: {
    type: "CAMPUS" as FacilityType,
    departments: sampleDepartments,
    organization,
  },
};

export const Hospital: Story = {
  args: {
    type: "HOSPITAL" as FacilityType,
    departments: sampleDepartments,
    organization,
  },
};

export const Clinic: Story = {
  args: {
    type: "CLINIC" as FacilityType,
    departments: [
      { name: "Cardiology", contact: "Dr. Heart", building: "Building A" },
    ],
    organization,
  },
};

export const Office: Story = {
  args: {
    type: "OFFICE" as FacilityType,
    departments: sampleDepartments,
    organization,
  },
};

export const Other: Story = {
  args: {
    type: "OTHER" as FacilityType,
    departments: [{ name: "Mobile Unit", contact: "Dr. Mobile" }],
    organization,
  },
};
