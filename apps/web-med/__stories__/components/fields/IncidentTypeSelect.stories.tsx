import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import React from "react";
import { useArgs } from "storybook/preview-api";
import { expect, userEvent, within } from "storybook/test";

import IncidentTypeSelect, {
  INCIDENT_TYPES,
} from "../../../src/components/forms/fields/IncidentType";

const meta: Meta<typeof IncidentTypeSelect> = {
  title: "Components/Fields/IncidentTypeSelect",
  component: IncidentTypeSelect,
  argTypes: {
    value: {
      control: { type: "select" },
      options: INCIDENT_TYPES,
    },
    onChange: { action: "changed" },
  },
};
export default meta;

type Story = StoryObj<typeof IncidentTypeSelect>;

export const Default: Story = {
  args: {
    value: INCIDENT_TYPES[0],
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    return (
      <div style={{ maxWidth: 320 }}>
        <IncidentTypeSelect
          {...args}
          value={value}
          onChange={(newValue) => updateArgs({ value: newValue })}
        />
        <div style={{ marginTop: 16, fontSize: 12, color: "#666" }}>
          Current value: <b>{value}</b>
        </div>
      </div>
    );
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const trigger = await canvas.findByRole("combobox");
    await userEvent.click(trigger);
    // renders outside of canvas
    const listbox = await within(document.body).findByRole("listbox");
    const options = await within(listbox).findAllByRole("option");
    if (options.length > 1) {
      // select second option
      const secondOption = options[1]!;
      await userEvent.click(secondOption);
      await expect(
        canvas.getByText(secondOption.textContent ?? ""),
      ).toBeInTheDocument();
      await expect(canvas.getByText(/Current value:/)).toHaveTextContent(
        secondOption.textContent?.toUpperCase() ?? "",
      );
    }
  },
};
