import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import React from "react";
import { useArgs } from "storybook/preview-api";
import { expect, userEvent, within } from "storybook/test";

import IncidentSeveritySelect, {
  INCIDENT_SEVERITIES,
} from "../../../src/components/forms/fields/IncidentSeverity";

const meta: Meta<typeof IncidentSeveritySelect> = {
  title: "Components/Fields/IncidentSeveritySelect",
  component: IncidentSeveritySelect,
  argTypes: {
    value: {
      control: { type: "select" },
      options: INCIDENT_SEVERITIES,
    },
    onChange: { action: "changed" },
  },
};
export default meta;

type Story = StoryObj<typeof IncidentSeveritySelect>;

export const Default: Story = {
  args: {
    value: INCIDENT_SEVERITIES[0],
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    return (
      <div style={{ maxWidth: 320 }}>
        <IncidentSeveritySelect
          {...args}
          value={value}
          onChange={(newValue) => updateArgs({ value: newValue })}
        />
        <div style={{ marginTop: 16, fontSize: 12, color: "#666" }}>
          Current value: <b>{value}</b>
        </div>
      </div>
    );
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const trigger = await canvas.findByRole("combobox");
    await userEvent.click(trigger);
    // renders outside of canvas
    const listbox = await within(document.body).findByRole("listbox");
    const options = await within(listbox).findAllByRole("option");
    if (options.length > 1) {
      // select second option
      const secondOption = options[1]!;
      await userEvent.click(secondOption);
      await expect(
        canvas.getByText(secondOption.textContent ?? ""),
      ).toBeInTheDocument();
      await expect(canvas.getByText(/Current value:/)).toHaveTextContent(
        secondOption.textContent?.toUpperCase() ?? "",
      );
    }
  },
};
