import type { <PERSON>a, StoryObj } from "@storybook/react";

import React from "react";
import { useArgs } from "storybook/preview-api";
import { expect, userEvent, within } from "storybook/test";

import ContractStatusControl, {
  CONTRACT_STATUSES,
} from "@/components/forms/fields/ContractStatus";

const meta: Meta<typeof ContractStatusControl> = {
  parameters: {
    layout: "centered",
  },
  title: "Components/Fields/ContractStatusControl",
  component: ContractStatusControl,
  argTypes: {
    value: {
      control: "select",
      options: CONTRACT_STATUSES,
    },
  },
};
export default meta;

type Story = StoryObj<typeof ContractStatusControl>;

export const Default: Story = {
  args: {
    value: CONTRACT_STATUSES[0],
    placeholder: "Select contract status",
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    return (
      <div style={{ maxWidth: 320 }}>
        <ContractStatusControl
          {...args}
          value={value}
          onChange={(newValue) => updateArgs({ value: newValue })}
        />
        <div style={{ marginTop: 16, fontSize: 12, color: "#666" }}>
          Current value: <b>{value}</b>
        </div>
      </div>
    );
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const trigger = await canvas.findByRole("combobox");
    await userEvent.click(trigger);

    // renders outside of canvas
    const listbox = await within(document.body).findByRole("listbox");
    const options = await within(listbox).findAllByRole("option");
    if (options.length > 1) {
      // select second option
      const secondOption = options[1]!;
      await userEvent.click(secondOption);
      await expect(
        canvas.getByText(secondOption.textContent ?? ""),
      ).toBeInTheDocument();
      // enum values are usually all capitalized
      await expect(canvas.getByText(/Current value:/)).toHaveTextContent(
        secondOption.textContent?.toUpperCase() ?? "",
      );
    }
  },
};
