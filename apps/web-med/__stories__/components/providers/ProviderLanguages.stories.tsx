import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import ProviderLanguages from "@/components/providers/ProviderLanguages";

const meta: Meta<typeof ProviderLanguages> = {
  title: "Components/Providers/ProviderLanguages",
  component: ProviderLanguages,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderLanguages>;

const sampleLanguages = [
  "English",
  "Spanish",
  "French",
  "German",
  "Mandarin",
  "Arabic",
];

export const Default: Story = {
  args: {
    languages: sampleLanguages.slice(0, 3),
    onLanguageAdd: async (language: string) => {
      console.log("Adding language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onLanguageRemove: async (language: string) => {
      console.log("Removing language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ languages }, updateArgs] = useArgs();

    const handleLanguageAdd = async (language: string) => {
      await args.onLanguageAdd?.(language);
      updateArgs({ languages: [...(languages ?? []), language] });
    };

    const handleLanguageRemove = async (language: string) => {
      await args.onLanguageRemove?.(language);
      updateArgs({
        languages: languages?.filter((lang) => lang !== language),
      });
    };

    return (
      <ProviderLanguages
        languages={languages ?? []}
        onLanguageAdd={handleLanguageAdd}
        onLanguageRemove={handleLanguageRemove}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    languages: [],
    onLanguageAdd: async (language: string) => {
      console.log("Adding language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const SingleLanguage: Story = {
  args: {
    languages: [sampleLanguages[0]],
    onLanguageAdd: async (language: string) => {
      console.log("Adding language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onLanguageRemove: async (language: string) => {
      console.log("Removing language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const ManyLanguages: Story = {
  args: {
    languages: sampleLanguages,
  },
};

export const Interaction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test adding language
    await userEvent.click(canvas.getByText(/add language/i));

    // Test deleting language
    const deleteButtons = await canvas.findAllByRole("button", {
      name: /delete language/i,
    });
    if (deleteButtons[0]) {
      await userEvent.click(deleteButtons[0]);
    }
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    languages: [],
  },
};

export const LoadingWithData: Story = {
  args: {
    loading: true,
    languages: sampleLanguages.slice(0, 3),
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load language data. Please try again later.",
    languages: [],
  },
};

export const ErrorWithData: Story = {
  args: {
    error: "Failed to update language data. Please try again later.",
    languages: sampleLanguages.slice(0, 3),
  },
};
