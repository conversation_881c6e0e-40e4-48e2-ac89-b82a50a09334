import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import ProviderAbstract from "@/components/providers/ProviderAbstract";

const meta: Meta<typeof ProviderAbstract> = {
  title: "Components/Providers/ProviderAbstract",
  component: ProviderAbstract,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderAbstract>;

const mockPerson = {
  firstName: "Jane",
  lastName: "<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "/placeholder.svg",
};

export const Default: Story = {
  args: {
    person: mockPerson,
    title: "Emergency Room Physician",
    onTitleChange: async (title) => {
      console.log("Title changed:", title);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ title }, updateArgs] = useArgs();

    const handleTitleChange = async (newTitle: string) => {
      await args.onTitleChange?.(newTitle);
      updateArgs({ title: newTitle });
    };

    return (
      <ProviderAbstract
        {...args}
        title={title}
        onTitleChange={handleTitleChange}
      />
    );
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithoutAvatar: Story = {
  args: {
    person: {
      ...mockPerson,
      avatar: null,
    },
    title: "Registered Nurse",
  },
};

export const Empty: Story = {
  args: {
    person: {
      firstName: "New",
      lastName: "Provider",
      email: "<EMAIL>",
      phone: "",
    },
  },
};

export const LongTitle: Story = {
  args: {
    person: mockPerson,
    title: "Senior Emergency Medicine Specialist & Critical Care Unit Director",
  },
};
