import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import ProviderSpecialties from "@/components/providers/ProviderSpecialties";

const meta: Meta<typeof ProviderSpecialties> = {
  title: "Components/Providers/ProviderSpecialties",
  component: ProviderSpecialties,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderSpecialties>;

const sampleSpecialties = [
  {
    id: "1",
    name: "Emergency Medicine",
    description: "Acute care and emergency medical treatment",
  },
  {
    id: "2",
    name: "Critical Care",
    description: "Intensive care and critical patient management",
  },
  {
    id: "3",
    name: "Pediatrics",
    description: "Medical care for infants, children, and adolescents",
  },
  {
    id: "4",
    name: "Family Medicine",
    description: "Comprehensive healthcare for individuals and families",
  },
];

export const Default: Story = {
  args: {
    specialties: sampleSpecialties.slice(0, 2),
    onSpecialtyAdd: async (specialty) => {
      console.log("Adding specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onSpecialtyRemove: async (specialty) => {
      console.log("Removing specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ specialties }, updateArgs] = useArgs();

    const handleSpecialtyAdd = async (values: {
      name: string;
      description?: string;
    }) => {
      await args.onSpecialtyAdd?.(values);
      updateArgs({
        specialties: [
          ...(specialties ?? []),
          { id: crypto.randomUUID(), ...values },
        ],
      });
    };

    const handleSpecialtyRemove = async (specialty: { id: string }) => {
      await args.onSpecialtyRemove?.(specialty);
      updateArgs({
        specialties: specialties?.filter((spec) => spec.id !== specialty.id),
      });
    };

    return (
      <ProviderSpecialties
        specialties={specialties ?? []}
        onSpecialtyAdd={handleSpecialtyAdd}
        onSpecialtyRemove={handleSpecialtyRemove}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    specialties: [],
    onSpecialtyAdd: async (specialty) => {
      console.log("Adding specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const SingleSpecialty: Story = {
  args: {
    specialties: [sampleSpecialties[0]],
    onSpecialtyAdd: async (specialty) => {
      console.log("Adding specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onSpecialtyRemove: async (specialty) => {
      console.log("Removing specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const ManySpecialties: Story = {
  args: {
    specialties: sampleSpecialties,
  },
};

export const Interaction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test adding specialty
    await userEvent.click(canvas.getByText(/add specialty/i));

    // Test deleting specialty
    const deleteButtons = await canvas.findAllByRole("button", {
      name: /delete specialty/i,
    });
    if (deleteButtons[0]) {
      await userEvent.click(deleteButtons[0]);
    }
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    specialties: [],
  },
};

export const LoadingWithData: Story = {
  args: {
    loading: true,
    specialties: sampleSpecialties.slice(0, 3),
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load specialty data. Please try again later.",
    specialties: [],
  },
};

export const ErrorWithData: Story = {
  args: {
    error: "Failed to update specialty data. Please try again later.",
    specialties: sampleSpecialties.slice(0, 3),
  },
};
