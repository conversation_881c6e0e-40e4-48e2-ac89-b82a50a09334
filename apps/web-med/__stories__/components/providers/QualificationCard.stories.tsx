import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { QualificationStatus, QualificationType } from "@/api";
import { QualificationCard } from "@/components/providers/QualificationCard";

const meta: Meta<typeof QualificationCard> = {
  title: "Components/Providers/QualificationCard",
  component: QualificationCard,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="max-w-md p-6">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof QualificationCard>;

// Base qualification with common properties
const baseQualification = {
  name: "Sample Qualification",
  institution: "Sample Authority",
  startDate: new Date("2022-01-01"),
  status: QualificationStatus.PENDING,
  identifier: "ID-123456",
};

// Certificate Examples
export const CertificatePending: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.CERTIFICATE,
      name: "Advanced Cardiac Life Support",
      identifier: "ACLS-123456",
      status: QualificationStatus.PENDING,
    },
  },
};

export const CertificateApproved: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.CERTIFICATE,
      name: "Basic Life Support",
      identifier: "BLS-789012",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2023-03-15"),
      endDate: new Date("2025-03-15"),
    },
  },
};

export const CertificateExpired: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.CERTIFICATE,
      name: "Advanced Trauma Life Support",
      identifier: "ATLS-456789",
      status: QualificationStatus.EXPIRED,
      startDate: new Date("2020-05-10"),
      endDate: new Date("2022-05-10"),
    },
  },
};

// Degree Examples
export const DegreePending: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.DEGREE,
      name: "Doctor of Medicine",
      institution: "University of Medical Sciences",
      status: QualificationStatus.PENDING,
      startDate: new Date("2015-09-01"),
      endDate: new Date("2019-06-15"),
    },
  },
};

export const DegreeApproved: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.DEGREE,
      name: "Bachelor of Science in Nursing",
      institution: "State University School of Nursing",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2016-09-01"),
      endDate: new Date("2020-05-15"),
    },
  },
};

export const DegreeRejected: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.DEGREE,
      name: "Master of Public Health",
      institution: "Health Sciences University",
      status: QualificationStatus.REJECTED,
      startDate: new Date("2018-09-01"),
      endDate: new Date("2020-05-15"),
    },
  },
};

// License Examples
export const LicensePending: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.LICENSE,
      name: "Medical License",
      institution: "State Medical Board",
      status: QualificationStatus.PENDING,
      startDate: new Date("2022-01-15"),
      endDate: new Date("2024-01-15"),
      state: "California",
      country: "United States",
      identifier: "MD-123456-CA",
    },
  },
};

export const LicenseApproved: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.LICENSE,
      name: "Registered Nurse License",
      institution: "State Board of Nursing",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2021-06-01"),
      endDate: new Date("2023-06-01"),
      state: "New York",
      country: "United States",
      identifier: "RN-789012-NY",
    },
  },
};

export const LicenseExpired: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.LICENSE,
      name: "Pharmacy License",
      institution: "State Board of Pharmacy",
      status: QualificationStatus.EXPIRED,
      startDate: new Date("2020-03-01"),
      endDate: new Date("2022-03-01"),
      state: "Texas",
      country: "United States",
      identifier: "RPh-345678-TX",
    },
  },
};

export const LicenseRejected: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.LICENSE,
      name: "Physician Assistant License",
      institution: "State Medical Board",
      status: QualificationStatus.REJECTED,
      startDate: new Date("2022-02-15"),
      endDate: new Date("2024-02-15"),
      state: "Florida",
      country: "United States",
      identifier: "PA-901234-FL",
    },
  },
};

// Other Examples
export const OtherQualification: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.OTHER,
      name: "Professional Membership",
      institution: "American Medical Association",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2021-01-01"),
      endDate: new Date("2023-01-01"),
      identifier: "AMA-567890",
    },
  },
};

export const LongContent: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.DEGREE,
      name: "Bachelor of Science in Nursing with a Minor in Psychology and Healthcare Management",
      institution:
        "The University of Healthcare Sciences and Advanced Medical Studies, School of Nursing and Allied Health Professions",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2015-09-01"),
      endDate: new Date("2019-05-15"),
      identifier: "BSN-PSYCH-HCM-123456",
    },
  },
};

// Dark Mode Example (for documentation purposes)
export const DarkModeExample: Story = {
  args: {
    qualification: {
      ...baseQualification,
      type: QualificationType.LICENSE,
      name: "Medical License",
      institution: "State Medical Board",
      status: QualificationStatus.APPROVED,
      startDate: new Date("2022-01-15"),
      endDate: new Date("2024-01-15"),
      state: "California",
      country: "United States",
      identifier: "MD-123456-CA",
    },
  },
  parameters: {
    backgrounds: { default: "dark" },
  },
};
