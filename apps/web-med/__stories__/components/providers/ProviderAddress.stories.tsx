import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import ProviderAddress from "@/components/providers/ProviderAddress";

const meta: Meta<typeof ProviderAddress> = {
  title: "Components/Providers/ProviderAddress",
  component: ProviderAddress,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderAddress>;

const sampleAddress = {
  formatted: "123 Medical Center Drive, San Francisco, CA 94143",
  street: "123 Medical Center Drive",
  city: "San Francisco",
  state: "CA",
  postal: "94143",
  country: "USA",
  latitude: 37.7629,
  longitude: -122.4579,
};

export const Default: Story = {
  args: {
    address: sampleAddress,
    onAddressChange: async (address) => {
      console.log("Address changed:", address);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ address }, updateArgs] = useArgs();

    const handleAddressChange = async (newAddress: typeof sampleAddress) => {
      await args.onAddressChange?.(newAddress);
      updateArgs({ address: newAddress });
    };

    return (
      <ProviderAddress
        address={address}
        onAddressChange={handleAddressChange}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    onAddressChange: async (address) => {
      console.log("Address changed:", address);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const LoadingWithAddress: Story = {
  args: {
    loading: true,
    address: sampleAddress,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load address data. Please try again later.",
  },
};

export const ErrorWithAddress: Story = {
  args: {
    error: "Failed to update address data. Please try again later.",
    address: sampleAddress,
  },
};

export const DifferentLocations: Story = {
  args: {
    ...Default.args,
    address: {
      formatted: "456 Hospital Avenue, New York, NY 10001",
      street: "456 Hospital Avenue",
      city: "New York",
      state: "NY",
      postal: "10001",
      country: "USA",
      latitude: 40.7505,
      longitude: -73.9934,
    },
  },
};

export const InternationalAddress: Story = {
  args: {
    ...Default.args,
    address: {
      formatted: "10 Harley Street, London W1G 9PF, United Kingdom",
      street: "10 Harley Street",
      city: "London",
      state: "",
      postal: "W1G 9PF",
      country: "United Kingdom",
      latitude: 51.5177,
      longitude: -0.1464,
    },
  },
};

export const Interaction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test opening address dialog
    const editButton = canvas.getByRole("button", {
      name: /edit address/i,
    });
    if (editButton) {
      await userEvent.click(editButton);
    }

    // Wait for dialog to open and type in search
    const searchInput = await canvas.findByPlaceholderText(/type an address/i);
    await userEvent.type(searchInput, "123 Medical");

    // Wait for results and select first one
    const firstResult = await canvas.findByRole("option");
    if (firstResult) {
      await userEvent.click(firstResult);
    }
  },
};
