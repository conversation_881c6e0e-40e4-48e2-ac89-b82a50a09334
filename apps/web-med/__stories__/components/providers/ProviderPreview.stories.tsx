import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { But<PERSON> } from "@axa/ui/primitives/button";

import ProviderPreview from "@/components/providers/ProviderPreview";

const meta: Meta<typeof ProviderPreview> = {
  title: "Components/Providers/ProviderPreview",
  component: ProviderPreview,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderPreview>;

const mockProvider = {
  person: {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    avatar: "/placeholder.svg",
  },
  title: "Emergency Room Physician",
  yearsOfExperience: 8,
  spokenLanguages: ["English", "Spanish", "French"],
  specialties: [
    { name: "Emergency Medicine" },
    { name: "Critical Care" },
    { name: "Trauma" },
  ],
  qualifications: [
    {
      type: "DEGREE",
      title: "Doctor of Medicine",
    },
    {
      type: "LICENSE",
      title: "Medical License",
    },
    {
      type: "CERTIFICATE",
      title: "Advanced Cardiac Life Support",
    },
  ],
};

export const Default: Story = {
  args: {
    provider: mockProvider,
  },
};

export const WithoutAvatar: Story = {
  args: {
    provider: {
      ...mockProvider,
      person: {
        ...mockProvider.person,
        avatar: undefined,
      },
    },
  },
};

export const MinimalInfo: Story = {
  args: {
    provider: {
      person: mockProvider.person,
      title: mockProvider.title,
    },
  },
};

export const WithoutQualifications: Story = {
  args: {
    provider: {
      ...mockProvider,
      qualifications: undefined,
    },
  },
};

export const WithoutSpecialties: Story = {
  args: {
    provider: {
      ...mockProvider,
      specialties: undefined,
    },
  },
};

export const WithoutLanguages: Story = {
  args: {
    provider: {
      ...mockProvider,
      spokenLanguages: undefined,
    },
  },
};

export const WithActions: Story = {
  args: {
    provider: mockProvider,
  },
  render: (args) => (
    <ProviderPreview {...args}>
      <div className="flex w-full items-center justify-end gap-2">
        <Button variant="outline">View Profile</Button>
        <Button>Contact Provider</Button>
      </div>
    </ProviderPreview>
  ),
};

export const WithCustomContent: Story = {
  args: {
    provider: mockProvider,
  },
  render: (args) => (
    <ProviderPreview {...args}>
      <div className="flex w-full flex-col gap-2">
        <h4 className="font-medium">Additional Information</h4>
        <p className="text-sm text-muted-foreground">
          Available for emergency on-call shifts and weekend rotations.
        </p>
      </div>
    </ProviderPreview>
  ),
};
