import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import ProviderPreferences, {
  ProviderPreferencesWhy,
} from "@/components/providers/ProviderPreferences";

const meta: Meta<typeof ProviderPreferences> = {
  title: "Components/Providers/ProviderPreferences",
  component: ProviderPreferences,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderPreferences>;

export const Default: Story = {
  args: {
    preferences: {
      openToWork: true,
      openToOnCall: false,
    },
    onPreferencesChange: async (preferences) => {
      console.log("Preferences changed:", preferences);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ preferences }, updateArgs] = useArgs();

    const handlePreferencesChange = async (newPreferences: {
      openToWork?: boolean;
      openToOnCall?: boolean;
    }) => {
      await args.onPreferencesChange?.(newPreferences);
      updateArgs({
        preferences: {
          ...preferences,
          ...newPreferences,
        },
      });
    };

    return (
      <ProviderPreferences
        {...args}
        preferences={preferences}
        onPreferencesChange={handlePreferencesChange}
      />
    );
  },
};

export const WithWhySection: Story = {
  args: {
    preferences: {
      openToWork: true,
      openToOnCall: false,
    },
    onPreferencesChange: async (preferences) => {
      console.log("Preferences changed:", preferences);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ preferences }, updateArgs] = useArgs();

    const handlePreferencesChange = async (newPreferences: {
      openToWork?: boolean;
      openToOnCall?: boolean;
    }) => {
      await args.onPreferencesChange?.(newPreferences);
      updateArgs({
        preferences: {
          ...preferences,
          ...newPreferences,
        },
      });
    };

    return (
      <ProviderPreferences
        {...args}
        preferences={preferences}
        onPreferencesChange={handlePreferencesChange}
      >
        <ProviderPreferencesWhy />
      </ProviderPreferences>
    );
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load preferences. Please try again later.",
  },
};

export const BothEnabled: Story = {
  args: {
    preferences: {
      openToWork: true,
      openToOnCall: true,
    },
  },
};

export const BothDisabled: Story = {
  args: {
    preferences: {
      openToWork: false,
      openToOnCall: false,
    },
  },
};
