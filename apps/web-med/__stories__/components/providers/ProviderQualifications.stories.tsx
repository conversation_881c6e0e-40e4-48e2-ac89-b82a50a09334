import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import type { Qualification } from "@/components/providers/qualifications/ProviderQualifications";

import ProviderQualifications from "@/components/providers/qualifications/ProviderQualifications";

const meta: Meta<typeof ProviderQualifications> = {
  title: "Components/Providers/ProviderQualifications",
  component: ProviderQualifications,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderQualifications>;

const sampleQualifications: Qualification[] = [
  {
    id: "1",
    type: "DEGREE",
    status: "APPROVED",
    name: "Doctor of Medicine",
    issuingAuthority: "University of Medical Sciences",
    institution: "School of Medicine",
    issueDate: new Date("2015-05-15"),
  },
  {
    id: "2",
    type: "LICENSE",
    status: "APPROVED",
    name: "Medical License",
    issuingAuthority: "State Medical Board",
    issueState: "California",
    issueDate: new Date("2015-07-01"),
    expirationDate: new Date("2025-07-01"),
  },
  {
    id: "3",
    type: "CERTIFICATE",
    status: "PENDING",
    name: "Advanced Cardiac Life Support",
    issuingAuthority: "American Heart Association",
    issueDate: new Date("2023-01-15"),
    expirationDate: new Date("2025-01-15"),
  },
];

export const Default: Story = {
  args: {
    qualifications: sampleQualifications,
    onAddQualification: async (qualification: Qualification) => {
      console.log("Adding qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditQualification: async (qualification: Qualification) => {
      console.log("Editing qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteQualification: async (qualification: Qualification) => {
      console.log("Deleting qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ qualifications }, updateArgs] = useArgs();

    const handleAddQualification = async (newQualification: Qualification) => {
      await args.onAddQualification?.(newQualification);
      updateArgs({
        qualifications: [...(qualifications ?? []), newQualification],
      });
    };

    const handleEditQualification = async (
      updatedQualification: Qualification,
    ) => {
      await args.onEditQualification?.(updatedQualification);
      updateArgs({
        qualifications: qualifications?.map((qual) =>
          qual.id === updatedQualification.id ? updatedQualification : qual,
        ),
      });
    };

    const handleDeleteQualification = async (
      qualificationToDelete: Qualification,
    ) => {
      await args.onDeleteQualification?.(qualificationToDelete);
      updateArgs({
        qualifications: qualifications?.filter(
          (qual) => qual.id !== qualificationToDelete.id,
        ),
      });
    };

    return (
      <ProviderQualifications
        qualifications={qualifications ?? []}
        onAddQualification={handleAddQualification}
        onEditQualification={handleEditQualification}
        onDeleteQualification={handleDeleteQualification}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    qualifications: [],
    onAddQualification: async (qualification: Qualification) => {
      console.log("Adding qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const SingleEntry: Story = {
  args: {
    qualifications: sampleQualifications.slice(0, 1),
    onAddQualification: async (qualification: Qualification) => {
      console.log("Adding qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditQualification: async (qualification: Qualification) => {
      console.log("Editing qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteQualification: async (qualification: Qualification) => {
      console.log("Deleting qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const WithExpiredQualification: Story = {
  args: {
    qualifications: [
      ...sampleQualifications,
      {
        id: "4",
        type: "LICENSE",
        status: "EXPIRED",
        name: "Basic Life Support",
        issuingAuthority: "American Heart Association",
        issueDate: new Date("2020-01-01"),
        expirationDate: new Date("2022-01-01"),
      },
    ],
  },
};

export const AllStatuses: Story = {
  args: {
    qualifications: [
      {
        id: "1",
        type: "DEGREE",
        status: "APPROVED",
        name: "Doctor of Medicine",
        issuingAuthority: "University of Medical Sciences",
        institution: "School of Medicine",
        issueDate: new Date("2015-05-15"),
      },
      {
        id: "2",
        type: "LICENSE",
        status: "PENDING",
        name: "Medical License",
        issuingAuthority: "State Medical Board",
        issueState: "California",
        issueDate: new Date("2023-12-01"),
        expirationDate: new Date("2025-12-01"),
      },
      {
        id: "3",
        type: "CERTIFICATE",
        status: "REJECTED",
        name: "Advanced Cardiac Life Support",
        issuingAuthority: "American Heart Association",
        issueDate: new Date("2023-01-15"),
        expirationDate: new Date("2025-01-15"),
      },
      {
        id: "4",
        type: "LICENSE",
        status: "EXPIRED",
        name: "Basic Life Support",
        issuingAuthority: "American Heart Association",
        issueDate: new Date("2020-01-01"),
        expirationDate: new Date("2022-01-01"),
      },
    ],
  },
};

export const Interaction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test adding qualification
    await userEvent.click(canvas.getByText(/add qualification/i));

    // Test editing qualification (clicking edit button)
    const editButtons = await canvas.findAllByRole("button", {
      name: /edit qualification/i,
    });
    if (editButtons[0]) {
      await userEvent.click(editButtons[0]);
    }

    // Test deleting qualification (clicking delete button)
    const deleteButtons = await canvas.findAllByRole("button", {
      name: /delete qualification/i,
    });
    if (deleteButtons[0]) {
      await userEvent.click(deleteButtons[0]);
    }
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    qualifications: [],
  },
};

export const LoadingWithData: Story = {
  args: {
    loading: true,
    qualifications: sampleQualifications,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load qualification data. Please try again later.",
    qualifications: [],
  },
};

export const ErrorWithData: Story = {
  args: {
    error: "Failed to update qualification data. Please try again later.",
    qualifications: sampleQualifications,
  },
};
