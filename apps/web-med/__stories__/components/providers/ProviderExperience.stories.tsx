import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import type { Experience } from "@/components/providers/ProviderExperience";

import ProviderExperience from "@/components/providers/ProviderExperience";

const meta: Meta<typeof ProviderExperience> = {
  title: "Components/Providers/ProviderExperience",
  component: ProviderExperience,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderExperience>;

const sampleExperience: Experience[] = [
  {
    id: "1",
    role: "Senior Medical Officer",
    company: "City General Hospital",
    startDate: new Date("2020-01-01"),
    endDate: new Date("2023-12-31"),
    isCurrent: false,
  },
  {
    id: "2",
    role: "Medical Resident",
    company: "University Medical Center",
    startDate: new Date("2017-06-01"),
    endDate: new Date("2019-12-31"),
    isCurrent: false,
  },
  {
    id: "3",
    role: "Medical Intern",
    company: "Regional Health Center",
    startDate: new Date("2016-01-01"),
    endDate: new Date("2017-05-31"),
    isCurrent: false,
  },
];

export const Default: Story = {
  args: {
    experience: sampleExperience,
    onAddExperience: async (experience: Experience) => {
      console.log("Adding experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditExperience: async (experience: Experience) => {
      console.log("Editing experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteExperience: async (experience: Experience) => {
      console.log("Deleting experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ experience }, updateArgs] = useArgs();

    const handleAddExperience = async (newExperience: Experience) => {
      await args.onAddExperience?.(newExperience);
      updateArgs({ experience: [...(experience ?? []), newExperience] });
    };

    const handleEditExperience = async (updatedExperience: Experience) => {
      await args.onEditExperience?.(updatedExperience);
      updateArgs({
        experience: experience?.map((exp: Experience) =>
          exp.id === updatedExperience.id ? updatedExperience : exp,
        ),
      });
    };

    const handleDeleteExperience = async (experienceToDelete: Experience) => {
      await args.onDeleteExperience?.(experienceToDelete);
      updateArgs({
        experience: experience?.filter(
          (exp: Experience) => exp.id !== experienceToDelete.id,
        ),
      });
    };

    return (
      <ProviderExperience
        experience={experience ?? []}
        onAddExperience={handleAddExperience}
        onEditExperience={handleEditExperience}
        onDeleteExperience={handleDeleteExperience}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    experience: [],
    onAddExperience: async (experience: Experience) => {
      console.log("Adding experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const SingleEntry: Story = {
  args: {
    experience: [sampleExperience[0]!],
    onAddExperience: async (experience: Experience) => {
      console.log("Adding experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditExperience: async (experience: Experience) => {
      console.log("Editing experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteExperience: async (experience: Experience) => {
      console.log("Deleting experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const CurrentlyEmployed: Story = {
  args: {
    experience: [
      {
        id: "1",
        role: "Chief Medical Officer",
        company: "Metropolitan Hospital",
        startDate: new Date("2020-01-01"),
        isCurrent: true, // Showcasing current position
      },
      ...sampleExperience.slice(1),
    ],
  },
};

export const LongHistory: Story = {
  args: {
    experience: [
      {
        id: "1",
        role: "Chief Medical Officer",
        company: "Metropolitan Hospital",
        startDate: new Date("2020-01-01"),
        isCurrent: true,
      },
      ...sampleExperience,
      {
        id: "4",
        role: "Medical Student",
        company: "University Hospital",
        startDate: new Date("2012-09-01"),
        endDate: new Date("2015-12-31"),
        isCurrent: false,
      },
      {
        id: "5",
        role: "Healthcare Assistant",
        company: "Community Clinic",
        startDate: new Date("2010-06-01"),
        endDate: new Date("2012-08-31"),
        isCurrent: false,
      },
    ],
  },
};

export const Interaction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test adding experience
    await userEvent.click(canvas.getByText(/add experience/i));

    // Test editing experience (clicking edit button)
    const editButtons = await canvas.findAllByRole("button", {
      name: /edit experience/i,
    });
    if (editButtons[0]) {
      await userEvent.click(editButtons[0]);
    }

    // Test deleting experience (clicking delete button)
    const deleteButtons = await canvas.findAllByRole("button", {
      name: /delete experience/i,
    });
    if (deleteButtons[0]) {
      await userEvent.click(deleteButtons[0]);
    }
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    experience: [],
  },
};

export const LoadingWithData: Story = {
  args: {
    loading: true,
    experience: sampleExperience,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load experience data. Please try again later.",
    experience: [],
  },
};

export const ErrorWithData: Story = {
  args: {
    error: "Failed to update experience data. Please try again later.",
    experience: sampleExperience,
  },
};
