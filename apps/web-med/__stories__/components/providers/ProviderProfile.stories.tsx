import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ProviderProfile from "@/components/providers/ProviderProfile";

const meta: Meta<typeof ProviderProfile> = {
  title: "Components/Providers/ProviderProfile",
  component: ProviderProfile,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderProfile>;

const mockProvider = {
  person: {
    avatar: "/placeholder.svg",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
  },
  address: {
    formatted: "123 Medical Dr, Cityville, ST 12345",
    street: "123 Medical Dr",
    city: "Cityville",
    state: "NY",
    postal: "12345",
    country: "USA",
    latitude: 37.7749,
    longitude: -122.4194,
  },
  professionalTitle: "Emergency Room Physician",
  spokenLanguages: ["English", "Spanish"],
  specialties: [
    {
      id: "1",
      name: "Emergency Medicine",
      description: "Emergency Medicine",
    },
    {
      id: "2",
      name: "Critical Care",
      description: "Critical Care",
    },
  ],
  qualifications: [
    {
      id: "1",
      type: "DEGREE",
      status: "APPROVED",
      name: "Doctor of Medicine",
      issuingAuthority: "University of Medical Sciences",
      institution: "School of Medicine",
      issueDate: new Date("2015-05-15"),
    },
    {
      id: "2",
      type: "LICENSE",
      status: "EXPIRED",
      name: "Medical License",
      issuingAuthority: "State Medical Board",
      issueState: "California",
      issueDate: new Date("2015-07-01"),
      expirationDate: new Date("2025-07-01"),
    },
    {
      id: "3",
      type: "CERTIFICATE",
      status: "PENDING",
      name: "Advanced Cardiac Life Support",
      issuingAuthority: "American Heart Association",
      issueDate: new Date("2023-01-15"),
      expirationDate: new Date("2025-01-15"),
    },
  ],
  experience: [
    {
      id: "1",
      role: "Emergency Room Physician",
      company: "City General Hospital",
      startDate: new Date("2018-01-01"),
      isCurrent: true,
    },
    {
      id: "2",
      role: "Resident Physician",
      company: "University Hospital",
      startDate: new Date("2015-07-01"),
      endDate: new Date("2017-12-31"),
      isCurrent: false,
    },
  ],
};

export const Default: Story = {
  args: {
    provider: mockProvider,
    onAddQualification: async (qualification) => {
      console.log("Adding qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditQualification: async (qualification) => {
      console.log("Editing qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteQualification: async (qualification) => {
      console.log("Deleting qualification:", qualification);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onAddExperience: async (experience) => {
      console.log("Adding experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onEditExperience: async (experience) => {
      console.log("Editing experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDeleteExperience: async (experience) => {
      console.log("Deleting experience:", experience);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onLanguageAdd: async (language) => {
      console.log("Adding language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onLanguageRemove: async (language) => {
      console.log("Removing language:", language);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onSpecialtyAdd: async (specialty) => {
      console.log("Adding specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onSpecialtyRemove: async (specialty) => {
      console.log("Removing specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
};

export const WithoutProfilePicture: Story = {
  args: {
    provider: { ...mockProvider, profilePicture: undefined },
  },
};

export const Empty: Story = {
  args: {
    provider: {
      fullName: "New Provider",
      email: "<EMAIL>",
      phoneNumber: "+****************",
      professionalTitle: "Healthcare Professional",
    },
  },
};
