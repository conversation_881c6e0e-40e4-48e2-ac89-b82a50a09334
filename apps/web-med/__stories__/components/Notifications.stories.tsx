import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import Notifications from "@/components/widgets/Notifications";

const meta: Meta<typeof Notifications> = {
  title: "Components/Notifications",
  component: Notifications,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof Notifications>;

const sampleNotifications = [
  {
    id: "1",
    title: "New shift available",
    description:
      "A new shift is available for booking at City General Hospital.",
    timestamp: "5 minutes ago",
    read: false,
    type: "info" as const,
  },
  {
    id: "2",
    title: "Shift reminder",
    description: "Your shift at Children's Medical Center starts in 1 hour.",
    timestamp: "1 hour ago",
    read: false,
    type: "warning" as const,
  },
  {
    id: "3",
    title: "Payment processed",
    description: "Your payment of $1,500 has been processed.",
    timestamp: "2 hours ago",
    read: true,
    type: "success" as const,
  },
  {
    id: "4",
    title: "Document expiring",
    description:
      "Your medical license is expiring in 30 days. Please renew it.",
    timestamp: "1 day ago",
    read: false,
    type: "error" as const,
  },
];

export const Default: Story = {
  args: {
    initialNotifications: sampleNotifications,
  },
};

export const Empty: Story = {
  args: {
    initialNotifications: [],
  },
};
