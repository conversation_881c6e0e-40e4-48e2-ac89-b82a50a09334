import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OnboardingLayout from "@/components/layouts/OnboardingLayout";

const meta: Meta<typeof OnboardingLayout> = {
  title: "Components/Layouts/OnboardingLayout",
  component: OnboardingLayout,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof OnboardingLayout>;

export const Default: Story = {
  args: {
    children: (
      <div className="p-4">
        <h1 className="mb-4 text-2xl font-bold">Welcome to AXA Med</h1>
        <p>
          This is the onboarding layout. You can put your onboarding content
          here.
        </p>
      </div>
    ),
  },
};
