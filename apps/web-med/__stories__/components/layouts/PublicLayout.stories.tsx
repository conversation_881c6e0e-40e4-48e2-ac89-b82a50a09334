import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { But<PERSON> } from "@axa/ui/primitives/button";

import PublicLayout from "@/components/layouts/public/PublicLayout";
import { cn } from "@/ui/lib";

const meta: Meta<typeof PublicLayout> = {
  title: "Components/Layouts/PublicLayout",
  component: PublicLayout,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: "/",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof PublicLayout>;

const ExampleContent = () => (
  <div className="mx-auto max-w-screen-lg px-4 py-16">
    <div
      className={cn(
        "rounded-lg border-2 border-dashed border-muted p-8 text-center",
      )}
    >
      <h1 className="text-3xl font-semibold text-primary">Example Content</h1>
      <p className="mt-4 text-muted-foreground">
        This is an example of content that would be rendered inside the public
        layout.
      </p>
      <div className="mt-8 flex justify-center gap-4">
        <Button variant="primary">Primary Action</Button>
        <Button variant="outline">Secondary Action</Button>
      </div>
    </div>
  </div>
);

const HeroContent = () => (
  <div className="w-full">
    <div className="bg-gradient-to-r from-primary/10 to-primary/5 py-24">
      <div className="mx-auto max-w-screen-lg px-4">
        <div className="max-w-2xl">
          <h1 className="text-4xl font-bold text-primary md:text-5xl">
            Welcome to AXA Med
          </h1>
          <p className="mt-6 text-xl text-muted-foreground">
            Connecting healthcare professionals with the right opportunities
          </p>
          <div className="mt-8 flex flex-wrap gap-4">
            <Button variant="primary" size="lg">
              Get Started
            </Button>
            <Button variant="outline" size="lg">
              Learn More
            </Button>
          </div>
        </div>
      </div>
    </div>
    <div className="mx-auto max-w-screen-lg px-4 py-16">
      <div className="grid gap-8 md:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="rounded-lg border bg-card p-6 shadow-sm">
            <h2 className="text-xl font-semibold">Feature {i + 1}</h2>
            <p className="mt-2 text-muted-foreground">
              Description of this amazing feature that makes AXA Med stand out
              from the competition.
            </p>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    children: <ExampleContent />,
  },
};

export const WithHeroSection: Story = {
  args: {
    children: <HeroContent />,
  },
};

export const WithLongContent: Story = {
  args: {
    children: (
      <div className="mx-auto max-w-screen-lg space-y-8 px-4 py-16">
        <h1 className="text-3xl font-bold text-primary">Long Content Page</h1>
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="rounded-lg border p-6">
            <h2 className="text-xl font-semibold">Section {i + 1}</h2>
            <p className="mt-2 text-muted-foreground">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
              euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis
              aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam
              ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis
              nisl.
            </p>
            <p className="mt-4 text-muted-foreground">
              Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet
              nunc, quis aliquam nisl nunc quis nisl. Lorem ipsum dolor sit
              amet, consectetur adipiscing elit.
            </p>
          </div>
        ))}
      </div>
    ),
  },
};
