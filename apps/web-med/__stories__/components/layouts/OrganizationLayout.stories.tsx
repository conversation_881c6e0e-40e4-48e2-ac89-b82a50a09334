import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@axa/ui/primitives/card";

import OrganizationLayout from "@/components/layouts/OrganizationLayout";

const meta: Meta<typeof OrganizationLayout> = {
  title: "Components/Layouts/OrganizationLayout",
  component: OrganizationLayout,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: "/app/shifts",
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof OrganizationLayout>;

const ExampleContent = () => (
  <div className="space-y-4">
    <h1 className="text-2xl font-semibold">Organization Dashboard</h1>
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[
        "Total Shifts",
        "Active Jobs",
        "Available Providers",
        "Pending Reviews",
      ].map((title) => (
        <Card key={title}>
          <CardHeader>
            <CardTitle>{title}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">123</p>
          </CardContent>
        </Card>
      ))}
    </div>
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="size-2 rounded-full bg-primary" />
                <p className="text-sm text-muted-foreground">
                  Activity item {i + 1}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Shifts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="size-2 rounded-full bg-primary" />
                <p className="text-sm text-muted-foreground">Shift {i + 1}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    children: <ExampleContent />,
  },
};

export const Loading: Story = {
  args: {
    children: <ExampleContent />,
    loading: true,
  },
};

export const WithLongContent: Story = {
  args: {
    children: (
      <div className="mx-auto max-w-screen-lg space-y-8">
        <ExampleContent />
        {Array.from({ length: 10 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <CardTitle>Section {i + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    ),
  },
};

export const Mobile: Story = {
  ...Default,
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const MobileLoading: Story = {
  ...Loading,
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const Tablet: Story = {
  ...Default,
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};

export const TabletLoading: Story = {
  ...Loading,
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};
