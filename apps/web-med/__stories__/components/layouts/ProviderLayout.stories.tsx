import type { <PERSON>a, StoryObj } from "@storybook/react";

import ProviderLayout from "@/components/layouts/ProviderLayout";

const meta: Meta<typeof ProviderLayout> = {
  title: "Components/Layouts/ProviderLayout",
  component: ProviderLayout,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: "/providers/app/shifts",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ProviderLayout>;

const ExampleContent = () => (
  <div className="rounded-lg border-2 border-dashed border-muted p-8 text-center">
    <h1 className="text-2xl font-semibold">Example Content</h1>
    <p className="mt-2 text-muted-foreground">
      This is an example of content that would be rendered inside the provider
      layout.
    </p>
  </div>
);

export const Default: Story = {
  args: {
    children: <ExampleContent />,
  },
};

export const WithLongContent: Story = {
  args: {
    children: (
      <div className="mx-auto max-w-screen-lg space-y-8">
        {Array.from({ length: 20 }).map((_, i) => (
          <div key={i} className="rounded-lg border p-4">
            <h2 className="text-lg font-semibold">Section {i + 1}</h2>
            <p className="mt-2 text-muted-foreground">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
        ))}
      </div>
    ),
  },
};

// Add viewport configuration to show mobile view
export const Mobile: Story = {
  ...Default,
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

// Add viewport configuration to show tablet view
export const Tablet: Story = {
  ...Default,
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};
