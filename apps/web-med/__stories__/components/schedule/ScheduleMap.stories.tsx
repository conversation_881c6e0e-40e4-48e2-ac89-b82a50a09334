import type { Meta, StoryObj } from "@storybook/react";

import ScheduleMap from "@/components/schedule/ScheduleMap";

const meta = {
  title: "Components/Schedule/ScheduleMap",
  component: ScheduleMap,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ScheduleMap>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockDates = [
  {
    id: "1",
    startsAt: new Date("2024-02-20T09:00:00"),
    endsAt: new Date("2024-02-20T17:00:00"),
  },
  {
    id: "2",
    startsAt: new Date("2024-02-21T10:00:00"),
    endsAt: new Date("2024-02-21T18:00:00"),
  },
];

export const Empty: Story = {
  args: {
    dates: [],
    onChange: (dates) => {
      console.log("Dates changed:", dates);
    },
  },
};

export const WithDates: Story = {
  args: {
    dates: mockDates,
    onChange: (dates) => {
      console.log("Dates changed:", dates);
    },
  },
};

export const WithManyDates: Story = {
  args: {
    dates: [
      ...mockDates,
      {
        id: "3",
        startsAt: new Date("2024-02-22T08:00:00"),
        endsAt: new Date("2024-02-22T16:00:00"),
      },
      {
        id: "4",
        startsAt: new Date("2024-02-23T09:30:00"),
        endsAt: new Date("2024-02-23T17:30:00"),
      },
      {
        id: "5",
        startsAt: new Date("2024-02-24T11:00:00"),
        endsAt: new Date("2024-02-24T19:00:00"),
      },
      {
        id: "6",
        startsAt: new Date("2024-02-25T07:00:00"),
        endsAt: new Date("2024-02-25T15:00:00"),
      },
    ],
    onChange: (dates) => {
      console.log("Dates changed:", dates);
    },
  },
};
