import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { addDays, addMonths, addWeeks } from "date-fns";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";
import TimeBlock from "@/components/schedule/TimeBlock";

const meta: Meta<typeof TimeBlock> = {
  title: "Components/Schedule/TimeBlock",
  component: TimeBlock,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    onEdit: { action: "onEdit" },
    onDelete: { action: "onDelete" },
  },
};

export default meta;
type Story = StoryObj<typeof TimeBlock>;

// Base date for all stories
const baseDate = new Date();
baseDate.setHours(9, 0, 0, 0);

// Helper to create a time with specific hours
const createTime = (date: Date, hours: number, minutes = 0) => {
  const newDate = new Date(date);
  newDate.setHours(hours, minutes, 0, 0);
  return newDate;
};

export const WorkShift: Story = {
  args: {
    id: "1",
    type: TimeBlockType.SHIFT,
    startDate: baseDate,
    endDate: baseDate,
    startsAt: createTime(baseDate, 9),
    endsAt: createTime(baseDate, 17),
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

export const AvailableTime: Story = {
  args: {
    id: "2",
    type: TimeBlockType.AVAILABILITY,
    startDate: baseDate,
    endDate: baseDate,
    startsAt: createTime(baseDate, 13),
    endsAt: createTime(baseDate, 18, 30),
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

export const TimeOff: Story = {
  args: {
    id: "3",
    type: TimeBlockType.TIME_OFF,
    startDate: baseDate,
    endDate: baseDate,
    startsAt: null,
    endsAt: null,
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

export const RecurringWeekly: Story = {
  args: {
    id: "4",
    type: TimeBlockType.SHIFT,
    startDate: baseDate,
    endDate: addMonths(baseDate, 3),
    startsAt: createTime(baseDate, 9),
    endsAt: createTime(baseDate, 17),
    dayOfWeek: 1, // Tuesday
    recurrence: TimeBlockRecurrence.WEEKLY,
  },
};

export const RecurringBiweekly: Story = {
  args: {
    id: "5",
    type: TimeBlockType.SHIFT,
    startDate: baseDate,
    endDate: addMonths(baseDate, 6),
    startsAt: createTime(baseDate, 10),
    endsAt: createTime(baseDate, 16),
    dayOfWeek: 3, // Thursday
    recurrence: TimeBlockRecurrence.BIWEEKLY,
  },
};

export const DateRange: Story = {
  args: {
    id: "6",
    type: TimeBlockType.TIME_OFF,
    startDate: baseDate,
    endDate: addDays(baseDate, 14),
    startsAt: null,
    endsAt: null,
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

export const ShortDateRange: Story = {
  args: {
    id: "7",
    type: TimeBlockType.SHIFT,
    startDate: baseDate,
    endDate: addDays(baseDate, 2),
    startsAt: createTime(baseDate, 8),
    endsAt: createTime(baseDate, 16),
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

export const RecurringMonthly: Story = {
  args: {
    id: "8",
    type: TimeBlockType.AVAILABILITY,
    startDate: baseDate,
    endDate: addMonths(baseDate, 12),
    startsAt: createTime(baseDate, 14),
    endsAt: createTime(baseDate, 20),
    dayOfWeek: 5, // Saturday
    recurrence: TimeBlockRecurrence.MONTHLY,
  },
};

export const AllDayShift: Story = {
  args: {
    id: "9",
    type: TimeBlockType.SHIFT,
    startDate: baseDate,
    endDate: baseDate,
    startsAt: null,
    endsAt: null,
    dayOfWeek: baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1,
    recurrence: null,
  },
};

// Group of blocks for comparison
export const AllTypes = {
  render: () => (
    <div className="flex w-[400px] flex-col space-y-4">
      <TimeBlock
        id="1"
        type={TimeBlockType.SHIFT}
        startDate={baseDate}
        endDate={baseDate}
        startsAt={createTime(baseDate, 9)}
        endsAt={createTime(baseDate, 17)}
        dayOfWeek={1}
        recurrence={null}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
      <TimeBlock
        id="2"
        type={TimeBlockType.AVAILABILITY}
        startDate={baseDate}
        endDate={baseDate}
        startsAt={createTime(baseDate, 13)}
        endsAt={createTime(baseDate, 18, 30)}
        dayOfWeek={3}
        recurrence={null}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
      <TimeBlock
        id="3"
        type={TimeBlockType.TIME_OFF}
        startDate={baseDate}
        endDate={addDays(baseDate, 7)}
        startsAt={null}
        endsAt={null}
        dayOfWeek={5}
        recurrence={null}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
    </div>
  ),
};

// Group of blocks with different modes
export const AllModes = {
  render: () => (
    <div className="flex w-[400px] flex-col space-y-4">
      <TimeBlock
        id="1"
        type={TimeBlockType.SHIFT}
        startDate={baseDate}
        endDate={baseDate}
        startsAt={createTime(baseDate, 9)}
        endsAt={createTime(baseDate, 17)}
        dayOfWeek={baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1}
        recurrence={null}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
      <TimeBlock
        id="2"
        type={TimeBlockType.SHIFT}
        startDate={baseDate}
        endDate={addWeeks(baseDate, 12)}
        startsAt={createTime(baseDate, 9)}
        endsAt={createTime(baseDate, 17)}
        dayOfWeek={2}
        recurrence={TimeBlockRecurrence.WEEKLY}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
      <TimeBlock
        id="3"
        type={TimeBlockType.SHIFT}
        startDate={baseDate}
        endDate={addDays(baseDate, 5)}
        startsAt={createTime(baseDate, 9)}
        endsAt={createTime(baseDate, 17)}
        dayOfWeek={baseDate.getDay() === 0 ? 6 : baseDate.getDay() - 1}
        recurrence={null}
        onEdit={() => console.log("Edit")}
        onDelete={() => console.log("Delete")}
      />
    </div>
  ),
};
