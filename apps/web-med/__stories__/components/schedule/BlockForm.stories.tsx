import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useState } from "react";

import { Card } from "@axa/ui/primitives/card";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";
import BlockForm from "@/components/schedule/BlockForm";

const meta: Meta<typeof BlockForm> = {
  title: "Components/Schedule/BlockForm",
  component: BlockForm,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof BlockForm>;

// Helper component to wrap the BlockForm and display submitted values
const BlockFormTester = ({
  defaultValues,
  selectedDay,
}: {
  defaultValues?: any;
  selectedDay?: number;
}) => {
  const [submittedValues, setSubmittedValues] = useState<any>(null);

  const handleSubmit = (values: any) => {
    setSubmittedValues(values);
  };

  return (
    <div className="flex w-[600px] flex-col gap-6">
      <Card className="p-6">
        <h2 className="mb-4 text-lg font-medium">Block Form</h2>
        <BlockForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={() => {}}
          selectedDay={selectedDay}
        />
      </Card>

      {submittedValues && (
        <Card className="bg-muted/10 p-6">
          <h3 className="text-md mb-2 font-medium">Submitted Values:</h3>
          <div className="mb-4 grid grid-cols-2 gap-2 text-sm">
            <div className="font-medium">Day of Week:</div>
            <div>
              {submittedValues.dayOfWeek} ({typeof submittedValues.dayOfWeek}) -
              {submittedValues.dayOfWeek >= 0 && submittedValues.dayOfWeek <= 6
                ? [
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday",
                  ][submittedValues.dayOfWeek]
                : "Invalid day"}
            </div>
          </div>
          <pre className="overflow-auto rounded bg-background p-3 text-xs">
            {JSON.stringify(submittedValues, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export const Default: Story = {
  render: () => <BlockFormTester />,
};

export const WithDefaultValues: Story = {
  render: () => (
    <BlockFormTester
      defaultValues={{
        type: TimeBlockType.SHIFT,
        dayOfWeek: 2, // Wednesday
        startTime: "09:00",
        endTime: "17:00",
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        recurrence: TimeBlockRecurrence.WEEKLY,
      }}
    />
  ),
};

export const WithSelectedDay: Story = {
  render: () => <BlockFormTester selectedDay={4} />, // Friday
};

export const TestAllDays: Story = {
  render: () => (
    <div className="flex flex-col gap-8">
      <h2 className="text-xl font-bold">Testing All Days of Week</h2>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {[0, 1, 2, 3, 4, 5, 6].map((day) => (
          <Card key={day} className="p-4">
            <h3 className="text-md mb-2 font-medium">
              Day {day} (
              {
                [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday",
                  "Saturday",
                  "Sunday",
                ][day]
              }
              )
            </h3>
            <BlockFormTester
              defaultValues={{
                dayOfWeek: day,
                startTime: "09:00",
                endTime: "17:00",
              }}
            />
          </Card>
        ))}
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};

export const EdgeCases: Story = {
  render: () => {
    // Create a date for testing
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    return (
      <div className="flex flex-col gap-8">
        <h2 className="text-xl font-bold">Edge Cases</h2>

        <Card className="p-6">
          <h3 className="mb-4 text-lg font-medium">Same Start and End Date</h3>
          <BlockFormTester
            defaultValues={{
              startDate: today,
              endDate: today,
              dayOfWeek: today.getDay() === 0 ? 6 : today.getDay() - 1, // Convert JS day to our format
            }}
          />
        </Card>

        <Card className="p-6">
          <h3 className="mb-4 text-lg font-medium">
            Start Date After End Date (should be validated)
          </h3>
          <BlockFormTester
            defaultValues={{
              startDate: tomorrow,
              endDate: today,
            }}
          />
        </Card>
      </div>
    );
  },
};
