import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";
import ScheduleShop from "@/components/schedule/ScheduleShop";

const meta: Meta<typeof ScheduleShop> = {
  title: "Components/Schedule/ScheduleShop",
  component: ScheduleShop,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ScheduleShop>;

export const Default: Story = {
  args: {},
};

export const WithLimitedTypes: Story = {
  args: {
    allowedTypes: [TimeBlockType.SHIFT],
  },
  name: "With Limited Block Types",
};

export const WithLimitedRecurrences: Story = {
  args: {
    allowedRecurrences: [
      TimeBlockRecurrence.WEEKLY,
      TimeBlockRecurrence.BIWEEKLY,
    ],
  },
  name: "With Limited Recurrence Options",
};

export const WithPrefilledData: Story = {
  render: () => {
    // Mock implementation to demonstrate how the component would look with data
    // In a real implementation, you would use the component's API to set initial data

    // This is just for demonstration in Storybook
    const mockScheduleWithData = (
      <ScheduleShop
      // The component manages its own state, so we're just showing how it would look
      // after some blocks have been added
      />
    );

    // Simulate adding blocks after component mount
    setTimeout(() => {
      // This is just for visual demonstration in Storybook
      // The actual component handles state internally
      const today = new Date();

      // Find the component in the DOM and trigger events programmatically
      // This is a simplified example - in a real story you might use
      // the Storybook play function or other mechanisms

      // Note: This is just to show how the component would look with data
      // The actual implementation would use the component's API
      console.log("ScheduleShop would display blocks for:", today);
    }, 100);

    return mockScheduleWithData;
  },
  name: "With Prefilled Schedule Data",
  parameters: {
    docs: {
      description: {
        story:
          "This shows how the component would look after some time blocks have been added. The actual component manages its own state internally.",
      },
    },
  },
};

export const InDifferentSizes: Story = {
  render: () => (
    <div className="flex flex-col gap-8">
      <div>
        <h3 className="mb-2 text-lg font-medium">Small Container</h3>
        <div className="w-[400px]">
          <ScheduleShop />
        </div>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-medium">Medium Container</h3>
        <div className="w-[600px]">
          <ScheduleShop />
        </div>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-medium">Large Container</h3>
        <div className="w-[800px]">
          <ScheduleShop />
        </div>
      </div>
    </div>
  ),
  name: "In Different Container Sizes",
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        story:
          "Demonstrates how the component adapts to different container widths.",
      },
    },
  },
};
