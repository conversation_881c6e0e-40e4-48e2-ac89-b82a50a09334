import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useState } from "react";

import { Button } from "@axa/ui/primitives/button";

import type { TimeBlockFormValues } from "@/components/forms/TimeBlockForm";

import TimeBlockForm from "@/components/forms/TimeBlockForm";

const meta: Meta<typeof TimeBlockForm> = {
  title: "Components/Schedule/TimeBlockFormTest",
  component: TimeBlockForm,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof TimeBlockForm>;

export const Default: Story = {
  render: () => {
    // State to store form values for inspection
    const [formValues, setFormValues] = useState<TimeBlockFormValues | null>(
      null,
    );
    const [dayOfWeekInfo, setDayOfWeekInfo] = useState<{
      value: string | undefined;
      type: string;
      asNumber: number | null;
    } | null>(null);

    // Handler for form submission
    const handleSubmit = (values: TimeBlockFormValues) => {
      setFormValues(values);

      // Extract and analyze the dayOfWeek value
      const dayValue = values.dayOfWeek;
      let dayAsNumber: number | null = null;

      // Try to convert to number if it's a string containing a number
      if (dayValue && !isNaN(Number(dayValue))) {
        dayAsNumber = Number(dayValue);
      }

      setDayOfWeekInfo({
        value: dayValue,
        type: typeof dayValue,
        asNumber: dayAsNumber,
      });
    };

    return (
      <div className="flex w-[600px] flex-col gap-6">
        <div className="rounded-md border p-4">
          <h2 className="mb-4 text-lg font-medium">TimeBlockForm Test</h2>
          <TimeBlockForm onSubmit={handleSubmit}>
            <div className="flex justify-end">
              <Button type="submit">Submit</Button>
            </div>
          </TimeBlockForm>
        </div>

        {formValues && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Form Values:</h3>
            <pre className="overflow-auto rounded bg-background p-2 text-xs">
              {JSON.stringify(formValues, null, 2)}
            </pre>
          </div>
        )}

        {dayOfWeekInfo && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Day of Week Analysis:</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Value:</div>
              <div>
                {dayOfWeekInfo.value === undefined
                  ? "undefined"
                  : `"${dayOfWeekInfo.value}"`}
              </div>

              <div className="font-medium">Type:</div>
              <div>{dayOfWeekInfo.type}</div>

              <div className="font-medium">As Number:</div>
              <div>
                {dayOfWeekInfo.asNumber === null
                  ? "N/A"
                  : dayOfWeekInfo.asNumber}
              </div>

              <div className="font-medium">Would map to:</div>
              <div>
                {dayOfWeekInfo.asNumber !== null &&
                dayOfWeekInfo.asNumber >= 0 &&
                dayOfWeekInfo.asNumber <= 6
                  ? [
                      "Monday",
                      "Tuesday",
                      "Wednesday",
                      "Thursday",
                      "Friday",
                      "Saturday",
                      "Sunday",
                    ][dayOfWeekInfo.asNumber]
                  : "Unknown (out of range)"}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
  name: "TimeBlockForm Value Test",
};

export const WithDefaultValues: Story = {
  render: () => {
    const [formValues, setFormValues] = useState<TimeBlockFormValues | null>(
      null,
    );
    const [dayOfWeekInfo, setDayOfWeekInfo] = useState<{
      value: string | undefined;
      type: string;
      asNumber: number | null;
    } | null>(null);

    const handleSubmit = (values: TimeBlockFormValues) => {
      setFormValues(values);

      const dayValue = values.dayOfWeek;
      let dayAsNumber: number | null = null;

      if (dayValue && !isNaN(Number(dayValue))) {
        dayAsNumber = Number(dayValue);
      }

      setDayOfWeekInfo({
        value: dayValue,
        type: typeof dayValue,
        asNumber: dayAsNumber,
      });
    };

    return (
      <div className="flex w-[600px] flex-col gap-6">
        <div className="rounded-md border p-4">
          <h2 className="mb-4 text-lg font-medium">
            TimeBlockForm with Default Values
          </h2>
          <TimeBlockForm
            defaultValues={{
              dayOfWeek: "1", // Tuesday in string format
              startTime: 9 * 60, // 9:00 AM in minutes
              endTime: 17 * 60, // 5:00 PM in minutes
            }}
            onSubmit={handleSubmit}
          >
            <div className="flex justify-end">
              <Button type="submit">Submit</Button>
            </div>
          </TimeBlockForm>
        </div>

        {formValues && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Form Values:</h3>
            <pre className="overflow-auto rounded bg-background p-2 text-xs">
              {JSON.stringify(formValues, null, 2)}
            </pre>
          </div>
        )}

        {dayOfWeekInfo && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Day of Week Analysis:</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Value:</div>
              <div>
                {dayOfWeekInfo.value === undefined
                  ? "undefined"
                  : `"${dayOfWeekInfo.value}"`}
              </div>

              <div className="font-medium">Type:</div>
              <div>{dayOfWeekInfo.type}</div>

              <div className="font-medium">As Number:</div>
              <div>
                {dayOfWeekInfo.asNumber === null
                  ? "N/A"
                  : dayOfWeekInfo.asNumber}
              </div>

              <div className="font-medium">Would map to:</div>
              <div>
                {dayOfWeekInfo.asNumber !== null &&
                dayOfWeekInfo.asNumber >= 0 &&
                dayOfWeekInfo.asNumber <= 6
                  ? [
                      "Monday",
                      "Tuesday",
                      "Wednesday",
                      "Thursday",
                      "Friday",
                      "Saturday",
                      "Sunday",
                    ][dayOfWeekInfo.asNumber]
                  : "Unknown (out of range)"}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
  name: "With Default Values",
};

// Test with various day of week values to see how they're processed
export const DayOfWeekVariations: Story = {
  render: () => {
    const [selectedVariation, setSelectedVariation] = useState<string | null>(
      null,
    );
    const [formValues, setFormValues] = useState<TimeBlockFormValues | null>(
      null,
    );
    const [dayOfWeekInfo, setDayOfWeekInfo] = useState<{
      value: string | undefined;
      type: string;
      asNumber: number | null;
    } | null>(null);

    const variations = [
      { label: "String '0'", value: "0" },
      { label: "String '1'", value: "1" },
      { label: "String '6'", value: "6" },
      { label: "String '7'", value: "7" }, // Out of range
      { label: "String 'Monday'", value: "Monday" },
      { label: "String 'Sunday'", value: "Sunday" },
      { label: "Empty string", value: "" },
      { label: "Undefined", value: undefined },
    ];

    const handleSubmit = (values: TimeBlockFormValues) => {
      setFormValues(values);

      const dayValue = values.dayOfWeek;
      let dayAsNumber: number | null = null;

      if (dayValue && !isNaN(Number(dayValue))) {
        dayAsNumber = Number(dayValue);
      }

      setDayOfWeekInfo({
        value: dayValue,
        type: typeof dayValue,
        asNumber: dayAsNumber,
      });
    };

    return (
      <div className="flex w-[600px] flex-col gap-6">
        <div className="rounded-md border p-4">
          <h2 className="mb-4 text-lg font-medium">Day of Week Variations</h2>

          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium">
              Select a variation to test:
            </label>
            <div className="grid grid-cols-2 gap-2">
              {variations.map((variation) => (
                <Button
                  key={variation.label}
                  variant={
                    selectedVariation === variation.label
                      ? "default"
                      : "outline"
                  }
                  onClick={() => setSelectedVariation(variation.label)}
                  className="justify-start"
                >
                  {variation.label}
                </Button>
              ))}
            </div>
          </div>

          {selectedVariation && (
            <TimeBlockForm
              key={selectedVariation} // Force re-render on variation change
              defaultValues={{
                dayOfWeek: variations.find((v) => v.label === selectedVariation)
                  ?.value,
                startTime: 9 * 60,
                endTime: 17 * 60,
              }}
              onSubmit={handleSubmit}
            >
              <div className="flex justify-end">
                <Button type="submit">Submit</Button>
              </div>
            </TimeBlockForm>
          )}
        </div>

        {formValues && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Form Values:</h3>
            <pre className="overflow-auto rounded bg-background p-2 text-xs">
              {JSON.stringify(formValues, null, 2)}
            </pre>
          </div>
        )}

        {dayOfWeekInfo && (
          <div className="rounded-md border bg-muted/20 p-4">
            <h3 className="text-md mb-2 font-medium">Day of Week Analysis:</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Value:</div>
              <div>
                {dayOfWeekInfo.value === undefined
                  ? "undefined"
                  : `"${dayOfWeekInfo.value}"`}
              </div>

              <div className="font-medium">Type:</div>
              <div>{dayOfWeekInfo.type}</div>

              <div className="font-medium">As Number:</div>
              <div>
                {dayOfWeekInfo.asNumber === null
                  ? "N/A"
                  : dayOfWeekInfo.asNumber}
              </div>

              <div className="font-medium">Would map to:</div>
              <div>
                {dayOfWeekInfo.asNumber !== null &&
                dayOfWeekInfo.asNumber >= 0 &&
                dayOfWeekInfo.asNumber <= 6
                  ? [
                      "Monday",
                      "Tuesday",
                      "Wednesday",
                      "Thursday",
                      "Friday",
                      "Saturday",
                      "Sunday",
                    ][dayOfWeekInfo.asNumber]
                  : "Unknown (out of range)"}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
  name: "Day of Week Variations",
};
