import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { WeeklyScheduleTime } from "@/components/schedule/WeeklyScheduleMap";

import MonthlySchedulePreview from "@/components/schedule/MonthlySchedulePreview";

const meta = {
  title: "Components/Schedule/MonthlySchedulePreview",
  component: MonthlySchedulePreview,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof MonthlySchedulePreview>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockTimes: WeeklyScheduleTime[] = [
  {
    id: "1",
    dayOfWeek: "Monday",
    startTime: "09:00",
    endTime: "17:00",
    type: "SHIFT",
  },
  {
    id: "2",
    dayOfWeek: "Monday",
    startTime: "18:00",
    endTime: "22:00",
    type: "AVAILABILITY",
  },
  {
    id: "3",
    dayOfWeek: "Wednesday",
    startTime: "10:00",
    endTime: "18:00",
    type: "SHIFT",
    recurrence: "WEEKLY",
  },
  {
    id: "4",
    dayOfWeek: "Friday",
    startTime: "08:00",
    endTime: "16:00",
    type: "TIME_OFF",
  },
];

export const Empty: Story = {
  args: {
    times: [],
  },
};

export const WithTimes: Story = {
  args: {
    times: mockTimes,
  },
};

export const WithManyTimes: Story = {
  args: {
    times: [
      ...mockTimes,
      {
        id: "5",
        dayOfWeek: "Tuesday",
        startTime: "07:00",
        endTime: "15:00",
        type: "SHIFT",
        recurrence: "WEEKLY",
      },
      {
        id: "6",
        dayOfWeek: "Tuesday",
        startTime: "16:00",
        endTime: "23:00",
        type: "AVAILABILITY",
      },
      {
        id: "7",
        dayOfWeek: "Thursday",
        startTime: "09:30",
        endTime: "17:30",
        type: "SHIFT",
        recurrence: "BIWEEKLY",
      },
      {
        id: "8",
        dayOfWeek: "Saturday",
        startTime: "10:00",
        endTime: "14:00",
        type: "TIME_OFF",
        recurrence: "MONTHLY",
      },
      {
        id: "9",
        dayOfWeek: "Sunday",
        startTime: "12:00",
        endTime: "20:00",
        type: "AVAILABILITY",
      },
    ],
  },
};

export const CustomWeeks: Story = {
  args: {
    times: mockTimes,
    weeks: 4,
  },
};
