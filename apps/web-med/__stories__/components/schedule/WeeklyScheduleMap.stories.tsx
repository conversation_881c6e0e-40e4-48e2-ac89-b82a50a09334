import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { WeeklyScheduleTime } from "@/components/schedule/WeeklyScheduleMap";

import WeeklyScheduleMap from "@/components/schedule/WeeklyScheduleMap";

const meta = {
  title: "Components/Schedule/WeeklyScheduleMap",
  component: WeeklyScheduleMap,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof WeeklyScheduleMap>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockTimes: WeeklyScheduleTime[] = [
  {
    id: "1",
    dayOfWeek: "Monday",
    startTime: "09:00",
    endTime: "17:00",
    type: "SHIFT",
  },
  {
    id: "2",
    dayOfWeek: "Monday",
    startTime: "18:00",
    endTime: "22:00",
    type: "AVAILABILITY",
  },
  {
    id: "3",
    dayOfWeek: "Wednesday",
    startTime: "10:00",
    endTime: "18:00",
    type: "SHIFT",
    recurrence: "WEEKLY",
  },
  {
    id: "4",
    dayOfWeek: "Friday",
    startTime: "08:00",
    endTime: "16:00",
    type: "TIME_OFF",
  },
];

export const Empty: Story = {
  args: {
    times: [],
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const EmptyWithPlaceholders: Story = {
  args: {
    times: [],
    showEmptyDays: true,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const WithTimes: Story = {
  args: {
    times: mockTimes,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const WithTimesAndHours: Story = {
  args: {
    times: mockTimes,
    showHours: true,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const WithTypes: Story = {
  args: {
    times: mockTimes,
    showType: true,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const WithRecurrence: Story = {
  args: {
    times: mockTimes,
    showRecurrence: true,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const WithManyTimes: Story = {
  args: {
    times: [
      ...mockTimes,
      {
        id: "5",
        dayOfWeek: "Tuesday",
        startTime: "07:00",
        endTime: "15:00",
        type: "SHIFT",
        recurrence: "WEEKLY",
      },
      {
        id: "6",
        dayOfWeek: "Tuesday",
        startTime: "16:00",
        endTime: "23:00",
        type: "AVAILABILITY",
      },
      {
        id: "7",
        dayOfWeek: "Thursday",
        startTime: "09:30",
        endTime: "17:30",
        type: "SHIFT",
        recurrence: "BIWEEKLY",
      },
      {
        id: "8",
        dayOfWeek: "Saturday",
        startTime: "10:00",
        endTime: "14:00",
        type: "TIME_OFF",
        recurrence: "MONTHLY",
      },
      {
        id: "9",
        dayOfWeek: "Sunday",
        startTime: "12:00",
        endTime: "20:00",
        type: "AVAILABILITY",
      },
    ],
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};

export const Complete: Story = {
  args: {
    times: [
      ...mockTimes,
      {
        id: "5",
        dayOfWeek: "Tuesday",
        startTime: "07:00",
        endTime: "15:00",
        type: "SHIFT",
        recurrence: "WEEKLY",
      },
      {
        id: "6",
        dayOfWeek: "Tuesday",
        startTime: "16:00",
        endTime: "23:00",
        type: "AVAILABILITY",
      },
      {
        id: "7",
        dayOfWeek: "Thursday",
        startTime: "09:30",
        endTime: "17:30",
        type: "SHIFT",
        recurrence: "BIWEEKLY",
      },
      {
        id: "8",
        dayOfWeek: "Saturday",
        startTime: "10:00",
        endTime: "14:00",
        type: "TIME_OFF",
        recurrence: "MONTHLY",
      },
      {
        id: "9",
        dayOfWeek: "Sunday",
        startTime: "12:00",
        endTime: "20:00",
        type: "AVAILABILITY",
      },
    ],
    showHours: true,
    showEmptyDays: true,
    showType: true,
    showRecurrence: true,
    onChange: (times) => {
      console.log("Times changed:", times);
    },
  },
};
