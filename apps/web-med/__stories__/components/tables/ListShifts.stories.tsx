import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import ListShifts from "@/components/tables/ListShifts";

const meta = {
  title: "Components/Tables/ListShifts",
  component: ListShifts,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListShifts>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockShift = {
  id: "shift-001",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
  deletedAt: null,
  startedAt: null,
  completedAt: null,
  cancelledAt: null,
  billedAt: null,
  invoicedAt: null,
  paidAt: null,
  status: "PENDING",
  summary: "Morning Shift",
  description: "Standard morning shift at the main hospital",
  timeZone: "America/Los_Angeles",
  startDate: new Date("2024-02-01T09:00:00Z"),
  endDate: new Date("2024-02-01T17:00:00Z"),
  startTime: 0,
  endTime: 0,
  duration: 480,
  paymentType: "HOURLY",
  paymentRate: 50.0,
  paymentTotal: 400.0,
  overtimeHours: 0,
  nightTimeHours: 0,
  holidayTimeHours: 0,
  nightRate: 1.25,
  overtimeRate: 1.5,
  holidayRate: 2.0,
  organizationId: "org1",
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
  locationId: "loc1",
  location: null,
  jobId: "job1",
  job: null,
  invoiceId: "inv1",
  invoice: null,
  departmentId: null,
  department: null,
  providerId: null,
  provider: null,
  payoutId: null,
  payout: null,
  scheduleId: null,
  schedule: null,
  thread: null,
  review: null,
  documents: [],
  contacts: [],
  specialties: [],
  contracts: [],
  actions: [],
  _count: {
    documents: 0,
    contacts: 0,
    specialties: 0,
    contracts: 0,
    actions: 0,
    organization: 1,
    invoice: 1,
    location: 1,
    job: 1,
    schedule: 0,
    department: 0,
    provider: 0,
    payout: 0,
    thread: 0,
    review: 0,
  },
} as const;

export const Loading: Story = {
  args: {
    loading: true,
    shifts: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    shifts: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    shifts: {
      items: [mockShift],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    shifts: {
      items: [
        mockShift,
        {
          ...mockShift,
          id: "shift-002",
          status: "ACTIVE",
          startedAt: new Date("2024-02-01T09:00:00Z"),
          summary: "Afternoon Shift",
          description: "Afternoon shift at the downtown clinic",
          startDate: new Date("2024-02-01T13:00:00Z"),
          endDate: new Date("2024-02-01T21:00:00Z"),
        },
        {
          ...mockShift,
          id: "shift-003",
          status: "COMPLETED",
          startedAt: new Date("2024-01-15T09:00:00Z"),
          completedAt: new Date("2024-01-15T17:00:00Z"),
          summary: "Night Shift",
          description: "Night shift at the emergency department",
          startDate: new Date("2024-01-15T21:00:00Z"),
          endDate: new Date("2024-01-16T05:00:00Z"),
          nightTimeHours: 8,
          paymentTotal: 500.0,
        },
      ],
      total: 3,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
