import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListFacilities from "@/components/tables/ListFacilities";

const meta = {
  title: "Components/Tables/ListFacilities",
  component: ListFacilities,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListFacilities>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockFacility: RouterOutputs["locations"]["getMany"]["items"][number] = {
  id: "facility-001",
  name: "Main Hospital",
  type: "HOSPITAL",
  description: "Main hospital campus with emergency services",
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
  address: {
    formatted: "123 Main St, New York, NY 10001",
    timeZone: "America/New_York",
    latitude: 40.7128,
    longitude: -74.006,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    facilities: {
      items: [],
      total: 0,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    facilities: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    facilities: {
      items: [mockFacility],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    facilities: {
      items: [
        {
          ...mockFacility,
          id: "facility-001",
          name: "Medical Campus",
          type: "CAMPUS",
          description: "Large medical complex with multiple facilities",
          address: {
            ...mockFacility.address,
            formatted: "123 Medical Plaza, New York, NY 10001",
          },
        },
        {
          ...mockFacility,
          id: "facility-002",
          name: "General Hospital",
          type: "HOSPITAL",
          description: "Full-service medical facility",
          address: {
            ...mockFacility.address,
            formatted: "456 Hospital Dr, New York, NY 10022",
          },
        },
        {
          ...mockFacility,
          id: "facility-003",
          name: "Downtown Clinic",
          type: "CLINIC",
          description: "Outpatient medical facility",
          address: {
            ...mockFacility.address,
            formatted: "789 Clinic Ave, New York, NY 10003",
          },
        },
        {
          ...mockFacility,
          id: "facility-004",
          name: "Medical Office",
          type: "OFFICE",
          description: "Private practice office",
          address: {
            ...mockFacility.address,
            formatted: "321 Office Blvd, New York, NY 10016",
          },
        },
        {
          ...mockFacility,
          id: "facility-005",
          name: "City Pharmacy",
          type: "PHARMACY",
          description: "Retail pharmacy with clinical services",
          address: {
            ...mockFacility.address,
            formatted: "654 Pharmacy St, New York, NY 10019",
          },
        },
        {
          ...mockFacility,
          id: "facility-006",
          name: "Medical Laboratory",
          type: "LAB",
          description: "Medical testing facility",
          address: {
            ...mockFacility.address,
            formatted: "987 Lab Lane, New York, NY 10024",
          },
        },
        {
          ...mockFacility,
          id: "facility-007",
          name: "Imaging Center",
          type: "IMAGING",
          description: "Advanced imaging and radiology center",
          address: {
            ...mockFacility.address,
            formatted: "741 Imaging Way, New York, NY 10028",
          },
        },
        {
          ...mockFacility,
          id: "facility-008",
          name: "Rehabilitation Center",
          type: "REHABILITATION",
          description: "Physical therapy and rehabilitation center",
          address: {
            ...mockFacility.address,
            formatted: "852 Rehab Rd, New York, NY 10032",
          },
        },
        {
          ...mockFacility,
          id: "facility-009",
          name: "Specialty Center",
          type: "OTHER",
          description: "Specialized medical facility",
          address: {
            ...mockFacility.address,
            formatted: "963 Specialty Ct, New York, NY 10036",
          },
        },
      ],
      total: 9,
    },
  },
};
