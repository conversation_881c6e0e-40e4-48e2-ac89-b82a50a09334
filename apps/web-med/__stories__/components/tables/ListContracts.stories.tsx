import type { Meta, StoryObj } from "@storybook/react";

import ListContracts from "@/components/tables/ListContracts";

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider = {
  person: {
    firstName: "<PERSON>",
    lastName: "Doe",
    avatar: null,
  },
} as const;

const mockOrganization = {
  id: "org-001",
  name: "Main Hospital",
  avatar: null,
} as const;

const mockContract = {
  id: "contract-001",
  title: "Provider Agreement",
  type: "PROVIDER",
  status: "PENDING",
  documensoId: "doc-001",
  provider: mockProvider,
  organization: mockOrganization,
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  organizationId: "org-001",
  providerId: "provider-001",
  jobId: null,
  job: null,
  shiftId: null,
  shift: null,
  signatures: [],
  people: [],
  _count: {
    signatures: 0,
    people: 0,
  },
} as const;

const meta = {
  title: "Components/Tables/ListContracts",
  component: ListContracts,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  args: {
    loading: false,
    contracts: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
} satisfies Meta<typeof ListContracts>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    loading: true,
    contracts: {
      items: [],
      total: 0,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    contracts: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    contracts: {
      items: [mockContract],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    contracts: {
      items: [
        mockContract,
        {
          ...mockContract,
          id: "contract-002",
          title: "Organization Agreement",
          type: "ORGANIZATION",
          status: "SIGNED",
        },
        {
          ...mockContract,
          id: "contract-003",
          title: "Provider Agreement 2",
          type: "PROVIDER",
          status: "REJECTED",
        },
        {
          ...mockContract,
          id: "contract-004",
          title: "Organization Agreement 2",
          type: "ORGANIZATION",
          status: "PENDING",
        },
        {
          ...mockContract,
          id: "contract-005",
          title: "Provider Agreement 3",
          type: "PROVIDER",
          status: "SIGNED",
        },
      ],
      total: 5,
    },
  },
};
