import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListPayouts from "@/components/tables/ListPayouts";

const meta = {
  title: "Components/Tables/ListPayouts",
  component: ListPayouts,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof ListPayouts>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider = {
  id: "provider-001",
  status: "ACTIVE",
  person: {
    id: "person-001",
    firstName: "John",
    lastName: "Doe",
    avatar: "https://via.placeholder.com/150",
  },
} as const;

const mockPayout: RouterOutputs["payouts"]["getMany"]["items"][number] = {
  id: "payout-001",
  status: "PENDING",
  amount: 1500.0,
  overtimeAmount: 250.0,
  holidayAmount: 300.0,
  nightAmount: 187.5,
  provider: mockProvider,
};

export const Loading: Story = {
  args: {
    loading: true,
    payouts: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    payouts: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    payouts: {
      items: [mockPayout],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    payouts: {
      items: [
        mockPayout,
        {
          ...mockPayout,
          id: "payout-002",
          status: "PROCESSING",
          amount: 2000.0,
          overtimeAmount: 400.0,
          provider: {
            ...mockProvider,
            id: "provider-002",
            person: {
              ...mockProvider.person,
              id: "person-002",
              firstName: "Jane",
              lastName: "Smith",
            },
          },
        },
        {
          ...mockPayout,
          id: "payout-003",
          status: "COMPLETED",
          amount: 1750.0,
          nightAmount: 218.75,
          provider: {
            ...mockProvider,
            id: "provider-003",
            person: {
              ...mockProvider.person,
              id: "person-003",
              firstName: "Robert",
              lastName: "Johnson",
            },
          },
        },
        {
          ...mockPayout,
          id: "payout-004",
          status: "FAILED",
          amount: 1200.0,
          provider: {
            ...mockProvider,
            id: "provider-004",
            person: {
              ...mockProvider.person,
              id: "person-004",
              firstName: "Sarah",
              lastName: "Williams",
            },
          },
        },
        {
          ...mockPayout,
          id: "payout-005",
          status: "CANCELLED",
          amount: 900.0,
          holidayAmount: 180.0,
          provider: {
            ...mockProvider,
            id: "provider-005",
            person: {
              ...mockProvider.person,
              id: "person-005",
              firstName: "Michael",
              lastName: "Brown",
            },
          },
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
