import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListApplications from "@/components/tables/ListApplications";

const meta = {
  title: "Components/Tables/ListApplications",
  component: ListApplications,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof ListApplications>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider = {
  id: "provider-001",
  person: {
    id: "person-001",
    firstName: "<PERSON>",
    lastName: "Doe",
    avatar: "https://via.placeholder.com/150",
  },
};

const mockJob = {
  id: "job-001",
  summary: "Full-time Emergency Room Physician",
} as RouterOutputs["applications"]["getMany"]["items"][number]["job"];

const mockOrganization = {
  id: "organization-001",
  name: "City General Hospital",
  avatar: "/placeholder.svg",
};

const mockApplication = {
  id: "application-001",
  status: "PENDING",
  notes: "I would like to apply for the Emergency Room Physician position",
  createdAt: new Date(),
  updatedAt: new Date(),
  submittedAt: new Date(),
  provider: mockProvider,
  job: mockJob,
  organization: mockOrganization,
};

export const Loading: Story = {
  args: {
    loading: true,
    applications: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    applications: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    applications: {
      items: [mockApplication],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    applications: {
      items: [
        mockApplication,
        {
          ...mockApplication,
          id: "application-003",
          status: "ACCEPTED",
          provider: {
            ...mockProvider,
            id: "provider-003",
            person: {
              ...mockProvider.person,
              id: "person-003",
              firstName: "Robert",
              lastName: "Johnson",
            },
          },
          job: {
            ...mockJob,
            id: "job-003",
            summary: "Pediatric Specialist",
          },
        },
        {
          ...mockApplication,
          id: "application-004",
          status: "REJECTED",
          provider: {
            ...mockProvider,
            id: "provider-004",
            person: {
              ...mockProvider.person,
              id: "person-004",
              firstName: "Sarah",
              lastName: "Williams",
            },
          },
          job: {
            ...mockJob,
            id: "job-004",
            summary: "Surgical Technician",
          },
        },
        {
          ...mockApplication,
          id: "application-005",
          status: "WITHDRAWN",
          provider: {
            ...mockProvider,
            id: "provider-005",
            person: {
              ...mockProvider.person,
              id: "person-005",
              firstName: "Michael",
              lastName: "Brown",
            },
          },
          job: {
            ...mockJob,
            id: "job-005",
            summary: "Radiology Technologist",
          },
        },
      ],
      total: 5,
    },
  },
};
