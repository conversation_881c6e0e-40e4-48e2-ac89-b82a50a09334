import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import {
  IncidentSeverity,
  IncidentStatus,
  ProviderStatus,
  ShiftStatus,
} from "@axa/database-medical";

import type { RouterOutputs } from "@/api";

import { IncidentType } from "@/api";
import ListIncidents from "@/components/tables/ListIncidents";

const meta: Meta<typeof ListIncidents> = {
  title: "Components/Tables/ListIncidents",
  component: ListIncidents,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ListIncidents>;

type IncidentStruct = RouterOutputs["incidents"]["get"];
interface UpdateIncidentValues {
  type: IncidentType;
  title: string;
  description: string;
  severity: IncidentSeverity;
  status?: IncidentStatus;
  id?: string;
  organizationId?: string;
  shiftId?: string;
  providerId?: string;
}

const sampleIncidents = [
  {
    id: "1",
    title: "Emergency Room Equipment Failure",
    description: "Critical equipment malfunction in ER room 3",
    severity: IncidentSeverity.CRITICAL,
    status: IncidentStatus.OPEN,
    type: IncidentType.SAFETY,
    providerId: "p1",
    organizationId: "org1",
    shiftId: "s1",
    provider: {
      id: "p1",
      status: ProviderStatus.ACTIVE,
      title: "Emergency Physician",
      person: {
        id: "person1",
        firstName: "John",
        lastName: "Doe",
        avatar: null,
      },
    },
    organization: {
      id: "org1",
      name: "City General Hospital",
      avatar: null,
    },
    shift: {
      id: "s1",
      summary: "Night Shift - Emergency Room",
      scope: "FULL_TIME",
      status: ShiftStatus.ACTIVE,
      startDate: new Date("2024-02-01T20:00:00Z"),
      endDate: new Date("2024-02-02T08:00:00Z"),
    },
    createdAt: new Date("2024-02-01T22:30:00Z"),
    updatedAt: new Date("2024-02-01T23:15:00Z"),
  },
  {
    id: "2",
    title: "Patient Fall Incident",
    description: "Patient fell while attempting to get out of bed unassisted",
    severity: IncidentSeverity.MAJOR,
    status: IncidentStatus.IN_PROGRESS,
    type: IncidentType.HEALTH,
    providerId: "p2",
    organizationId: "org1",
    shiftId: "s2",
    provider: {
      id: "p2",
      status: ProviderStatus.ACTIVE,
      title: "Registered Nurse",
      person: {
        id: "person2",
        firstName: "Jane",
        lastName: "Smith",
        avatar: null,
      },
    },
    organization: {
      id: "org1",
      name: "City General Hospital",
      avatar: null,
    },
    shift: {
      id: "s2",
      summary: "Day Shift - Medical Ward",
      scope: "FULL_TIME",
      status: ShiftStatus.ACTIVE,
      startDate: new Date("2024-02-01T08:00:00Z"),
      endDate: new Date("2024-02-01T16:00:00Z"),
    },
    createdAt: new Date("2024-02-01T10:15:00Z"),
    updatedAt: new Date("2024-02-01T11:30:00Z"),
  },
  {
    id: "3",
    title: "Medication Administration Error",
    description: "Wrong medication dosage administered to patient",
    severity: IncidentSeverity.MAJOR,
    status: IncidentStatus.RESOLVED,
    type: IncidentType.HEALTH,
    providerId: "p3",
    organizationId: "org2",
    shiftId: "s3",
    provider: {
      id: "p3",
      status: ProviderStatus.ACTIVE,
      title: "Clinical Pharmacist",
      person: {
        id: "person3",
        firstName: "Mike",
        lastName: "Johnson",
        avatar: null,
      },
    },
    organization: {
      id: "org2",
      name: "Metro Medical Center",
      avatar: null,
    },
    shift: {
      id: "s3",
      summary: "Evening Shift - Pharmacy",
      scope: "FULL_TIME",
      status: ShiftStatus.COMPLETED,
      startDate: new Date("2024-02-01T16:00:00Z"),
      endDate: new Date("2024-02-02T00:00:00Z"),
    },
    createdAt: new Date("2024-02-01T18:45:00Z"),
    updatedAt: new Date("2024-02-01T20:30:00Z"),
  },
  {
    id: "4",
    title: "HVAC System Malfunction",
    description: "Temperature control issues in ICU wing",
    severity: IncidentSeverity.MINOR,
    status: IncidentStatus.OPEN,
    type: IncidentType.ENVIRONMENT,
    providerId: "p4",
    organizationId: "org2",
    shiftId: "s4",
    provider: {
      id: "p4",
      status: ProviderStatus.ACTIVE,
      title: "Facility Manager",
      person: {
        id: "person4",
        firstName: "Sarah",
        lastName: "Wilson",
        avatar: null,
      },
    },
    organization: {
      id: "org2",
      name: "Metro Medical Center",
      avatar: null,
    },
    shift: {
      id: "s4",
      summary: "Day Shift - Facilities",
      scope: "FULL_TIME",
      status: ShiftStatus.ACTIVE,
      startDate: new Date("2024-02-01T09:00:00Z"),
      endDate: new Date("2024-02-01T17:00:00Z"),
    },
    createdAt: new Date("2024-02-01T11:00:00Z"),
    updatedAt: new Date("2024-02-01T11:45:00Z"),
  },
] satisfies IncidentStruct[];

export const Default: Story = {
  args: {
    incidents: {
      items: sampleIncidents.slice(0, 2),
      total: 2,
    },
    onUpdate: async (incident: UpdateIncidentValues) => {
      console.log("Updating incident:", incident);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDelete: async (incident: { id: string }) => {
      console.log("Deleting incident:", incident);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ incidents }, updateArgs] = useArgs();

    const handleUpdate = async (values: UpdateIncidentValues) => {
      await args.onUpdate?.({
        ...values,
        severity: values.severity,
      });
      // In a real app, you would update the data after successful API call
      console.log("Updated incident:", values);
    };

    const handleDelete = async (values: { id: string }) => {
      await args.onDelete?.(values);
      // In a real app, you would remove the item after successful API call
      const updatedItems = incidents.items.filter(
        (incident: IncidentStruct) => incident.id !== values.id,
      );
      updateArgs({
        incidents: {
          items: updatedItems,
          total: updatedItems.length,
        },
      });
    };

    return (
      <ListIncidents
        incidents={incidents}
        onUpdate={handleUpdate}
        onDelete={handleDelete}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    incidents: {
      items: [],
      total: 0,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    incidents: {
      items: [],
      total: 0,
    },
  },
};

export const ManyItems: Story = {
  args: {
    incidents: {
      items: sampleIncidents,
      total: sampleIncidents.length,
    },
  },
};

export const WithInteraction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test search
    const searchInput = canvas.getByPlaceholderText(/search incidents/i);
    await userEvent.type(searchInput, "Emergency");
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Test table settings
    const settingsButton = canvas.getByLabelText(/table settings/i);
    await userEvent.click(settingsButton);

    // Test row selection
    const checkboxes = await canvas.findAllByRole("checkbox");
    if (checkboxes[1]) {
      await userEvent.click(checkboxes[1]);
    }

    // Test filters
    const severityFilter = canvas.getByText(/severity/i);
    await userEvent.click(severityFilter);
    await new Promise((resolve) => setTimeout(resolve, 300));
  },
};
