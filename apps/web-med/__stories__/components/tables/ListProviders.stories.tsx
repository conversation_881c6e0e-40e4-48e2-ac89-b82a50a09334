import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListProviders from "@/components/tables/ListProviders";

const meta = {
  title: "Components/Tables/ListProviders",
  component: ListProviders,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListProviders>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider: RouterOutputs["providers"]["getMany"]["items"][number] = {
  id: "provider-001",
  status: "ACTIVE",
  backgroundCheckStatus: "APPROVED",
  i9VerificationStatus: "APPROVED",
  identityVerificationStatus: "APPROVED",
  title: "MD",
  spokenLanguages: ["English", "Spanish"],
  person: {
    id: "person-001",
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+****************",
    avatar: "https://via.placeholder.com/150",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    providers: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    providers: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    providers: {
      items: [mockProvider],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    providers: {
      items: [
        mockProvider,
        {
          ...mockProvider,
          id: "provider-002",
          status: "PENDING",
          title: "RN",
          person: {
            ...mockProvider.person,
            firstName: "Jane",
            lastName: "Smith",
            email: "<EMAIL>",
            phone: "+****************",
          },
        },
        {
          ...mockProvider,
          id: "provider-003",
          status: "INACTIVE",
          title: "PA",
          person: {
            ...mockProvider.person,
            firstName: "Robert",
            lastName: "Johnson",
            email: "<EMAIL>",
            phone: "+****************",
          },
        },
        {
          ...mockProvider,
          id: "provider-004",
          status: "SUSPENDED",
          title: "NP",
          person: {
            ...mockProvider.person,
            firstName: "Sarah",
            lastName: "Williams",
            email: "<EMAIL>",
            phone: "+****************",
          },
        },
        {
          ...mockProvider,
          id: "provider-005",
          status: "REJECTED",
          title: "PT",
          person: {
            ...mockProvider.person,
            firstName: "Michael",
            lastName: "Brown",
            email: "<EMAIL>",
            phone: "+****************",
          },
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
