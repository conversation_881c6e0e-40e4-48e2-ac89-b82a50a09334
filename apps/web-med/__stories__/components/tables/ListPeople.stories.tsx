import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListPeople from "@/components/tables/ListPeople";

const meta = {
  title: "Components/Tables/ListPeople",
  component: ListPeople,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListPeople>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockPerson: RouterOutputs["people"]["getMany"]["items"][number] = {
  id: "person-001",
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  role: "NONE",
  avatar: "https://via.placeholder.com/150",
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "+****************",
  organizationId: "org1",
  organization: {
    id: "org1",
    path: "/org1",
    type: "CLIENT",
    status: "ACTIVE",
    name: "Healthcare Organization",
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    approvedAt: null,
    rejectedAt: null,
    avatar: null,
    depth: 0,
    numchild: 0,
    phone: null,
    email: null,
    balance: 0,
    threshold: 0,
    assistPercentage: 0,
    basePercentage: 0,
    managerId: null,
    addressId: null,
    class: "PRIVATE",
    billing: "INVOICE",
    accountId: null,
    taxId: null,
    vatId: null,
    structure: null,
  },
  addressId: null,
  address: {
    id: "address-001",
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    formatted: "123 Main St, New York, NY 10001",
    street: null,
    city: null,
    borough: null,
    neighborhood: null,
    county: null,
    postal: null,
    state: null,
    country: "US",
    timeZone: "America/New_York",
    latitude: 40.7128,
    longitude: -74.006,
  },
  provider: {
    id: "provider-001",
    accountId: null,
    accountStatus: "PENDING",
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    status: "ACTIVE",
    backgroundCheckStatus: null,
    i9VerificationStatus: null,
    identityVerificationStatus: null,
    title: null,
    gender: null,
    spokenLanguages: [],
    personId: "person-001",
    addressId: "address-001",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    people: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    people: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    people: {
      items: [mockPerson],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    people: {
      items: [
        mockPerson,
        {
          ...mockPerson,
          id: "person-002",
          firstName: "Jane",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: "+****************",
          avatar: "https://via.placeholder.com/150",
        },
        {
          ...mockPerson,
          id: "person-003",
          firstName: "Robert",
          lastName: "Johnson",
          email: "<EMAIL>",
          phone: "+****************",
          avatar: "https://via.placeholder.com/150",
          organization: null,
          organizationId: null,
          address: null,
          provider: null,
        },
      ],
      total: 3,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
