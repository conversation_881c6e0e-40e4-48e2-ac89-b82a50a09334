import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListDocuments from "@/components/tables/ListDocuments";

const meta = {
  title: "Components/Tables/ListDocuments",
  component: ListDocuments,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListDocuments>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockDocument: RouterOutputs["documents"]["getMany"]["items"][number] = {
  id: "doc-001",
  name: "Patient Consent Form",
  url: "https://example.com/documents/consent-form.pdf",
  size: 1024,
  type: "PDF",
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  organizationId: "org1",
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    documents: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    documents: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    documents: {
      items: [mockDocument],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    documents: {
      items: [
        mockDocument,
        {
          ...mockDocument,
          id: "doc-002",
          name: "Medical Report",
          url: "https://example.com/documents/medical-report.pdf",
          type: "DOCX",
          organization: {
            ...mockDocument.organization,
            id: "org2",
            name: "City Medical Center",
            avatar: null,
          },
        },
        {
          ...mockDocument,
          id: "doc-003",
          name: "Insurance Policy",
          url: "https://example.com/documents/insurance-policy.pdf",
          type: "PDF",
          organization: {
            ...mockDocument.organization,
            id: "org3",
            name: "County Hospital",
            avatar: null,
          },
        },
        {
          ...mockDocument,
          id: "doc-004",
          name: "Lab Results",
          url: "https://example.com/documents/lab-results.pdf",
          type: "PDF",
          organization: {
            ...mockDocument.organization,
            id: "org4",
            name: "Rehabilitation Center",
            avatar: null,
          },
        },
        {
          ...mockDocument,
          id: "doc-005",
          name: "Prescription",
          url: "https://example.com/documents/prescription.pdf",
          type: "PDF",
          organization: {
            ...mockDocument.organization,
            id: "org5",
            name: "Family Practice Clinic",
            avatar: null,
          },
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
