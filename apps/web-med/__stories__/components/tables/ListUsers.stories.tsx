import type { <PERSON>a, <PERSON>Obj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListUser from "@/components/tables/ListUsers";

const meta = {
  title: "Components/Tables/ListUsers",
  component: ListUser,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListUser>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockUser: RouterOutputs["user"]["getMany"]["users"][number] = {
  id: "user-001",
  role: "ADMIN",
  firstName: "<PERSON>",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "https://via.placeholder.com/150",
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    users: {
      users: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    users: {
      users: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    users: {
      users: [mockUser],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    users: {
      users: [
        mockUser,
        {
          ...mockUser,
          id: "user-002",
          role: "BILLING",
          firstName: "Jane",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: "+****************",
          avatar: "https://via.placeholder.com/150",
        },
        {
          ...mockUser,
          id: "user-003",
          role: "PROVIDER",
          firstName: "Robert",
          lastName: "Johnson",
          email: "<EMAIL>",
          phone: "+****************",
          avatar: "https://via.placeholder.com/150",
          organization: undefined,
        },
      ],
      total: 3,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
