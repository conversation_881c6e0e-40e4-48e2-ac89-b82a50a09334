import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListInvoices from "@/components/tables/ListInvoices";

const meta = {
  title: "Components/Tables/ListInvoices",
  component: ListInvoices,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof ListInvoices>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockInvoice: RouterOutputs["invoices"]["getMany"]["items"][number] = {
  id: "invoice-001",
  status: "DRAFT",
  number: "INV-001",
  date: new Date(),
  due: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  total: 1500.0,
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    invoices: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
    setStatus: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    invoices: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
    setStatus: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    invoices: {
      items: [mockInvoice],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
    setStatus: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    invoices: {
      items: [
        mockInvoice,
        {
          ...mockInvoice,
          id: "invoice-002",
          number: "INV-002",
          status: "OPEN",
          total: 2500.0,
        },
        {
          ...mockInvoice,
          id: "invoice-003",
          number: "INV-003",
          status: "PAID",
          total: 3500.0,
        },
        {
          ...mockInvoice,
          id: "invoice-004",
          number: "INV-004",
          status: "DUE",
          total: 4500.0,
          due: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        },
        {
          ...mockInvoice,
          id: "invoice-005",
          number: "INV-005",
          status: "VOID",
          total: 5500.0,
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
    setStatus: () => {},
  },
};
