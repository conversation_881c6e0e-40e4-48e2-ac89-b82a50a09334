import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListOrganizations from "@/components/tables/ListOrganizations";

const meta = {
  title: "Components/Tables/ListOrganizations",
  component: ListOrganizations,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListOrganizations>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockOrganization: RouterOutputs["organizations"]["getMany"]["items"][number] =
  {
    id: "org-001",
    name: "Healthcare Organization",
    type: "CLIENT",
    status: "ACTIVE",
    class: "PRIVATE",
    avatar: null,
  };

export const Loading: Story = {
  args: {
    loading: true,
    organizations: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    organizations: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    organizations: {
      items: [mockOrganization],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    organizations: {
      items: [
        mockOrganization,
        {
          ...mockOrganization,
          id: "org-002",
          name: "City Medical Center",
          type: "ACCOUNT",
          status: "PENDING",
          class: "GOVERNMENT",
        },
        {
          ...mockOrganization,
          id: "org-003",
          name: "County Hospital",
          type: "INTERNAL",
          status: "INACTIVE",
          class: "NONPROFIT",
        },
        {
          ...mockOrganization,
          id: "org-004",
          name: "Rehabilitation Center",
          type: "CLIENT",
          status: "SUSPENDED",
          class: "PRIVATE",
        },
        {
          ...mockOrganization,
          id: "org-005",
          name: "Family Practice Clinic",
          type: "ACCOUNT",
          status: "REJECTED",
          class: "GOVERNMENT",
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
