import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";
import { userEvent, within } from "storybook/test";

import type { JobPostStatus, ProviderStatus } from "@/api";

import ListSpecialties from "@/components/tables/ListSpecialties";

const meta: Meta<typeof ListSpecialties> = {
  title: "Components/Tables/ListSpecialties",
  component: ListSpecialties,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ListSpecialties>;

const sampleSpecialties = [
  {
    id: "1",
    name: "Emergency Medicine",
    description: "Acute care and emergency medical treatment",
    providers: [
      {
        id: "p1",
        status: "ACTIVE" as ProviderStatus,
        person: {
          id: "person1",
          firstName: "<PERSON>",
          lastName: "Doe",
          avatar: null,
        },
      },
      {
        id: "p2",
        status: "ACTIVE" as ProviderStatus,
        person: {
          id: "person2",
          firstName: "<PERSON>",
          lastName: "<PERSON>",
          avatar: null,
        },
      },
    ],
    jobs: [
      {
        id: "j1",
        summary: "ER Doctor",
        role: "DOCTO<PERSON>",
        status: "OPEN" as JobPostStatus,
      },
    ],
    shifts: [
      { id: "s1", summary: "Night Shift", role: "DOCTOR", status: "OPEN" },
      { id: "s2", summary: "Day Shift", role: "DOCTOR", status: "OPEN" },
    ],
    experiences: [
      {
        id: "e1",
        role: "DOCTOR",
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-12-31"),
      },
    ],
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "2",
    name: "Critical Care",
    description: "Intensive care and critical patient management",
    providers: [
      {
        id: "p3",
        status: "ACTIVE" as ProviderStatus,
        person: {
          id: "person3",
          firstName: "Mike",
          lastName: "Johnson",
          avatar: null,
        },
      },
    ],
    jobs: [
      {
        id: "j2",
        summary: "ICU Doctor",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
      {
        id: "j3",
        summary: "CCU Doctor",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    shifts: [
      {
        id: "s4",
        summary: "ICU Shift",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    experiences: [
      {
        id: "e2",
        role: "DOCTOR",
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-12-31"),
      },
    ],
    createdAt: new Date("2024-01-02"),
    updatedAt: new Date("2024-01-16"),
  },
  {
    id: "3",
    name: "Pediatrics",
    description: "Medical care for infants, children, and adolescents",
    providers: [
      {
        id: "p4",
        status: "ACTIVE" as ProviderStatus,
        person: {
          id: "person4",
          firstName: "Sarah",
          lastName: "Wilson",
          avatar: null,
        },
      },
    ],
    jobs: [
      {
        id: "j4",
        summary: "Pediatrician",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    shifts: [
      {
        id: "s5",
        summary: "Pediatric Shift",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    experiences: [
      {
        id: "e4",
        role: "DOCTOR",
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-12-31"),
      },
    ],
    createdAt: new Date("2024-01-03"),
    updatedAt: new Date("2024-01-17"),
  },
  {
    id: "4",
    name: "Family Medicine",
    description: "Comprehensive healthcare for individuals and families",
    providers: [
      {
        id: "p7",
        status: "ACTIVE" as ProviderStatus,
        person: {
          id: "person7",
          firstName: "Robert",
          lastName: "Brown",
          avatar: null,
        },
      },
    ],
    jobs: [
      {
        id: "j5",
        summary: "Family Doctor",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
      {
        id: "j6",
        summary: "GP",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    shifts: [
      {
        id: "s6",
        summary: "Morning Clinic",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
      {
        id: "s7",
        summary: "Evening Clinic",
        role: "DOCTOR",
        status: "OPEN" as JobPostStatus,
      },
    ],
    experiences: [
      {
        id: "e5",
        role: "DOCTOR",
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-12-31"),
      },
    ],
    createdAt: new Date("2024-01-04"),
    updatedAt: new Date("2024-01-18"),
  },
];

export const Default: Story = {
  args: {
    specialties: {
      items: sampleSpecialties.slice(0, 2),
      total: 2,
    },
    onUpdate: async (specialty) => {
      console.log("Updating specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
    onDelete: async (specialty) => {
      console.log("Deleting specialty:", specialty);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ specialties }, updateArgs] = useArgs();

    const handleUpdate = async (values: any) => {
      await args.onUpdate?.(values);
      // In a real app, you would update the data after successful API call
      console.log("Updated specialty:", values);
    };

    const handleDelete = async (values: { id: string }) => {
      await args.onDelete?.(values);
      // In a real app, you would remove the item after successful API call
      const updatedItems = specialties.items.filter(
        (specialty) => specialty.id !== values.id,
      );
      updateArgs({
        specialties: {
          items: updatedItems,
          total: updatedItems.length,
        },
      });
    };

    return (
      <ListSpecialties
        specialties={specialties}
        onUpdate={handleUpdate}
        onDelete={handleDelete}
      />
    );
  },
};

export const Empty: Story = {
  args: {
    specialties: {
      items: [],
      total: 0,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    specialties: {
      items: [],
      total: 0,
    },
  },
};

export const ManyItems: Story = {
  args: {
    specialties: {
      items: sampleSpecialties,
      total: sampleSpecialties.length,
    },
  },
};

export const WithInteraction: Story = {
  ...Default,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test search
    const searchInput = canvas.getByPlaceholderText(/search specialties/i);
    await userEvent.type(searchInput, "Emergency");
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Test table settings
    const settingsButton = canvas.getByLabelText(/table settings/i);
    await userEvent.click(settingsButton);

    // Test row selection
    const checkboxes = await canvas.findAllByRole("checkbox");
    if (checkboxes[1]) {
      await userEvent.click(checkboxes[1]);
    }
  },
};
