import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListJobs from "@/components/tables/ListJobs";

const meta = {
  title: "Components/Tables/ListJobs",
  component: ListJobs,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ListJobs>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockJob: RouterOutputs["jobs"]["getMany"]["items"][number] = {
  id: "job-001",
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  expiresAt: null,
  openedAt: new Date(),
  filledAt: null,
  cancelledAt: null,
  completedAt: null,
  expiredAt: null,
  status: "OPEN",
  mode: "INDEPENDENT",
  type: "PERMANENT",
  summary: "Senior Registered Nurse",
  description: "Full-time position for an experienced RN",
  paymentType: "HOURLY",
  paymentRate: 45.0,
  nightRate: 1.25,
  overtimeRate: 1.5,
  holidayRate: 2.0,
  organizationId: "org1",
  organization: {
    id: "org1",
    name: "Healthcare Organization",
    avatar: null,
  },
  locationId: "location-001",
  location: {
    id: "location-001",
    name: "Main Hospital",
    type: "HOSPITAL",
    description: "Main hospital campus",
    address: {
      formatted: "123 Medical Center Dr, New York, NY 10001",
      timeZone: "America/New_York",
      latitude: 40.7128,
      longitude: -74.006,
    },
  },
  scheduleId: "schedule-001",
  schedule: null,
  departmentId: "department-001",
  department: {
    name: "Emergency Medicine",
    type: "CLINICAL",
  },
  specialties: [],
  thread: null,
  offers: [],
  applications: [],
  _count: {
    specialties: 0,
    offers: 0,
    applications: 0,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    jobs: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    jobs: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    jobs: {
      items: [mockJob],
      total: 1,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    jobs: {
      items: [
        mockJob,
        {
          ...mockJob,
          id: "job-002",
          status: "PENDING",
          type: "TEMPORARY",
          summary: "ICU Nurse",
          description: "Temporary position for ICU nurse",
          paymentRate: 55.0,
          organization: {
            ...mockJob.organization,
            id: "org2",
            name: "City Medical Center",
          },
        },
        {
          ...mockJob,
          id: "job-003",
          status: "FILLED",
          type: "PER_DIEM",
          summary: "Emergency Room Physician",
          description: "Per diem position for ER physician",
          paymentRate: 120.0,
          organization: {
            ...mockJob.organization,
            id: "org3",
            name: "County Hospital",
          },
        },
        {
          ...mockJob,
          id: "job-004",
          status: "CANCELLED",
          type: "PERMANENT",
          summary: "Physical Therapist",
          description: "Full-time position for PT",
          paymentRate: 40.0,
          organization: {
            ...mockJob.organization,
            id: "org4",
            name: "Rehabilitation Center",
          },
        },
        {
          ...mockJob,
          id: "job-005",
          status: "COMPLETED",
          type: "TEMPORARY",
          summary: "Medical Assistant",
          description: "Temporary position for MA",
          paymentRate: 25.0,
          organization: {
            ...mockJob.organization,
            id: "org5",
            name: "Family Practice Clinic",
          },
        },
      ],
      total: 5,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
};
