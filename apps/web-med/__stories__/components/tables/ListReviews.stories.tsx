import type { Meta, StoryObj } from "@storybook/react";

import ListReviews from "@/components/tables/ListReviews";

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider = {
  id: "provider-001",
  person: {
    firstName: "<PERSON>",
    lastName: "Doe",
    avatar: null,
  },
} as const;

const mockReviewer = {
  firstName: "<PERSON>",
  lastName: "Smith",
  avatar: null,
} as const;

const mockShift = {
  id: "shift-001",
  summary: "Emergency Room Shift",
} as const;

const mockReview = {
  id: "review-001",
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  rating: 5,
  comment: "Great work and professionalism",
  provider: mockProvider,
  reviewer: mockReviewer,
  shift: mockShift,
  providerId: "provider-001",
  reviewerId: "reviewer-001",
  shiftId: "shift-001",
  organizationId: "org-001",
} as const;

const meta = {
  title: "Components/Tables/ListReviews",
  component: ListReviews,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  args: {
    loading: false,
    reviews: {
      items: [],
      total: 0,
    },
    pagination: mockPagination,
    setPagination: () => {},
  },
} satisfies Meta<typeof ListReviews>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    loading: true,
    reviews: {
      items: [],
      total: 0,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    reviews: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    reviews: {
      items: [mockReview],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    reviews: {
      items: [
        mockReview,
        {
          ...mockReview,
          id: "review-002",
          rating: 4,
          comment: "Good communication and reliability",
          reviewer: {
            ...mockReviewer,
            firstName: "Bob",
            lastName: "Johnson",
          },
        },
        {
          ...mockReview,
          id: "review-003",
          rating: 3,
          comment: "Average performance",
          reviewer: {
            ...mockReviewer,
            firstName: "Alice",
            lastName: "Brown",
          },
        },
        {
          ...mockReview,
          id: "review-004",
          rating: 5,
          comment: "Excellent attention to detail",
          reviewer: {
            ...mockReviewer,
            firstName: "Mike",
            lastName: "Wilson",
          },
        },
        {
          ...mockReview,
          id: "review-005",
          rating: 4,
          comment: "Very knowledgeable and helpful",
          reviewer: {
            ...mockReviewer,
            firstName: "Sarah",
            lastName: "Davis",
          },
        },
      ],
      total: 5,
    },
  },
};
