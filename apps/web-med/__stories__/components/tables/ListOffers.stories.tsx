import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ListOffers from "@/components/tables/ListOffers";

const meta = {
  title: "Components/Tables/ListOffers",
  component: ListOffers,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof ListOffers>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPagination = {
  pageIndex: 0,
  pageSize: 10,
};

const mockProvider = {
  id: "provider-001",
  person: {
    id: "person-001",
    firstName: "<PERSON>",
    lastName: "Doe",
    avatar: "https://via.placeholder.com/150",
  },
} as unknown as RouterOutputs["offers"]["getMany"]["items"][number]["provider"];

const mockJob = {
  id: "job-001",
  summary: "Full-time Emergency Room Physician",
} as RouterOutputs["offers"]["getMany"]["items"][number]["job"];

const mockOrganization = {
  id: "organization-001",
  name: "City General Hospital",
  avatar: "/placeholder.svg",
};

const mockOffer = {
  id: "offer-001",
  status: "PENDING",
  notes: "We would like to offer you a position as an Emergency Room Physician",
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
  createdAt: new Date(),
  provider: mockProvider,
  organization: mockOrganization,
  job: mockJob,
} as RouterOutputs["offers"]["getMany"]["items"][number];

export const Loading: Story = {
  args: {
    loading: true,
    offers: {
      items: [],
      total: 0,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    offers: {
      items: [],
      total: 0,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    offers: {
      items: [mockOffer],
      total: 1,
    },
  },
};

export const MultipleItems: Story = {
  args: {
    loading: false,
    offers: {
      items: [
        mockOffer,
        {
          ...mockOffer,
          id: "offer-002",
          status: "ACCEPTED",
          provider: {
            ...mockProvider,
            id: "provider-002",
            person: {
              ...mockProvider.person,
              id: "person-002",
              firstName: "Jane",
              lastName: "Smith",
            },
          },
          job: {
            ...mockJob,
            id: "job-002",
            summary: "Part-time ICU Nurse",
          },
        } as RouterOutputs["offers"]["getMany"]["items"][number],
        {
          ...mockOffer,
          id: "offer-003",
          status: "REJECTED",
          provider: {
            ...mockProvider,
            id: "provider-003",
            person: {
              ...mockProvider.person,
              id: "person-003",
              firstName: "Robert",
              lastName: "Johnson",
            },
          },
          job: {
            ...mockJob,
            id: "job-003",
            summary: "Pediatric Specialist",
          },
        } as RouterOutputs["offers"]["getMany"]["items"][number],
        {
          ...mockOffer,
          id: "offer-004",
          status: "WITHDRAWN",
          provider: {
            ...mockProvider,
            id: "provider-004",
            person: {
              ...mockProvider.person,
              id: "person-004",
              firstName: "Sarah",
              lastName: "Williams",
            },
          },
          job: {
            ...mockJob,
            id: "job-004",
            summary: "Surgical Technician",
          },
        } as RouterOutputs["offers"]["getMany"]["items"][number],
        {
          ...mockOffer,
          id: "offer-005",
          status: "PENDING",
          expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          provider: {
            ...mockProvider,
            id: "provider-005",
            person: {
              ...mockProvider.person,
              id: "person-005",
              firstName: "Michael",
              lastName: "Brown",
            },
          },
          job: {
            ...mockJob,
            id: "job-005",
            summary: "Radiology Technologist",
          },
        } as RouterOutputs["offers"]["getMany"]["items"][number],
      ],
      total: 5,
    },
  },
};
