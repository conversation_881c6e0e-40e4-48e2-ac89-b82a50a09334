import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ShiftStatusComponent, {
  ShiftStatusSkeleton, // Assuming skeleton exists
} from "@/components/shifts/ShiftStatus";

import { createMockMutation } from "../../helpers";
import { defaultMockShift as baseShift, createMockShift } from "./data";

const meta = {
  title: "Pages/Organizations/Shift/Components/ShiftStatus",
  component: ShiftStatusComponent,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "shift", "status"],
  // Add argTypes if needed to control props like openRateDialog function in Storybook UI
  argTypes: {
    openRateDialog: { action: "openedRateDialog" }, // Log action when called
  },
} satisfies Meta<typeof ShiftStatusComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

// Common args for mutations and the dialog function
const commonArgs = {
  cancel: createMockMutation(),
  approve: createMockMutation(),
  // Placeholder function for openRateDialog, action logged via argTypes
  openRateDialog: () => console.log("openRateDialog called"),
};

// --- Default Story (Usually Pending) ---

export const DefaultPending: Story = {
  args: {
    loading: false,
    shift: baseShift, // Base shift is likely pending
    ...commonArgs,
  } as any,
  name: "Status: Pending",
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    shift: undefined,
    // Mutations might not be relevant or passed during loading
  },
  render: () => <ShiftStatusSkeleton />,
};

// --- Error Story (Simulated) ---

// Create a minimal mock error object matching TRPCClientErrorLike structure
const mockTrpcError = (message: string): Partial<any> => ({
  message,
  // Add minimal required fields for the type checker
  shape: { data: {}, message: "", code: 0 },
  data: { code: "INTERNAL_SERVER_ERROR", httpStatus: 500 }, // Example values
});

export const WithError: Story = {
  args: {
    loading: false,
    shift: baseShift,
    cancel: {
      ...createMockMutation(),
      isError: true,
      error: mockTrpcError("Cancel failed"),
    },
    approve: {
      ...createMockMutation(),
      isError: true,
      error: mockTrpcError("Approve failed"),
    },
    openRateDialog: () => console.log("openRateDialog called"),
  } as any, // Keep as any for the overall args object
};

// --- Different Status Stories ---

export const Confirmed: Story = {
  args: {
    loading: false,
    shift: createMockShift({ status: "CONFIRMED", confirmedAt: new Date() }),
    ...commonArgs,
  } as any,
  name: "Status: Confirmed",
};

export const Active: Story = {
  args: {
    loading: false,
    shift: createMockShift({ status: "ACTIVE", startedAt: new Date() }),
    ...commonArgs,
  } as any,
  name: "Status: Active",
};

export const Completed: Story = {
  args: {
    loading: false,
    shift: createMockShift({ status: "COMPLETED", completedAt: new Date() }),
    ...commonArgs,
  } as any,
  name: "Status: Completed",
};

export const Cancelled: Story = {
  args: {
    loading: false,
    shift: createMockShift({ status: "CANCELLED", cancelledAt: new Date() }),
    ...commonArgs,
  } as any,
  name: "Status: Cancelled",
};

export const Approved: Story = {
  args: {
    loading: false,
    shift: createMockShift({ status: "APPROVED", approvedAt: new Date() }),
    ...commonArgs,
  } as any,
  name: "Status: Approved",
};
