import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  DepartmentType,
  FacilityType,
  IncidentSeverity,
  IncidentStatus,
  IncidentType,
  PaymentType,
  PersonRole,
  ProviderStatus,
  ShiftStatus,
} from "@/api";

// Define the full type for a shift based on RouterOutputs, ensuring all fields are covered
export type MockShiftData = NonNullable<RouterOutputs["shifts"]["get"]>;

// Define partial type for overrides - Simplified: only top-level overrides
export type PartialShiftData = Partial<MockShiftData>;

// Options for generation logic
export interface ShiftGenerationOptions {
  includeOrganization?: boolean;
  includeLocation?: boolean;
  includeDepartment?: boolean;
  includeProvider?: boolean;
  includeIncidents?: boolean | number; // boolean or specific count
}

/**
 * Creates a mock shift object with realistic data using faker-js.
 * Allows overriding specific fields and controlling optional data inclusion.
 */
export function createMockShift(
  overrides: PartialShiftData = {},
  options: ShiftGenerationOptions = {},
): MockShiftData {
  const status = overrides.status ?? faker.helpers.enumValue(ShiftStatus);
  const paymentType =
    overrides.paymentType ?? faker.helpers.enumValue(PaymentType);
  const isHourly = paymentType === PaymentType.HOURLY;

  // Base Rates & Hours
  const paymentRate = faker.number.float({
    min: isHourly ? 15 : 200,
    max: isHourly ? 100 : 1000,
    fractionDigits: 2,
  });
  const hours = isHourly
    ? faker.number.float({ min: 4, max: 12, fractionDigits: 1 })
    : 0;
  const overtimeHours = isHourly
    ? faker.number.float({ min: 0, max: 4, fractionDigits: 1 })
    : 0;
  const holidayHours = isHourly
    ? faker.number.float({ min: 0, max: hours, fractionDigits: 1 })
    : 0;
  const nightHours = isHourly
    ? faker.number.float({ min: 0, max: hours, fractionDigits: 1 })
    : 0;
  const bonusHours = isHourly
    ? faker.number.float({ min: 0, max: 2, fractionDigits: 1 })
    : 0;

  const overtimeRate = 1.5;
  const holidayRate = 2.0;
  const nightRate = 1.25;
  const bonusRate = 1.0; // Or could be a fixed bonus amount instead of rate * hours

  // Calculated Amounts
  let paymentAmount = 0;
  if (isHourly) {
    paymentAmount = paymentRate * hours;
  } else {
    paymentAmount = paymentRate; // Assuming fixed rate is the total amount
  }

  const overtimeAmount = isHourly
    ? paymentRate * overtimeRate * overtimeHours
    : 0;
  const holidayAmount = isHourly ? paymentRate * holidayRate * holidayHours : 0;
  const nightAmount = isHourly ? paymentRate * nightRate * nightHours : 0;
  const bonusAmount = isHourly ? paymentRate * bonusRate * bonusHours : 0;

  const paymentTotal =
    paymentAmount + overtimeAmount + holidayAmount + nightAmount + bonusAmount;

  const billingRate = 1.15;
  const billingAmount = paymentTotal * (billingRate * 100);
  const billingTotal = billingAmount;

  // Base Shift Structure - Ensure ALL fields from MockShiftData are present
  const baseShift: MockShiftData = {
    id: faker.string.uuid(),
    status,
    summary: faker.lorem.sentence(),
    scope: faker.lorem.words(5),
    role: faker.person.jobTitle(),
    confirmedAt: status === ShiftStatus.CONFIRMED ? faker.date.recent() : null,
    startedAt: status === ShiftStatus.ACTIVE ? faker.date.recent() : null,
    completedAt: status === ShiftStatus.COMPLETED ? faker.date.recent() : null,
    cancelledAt: status === ShiftStatus.CANCELLED ? faker.date.recent() : null,
    timeZone: faker.location.timeZone(),
    startDate: faker.date.future(),
    endDate: faker.date.future(),
    paymentType,
    paymentRate,
    paymentAmount,
    holidayAmount,
    nightAmount,
    overtimeAmount,
    overtimeRate,
    holidayRate,
    nightRate,
    paymentTotal,
    bonusAmount,
    bonusRate,
    bonusHours,
    hours,
    overtimeHours,
    nightTimeHours: nightHours, // Ensure consistency
    holidayTimeHours: holidayHours, // Ensure consistency
    billingRate,
    billingAmount,
    organizationId: faker.string.uuid(),
    // Default empty/undefined related objects
    organization: undefined,
    location: undefined,
    provider: undefined,
    department: undefined,
    specialties: [],
    job: undefined,
    invoice: undefined,
    actions: [],
    review: undefined,
    contacts: [],
    incidents: [],
  };

  // Apply simple overrides first
  const simpleOverrides = { ...overrides };
  const finalShift: MockShiftData = { ...baseShift, ...simpleOverrides };

  // --- Generate optional nested data ---
  // If an override exists for the entire nested object, use it. Otherwise, generate if option is true.

  if (overrides.hasOwnProperty("organization")) {
    finalShift.organization = overrides.organization;
  } else if (options.includeOrganization ?? true) {
    finalShift.organization = {
      id: finalShift.organizationId, // Use existing ID
      name: faker.company.name(),
      avatar: faker.image.avatar(),
    };
  } // else it remains undefined from baseShift

  if (overrides.hasOwnProperty("location")) {
    finalShift.location = overrides.location;
  } else if (options.includeLocation ?? true) {
    finalShift.location = {
      id: faker.string.uuid(),
      name: faker.company.name() + " Medical Center",
      type: faker.helpers.enumValue(FacilityType),
      address: {
        formatted: faker.location.streetAddress(true),
        timeZone: finalShift.timeZone, // Use shift's timezone
        latitude: faker.location.latitude(),
        longitude: faker.location.longitude(),
      },
    };
  } // else it remains undefined

  if (overrides.hasOwnProperty("department")) {
    finalShift.department = overrides.department;
  } else if ((options.includeDepartment ?? true) && finalShift.location) {
    // Department requires location
    finalShift.department = {
      id: faker.string.uuid(),
      name: faker.commerce.department(),
      type: faker.helpers.enumValue(DepartmentType),
      contacts: [],
    };
  } // else it remains undefined

  if (overrides.hasOwnProperty("provider")) {
    finalShift.provider = overrides.provider;
  } else if (options.includeProvider ?? true) {
    finalShift.provider = {
      id: faker.string.uuid(),
      title: faker.person.jobTitle(),
      status: faker.helpers.enumValue(ProviderStatus),
      person: {
        id: faker.string.uuid(),
        role: PersonRole.PROVIDER,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: faker.image.avatar(),
        email: faker.internet.email(),
        phone: faker.phone.number(),
      },
    };
  } // else it remains undefined

  if (overrides.hasOwnProperty("incidents")) {
    finalShift.incidents = overrides.incidents ?? []; // Use override or empty array if nullish
  } else if (options.includeIncidents) {
    const incidentCount =
      typeof options.includeIncidents === "number"
        ? options.includeIncidents
        : faker.number.int({ min: 1, max: 3 });
    finalShift.incidents = Array.from({ length: incidentCount }, () => ({
      id: faker.string.uuid(),
      type: faker.helpers.enumValue(IncidentType),
      status: faker.helpers.enumValue(IncidentStatus),
      severity: faker.helpers.enumValue(IncidentSeverity),
      description: faker.lorem.paragraph(),
      title: faker.lorem.sentence(),
      createdAt: faker.date.recent(),
    }));
  } else {
    finalShift.incidents = []; // Default to empty if not included and not overridden
  }

  return finalShift;
}

// Example pre-generated shifts (optional)
export const defaultMockShift = createMockShift();
export const completedMockShift = createMockShift({
  status: ShiftStatus.COMPLETED,
});
export const fixedMockShift = createMockShift({
  paymentType: PaymentType.FIXED,
});
export const shiftWithIncidents = createMockShift(
  {},
  { includeIncidents: true },
);
