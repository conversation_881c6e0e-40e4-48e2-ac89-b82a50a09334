import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ShiftStatus } from "@axa/database-medical";

import { Shift } from "@/components/shifts/Shift";

const meta: Meta<typeof Shift> = {
  title: "Components/Shifts/Shift",
  component: Shift,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof Shift>;

const baseShift = {
  id: "1",
  summary: "Emergency Room Nurse",
  description: "We need an experienced ER nurse for a busy shift.",
  status: "OPEN" as const,
  startDateTime: new Date("2023-07-15T09:00:00"),
  endDateTime: new Date("2023-07-15T17:00:00"),
  location: {
    name: "City General Hospital",
    address: "123 Medical Center Blvd, Cityville, ST 12345",
  },
  department: {
    name: "Emergency Department",
  },
  billingRate: 45,
  providerRate: 35,
};

export const Pending: Story = {
  args: {
    shift: { ...baseShift, status: ShiftStatus.PENDING },
  },
};

export const Confirmed: Story = {
  args: {
    shift: {
      ...baseShift,
      status: ShiftStatus.CONFIRMED,
      provider: {
        name: "Dr. <PERSON>",
      },
    },
  },
};

export const Active: Story = {
  args: {
    shift: {
      ...baseShift,
      status: "ACTIVE",
      provider: {
        name: "Dr. Jane Smith",
      },
    },
  },
};

export const Completed: Story = {
  args: {
    shift: {
      ...baseShift,
      status: "COMPLETED",
      provider: {
        name: "Dr. Jane Smith",
      },
    },
  },
};
