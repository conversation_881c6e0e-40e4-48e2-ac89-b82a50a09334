import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

// Import RouterOutputs to get correct types
import type { RouterOutputs } from "@/api";

import { PersonRole, ProviderStatus } from "@/api"; // Import enums if needed for mock data

import ShiftProviderCard from "@/components/shifts/ShiftProviderCard";

// Define the necessary data subset for props, mirroring the component
// Use NonNullable to ensure the nested objects exist for mock generation
// Explicitly defining these types here makes the story self-contained and clearer
type ProviderData = NonNullable<RouterOutputs["shifts"]["get"]["provider"]>;
type PersonData = NonNullable<ProviderData["person"]>;

// Mock data generation functions updated to match PersonData and ProviderData
const createMockPerson = (): PersonData => ({
  id: faker.string.uuid(),
  // Add fields required by PersonData (check RouterOutputs or component if unsure)
  role: faker.helpers.enumValue(PersonRole), // Example: Assuming role is needed
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  avatar: faker.image.avatar(),
  email: faker.internet.email(), // Example: Add email if needed
  phone: faker.phone.number(), // Example: Add phone if needed
  // Add other required fields from PersonData if necessary
});

const createMockProvider = (person: PersonData): ProviderData => ({
  id: faker.string.uuid(),
  title: faker.person.jobTitle(),
  status: faker.helpers.enumValue(ProviderStatus), // Example: Assuming status is needed
  person: person,
  // Add other required fields from ProviderData if necessary
});

// Storybook Meta configuration
const meta: Meta<typeof ShiftProviderCard> = {
  title: "Components/Shifts/ShiftProviderCard",
  component: ShiftProviderCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    provider: {
      control: "object",
      description:
        "The provider object containing details and nested person object.",
    },
    // Person is derived from provider, but defining it helps Storybook controls
    person: {
      control: "object",
      description: "The person object derived from the provider.",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// --- Stories --- //

// Default story data
const mockPerson1 = createMockPerson();
const mockProvider1 = createMockProvider(mockPerson1);

export const Default: Story = {
  args: {
    provider: mockProvider1,
    person: mockProvider1.person, // Pass person derived from provider
  },
};

// Story with no avatar
const mockPersonNoAvatar = createMockPerson();
mockPersonNoAvatar.avatar = null;
const mockProviderNoAvatar = createMockProvider(mockPersonNoAvatar);

export const NoAvatar: Story = {
  args: {
    provider: mockProviderNoAvatar,
    person: mockProviderNoAvatar.person,
  },
};

// Story simulating missing provider/person data
export const NoProvider: Story = {
  args: {
    provider: undefined,
    person: undefined,
  },
  render: (args) => (
    <div style={{ padding: "20px", border: "1px dashed grey" }}>
      {/* Render placeholder or message if needed, or just the component which should handle undefined props */}
      <ShiftProviderCard {...args} />
      <p style={{ marginTop: "10px", fontSize: "small", color: "grey" }}>
        Renders component with undefined props.
      </p>
    </div>
  ),
};

// Story with long text
const mockPersonLongName = createMockPerson();
mockPersonLongName.firstName = faker.lorem.words(3); // Long first name
mockPersonLongName.lastName = faker.lorem.words(3); // Long last name
const mockProviderLongName = createMockProvider(mockPersonLongName);
mockProviderLongName.title = faker.lorem.sentence(6); // Long title

export const LongText: Story = {
  args: {
    provider: mockProviderLongName,
    person: mockProviderLongName.person,
  },
};

// Story with no title
const mockPersonNoTitle = createMockPerson();
const mockProviderNoTitle = createMockProvider(mockPersonNoTitle);
mockProviderNoTitle.title = null; // No title

export const NoTitle: Story = {
  args: {
    provider: mockProviderNoTitle,
    person: mockProviderNoTitle.person,
  },
};
