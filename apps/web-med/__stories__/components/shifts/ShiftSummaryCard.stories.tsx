import type { Meta, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import ShiftSummaryCard from "@/components/shifts/ShiftSummaryCard";

import { createMockShift } from "./data";

const meta = {
  title: "Components/Shifts/ShiftSummaryCard",
  component: ShiftSummaryCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "350px" }}>
        {" "}
        {/* Adjust width */}
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ShiftSummaryCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    shift: createMockShift(),
  },
};

export const MissingDetails: Story = {
  args: {
    shift: createMockShift({
      role: "",
      scope: "",
      summary: "",
    }),
  },
};
