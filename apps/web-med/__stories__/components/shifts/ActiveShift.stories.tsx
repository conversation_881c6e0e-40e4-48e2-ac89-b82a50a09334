import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ShiftStatus } from "@/api";
import { ActiveShift } from "@/components/shifts/ActiveShift";

const meta: Meta<typeof ActiveShift> = {
  title: "Components/Shifts/ActiveShift",
  component: ActiveShift,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ActiveShift>;

const baseShift = {
  id: "1",
  summary: "Emergency Room Nurse",
  status: ShiftStatus.ACTIVE,
  startDateTime: new Date(new Date().setHours(new Date().getHours() - 1)),
  endDateTime: new Date(new Date().setHours(new Date().getHours() + 7)),
  billingRate: 45,
  providerRate: 35,
  isCheckedIn: false,
  location: {
    id: "loc1",
    name: "City General Hospital",
    type: "HOSPITAL",
    address: {
      formatted: "123 Medical Center Blvd, Cityville, ST 12345",
    },
  },
  department: {
    id: "dep1",
    name: "Emergency Department",
    type: "DEPARTMENT",
    description:
      "24/7 emergency care for critical and life-threatening conditions.",
  },
  organization: {
    id: "org1",
    name: "HealthCare Inc.",
    type: "FACILITY",
    avatar: "/placeholder.svg",
  },
};

export const NotCheckedIn: Story = {
  args: {
    shift: baseShift,
  },
};

export const CheckedIn: Story = {
  args: {
    shift: {
      ...baseShift,
      isCheckedIn: true,
    },
  },
};

export const ShiftEnded: Story = {
  args: {
    shift: {
      ...baseShift,
      status: ShiftStatus.COMPLETED,
      endDateTime: new Date(new Date().setHours(new Date().getHours() - 1)),
    },
  },
};

export const ShiftNotStarted: Story = {
  args: {
    shift: {
      ...baseShift,
      status: "FILLED" as ShiftStatusType,
      startDateTime: new Date(new Date().setHours(new Date().getHours() + 1)),
      endDateTime: new Date(new Date().setHours(new Date().getHours() + 9)),
    },
  },
};
