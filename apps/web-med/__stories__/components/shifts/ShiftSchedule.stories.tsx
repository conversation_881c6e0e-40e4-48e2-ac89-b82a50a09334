import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { ShiftStatus } from "@/api";
import { ShiftSchedule } from "@/components/shifts/ShiftSchedule";

const meta: Meta<typeof ShiftSchedule> = {
  title: "Components/Shifts/ShiftSchedule",
  component: ShiftSchedule,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ShiftSchedule>;

const baseShift = {
  id: "1",
  summary: "Emergency Room Nurse",
  status: ShiftStatus.OPEN,
  startDateTime: new Date("2023-07-15T09:00:00"),
  endDateTime: new Date("2023-07-15T17:00:00"),
  location: {
    name: "City General Hospital",
  },
  billingRate: 45,
};

const shifts = [
  baseShift,
  {
    ...baseShift,
    id: "2",
    startDateTime: new Date("2023-07-16T10:00:00"),
    endDateTime: new Date("2023-07-16T18:00:00"),
    status: ShiftStatus.FILLED,
  },
  {
    ...baseShift,
    id: "3",
    startDateTime: new Date("2023-07-17T08:00:00"),
    endDateTime: new Date("2023-07-17T16:00:00"),
    status: ShiftStatus.ACTIVE,
  },
  {
    ...baseShift,
    id: "4",
    startDateTime: new Date("2023-07-18T12:00:00"),
    endDateTime: new Date("2023-07-18T20:00:00"),
    status: ShiftStatus.OPEN,
  },
  {
    ...baseShift,
    id: "5",
    startDateTime: new Date("2023-07-19T14:00:00"),
    endDateTime: new Date("2023-07-19T22:00:00"),
    status: ShiftStatus.OPEN,
  },
];

export const Default: Story = {
  args: {
    shifts,
  },
};
