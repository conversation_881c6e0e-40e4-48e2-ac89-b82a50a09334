import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { DepartmentType, FacilityType, PaymentType, ShiftStatus } from "@/api";
// Import necessary enums
import ShiftFacilityCard from "@/components/shifts/ShiftFacilityCard";

import { createMockShift } from "./data";

const meta = {
  title: "Components/Shifts/ShiftFacilityCard",
  component: ShiftFacilityCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "350px" }}>
        {" "}
        {/* Adjust width */}
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ShiftFacilityCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    shift: createMockShift(
      {},
      { includeLocation: true, includeDepartment: true },
    ),
  },
};

export const MissingDepartment: Story = {
  args: {
    shift: createMockShift(
      { department: undefined },
      { includeLocation: true },
    ),
  },
};

export const MissingAddress: Story = {
  args: {
    shift: createMockShift({
      location: {
        id: faker.string.uuid(),
        name: faker.company.name() + " Clinic (No Address)",
        type: FacilityType.CLINIC,
        address: {
          formatted: "",
          timeZone: "",
          latitude: 0,
          longitude: 0,
        },
      },
    }),
  },
};

export const MissingLocation: Story = {
  args: {
    shift: createMockShift({ location: undefined, department: undefined }),
  },
};
