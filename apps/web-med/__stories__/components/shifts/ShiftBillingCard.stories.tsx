import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { PaymentType } from "@/api";
import ShiftBillingCard from "@/components/shifts/ShiftBillingCard";

import { createMockShift } from "./data";

const meta = {
  title: "Components/Shifts/ShiftBillingCard",
  component: ShiftBillingCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "350px" }}>
        {" "}
        {/* Adjust width as needed */}
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ShiftBillingCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Hourly: Story = {
  args: {
    shift: createMockShift({ paymentType: PaymentType.HOURLY }),
  },
};

export const Fixed: Story = {
  args: {
    shift: createMockShift({ paymentType: PaymentType.FIXED }),
  },
};

// Generate a shift with non-zero additional amounts for the story
const shiftWithExtras = createMockShift({ paymentType: PaymentType.HOURLY });
shiftWithExtras.overtimeAmount = shiftWithExtras.paymentRate * 1.5 * 2; // Example: 2 hours OT
shiftWithExtras.holidayAmount = shiftWithExtras.paymentRate * 2.0 * 4; // Example: 4 hours Holiday
shiftWithExtras.nightAmount = shiftWithExtras.paymentRate * 1.25 * 3; // Example: 3 hours Night
shiftWithExtras.paymentTotal =
  shiftWithExtras.paymentAmount +
  shiftWithExtras.overtimeAmount +
  shiftWithExtras.holidayAmount +
  shiftWithExtras.nightAmount;

export const HourlyWithAdditionalPay: Story = {
  args: {
    shift: shiftWithExtras,
  },
};
