import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { action } from "storybook/actions";

import type { RouterOutputs } from "@/api";
import type { api } from "@/api/client";

import { ShiftStatus } from "@/api";
import ShiftIncidentsCard from "@/components/shifts/ShiftIncidentsCard";

import { createMockShift } from "./data";

const meta = {
  title: "Components/Shifts/ShiftIncidentsCard",
  component: ShiftIncidentsCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "600px" }}>
        {" "}
        {/* Wider width for incident details */}
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ShiftIncidentsCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock createIncident mutation - basic version
const mockCreateIncident = {
  mutate: action("createIncident.mutate"),
  mutateAsync: action("createIncident.mutateAsync"),
  error: null,
  isError: false,
  isLoading: false,
  isSuccess: false,
  isIdle: true,
  reset: action("createIncident.reset"),
  status: "idle",
} as unknown as ReturnType<typeof api.incidents.create.useMutation>;

export const Default: Story = {
  args: {
    shift: createMockShift({}, { includeIncidents: true }),
    createIncident: mockCreateIncident,
  },
};

export const NoIncidents: Story = {
  args: {
    shift: createMockShift({ incidents: [] }),
    createIncident: mockCreateIncident,
  },
};

export const NotActiveOrCompleted: Story = {
  args: {
    shift: createMockShift(
      { status: ShiftStatus.PENDING },
      { includeIncidents: false },
    ),
    createIncident: mockCreateIncident,
  },
};
