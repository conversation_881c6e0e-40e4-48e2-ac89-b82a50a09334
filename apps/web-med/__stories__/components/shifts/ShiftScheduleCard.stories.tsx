import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ShiftScheduleCard from "@/components/shifts/ShiftScheduleCard";

import { createMockShift } from "./data";

const meta = {
  title: "Components/Shifts/ShiftScheduleCard",
  component: ShiftScheduleCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "350px" }}>
        {" "}
        {/* Adjust width */}
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ShiftScheduleCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper to create a date with a specific hour
const dateWithHour = (hour: number): Date => {
  const date = new Date();
  date.setHours(hour, 0, 0, 0); // Set hour, reset minutes/seconds/ms
  return date;
};

export const DayShift: Story = {
  args: {
    // Generate a shift starting at 9 AM
    shift: createMockShift({ startDate: dateWithHour(9) }),
  },
};

export const AfternoonShift: Story = {
  args: {
    // Generate a shift starting at 2 PM (14:00)
    shift: createMockShift({ startDate: dateWithHour(14) }),
  },
};

export const NightShift: Story = {
  args: {
    // Generate a shift starting at 10 PM (22:00)
    shift: createMockShift({ startDate: dateWithHour(22) }),
  },
};

export const MissingTimeZone: Story = {
  args: {
    shift: createMockShift({ timeZone: "" }),
  },
};

// Add more variations if needed, e.g., different date formats, invalid dates
