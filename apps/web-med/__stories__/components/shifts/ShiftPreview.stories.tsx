import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ShiftPreview } from "@/components/shifts/ShiftPreview";

const meta: Meta<typeof ShiftPreview> = {
  title: "Components/Shifts/ShiftPreview",
  component: ShiftPreview,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof ShiftPreview>;

const baseShift = {
  id: "1",
  summary: "Emergency Room Nurse",
  status: "OPEN" as const,
  startDateTime: new Date("2023-07-15T09:00:00"),
  endDateTime: new Date("2023-07-15T17:00:00"),
  location: {
    name: "City General Hospital",
  },
  billingRate: 45,
};

export const Default: Story = {
  args: {
    shift: baseShift,
  },
};

export const Filled: Story = {
  args: {
    shift: {
      ...baseShift,
      status: "FILLED",
    },
  },
};

export const Active: Story = {
  args: {
    shift: {
      ...baseShift,
      status: "ACTIVE",
    },
  },
};
