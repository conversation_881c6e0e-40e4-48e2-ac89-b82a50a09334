import type { <PERSON>a, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectMedicalRole,
  SelectMedicalRoleField,
} from "@/components/selectors/SelectMedicalRole";

import { medicalRoles } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectMedicalRole",
  component: SelectMedicalRole,
  parameters: {
    msw: {
      handlers: [medicalRoles],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectMedicalRole>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select a medical role",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      medicalRole: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectMedicalRoleField />
    </FormWrapper>
  ),
};

export const Loading: Story = {
  args: {
    placeholder: "Select a medical role",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a medical role",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a medical role",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a medical role",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a medical role",
    size: "md",
  },
};
