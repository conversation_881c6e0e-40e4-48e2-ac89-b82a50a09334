import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { ShiftStatus } from "@/api";
import {
  SelectShift,
  SelectShiftField,
} from "@/components/selectors/SelectShift";

import { shifts } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectShift",
  component: SelectShift,
  parameters: {
    msw: {
      handlers: [shifts],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectShift>;

export default meta;
type Story = StoryObj<typeof SelectShift>;

export const Default: Story = {
  args: {
    placeholder: "Select a shift",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      shift: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectShiftField />
    </FormWrapper>
  ),
};

// Note: The following stories use type assertions because the component props
// don't explicitly define these filter properties, but they are supported by the API

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select a shift",
    providerId: "provider123",
  } as any, // Type assertion to avoid type error
};

export const WithLocationFilter: Story = {
  args: {
    placeholder: "Select a shift",
    locationId: "location123",
  } as any, // Type assertion to avoid type error
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a shift",
    status: ShiftStatus.ACTIVE,
  } as any, // Type assertion to avoid type error
};

export const Loading: Story = {
  args: {
    placeholder: "Select a shift",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a shift",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a shift",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a shift",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a shift",
    size: "md",
  },
};
