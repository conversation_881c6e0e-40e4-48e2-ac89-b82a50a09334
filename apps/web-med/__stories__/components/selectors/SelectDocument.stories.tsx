import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectDocument,
  SelectDocumentField,
} from "@/components/selectors/SelectDocument";

import { documents } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectDocument",
  component: SelectDocument,
  parameters: {
    msw: {
      handlers: [documents],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectDocument>;

export default meta;
type Story = StoryObj<typeof SelectDocument>;

export const Default: Story = {
  args: {
    placeholder: "Select a document",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      document: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectDocumentField />
    </FormWrapper>
  ),
};

export const WithOwnerFilter: Story = {
  args: {
    placeholder: "Select a document",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a document",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a document",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a document",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a document",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a document",
    size: "md",
  },
};
