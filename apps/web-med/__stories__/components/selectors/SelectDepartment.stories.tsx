import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectDepartment,
  SelectDepartmentField,
} from "@/components/selectors/SelectDepartment";

import { departments } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectDepartment",
  component: SelectDepartment,
  parameters: {
    msw: {
      handlers: [departments],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectDepartment>;

export default meta;
type Story = StoryObj<typeof SelectDepartment>;

export const Default: Story = {
  args: {
    placeholder: "Select a department",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      department: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectDepartmentField />
    </FormWrapper>
  ),
};

export const WithFacilityFilter: Story = {
  args: {
    placeholder: "Select a department",
    facilityId: "facility123",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a department",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a department",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a department",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a department",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a department",
    size: "md",
  },
};
