import type { <PERSON>a, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { ApplicationStatus } from "@/api";
import {
  SelectApplication,
  SelectApplicationField,
} from "@/components/selectors/SelectApplication";

import { applications } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectApplication",
  component: SelectApplication,
  parameters: {
    msw: {
      handlers: [applications],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectApplication>;

export default meta;
type Story = StoryObj<typeof SelectApplication>;

export const Default: Story = {
  args: {
    placeholder: "Select an application",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      application: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectApplicationField />
    </FormWrapper>
  ),
};

export const WithJobFilter: Story = {
  args: {
    placeholder: "Select an application",
    jobId: "job1",
  } as any, // Type assertion to avoid type error
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select an application",
    providerId: "provider1",
  } as any, // Type assertion to avoid type error
};

export const Loading: Story = {
  args: {
    placeholder: "Select an application",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select an application",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select an application",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select an application",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select an application",
    size: "md",
  },
};
