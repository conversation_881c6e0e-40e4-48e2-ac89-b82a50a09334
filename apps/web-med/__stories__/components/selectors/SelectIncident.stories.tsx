import type { <PERSON>a, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { IncidentSeverity, IncidentType } from "@/api";
import {
  SelectIncident,
  SelectIncidentField,
} from "@/components/selectors/SelectIncident";

import { incidents } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectIncident",
  component: SelectIncident,
  parameters: {
    msw: {
      handlers: [incidents],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectIncident>;

export default meta;
type Story = StoryObj<typeof SelectIncident>;

export const Default: Story = {
  args: {
    placeholder: "Select an incident",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      incident: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectIncidentField />
    </FormWrapper>
  ),
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select an incident",
    providerId: "provider123",
  },
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select an incident",
    organizationId: "org123",
  },
};

export const WithSeverityFilter: Story = {
  args: {
    placeholder: "Select an incident",
    severity: IncidentSeverity.MAJOR,
  },
};

export const WithTypeFilter: Story = {
  args: {
    placeholder: "Select an incident",
    type: IncidentType.HEALTH,
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select an incident",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select an incident",
    enabled: false,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select an incident",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select an incident",
    size: "md",
  },
};
