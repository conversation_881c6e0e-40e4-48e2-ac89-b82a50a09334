import type { <PERSON>a, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { JobPositionStatus } from "@/api";
import {
  SelectPosition,
  SelectPositionField,
} from "@/components/selectors/SelectPosition";

import { positions } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectPosition",
  component: SelectPosition,
  parameters: {
    msw: {
      handlers: [positions],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectPosition>;

export default meta;
type Story = StoryObj<typeof SelectPosition>;

export const Default: Story = {
  args: {
    placeholder: "Select a position",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      position: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectPositionField />
    </FormWrapper>
  ),
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select a position",
    providerId: "provider123",
  },
};

export const WithLocationFilter: Story = {
  args: {
    placeholder: "Select a position",
    locationId: "location123",
  },
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a position",
    organizationId: "org123",
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a position",
    status: JobPositionStatus.ACTIVE,
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a position",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a position",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a position",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a position",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a position",
    size: "md",
  },
};
