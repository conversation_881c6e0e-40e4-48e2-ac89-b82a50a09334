import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectFacility,
  SelectFacilityField,
} from "@/components/selectors/SelectFacility";

import { facilities } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectFacility",
  component: SelectFacility,
  parameters: {
    msw: {
      handlers: [facilities],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectFacility>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select a facility",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      facility: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectFacilityField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a facility",
    organizationId: "org123",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a facility",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a facility",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a facility",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a facility",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a facility",
    size: "md",
  },
};
