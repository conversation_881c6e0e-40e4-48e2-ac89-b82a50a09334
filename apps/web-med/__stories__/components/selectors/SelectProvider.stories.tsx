import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { ProviderStatus } from "@/api";
import {
  SelectProvider,
  SelectProviderField,
} from "@/components/selectors/SelectProvider";

import { providers } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectProvider",
  component: SelectProvider,
  parameters: {
    msw: {
      handlers: [providers],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectProvider>;

export default meta;
type Story = StoryObj<typeof SelectProvider>;

export const Default: Story = {
  args: {
    placeholder: "Select a provider",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      provider: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectProviderField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a provider",
    organizationId: "org123",
  } as any, // Type assertion to avoid type error
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a provider",
    status: ProviderStatus.ACTIVE,
  } as any, // Type assertion to avoid type error
};

export const Loading: Story = {
  args: {
    placeholder: "Select a provider",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a provider",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a provider",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a provider",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a provider",
    size: "md",
  },
};
