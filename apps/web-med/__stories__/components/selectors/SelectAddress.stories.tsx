import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectAddress,
  SelectAddressField,
} from "@/components/selectors/SelectAddress";

import { addresses } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectAddress",
  component: SelectAddress,
  parameters: {
    msw: {
      handlers: [addresses],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectAddress>;

export default meta;
type Story = StoryObj<typeof SelectAddress>;

export const Default: Story = {
  args: {
    placeholder: "Select an address",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      address: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectAddressField />
    </FormWrapper>
  ),
};

export const Loading: Story = {
  args: {
    placeholder: "Select an address",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select an address",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select an address",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select an address",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select an address",
    size: "md",
  },
};
