import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectQualification,
  SelectQualificationField,
} from "@/components/selectors/SelectQualification";

import { qualifications } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectQualification",
  component: SelectQualification,
  parameters: {
    msw: {
      handlers: [qualifications],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectQualification>;

export default meta;
type Story = StoryObj<typeof SelectQualification>;

export const Default: Story = {
  args: {
    placeholder: "Select a qualification",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      qualification: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectQualificationField />
    </FormWrapper>
  ),
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select a qualification",
    providerId: "provider123",
  },
};

export const WithTypeFilter: Story = {
  args: {
    placeholder: "Select a qualification",
    type: "LICENSE",
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a qualification",
    status: "APPROVED",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a qualification",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a qualification",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a qualification",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a qualification",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a qualification",
    size: "md",
  },
};
