import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectContact,
  SelectContactField,
} from "@/components/selectors/SelectContact";

import { contacts } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectContact",
  component: SelectContact,
  parameters: {
    msw: {
      handlers: [contacts],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectContact>;

export default meta;
type Story = StoryObj<typeof SelectContact>;

export const Default: Story = {
  args: {
    placeholder: "Select a contact",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      contact: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectContactField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a contact",
    organizationId: "org123",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a contact",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a contact",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a contact",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a contact",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a contact",
    size: "md",
  },
};
