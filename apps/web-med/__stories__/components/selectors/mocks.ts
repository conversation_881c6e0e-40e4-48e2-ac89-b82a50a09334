import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  ApplicationStatus,
  ContractStatus,
  ContractType,
  DepartmentType,
  FacilityType,
  IncidentSeverity,
  IncidentType,
  JobPositionStatus,
  JobPostStatus,
  JobPostType,
  OfferStatus,
  OrganizationStatus,
  OrganizationType,
  ProviderStatus,
  QualificationStatus,
  QualificationType,
  ShiftStatus,
} from "@/api";
import { trpcMsw } from "@/api/mock";

export const addresses = trpcMsw.addresses.getMany.query(({ input: _ }) => {
  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        formatted: faker.location.streetAddress({ useFullAddress: true }),
        street: faker.location.street(),
        city: faker.location.city(),
        state: faker.location.state({ abbreviated: true }),
        postal: faker.location.zipCode(),
        country: faker.location.country(),
        latitude: faker.location.latitude(),
        longitude: faker.location.longitude(),
        timeZone: faker.helpers.arrayElement([
          "America/New_York",
          "America/Chicago",
          "America/Denver",
          "America/Los_Angeles",
          "Europe/London",
        ]),
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["addresses"]["getMany"];
});

export const providers = trpcMsw.providers.getMany.query(({ input }) => {
  const statuses = [
    ProviderStatus.ACTIVE,
    ProviderStatus.INACTIVE,
    ProviderStatus.PENDING,
    ProviderStatus.SUSPENDED,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        title: faker.person.prefix(),
        status: input.status ?? faker.helpers.arrayElement(statuses),
        person: {
          id: faker.string.uuid(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          email: faker.internet.email(),
          phone: faker.phone.number(),
          avatar: faker.image.avatar(),
          createdAt: faker.date.past(),
          updatedAt: faker.date.recent(),
          organization: input.organizationId
            ? {
                id: input.organizationId,
                name: faker.company.name(),
                avatar: null,
              }
            : {
                id: faker.string.uuid(),
                name: faker.company.name(),
                avatar: null,
              },
        },
        specialties: Array.from({ length: 2 }, () => ({
          id: faker.string.uuid(),
          name: faker.person.jobType(),
          createdAt: faker.date.past(),
          updatedAt: faker.date.recent(),
        })),
        qualifications: Array.from({ length: 2 }, () => ({
          id: faker.string.uuid(),
          name: faker.person.jobArea(),
          createdAt: faker.date.past(),
          updatedAt: faker.date.recent(),
        })),
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["providers"]["getMany"];
});

export const organizations = trpcMsw.organizations.getMany.query(() => {
  const organizationTypes = [
    OrganizationType.ACCOUNT,
    OrganizationType.CLIENT,
    OrganizationType.INTERNAL,
  ];

  const status = [
    OrganizationStatus.ACTIVE,
    OrganizationStatus.INACTIVE,
    OrganizationStatus.PENDING,
    OrganizationStatus.REJECTED,
    OrganizationStatus.SUSPENDED,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        name: faker.company.name(),
        description: faker.company.catchPhrase(),
        avatar: faker.image.avatar(),
        type: faker.helpers.arrayElement(organizationTypes),
        status: faker.helpers.arrayElement(status),
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent(),
        deletedAt: null,
        contacts: [],
        actions: [],
        documents: [],
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["organizations"]["getMany"];
});

export const people = trpcMsw.people.getMany.query(({ input: _ }) => {
  const organizations = ["org1", "org2", "org3", "org4", "org5"];
  return {
    items: [
      ...Array.from({ length: 5 }, (_, i) => ({
        id: faker.string.uuid(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        phone: faker.phone.number(),
        avatar: faker.image.avatar(),
        createdAt: new Date("2023-01-15"),
        updatedAt: new Date("2023-01-15"),
        organization: {
          id: organizations[i % organizations.length],
          name: faker.company.name(),
          avatar: null,
        },
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["people"]["getMany"];
});

export const specialties = trpcMsw.specialties.getMany.query(() => {
  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        name: faker.lorem.words(2),
        description: faker.lorem.sentence(),
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent(),
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["specialties"]["getMany"];
});

export const qualifications = trpcMsw.qualifications.getMany.query(
  ({ input }) => {
    const types: QualificationType[] = [
      QualificationType.CERTIFICATE,
      QualificationType.DEGREE,
      QualificationType.LICENSE,
      QualificationType.OTHER,
    ];
    const statuses: QualificationStatus[] = [
      QualificationStatus.APPROVED,
      QualificationStatus.EXPIRED,
      QualificationStatus.PENDING,
      QualificationStatus.REJECTED,
    ];

    return {
      items: [
        ...Array.from({ length: 5 }, () => ({
          id: faker.string.uuid(),
          name: faker.lorem.words(3),
          description: faker.lorem.sentence(),
          type: input.type ?? faker.helpers.arrayElement(types),
          status: input.status ?? faker.helpers.arrayElement(statuses),
          providerId: input.providerId ?? faker.string.uuid(),
          issuer: faker.company.name(),
          issuedAt: faker.date.past(),
          expiresAt: faker.date.future(),
          createdAt: new Date(),
          updatedAt: new Date(),
          provider: input.providerId
            ? {
                id: input.providerId,
                status: "ACTIVE",
                person: {
                  id: faker.string.uuid(),
                  firstName: faker.person.firstName(),
                  lastName: faker.person.lastName(),
                  avatar: faker.image.avatar(),
                },
              }
            : null,
        })),
      ],
      total: 5,
    } as unknown as RouterOutputs["qualifications"]["getMany"];
  },
);

export const positions = trpcMsw.jobs.positions.getMany.query(({ input }) => {
  const statuses = [
    JobPositionStatus.ACTIVE,
    JobPositionStatus.PENDING,
    JobPositionStatus.INACTIVE,
    JobPositionStatus.COMPLETED,
    JobPositionStatus.TERMINATED,
  ];

  const types = [
    JobPostType.PERMANENT,
    JobPostType.TEMPORARY,
    JobPostType.PER_DIEM,
  ];

  const paymentTypes = ["HOURLY", "DAILY", "WEEKLY", "MONTHLY", "ANNUALLY"];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        role: faker.person.jobTitle(),
        summary: faker.company.catchPhrase(),
        scope: faker.lorem.paragraph(),
        status: input.status ?? faker.helpers.arrayElement(statuses),
        type: faker.helpers.arrayElement(types),
        paymentType: faker.helpers.arrayElement(paymentTypes),
        paymentAmount: faker.number.int({ min: 50, max: 200 }),
        providerId: input.providerId ?? faker.string.uuid(),
        locationId: input.locationId ?? faker.string.uuid(),
        organizationId: input.organizationId ?? faker.string.uuid(),
        provider: input.providerId
          ? {
              id: input.providerId,
              title: faker.person.prefix(),
              status: "ACTIVE",
              person: {
                id: faker.string.uuid(),
                firstName: faker.person.firstName(),
                lastName: faker.person.lastName(),
                avatar: faker.image.avatar(),
              },
            }
          : {
              id: faker.string.uuid(),
              title: faker.person.prefix(),
              status: "ACTIVE",
              person: {
                id: faker.string.uuid(),
                firstName: faker.person.firstName(),
                lastName: faker.person.lastName(),
                avatar: faker.image.avatar(),
              },
            },
        location: input.locationId
          ? {
              id: input.locationId,
              name: faker.company.name(),
              address: {
                formatted: faker.location.streetAddress(),
              },
            }
          : {
              id: faker.string.uuid(),
              name: faker.company.name(),
              address: {
                formatted: faker.location.streetAddress(),
              },
            },
        organization: input.organizationId
          ? {
              id: input.organizationId,
              name: faker.company.name(),
              avatar: faker.image.avatar(),
            }
          : null,
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["jobs"]["positions"]["getMany"];
});

export const contacts = trpcMsw.contacts.getMany.query(({ input }) => {
  const contactRoles = [
    "Primary Contact",
    "Emergency Contact",
    "Administrative Contact",
    "Billing Contact",
    "Technical Contact",
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        role: faker.helpers.arrayElement(contactRoles),
        person: {
          id: faker.string.uuid(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          email: faker.internet.email(),
          phone: faker.phone.number(),
          avatar: faker.image.avatar(),
        },
        organization: input.organizationId
          ? {
              id: input.organizationId,
              name: faker.company.name(),
              avatar: null,
            }
          : {
              id: faker.string.uuid(),
              name: faker.company.name(),
              avatar: null,
            },
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["contacts"]["getMany"];
});

export const shifts = trpcMsw.shifts.getMany.query(({ input }) => {
  const statuses = [
    ShiftStatus.ACTIVE,
    ShiftStatus.APPROVED,
    ShiftStatus.CANCELLED,
    ShiftStatus.COMPLETED,
    ShiftStatus.CONFIRMED,
    ShiftStatus.PENDING,
    ShiftStatus.REJECTED,
  ] as ShiftStatus[];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        summary: `${faker.helpers.arrayElement(["Morning", "Evening", "Night", "Weekend"])} Shift - ${faker.company.buzzNoun()}`,
        status: input.status ?? faker.helpers.arrayElement(statuses),
        startDate: faker.date.future(),
        endDate: faker.date.future(),
        timeZone: faker.helpers.arrayElement([
          "America/New_York",
          "America/Chicago",
          "America/Los_Angeles",
        ]),
        role: faker.person.jobTitle(),
        paymentTotal: faker.number.int({ min: 100, max: 1000 }),
        provider: {
          id: faker.string.uuid(),
          title: faker.person.prefix(),
          status: "ACTIVE",
          person: {
            id: faker.string.uuid(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            avatar: faker.image.avatar(),
          },
        },
        location: {
          id: faker.string.uuid(),
          name: faker.company.name(),
          address: {
            formatted: faker.location.streetAddress(),
          },
        },
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["shifts"]["getMany"];
});

export const incidents = trpcMsw.incidents.getMany.query(({ input }) => {
  const severities: IncidentSeverity[] = [
    IncidentSeverity.CRITICAL,
    IncidentSeverity.MAJOR,
    IncidentSeverity.MINOR,
  ];
  const types: IncidentType[] = [
    IncidentType.ENVIRONMENT,
    IncidentType.HEALTH,
    IncidentType.OTHER,
    IncidentType.SAFETY,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        title: faker.lorem.sentence(3),
        description: faker.lorem.paragraph(),
        severity: input.severity ?? faker.helpers.arrayElement(severities),
        type: input.type ?? faker.helpers.arrayElement(types),
        providerId: input.providerId ?? null,
        organizationId: input.organizationId ?? null,
        shiftId: input.shiftId ?? null,
        createdAt: new Date(),
        updatedAt: new Date(),
        reportedAt: new Date(),
        resolvedAt: faker.datatype.boolean() ? new Date() : null,
        status: "OPEN",
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["incidents"]["getMany"];
});

export const departments = trpcMsw.departments.getMany.query(({ input }) => {
  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        name: faker.commerce.department(),
        description: faker.lorem.sentence(),
        type: faker.helpers.arrayElement([
          DepartmentType.CENTER,
          DepartmentType.DEPARTMENT,
          DepartmentType.INSTITUTE,
          DepartmentType.ROOM,
          DepartmentType.UNIT,
          DepartmentType.WARD,
          DepartmentType.OTHER,
        ]),
        facilityId: input.facilityId ?? faker.string.uuid(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["departments"]["getMany"];
});

export const facilities = trpcMsw.locations.getMany.query(({ input }) => {
  const types = [
    FacilityType.HOSPITAL,
    FacilityType.CLINIC,
    FacilityType.OFFICE,
    FacilityType.LAB,
    FacilityType.PHARMACY,
    FacilityType.IMAGING,
    FacilityType.REHABILITATION,
    FacilityType.OTHER,
  ];

  const timeZones = [
    "America/New_York",
    "America/Chicago",
    "America/Denver",
    "America/Los_Angeles",
    "Europe/London",
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        name: faker.company.name(),
        type: faker.helpers.arrayElement(types),
        description: faker.company.catchPhrase(),
        address: {
          id: faker.string.uuid(),
          formatted: faker.location.streetAddress({
            useFullAddress: true,
          }),
          timeZone: faker.helpers.arrayElement(timeZones),
          latitude: faker.location.latitude(),
          longitude: faker.location.longitude(),
        },
        organizationId: input.organizationId ?? null,
        organization: input.organizationId
          ? {
              id: input.organizationId,
              name: faker.company.name(),
              avatar: null,
            }
          : null,
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["locations"]["getMany"];
});

export const documents = trpcMsw.documents.getMany.query(({ input: _ }) => {
  const documentTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];

  const documentExtensions = [".pdf", ".jpg", ".png", ".docx", ".xlsx"];

  return {
    items: [
      ...Array.from({ length: 5 }, () => {
        const extension = faker.helpers.arrayElement(documentExtensions);
        const type = faker.helpers.arrayElement(documentTypes);
        return {
          id: faker.string.uuid(),
          name: `${faker.word.words(3)}${extension}`,
          type,
          url: `https://example.com/documents/${faker.lorem.slug(3)}${extension}`,
          size: faker.number.int({ min: 100000, max: 5000000 }),
          createdAt: faker.date.past(),
          updatedAt: faker.date.recent(),
        };
      }),
    ],
    total: 5,
  } as unknown as RouterOutputs["documents"]["getMany"];
});

export const jobs = trpcMsw.jobs.getMany.query(({ input }) => {
  const statuses = [
    JobPostStatus.DRAFT,
    JobPostStatus.PUBLISHED,
    JobPostStatus.FILLED,
    JobPostStatus.EXPIRED,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        summary: faker.company.catchPhrase(),
        role: faker.person.jobTitle(),
        status: faker.helpers.arrayElement(statuses),
        organizationId: input.organizationId ?? faker.string.uuid(),
        createdAt: new Date(),
        updatedAt: new Date(),
        publishedAt: faker.date.past(),
        expiresAt: faker.date.future(),
        organization: input.organizationId
          ? {
              id: input.organizationId,
              name: faker.company.name(),
              description: faker.company.catchPhrase(),
              avatar: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }
          : null,
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["jobs"]["getMany"];
});

export const applications = trpcMsw.applications.getMany.query(({ input }) => {
  const statuses = [
    ApplicationStatus.PENDING,
    ApplicationStatus.ACCEPTED,
    ApplicationStatus.REJECTED,
    ApplicationStatus.WITHDRAWN,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        notes: faker.lorem.paragraph(),
        status: input.status ?? faker.helpers.arrayElement(statuses),
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationId: faker.string.uuid(),
        providerId: input.providerId ?? faker.string.uuid(),
        jobId: input.jobId ?? faker.string.uuid(),
        positionId: null,
        contractId: null,
        organization: {
          id: faker.string.uuid(),
          name: faker.company.name(),
          avatar: null,
        },
        provider: {
          id: faker.string.uuid(),
          status: "ACTIVE",
          title: faker.person.jobTitle(),
          person: {
            id: faker.string.uuid(),
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            avatar: faker.image.avatar(),
            role: "PROVIDER",
            title: faker.person.jobTitle(),
          },
        },
        job: input.jobId
          ? {
              id: input.jobId,
              summary: faker.company.catchPhrase(),
              role: faker.person.jobTitle(),
              type: "FULL_TIME",
              status: "PUBLISHED",
              scope: faker.lorem.paragraph(),
              paymentType: "HOURLY",
              paymentAmount: faker.number.int({ min: 50, max: 200 }),
              location: {
                name: faker.location.city(),
                address: {
                  formatted: faker.location.streetAddress(),
                  latitude: faker.location.latitude(),
                  longitude: faker.location.longitude(),
                },
              },
            }
          : undefined,
        position: null,
        contract: null,
        thread: {
          id: faker.string.uuid(),
          messages: [
            {
              id: faker.string.uuid(),
              content: faker.lorem.paragraph(),
              createdAt: new Date(),
              updatedAt: new Date(),
              author: {
                id: faker.string.uuid(),
                firstName: faker.person.firstName(),
                lastName: faker.person.lastName(),
                avatar: faker.image.avatar(),
              },
            },
          ],
        },
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["applications"]["getMany"];
});

export const medicalRoles = trpcMsw.values.getMany.query(({ input }) => {
  if (input.type === "MEDICAL_ROLE") {
    const medicalRoles = [
      "Cardiologist",
      "Pediatrician",
      "Neurologist",
      "Orthopedic Surgeon",
      "Dermatologist",
      "Oncologist",
      "Psychiatrist",
      "Radiologist",
      "Anesthesiologist",
      "Emergency Physician",
    ];

    return {
      items: [
        ...Array.from({ length: 5 }, () => ({
          id: faker.string.uuid(),
          value: faker.helpers.arrayElement(medicalRoles),
          type: "MEDICAL_ROLE",
          createdAt: faker.date.past(),
          updatedAt: faker.date.recent(),
        })),
      ],
      total: 5,
    } as unknown as RouterOutputs["values"]["getMany"];
  }
  return { items: [], total: 0 };
});

export const contracts = trpcMsw.contracts.getMany.query(({ input }) => {
  const types: ContractType[] = [
    ContractType.EMPLOYMENT,
    ContractType.NON_COMPETE,
    ContractType.NON_DISCLOSURE,
    ContractType.SERVICE_RATE,
    ContractType.SERVICE_AGREEMENT,
    ContractType.OTHER,
  ];

  const statuses: ContractStatus[] = [
    ContractStatus.DRAFT,
    ContractStatus.PENDING,
    ContractStatus.SIGNED,
    ContractStatus.REJECTED,
    ContractStatus.EXPIRED,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        title: faker.company.catchPhrase(),
        type: faker.helpers.arrayElement(types),
        status: faker.helpers.arrayElement(statuses),
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent(),
        expiresAt: faker.date.future(),
        organizationId: input.organizationId ?? faker.string.uuid(),
        providerId: input.providerId ?? null,
        positionId: null,
        organization:
          input.organizationId ??
          (() => ({
            id: faker.string.uuid(),
            name: faker.company.name(),
            avatar: null,
          }))(),
        provider:
          input.providerId ??
          (() => ({
            id: faker.string.uuid(),
            status: "ACTIVE",
            person: {
              id: faker.string.uuid(),
              firstName: faker.person.firstName(),
              lastName: faker.person.lastName(),
              avatar: faker.image.avatar(),
              role: "PROVIDER",
              title: faker.person.jobTitle(),
            },
          }))(),
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["contracts"]["getMany"];
});

export const offers = trpcMsw.offers.getMany.query(({ input }) => {
  const statuses = [
    OfferStatus.PENDING,
    OfferStatus.ACCEPTED,
    OfferStatus.REJECTED,
    OfferStatus.WITHDRAWN,
    OfferStatus.CLOSED,
  ];

  return {
    items: [
      ...Array.from({ length: 5 }, () => ({
        id: faker.string.uuid(),
        status: faker.helpers.arrayElement(statuses),
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent(),
        expiresAt: faker.date.future(),
        organizationId: input.providerId
          ? faker.string.uuid()
          : faker.string.uuid(),
        providerId: input.providerId ?? null,
        applicationId: null,
        organization: (() => ({
          id: faker.string.uuid(),
          name: faker.company.name(),
          avatar: null,
        }))(),
        provider: input.providerId
          ? {
              id: input.providerId,
              status: "ACTIVE",
              person: {
                id: faker.string.uuid(),
                firstName: faker.person.firstName(),
                lastName: faker.person.lastName(),
                avatar: faker.image.avatar(),
                role: "PROVIDER",
                title: faker.person.jobTitle(),
              },
            }
          : null,
        application: null,
      })),
    ],
    total: 5,
  } as unknown as RouterOutputs["offers"]["getMany"];
});
