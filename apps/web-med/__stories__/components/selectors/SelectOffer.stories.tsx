import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectOffer,
  SelectOfferField,
} from "@/components/selectors/SelectOffer";

import { offers } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectOffer",
  component: SelectOffer,
  parameters: {
    msw: {
      handlers: [offers],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectOffer>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select an offer",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      offer: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectOfferField />
    </FormWrapper>
  ),
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select an offer",
    providerId: "provider123",
  },
};

export const WithJobFilter: Story = {
  args: {
    placeholder: "Select an offer",
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select an offer",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select an offer",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select an offer",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select an offer",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select an offer",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select an offer",
    size: "md",
  },
};
