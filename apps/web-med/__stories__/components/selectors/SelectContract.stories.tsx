import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectContract,
  SelectContractField,
} from "@/components/selectors/SelectContract";

import { contracts } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectContract",
  component: SelectContract,
  parameters: {
    msw: {
      handlers: [contracts],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectContract>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select a contract",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      contract: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectContractField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a contract",
  },
};

export const WithProviderFilter: Story = {
  args: {
    placeholder: "Select a contract",
  },
};

export const WithTypeFilter: Story = {
  args: {
    placeholder: "Select a contract",
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select a contract",
  }, // Type assertion to avoid type error
};

export const Loading: Story = {
  args: {
    placeholder: "Select a contract",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a contract",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a contract",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a contract",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a contract",
    size: "md",
  },
};
