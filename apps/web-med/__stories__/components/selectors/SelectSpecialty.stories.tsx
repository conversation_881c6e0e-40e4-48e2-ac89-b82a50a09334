import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectSpecialty,
  SelectSpecialtyField,
} from "@/components/selectors/SelectSpecialty";

import { specialties } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectSpecialty",
  component: SelectSpecialty,
  parameters: {
    msw: {
      handlers: [specialties],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectSpecialty>;

export default meta;
type Story = StoryObj<typeof SelectSpecialty>;

export const Default: Story = {
  args: {
    placeholder: "Select a specialty",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      specialty: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectSpecialtyField />
    </FormWrapper>
  ),
};

export const Loading: Story = {
  args: {
    placeholder: "Select a specialty",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a specialty",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a specialty",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a specialty",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a specialty",
    size: "md",
  },
};
