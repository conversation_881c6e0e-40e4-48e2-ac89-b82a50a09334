import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  SelectJobPost,
  SelectJobPostField,
} from "@/components/selectors/SelectJobPost";

import { jobs } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectJobPost",
  component: SelectJobPost,
  parameters: {
    msw: {
      handlers: [jobs],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectJobPost>;

export default meta;
type Story = StoryObj<typeof SelectJobPost>;

export const Default: Story = {
  args: {
    placeholder: "Select a job",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      job: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectJobPostField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a job",
    organizationId: "org123",
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select a job",
    loading: true,
  },
};

export const Pending: Story = {
  args: {
    placeholder: "Select a job",
    pending: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a job",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a job",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a job",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a job",
    size: "md",
  },
};
