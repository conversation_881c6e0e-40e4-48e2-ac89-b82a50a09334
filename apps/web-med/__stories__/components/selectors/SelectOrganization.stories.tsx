import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import { OrganizationStatus, OrganizationType } from "@/api";
import {
  SelectOrganization,
  SelectOrganizationField,
} from "@/components/selectors/SelectOrganization";

import { organizations } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectOrganization",
  component: SelectOrganization,
  parameters: {
    msw: {
      handlers: [organizations],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectOrganization>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select an organization",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      organization: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectOrganizationField />
    </FormWrapper>
  ),
};

export const WithTypeFilter: Story = {
  args: {
    placeholder: "Select an organization",
    types: [OrganizationType.CLIENT],
  },
};

export const WithStatusFilter: Story = {
  args: {
    placeholder: "Select an organization",
    status: [OrganizationStatus.ACTIVE],
  },
};

export const Loading: Story = {
  args: {
    placeholder: "Select an organization",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select an organization",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select an organization",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select an organization",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select an organization",
    size: "md",
  },
};
