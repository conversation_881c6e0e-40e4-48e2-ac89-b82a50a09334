import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { FormProvider, useForm } from "react-hook-form";

import {
  <PERSON><PERSON>erson,
  SelectPersonField,
} from "@/components/selectors/SelectPerson";

import { people } from "./mocks";

const meta = {
  title: "Components/Selectors/SelectPerson",
  component: SelectPerson,
  parameters: {
    msw: {
      handlers: [people],
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SelectPerson>;

export default meta;
type Story = StoryObj<typeof SelectPerson>;

export const Default: Story = {
  args: {
    placeholder: "Select a person",
  },
};

// Wrapper component for the field story to provide form context
function FormWrapper({ children }: { children: React.ReactNode }) {
  const methods = useForm({
    defaultValues: {
      person: "",
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
}

export const AsFormField: Story = {
  render: () => (
    <FormWrapper>
      <SelectPersonField />
    </FormWrapper>
  ),
};

export const WithOrganizationFilter: Story = {
  args: {
    placeholder: "Select a person",
    organizationId: "org1",
  } as any, // Type assertion to avoid type error
};

export const Loading: Story = {
  args: {
    placeholder: "Select a person",
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Select a person",
    enabled: false,
  },
};

export const WithDialog: Story = {
  args: {
    placeholder: "Select a person",
    useDialog: true,
  },
};

export const Small: Story = {
  args: {
    placeholder: "Select a person",
    size: "sm",
  },
};

export const Medium: Story = {
  args: {
    placeholder: "Select a person",
    size: "md",
  },
};
