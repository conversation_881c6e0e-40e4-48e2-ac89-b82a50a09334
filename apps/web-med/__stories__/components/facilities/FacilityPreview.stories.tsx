import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { But<PERSON> } from "@axa/ui/primitives/button";

import { FacilityPreview } from "@/components/facilities/FacilityPreview";

const meta: Meta<typeof FacilityPreview> = {
  title: "Components/Facilities/FacilityPreview",
  component: FacilityPreview,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof FacilityPreview>;

const baseFacility = {
  id: "1",
  name: "Central Hospital",
  type: "HOSPITAL" as const,
  address: {
    formatted: "123 Medical Center Blvd, Healthville, CA 90210",
  },
  contacts: [
    { name: "<PERSON>", phone: "(*************" },
    { name: "<PERSON>", phone: "(*************" },
  ],
  specialties: ["Cardiology", "Neurology", "Orthopedics"],
  departments: [
    { name: "Emergency Department", type: "DEPARTMENT" },
    { name: "Intensive Care Unit", type: "UNIT" },
    { name: "Pediatric Ward", type: "WARD" },
    { name: "Operating Room 1", type: "ROOM" },
    { name: "Mobile Clinic", type: "OTHER" },
  ],
};

export const Default: Story = {
  args: {
    facility: baseFacility,
  },
};

export const WithDescription: Story = {
  args: {
    facility: {
      ...baseFacility,
      description:
        "A state-of-the-art medical facility providing comprehensive healthcare services.",
    },
  },
};

export const Clinic: Story = {
  args: {
    facility: {
      ...baseFacility,
      name: "Downtown Medical Clinic",
      type: "CLINIC",
      address: {
        formatted: "456 Health St, Suite 200, Wellnessburg, NY 10001",
      },
      description: "Convenient outpatient care in the heart of the city.",
      specialties: ["Family Medicine", "Pediatrics"],
      departments: [
        { name: "General Practice", type: "DEPARTMENT" },
        { name: "Pediatrics", type: "DEPARTMENT" },
      ],
    },
  },
};

export const WithCustomChildren: Story = {
  args: {
    facility: baseFacility,
    children: (
      <div className="mt-4 flex justify-end">
        <Button variant="outline">Edit Facility</Button>
      </div>
    ),
  },
};

export const LongAddress: Story = {
  args: {
    facility: {
      ...baseFacility,
      address: {
        formatted:
          "789 Very Long Street Name That Might Wrap, Building Complex Name, Suite 1234, City With A Long Name, State With A Long Name 12345-6789",
      },
    },
  },
};
