import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { LocationTypeValue } from "@/components/facilities/LocationType";

import { LocationPreview } from "@/components/facilities/LocationPreview";

const meta: Meta<typeof LocationPreview> = {
  title: "Components/Facilities/LocationPreview",
  component: LocationPreview,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof LocationPreview>;

const baseLocation = {
  id: "1",
  name: "City General Hospital",
  type: "HOSPITAL" as LocationTypeValue,
  address: {
    formatted: "123 Medical Center Blvd, Healthville, CA 90210",
  },
};

export const Default: Story = {
  args: {
    location: baseLocation,
  },
};

export const WithPhoneNumber: Story = {
  args: {
    location: {
      ...baseLocation,
      phoneNumber: "(*************",
    },
  },
};

export const Clinic: Story = {
  args: {
    location: {
      ...baseLocation,
      name: "Downtown Medical Clinic",
      type: "CLIN<PERSON>" as LocationTypeValue,
      address: {
        formatted: "456 Health St, Suite 200, Wellnessburg, NY 10001",
      },
    },
  },
};

export const LongAddress: Story = {
  args: {
    location: {
      ...baseLocation,
      address: {
        formatted:
          "789 Very Long Street Name That Might Wrap, Building Complex Name, Suite 1234, City With A Long Name, State With A Long Name 12345-6789",
      },
    },
  },
};
