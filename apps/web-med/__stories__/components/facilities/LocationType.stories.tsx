import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { LocationTypeValue } from "@/components/facilities/LocationType";

import { LocationType } from "@/components/facilities/LocationType";

const meta: Meta<typeof LocationType> = {
  title: "Components/Facilities/LocationType",
  component: LocationType,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof LocationType>;

const types: LocationTypeValue[] = [
  "FACILITY",
  "HOSPITAL",
  "CLINIC",
  "OFFICE",
  "OTHER",
];

export const AllTypes: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      {types.map((type) => (
        <LocationType key={type} type={type} />
      ))}
    </div>
  ),
};

export const Facility: Story = {
  args: {
    type: "FACILITY",
  },
};

export const Hospital: Story = {
  args: {
    type: "HOSPITAL",
  },
};

export const Clinic: Story = {
  args: {
    type: "CLINIC",
  },
};

export const Office: Story = {
  args: {
    type: "OFFICE",
  },
};

export const Other: Story = {
  args: {
    type: "OTHER",
  },
};
