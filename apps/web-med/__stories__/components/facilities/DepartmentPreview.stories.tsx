import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { DepartmentTypeValue } from "@/components/facilities/DepartmentType";

import { DepartmentPreview } from "@/components/facilities/DepartmentPreview";

const meta: Meta<typeof DepartmentPreview> = {
  title: "Components/Facilities/DepartmentPreview",
  component: DepartmentPreview,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof DepartmentPreview>;

const baseDepartment = {
  id: "1",
  name: "Emergency Department",
  type: "DEPARTMENT" as DepartmentTypeValue,
  location: {
    name: "City General Hospital",
  },
};

export const Default: Story = {
  args: {
    department: baseDepartment,
  },
};

export const WithDescription: Story = {
  args: {
    department: {
      ...baseDepartment,
      description:
        "24/7 emergency care for critical and life-threatening conditions.",
    },
  },
};

export const Ward: Story = {
  args: {
    department: {
      ...baseDepartment,
      name: "Pediatric Ward",
      type: "WARD" as DepartmentTypeValue,
      description: "Specialized care for children and adolescents.",
    },
  },
};

export const Unit: Story = {
  args: {
    department: {
      ...baseDepartment,
      name: "Intensive Care Unit",
      type: "UNIT" as DepartmentTypeValue,
      description:
        "Advanced care for critically ill patients requiring constant monitoring.",
    },
  },
};
