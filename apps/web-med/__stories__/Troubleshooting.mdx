import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Troubleshooting" />

# Troubleshooting Storybook Stories

This guide addresses common issues you might encounter when creating and maintaining Storybook stories for the AXA Professionals project.

## Type Errors

### Incompatible Types

**Problem**: TypeScript reports type incompatibility between your mock data and component props.

```typescript
// Error: Type 'null' is not assignable to type 'string | undefined'
avatarUrl: user.avatar;
```

**Solution**: Use nullish coalescing to handle null values properly.

```typescript
// Fix: Convert null to undefined
avatarUrl: user.avatar ?? undefined;
```

### Missing Required Properties

**Problem**: TypeScript reports that required properties are missing.

```typescript
// Error: Property 'title' is missing in type '{ id: string; firstName: string; }'
message: { id: "1", firstName: "John" }
```

**Solution**: Ensure all required properties are provided.

```typescript
// Fix: Include all required properties
message: {
  id: "1",
  firstName: "<PERSON>",
  title: "Patient",
  // other required properties...
}

// Alternative: Spread from a base object
message: { ...baseMessage, content: "New content" }
```

### Optional vs. Required Properties

**Problem**: Confusion about which properties are optional.

**Solution**: Be explicit about optional properties in your interfaces.

```typescript
// Fix: Mark optional properties with ?
interface UserProps {
  id: string; // Required
  firstName: string; // Required
  lastName: string; // Required
  title?: string; // Optional
  avatar?: string; // Optional
}
```

### Type Assertions

**Problem**: Need to override TypeScript's type inference.

**Solution**: Use type assertions when you're certain about the type.

```typescript
// Fix: Assert the type
const user = data as User;

// Alternative: Use type parameter with generic functions
const getUser = <T extends User>(id: string): T => {
  // implementation
};
```

## Import Errors

### Path Issues

**Problem**: Cannot find module or its declarations.

```typescript
// Error: Cannot find module '@/components/path/to/Component'
import Component from "@/components/path/to/Component";
```

**Solution**: Verify import paths are correct.

```typescript
// Fix: Check for typos in the path
import Component from "@/components/path/to/Component";

// Fix: Use relative paths if needed
import Component from "../../components/Component";
```

### Missing Exports

**Problem**: The imported module doesn't export the requested name.

```typescript
// Error: Module '"./Component"' has no exported member 'Component'
import { Component } from "./Component";
```

**Solution**: Ensure components are properly exported.

```typescript
// Fix: Use the correct export name
// Fix: Use default import if the component is exported as default
import Component, { ComponentName } from "./Component";
```

## Storybook-Specific Issues

### Args Not Updating

**Problem**: Changes to args in the Storybook UI don't affect the component.

**Solution**: Use the `argTypes` property to define arg behavior.

```typescript
const meta = {
  component: Button,
  argTypes: {
    onClick: { action: "clicked" },
    variant: {
      control: { type: "select" },
      options: ["primary", "secondary", "tertiary"],
    },
  },
};
```

### Context Providers Missing

**Problem**: Components that rely on context providers fail in Storybook.

**Solution**: Wrap components in necessary providers using decorators.

```typescript
const meta = {
  component: ProfileCard,
  decorators: [
    (Story) => (
      <UserProvider initialUser={{ id: '1', name: 'John' }}>
        <Story />
      </UserProvider>
    ),
  ],
};
```

### Controls Not Working

**Problem**: Storybook controls don't appear or don't work correctly.

**Solution**: Check that props are correctly typed and documented.

```typescript
// Fix: Add JSDoc comments for better controls
/**
 * Button component for user actions
 * @param variant - The visual style of the button
 * @param size - The size of the button
 * @param onClick - Function called when button is clicked
 */
export function Button({
  variant = "primary",
  size = "medium",
  onClick,
}: ButtonProps) {
  // implementation
}
```

## Rendering Issues

### Component Not Rendering

**Problem**: Component doesn't appear in Storybook.

**Solution**: Check for errors in the console and verify the component renders something.

```typescript
// Fix: Add a fallback for empty states
function UserList({ users = [] }) {
  if (users.length === 0) {
    return <div>No users found</div>;
  }

  return (
    <ul>
      {users.map(user => <li key={user.id}>{user.name}</li>)}
    </ul>
  );
}
```

### Layout Issues

**Problem**: Component layout doesn't match the application.

**Solution**: Use decorators to provide the correct context and styling.

```typescript
const meta = {
  component: Card,
  decorators: [
    (Story) => (
      <div className="max-w-md mx-auto">
        <Story />
      </div>
    ),
  ],
};
```

## Data Fetching Issues

### API Calls Failing

**Problem**: Components that make API calls fail in Storybook.

**Solution**: Mock API calls or provide mock data directly.

```typescript
// Fix: Mock the API call
import { mockUser } from "../mocks/user";

// In your story
export const Default = {
  args: {
    user: mockUser,
  },
  parameters: {
    msw: {
      handlers: [
        rest.get("/api/user/:id", (req, res, ctx) => {
          return res(ctx.json(mockUser));
        }),
      ],
    },
  },
};
```

### Async Component Issues

**Problem**: Components that load data asynchronously don't render properly.

**Solution**: Use decorators or story parameters to handle loading states.

```typescript
export const Loading = {
  parameters: {
    msw: {
      handlers: [
        rest.get("/api/user/:id", (req, res, ctx) => {
          return res(ctx.delay(2000), ctx.json(mockUser));
        }),
      ],
    },
  },
};
```

## Advanced Troubleshooting

### Using the Storybook Logger

Storybook provides a logger that can help debug issues:

```typescript
import { logger } from '@storybook/client-logger';

export const Debugging = {
  render: (args) => {
    logger.info('Rendering with args:', args);
    return <Component {...args} />;
  },
};
```

### Inspecting the DOM

Use the browser's developer tools to inspect the rendered DOM and check for issues.

### Checking for React Key Warnings

Ensure that all items in lists have unique keys to avoid React warnings.

```typescript
// Fix: Add a unique key for each item
{items.map((item) => (
  <ListItem key={item.id} {...item} />
))}
```

## Getting Help

If you're still experiencing issues after trying these solutions:

1. Check the [Storybook documentation](https://storybook.js.org/docs)
2. Search for similar issues in the project repository
3. Ask for help in the team's communication channels
4. Create a detailed issue report with steps to reproduce

Remember that troubleshooting is a valuable learning experience that will improve your understanding of both Storybook and the component library.
