import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Data Mocking" />

# Data Mocking Strategies for Storybook

This guide covers best practices for creating and organizing mock data for your Storybook stories in the AXA Professionals project.

## Why Mock Data Matters

Well-structured mock data is crucial for effective Storybook stories because it:

- Provides realistic context for components
- Ensures consistent testing across stories
- Makes stories more maintainable
- Helps identify edge cases and potential issues
- Improves documentation by showing real-world usage

## Recommended Pattern: Type-Safe Factory Functions

The recommended pattern for creating mock data in our Storybook stories is to use type-safe factory functions with Faker. This approach ensures that:

1. All mock data matches the expected API types
2. Data is realistic and varied
3. Code is modular and maintainable
4. Edge cases can be easily tested

### Step 1: Define Types

First, import the necessary types from the API:

```typescript
import type { RouterOutputs } from "@/api";

import {
  AgreementStatus,
  ContractStatus,
  SignatureStatus,
  // Other enums as needed
} from "@/api";

// Define specific types for clarity
type ContractQuery = RouterOutputs["contracts"]["get"];
type AgreementStructure = NonNullable<ContractQuery["agreements"]>[number];
type SignatureStructure = NonNullable<AgreementStructure["signatures"]>[number];
```

### Step 2: Create Helper Functions

Create helper functions for generating different parts of your data structure:

```typescript
import { faker } from "@faker-js/faker";

// Helper function to create a person
const createPerson = (role = "Provider") => ({
  id: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  title: role,
  avatar: "/placeholder.svg",
});

// Helper function to create a signature
const createSignature = (
  status: SignatureStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
  } = {},
): SignatureStructure => ({
  id: faker.string.uuid(),
  status,
  createdAt: faker.date.recent(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  documensoToken: null,
  person: createPerson(
    status === SignatureStatus.SIGNED ? "Provider" : "Administrator",
  ),
});

// Helper function to create an agreement
const createAgreement = (
  status: AgreementStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
    expiresAt?: Date;
    signatures?: SignatureStructure[];
  } = {},
): AgreementStructure => ({
  id: faker.string.uuid(),
  status,
  documensoId: faker.string.alphanumeric(10),
  documensoUrl: faker.internet.url(),
  createdAt: faker.date.recent(),
  expiresAt: options.expiresAt || faker.date.future(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  signatures: options.signatures || [],
});
```

### Step 3: Create Specific Instances

Use the helper functions to create specific instances for different scenarios:

```typescript
// Create signatures for different states
const pendingSignatures = [
  createSignature(SignatureStatus.PENDING),
  createSignature(SignatureStatus.PENDING),
];

const signedSignatures = [
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
];

// Create agreements for different states
const agreements = [
  createAgreement(AgreementStatus.SIGNED, {
    signedAt: faker.date.recent(),
    signatures: [
      createSignature(SignatureStatus.SIGNED, {
        signedAt: faker.date.recent(),
      }),
      createSignature(SignatureStatus.PENDING),
    ],
  }),
];

const pendingAgreements = [
  createAgreement(AgreementStatus.PENDING, {
    signatures: pendingSignatures,
  }),
];
```

### Step 4: Create the Main Object

Combine all the pieces to create the main object:

```typescript
// Create the base contract
const baseContract: ContractQuery = {
  id: faker.string.uuid(),
  title: "Emergency Room Physician - Night Shift",
  type: ContractType.EMPLOYMENT,
  status: ContractStatus.PENDING,
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  deletedAt: null,
  expiresAt: faker.date.future(),
  agreements,
  provider: {
    id: faker.string.uuid(),
    title: "Registered Nurse",
    person: createPerson(),
  },
  organization: {
    id: faker.string.uuid(),
    name: "City General Hospital",
    avatar: "/placeholder.svg",
  },
  position: {
    id: faker.string.uuid(),
    summary: "Emergency Room Physician - Night Shift",
    status: JobPositionStatus.ACTIVE,
    provider: {
      id: faker.string.uuid(),
      title: "Registered Nurse",
      person: createPerson(),
    },
    job: {
      id: faker.string.uuid(),
      summary: "Emergency Room Physician - Night Shift",
      status: JobPostStatus.PUBLISHED,
      scope: faker.lorem.paragraph(),
    },
  },
};
```

### Step 5: Use in Stories

Use the created objects in your stories with type assertions:

```typescript
export const Default: Story = {
  args: {
    contract: baseContract,
  },
};

export const Pending: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.PENDING,
      agreements: pendingAgreements,
    } as ContractQuery,
  },
};
```

## Centralized Data Files

For components that share common data structures, consider creating centralized data files:

### Basic Structure

```
__stories__/
  components/
    contracts/
      data.ts         # Centralized data for contract components
      Contract.stories.tsx
      ContractList.stories.tsx
      ContractDetails.stories.tsx
```

### Example Implementation

```typescript
// __stories__/components/contracts/data.ts
import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { AgreementStatus, ContractStatus, SignatureStatus } from "@/api";

// Define types
type ContractQuery = RouterOutputs["contracts"]["get"];
type AgreementStructure = NonNullable<ContractQuery["agreements"]>[number];
type SignatureStructure = NonNullable<AgreementStructure["signatures"]>[number];

// Helper functions
export const createPerson = (role = "Provider") => ({
  id: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  title: role,
  avatar: "/placeholder.svg",
});

export const createSignature = (
  status: SignatureStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
  } = {},
): SignatureStructure => ({
  id: faker.string.uuid(),
  status,
  createdAt: faker.date.recent(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  documensoToken: null,
  person: createPerson(
    status === SignatureStatus.SIGNED ? "Provider" : "Administrator",
  ),
});

export const createAgreement = (
  status: AgreementStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
    expiresAt?: Date;
    signatures?: SignatureStructure[];
  } = {},
): AgreementStructure => ({
  id: faker.string.uuid(),
  status,
  documensoId: faker.string.alphanumeric(10),
  documensoUrl: faker.internet.url(),
  createdAt: faker.date.recent(),
  expiresAt: options.expiresAt || faker.date.future(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  signatures: options.signatures || [],
});

// Create common instances
export const pendingSignatures = [
  createSignature(SignatureStatus.PENDING),
  createSignature(SignatureStatus.PENDING),
];

export const signedSignatures = [
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
];

export const rejectedSignatures = [
  createSignature(SignatureStatus.REJECTED, {
    rejectedAt: faker.date.recent(),
  }),
  createSignature(SignatureStatus.PENDING),
];

// Create agreements for different states
export const agreements = [
  createAgreement(AgreementStatus.SIGNED, {
    signedAt: faker.date.recent(),
    signatures: [
      createSignature(SignatureStatus.SIGNED, {
        signedAt: faker.date.recent(),
      }),
      createSignature(SignatureStatus.PENDING),
    ],
  }),
];

export const pendingAgreements = [
  createAgreement(AgreementStatus.PENDING, {
    signatures: pendingSignatures,
  }),
];

export const signedAgreements = [
  createAgreement(AgreementStatus.SIGNED, {
    signedAt: faker.date.recent(),
    signatures: signedSignatures,
  }),
];

export const rejectedAgreements = [
  createAgreement(AgreementStatus.REJECTED, {
    rejectedAt: faker.date.recent(),
    signatures: rejectedSignatures,
  }),
];

export const expiredAgreements = [
  createAgreement(AgreementStatus.EXPIRED, {
    expiresAt: faker.date.past(),
  }),
];

// Create a base contract
export const createContract = (
  status: ContractStatus = ContractStatus.PENDING,
  options: {
    type?: ContractType;
    title?: string;
    agreements?: AgreementStructure[];
    provider?: any;
    organization?: any;
    position?: any;
  } = {},
): ContractQuery => ({
  id: faker.string.uuid(),
  title: options.title || "Emergency Room Physician - Night Shift",
  type: options.type || ContractType.EMPLOYMENT,
  status,
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  deletedAt: null,
  expiresAt: faker.date.future(),
  agreements: options.agreements || agreements,
  provider: options.provider || {
    id: faker.string.uuid(),
    title: "Registered Nurse",
    person: createPerson(),
  },
  organization: options.organization || {
    id: faker.string.uuid(),
    name: "City General Hospital",
    avatar: "/placeholder.svg",
  },
  position: options.position || {
    id: faker.string.uuid(),
    summary: "Emergency Room Physician - Night Shift",
    status: JobPositionStatus.ACTIVE,
    provider: {
      id: faker.string.uuid(),
      title: "Registered Nurse",
      person: createPerson(),
    },
    job: {
      id: faker.string.uuid(),
      summary: "Emergency Room Physician - Night Shift",
      status: JobPostStatus.PUBLISHED,
      scope: faker.lorem.paragraph(),
    },
  },
});

// Create contracts for different states
export const baseContract = createContract();
export const draftContract = createContract(ContractStatus.DRAFT, {
  agreements: [],
});
export const pendingContract = createContract(ContractStatus.PENDING, {
  agreements: pendingAgreements,
});
export const signedContract = createContract(ContractStatus.SIGNED, {
  agreements: signedAgreements,
});
export const rejectedContract = createContract(ContractStatus.REJECTED, {
  agreements: rejectedAgreements,
});
export const expiredContract = createContract(ContractStatus.EXPIRED, {
  agreements: expiredAgreements,
});
```

## Using Faker with API Enums

The Faker library is a powerful tool for generating realistic mock data. When working with the AXA Professionals project, you should leverage our API enum types to ensure consistency with the actual application.

### Randomly Selecting from Enum Values

To randomly select a value from an enum, use Faker's `helpers.arrayElement()` method:

```typescript
import { faker } from "@faker-js/faker";

import { ShiftStatus } from "@/api";

// Convert enum to array of values
const shiftStatusValues = Object.values(ShiftStatus);

// Randomly select a status
const randomStatus = faker.helpers.arrayElement(shiftStatusValues);
```

### Creating Mock Data with Enums

Here's how to incorporate enum values into your mock data factories:

```typescript
import { faker } from "@faker-js/faker";

import { PersonRole, ShiftStatus } from "@/api";

// Create a shift factory
export const createShift = (overrides = {}) => ({
  id: faker.string.uuid(),
  summary: faker.lorem.sentence(),
  description: faker.lorem.paragraph(),
  startDate: faker.date.future(),
  endDate: faker.date.future({ refDate: faker.date.future() }),
  // Use enum for status
  status: faker.helpers.arrayElement(Object.values(ShiftStatus)),
  ...overrides,
});

// Create a person factory with role from enum
export const createPerson = (overrides = {}) => ({
  id: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  email: faker.internet.email(),
  avatar: faker.image.avatar(),
  // Use enum for role
  role: faker.helpers.arrayElement(Object.values(PersonRole)),
  ...overrides,
});
```

## Best Practices

### 1. Always Use Type Assertions

Ensure your mock data matches the expected types by using type assertions:

```typescript
const mockContract = createContract() as ContractQuery;
```

### 2. Use Helper Functions for Reusability

Create helper functions for generating different parts of your data structure:

```typescript
const createPerson = (role = "Provider") => ({
  id: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  title: role,
  avatar: "/placeholder.svg",
});
```

### 3. Use Options Objects for Flexibility

Make your helper functions flexible with options objects:

```typescript
const createSignature = (
  status: SignatureStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
  } = {},
) => ({
  // ...
});
```

### 4. Keep Data Close to Stories

Store mock data in the same directory as the stories that use it.

### 5. Use Realistic Data

Make your mock data as realistic as possible to catch edge cases:

- Include special characters in text
- Use various lengths for strings
- Include empty states and null values
- Use realistic dates and times

### 6. Document Special Cases

Add comments to explain special test cases:

```typescript
// Create an expired agreement
const expiredAgreement = createAgreement(AgreementStatus.EXPIRED, {
  expiresAt: faker.date.past(), // Use a past date for expiration
});
```

### 7. Organize by Domain

Group mock data by domain or feature:

```typescript
// Contract-related mock data
export const contracts = {
  base: createContract(),
  draft: createContract(ContractStatus.DRAFT, { agreements: [] }),
  pending: createContract(ContractStatus.PENDING, {
    agreements: pendingAgreements,
  }),
};

// Provider-related mock data
export const providers = {
  active: createProvider(ProviderStatus.ACTIVE),
  pending: createProvider(ProviderStatus.PENDING),
};
```

## Conclusion

By following these patterns and best practices, you can create more maintainable, realistic, and useful stories for your component library. The type-safe factory function approach ensures that your mock data matches the expected API types while providing flexibility for different scenarios.

Remember that good mock data not only helps with testing but also serves as documentation for how components should be used with real data.
