import type { Meta, StoryObj } from "@storybook/react";

import UserPath from "@/www/onboarding/UserPath";

const meta = {
  title: "Pages/Onboarding/UserPath/page  ",
  component: UserPath,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/select-path",
      },
    },
  },
} satisfies Meta<typeof UserPath>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
