import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ProviderApprovalView } from "@/www/onboarding/provider/ProviderApproval";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderApproval/page",
  component: ProviderApprovalView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/approved",
      },
    },
  },
} satisfies Meta<typeof ProviderApprovalView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // @ts-expect-error - mock
    completeOnboarding: {
      mutate: () => {
        return {
          success: true,
        };
      },
    },
  },
};
