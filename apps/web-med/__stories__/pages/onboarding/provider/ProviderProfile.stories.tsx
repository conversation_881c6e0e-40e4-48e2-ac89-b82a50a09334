import type { Meta, StoryObj } from "@storybook/react";

import ProviderProfilePage from "@/www/onboarding/provider/ProviderProfile";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderProfile/page",
  component: ProviderProfilePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/profile",
      },
    },
  },
} satisfies Meta<typeof ProviderProfilePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
