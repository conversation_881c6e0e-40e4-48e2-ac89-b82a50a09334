import type { Meta, StoryObj } from "@storybook/react";

import ProviderBankingView from "@/www/onboarding/provider/ProviderBanking";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderBanking/page",
  component: ProviderBankingView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/banking",
      },
    },
  },
} satisfies Meta<typeof ProviderBankingView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
