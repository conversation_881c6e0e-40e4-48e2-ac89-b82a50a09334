import type { <PERSON>a, StoryObj } from "@storybook/react";

import { ProviderConfirmation as ProviderConfirmationView } from "@/www/onboarding/provider/ProviderConfirmation";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderConfirmation/page",
  component: ProviderConfirmationView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/confirmation",
      },
    },
  },
} satisfies Meta<typeof ProviderConfirmationView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: () => {},
  },
};
