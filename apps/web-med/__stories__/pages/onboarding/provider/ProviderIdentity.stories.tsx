import type { Meta, StoryObj } from "@storybook/react";

import ProviderIdentityPage from "@/www/onboarding/provider/ProviderIdentity";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderIdentity/page",
  component: ProviderIdentityPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/identity",
      },
    },
  },
} satisfies Meta<typeof ProviderIdentityPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
