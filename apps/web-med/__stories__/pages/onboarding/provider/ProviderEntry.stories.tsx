import type { Meta, StoryObj } from "@storybook/react";

import ProviderEntryPage from "@/www/onboarding/provider/ProviderEntry";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderEntry/page",
  component: ProviderEntryPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider",
      },
    },
  },
} satisfies Meta<typeof ProviderEntryPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
