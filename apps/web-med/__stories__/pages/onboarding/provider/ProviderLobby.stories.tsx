import type { Meta, StoryObj } from "@storybook/react";

import ProviderPendingPage from "@/www/onboarding/provider/ProviderLobby";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderLobby/page",
  component: ProviderPendingPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/lobby",
      },
    },
  },
} satisfies Meta<typeof ProviderPendingPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
