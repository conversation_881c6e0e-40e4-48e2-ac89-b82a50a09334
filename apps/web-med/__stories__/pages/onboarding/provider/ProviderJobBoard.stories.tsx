import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobPostStatus } from "@/api";
import { ProviderJobBoard } from "@/www/onboarding/provider/ProviderJobBoard";

const meta = {
  title: "Pages/Onboarding/Provider/ProviderJobBoard/page",
  component: ProviderJobBoard,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/provider/job-board",
      },
    },
  },
} satisfies Meta<typeof ProviderJobBoard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data that matches the actual API structure
const mockJobsData = {
  items: [
    {
      id: "job1",
      name: "Emergency Room Physician",
      summary: "Join our team of emergency medicine specialists",
      description: "Detailed job description here...",
      status: JobPostStatus.PUBLISHED,
      jobType: "FULL_TIME",
      createdAt: new Date("2023-06-15").toISOString(),
      updatedAt: new Date("2023-06-15").toISOString(),
      organization: {
        id: "org1",
        name: "AXA Medical Center",
        avatar: null,
      },
      location: {
        id: "loc1",
        name: "San Francisco",
        type: "HOSPITAL",
        address: {
          formatted: "123 Medical Dr, San Francisco, CA 94107",
          timeZone: "America/Los_Angeles",
          latitude: 37.7749,
          longitude: -122.4194,
        },
      },
      compensation: {
        min: 200,
        max: 250,
        currency: "USD",
      },
      specialties: [
        { id: "spec1", name: "Emergency Medicine" },
        { id: "spec2", name: "Critical Care" },
      ],
      // Include other required fields with default values
      department: null,
      schedule: null,
      thread: null,
      offers: [],
      applications: [],
      shifts: [],
      role: "PHYSICIAN",
      scope: "CLINICAL",
      requirements: [],
      benefits: [],
      skills: [],
      certifications: [],
      licenses: [],
      educations: [],
      experiences: [],
    },
    {
      id: "job2",
      name: "Family Medicine Physician",
      summary: "Primary care position in community clinic",
      description: "Detailed job description here...",
      status: JobPostStatus.PUBLISHED,
      jobType: "PART_TIME",
      createdAt: new Date("2023-06-10").toISOString(),
      updatedAt: new Date("2023-06-10").toISOString(),
      organization: {
        id: "org2",
        name: "AXA Community Clinic",
        avatar: null,
      },
      location: {
        id: "loc2",
        name: "Los Angeles",
        type: "CLINIC",
        address: {
          formatted: "456 Health Ave, Los Angeles, CA 90001",
          timeZone: "America/Los_Angeles",
          latitude: 34.0522,
          longitude: -118.2437,
        },
      },
      compensation: {
        min: 180,
        max: 220,
        currency: "USD",
      },
      specialties: [
        { id: "spec3", name: "Family Medicine" },
        { id: "spec4", name: "Primary Care" },
      ],
      // Include other required fields with default values
      department: null,
      schedule: null,
      thread: null,
      offers: [],
      applications: [],
      shifts: [],
      role: "PHYSICIAN",
      scope: "CLINICAL",
      requirements: [],
      benefits: [],
      skills: [],
      certifications: [],
      licenses: [],
      educations: [],
      experiences: [],
    },
    {
      id: "job3",
      name: "Pediatric Specialist",
      summary: "Pediatric care in children's hospital",
      description: "Detailed job description here...",
      status: JobPostStatus.PUBLISHED,
      jobType: "CONTRACT",
      createdAt: new Date("2023-06-13").toISOString(),
      updatedAt: new Date("2023-06-13").toISOString(),
      organization: {
        id: "org3",
        name: "AXA Children's Hospital",
        avatar: null,
      },
      location: {
        id: "loc3",
        name: "San Diego",
        type: "HOSPITAL",
        address: {
          formatted: "789 Children's Way, San Diego, CA 92123",
          timeZone: "America/Los_Angeles",
          latitude: 32.7157,
          longitude: -117.1611,
        },
      },
      compensation: {
        min: 190,
        max: 230,
        currency: "USD",
      },
      specialties: [
        { id: "spec5", name: "Pediatrics" },
        { id: "spec6", name: "Neonatology" },
      ],
      // Include other required fields with default values
      department: null,
      schedule: null,
      thread: null,
      offers: [],
      applications: [],
      shifts: [],
      role: "PHYSICIAN",
      scope: "CLINICAL",
      requirements: [],
      benefits: [],
      skills: [],
      certifications: [],
      licenses: [],
      educations: [],
      experiences: [],
    },
    {
      id: "job4",
      name: "Cardiologist",
      summary: "Cardiology position at heart center",
      description: "Detailed job description here...",
      status: JobPostStatus.PUBLISHED,
      jobType: "FULL_TIME",
      createdAt: new Date("2023-06-11").toISOString(),
      updatedAt: new Date("2023-06-11").toISOString(),
      organization: {
        id: "org4",
        name: "AXA Heart Center",
        avatar: null,
      },
      location: {
        id: "loc4",
        name: "Sacramento",
        type: "HOSPITAL",
        address: {
          formatted: "101 Heart Blvd, Sacramento, CA 95814",
          timeZone: "America/Los_Angeles",
          latitude: 38.5816,
          longitude: -121.4944,
        },
      },
      compensation: {
        min: 250,
        max: 300,
        currency: "USD",
      },
      specialties: [
        { id: "spec7", name: "Cardiology" },
        { id: "spec8", name: "Internal Medicine" },
      ],
      // Include other required fields with default values
      department: null,
      schedule: null,
      thread: null,
      offers: [],
      applications: [],
      shifts: [],
      role: "PHYSICIAN",
      scope: "CLINICAL",
      requirements: [],
      benefits: [],
      skills: [],
      certifications: [],
      licenses: [],
      educations: [],
      experiences: [],
    },
  ],
  total: 4,
  pageNumber: 0,
  pageSize: 4,
};

// Default story with data
export const Default: Story = {
  args: {
    jobs: {
      data: mockJobsData,
      loading: false,
      error: null,
    },
  },
};

// Loading state
export const Loading: Story = {
  args: {
    jobs: {
      data: undefined,
      loading: true,
      error: null,
    },
  },
};

// Error state
export const ErrorState: Story = {
  args: {
    jobs: {
      data: undefined,
      loading: false,
      error: new Error("Failed to fetch jobs"),
    },
  },
};

// Empty state
export const Empty: Story = {
  args: {
    jobs: {
      data: {
        items: [],
        total: 0,
        pageNumber: 0,
        pageSize: 4,
      },
      loading: false,
      error: null,
    },
  },
};
