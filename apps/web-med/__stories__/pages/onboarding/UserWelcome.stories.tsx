import type { Meta, StoryObj } from "@storybook/react";

import UserWelcomePage from "@/www/onboarding/UserWelcome";

const meta = {
  title: "Pages/Onboarding/UserWelcome/page",
  component: UserWelcomePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding",
        query: {
          destination: "provider",
        },
      },
    },
  },
} satisfies Meta<typeof UserWelcomePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const ProviderDestination: Story = {
  args: {
    destination: "provider",
  },
};

export const OrganizationDestination: Story = {
  args: {
    destination: "organization",
  },
};
