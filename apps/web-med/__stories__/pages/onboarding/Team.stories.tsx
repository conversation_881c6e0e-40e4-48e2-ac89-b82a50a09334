import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import React from "react";

import { TeamView } from "@/www/onboarding/team/Team";

// Create a simple mock for the completeOnboarding prop
const mockCompleteOnboarding = {
  mutateAsync: async () => ({ success: true }),
  isPending: false,
  isError: false,
  error: null,
} as any; // Using 'as any' to bypass type checking for the story

const mockPendingMutation = {
  mutateAsync: async () => ({ success: true }),
  isPending: true,
  isError: false,
  error: null,
} as any;

const mockErrorMutation = {
  mutateAsync: async () => {
    throw new Error("Failed to complete onboarding");
  },
  isPending: false,
  isError: true,
  error: new Error("Failed to complete onboarding"),
} as any;

const meta = {
  title: "Pages/Onboarding/Team/page",
  component: TeamView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/team",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-background">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof TeamView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultStory: Story = {
  name: "Default",
  args: {
    completeOnboarding: mockCompleteOnboarding,
  },
};

export const LoadingStory: Story = {
  name: "Loading",
  args: {
    completeOnboarding: mockPendingMutation,
  },
};

export const ErrorStory: Story = {
  name: "Error",
  args: {
    completeOnboarding: mockErrorMutation,
  },
};
