import type { Meta, StoryObj } from "@storybook/react";

import OrgInvitationsPage from "@/www/onboarding/organization/OrgInvitations";

const meta = {
  title: "Pages/Onboarding/Organization/OrgInvitations/page",
  component: OrgInvitationsPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/invitations",
      },
    },
  },
} satisfies Meta<typeof OrgInvitationsPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
