import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrgLobby from "@/www/onboarding/organization/OrgLobby";

const meta = {
  title: "Pages/Onboarding/Organization/OrgLobby/page",
  component: OrgLobby,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/lobby",
      },
    },
  },
} satisfies Meta<typeof OrgLobby>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
