import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationStart from "@/www/onboarding/organization/OrgEntry";

const meta = {
  title: "Pages/Onboarding/Organization/OrgEntry/page",
  component: OrganizationStart,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization",
      },
    },
  },
} satisfies Meta<typeof OrganizationStart>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
