import type { Meta, StoryObj } from "@storybook/react";

import OrgContractsPage from "@/www/onboarding/organization/OrgContracts";

const meta = {
  title: "Pages/Onboarding/Organization/OrgContracts/page",
  component: OrgContractsPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/contracts",
      },
    },
  },
} satisfies Meta<typeof OrgContractsPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
