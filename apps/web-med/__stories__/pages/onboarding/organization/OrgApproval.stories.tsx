import type { <PERSON>a, StoryObj } from "@storybook/react";

import { OrganizationApproval } from "@/www/onboarding/organization/OrgApproval";

const meta = {
  title: "Pages/Onboarding/Organization/OrgApproval/page",
  component: OrganizationApproval,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/approved",
      },
    },
  },
} satisfies Meta<typeof OrganizationApproval>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // @ts-expect-error - mock
    completeOnboarding: {
      mutate: () => {
        return {
          success: true,
        };
      },
    },
  },
};
