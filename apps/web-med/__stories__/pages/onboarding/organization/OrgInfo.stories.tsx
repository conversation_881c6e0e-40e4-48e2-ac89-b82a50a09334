import type { Meta, StoryObj } from "@storybook/react";

import OrgInfoPage from "@/www/onboarding/organization/OrgInfo";

const meta = {
  title: "Pages/Onboarding/Organization/OrgInfo/page",
  component: OrgInfoPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/info",
      },
    },
  },
} satisfies Meta<typeof OrgInfoPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
