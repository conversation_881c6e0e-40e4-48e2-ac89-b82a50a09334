import type { Meta, StoryObj } from "@storybook/react";

import OrgFacilityPage from "@/www/onboarding/organization/OrgFacility";

const meta = {
  title: "Pages/Onboarding/Organization/OrgFacility/page",
  component: OrgFacilityPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/facility",
      },
    },
  },
} satisfies Meta<typeof OrgFacilityPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
