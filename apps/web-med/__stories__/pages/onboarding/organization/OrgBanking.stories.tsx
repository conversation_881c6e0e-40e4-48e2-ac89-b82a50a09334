import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import { OrganizationBankingView } from "@/www/onboarding/organization/OrgBanking";

const meta = {
  title: "Pages/Onboarding/Organization/OrgBanking/page",
  component: OrganizationBankingView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
  args: {
    loading: false,
    showRequiredAlert: false,
    isLoadingMethods: false,
    hasPaymentMethods: true,
    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
    },
    paymentMethods: [
      {
        id: faker.string.uuid(),
        type: "card",
        brand: "visa",
        last4: "4242",
        expMonth: 12,
        expYear: 2025,
      },
      {
        id: faker.string.uuid(),
        type: "us_bank_account",
        brand: undefined,
        last4: "6789",
        expMonth: undefined,
        expYear: undefined,
      },
    ],
    createSetupIntent: {
      mutateAsync: async () => ({ clientSecret: "mock_client_secret" }),
      isPending: false,
    } as any,
    removePaymentMethod: {
      mutateAsync: async () => ({}),
      isPending: false,
    } as any,
    onPaymentMethodAdded: async () => {},
    onRemovePaymentMethod: async () => {},
    onNext: () => {},
  },
} satisfies Meta<typeof OrganizationBankingView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const NoPaymentMethods: Story = {
  args: {
    hasPaymentMethods: false,
    paymentMethods: [],
  },
};

export const WithRequiredAlert: Story = {
  args: {
    hasPaymentMethods: false,
    paymentMethods: [],
    showRequiredAlert: true,
  },
};

export const Loading: Story = {
  args: {
    isLoadingMethods: true,
  },
};
