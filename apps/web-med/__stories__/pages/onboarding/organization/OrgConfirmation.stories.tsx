import type { Meta, StoryObj } from "@storybook/react";

import { OrganizationConfirmation } from "@/www/onboarding/organization/OrgConfirmation";

const meta = {
  title: "Pages/Onboarding/Organization/OrgConfirmation/page",
  component: OrganizationConfirmation,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/onboarding/organization/confirmation",
      },
    },
  },
} satisfies Meta<typeof OrganizationConfirmation>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onSubmit: () => {},
  },
};
