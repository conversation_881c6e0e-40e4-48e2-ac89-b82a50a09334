import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  JobPostStatus,
  JobPostType,
  OfferStatus,
  PayType,
  ProviderStatus,
} from "@axa/database-medical";

import type { RouterError, RouterOutputs } from "@/api";

import Offers from "@/www/providers/offers/Offers";

import { createMockMutation } from "../../../helpers";

const meta = {
  title: "Pages/Providers/Offers/page",
  component: Offers,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/offers",
      },
    },
  },
} satisfies Meta<typeof Offers>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type OfferGetManyQuery = RouterOutputs["offers"]["getMany"];
type OfferItemStructure = OfferGetManyQuery["items"][number];
type OrganizationStructure = NonNullable<OfferItemStructure["organization"]>;
type ProviderStructure = NonNullable<OfferItemStructure["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type JobStructure = NonNullable<OfferItemStructure["job"]>;
type LocationStructure = NonNullable<JobStructure["location"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type ThreadStructure = NonNullable<OfferItemStructure["thread"]>;
type MessageStructure = NonNullable<ThreadStructure["messages"]>[number];

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createAddress = (
  options: {
    formatted?: string;
  } = {},
): AddressStructure => ({
  formatted:
    options.formatted ??
    `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`,
});

const createLocation = (
  options: {
    name?: string;
    address?: AddressStructure;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  address: options.address ?? createAddress(),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

const createJob = (
  options: {
    type?: JobPostType;
    status?: JobPostStatus;
    summary?: string;
    role?: string;
    scope?: string;
    paymentType?: PayType;
    paymentAmount?: number;
    location?: LocationStructure;
  } = {},
): JobStructure => ({
  id: faker.string.uuid(),
  type: options.type ?? "PER_DIEM",
  status: options.status ?? "PUBLISHED",
  summary: options.summary ?? faker.company.catchPhrase(),
  role: options.role ?? faker.person.jobTitle(),
  scope: options.scope ?? faker.lorem.paragraph(),
  paymentType: options.paymentType ?? "HOURLY",
  paymentAmount:
    options.paymentAmount ?? faker.number.int({ min: 50, max: 200 }),
  location: options.location ?? createLocation(),
});

const createMessage = (
  options: {
    content?: string;
    author?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  } = {},
): MessageStructure => ({
  id: faker.string.uuid(),
  content: options.content ?? faker.lorem.paragraph(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  author: options.author ?? {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

const createThread = (
  options: {
    messages?: MessageStructure[];
  } = {},
): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: options.messages ?? [
    createMessage(),
    createMessage(),
    createMessage(),
  ],
});

// Create common mock data
const organizations = {
  memorialHospital: createOrganization({ name: "Memorial Hospital" }),
  cityHospital: createOrganization({ name: "City Hospital" }),
  countyMedical: createOrganization({ name: "County Medical" }),
  childrensHospital: createOrganization({ name: "Children's Hospital" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
};

const locations = {
  mainHospital: createLocation({ name: "Main Hospital" }),
  emergencyCenter: createLocation({ name: "Emergency Care Center" }),
  outpatientClinic: createLocation({ name: "Outpatient Clinic" }),
  pediatricWing: createLocation({ name: "Pediatric Wing" }),
  surgicalCenter: createLocation({ name: "Surgical Center" }),
};

const providers = {
  drSmith: createProvider({
    title: "MD",
    person: createPerson({ firstName: "John", lastName: "Smith" }),
  }),
  drJohnson: createProvider({
    title: "DO",
    person: createPerson({ firstName: "Sarah", lastName: "Johnson" }),
  }),
  nurseWilliams: createProvider({
    title: "RN",
    person: createPerson({ firstName: "Michael", lastName: "Williams" }),
  }),
  drPatel: createProvider({
    title: "MD",
    person: createPerson({ firstName: "Priya", lastName: "Patel" }),
  }),
};

const jobs = {
  emergencyPhysician: createJob({
    summary: "Emergency Room Physician",
    role: "Physician",
    scope: "Emergency Medicine",
    paymentType: "HOURLY",
    paymentAmount: 200,
    location: locations.mainHospital,
  }),
  icuSpecialist: createJob({
    summary: "ICU Specialist",
    role: "Specialist",
    scope: "Intensive Care",
    paymentType: "HOURLY",
    paymentAmount: 225,
    location: locations.emergencyCenter,
  }),
  generalPractitioner: createJob({
    summary: "General Practitioner",
    role: "Physician",
    scope: "General Practice",
    paymentType: "HOURLY",
    paymentAmount: 150,
    location: locations.outpatientClinic,
  }),
  pediatricNurse: createJob({
    summary: "Pediatric Nurse",
    role: "Nurse",
    scope: "Pediatric Care",
    paymentType: "HOURLY",
    paymentAmount: 75,
    location: locations.pediatricWing,
  }),
  surgicalAssistant: createJob({
    summary: "Surgical Assistant",
    role: "Assistant",
    scope: "Surgical Support",
    paymentType: "HOURLY",
    paymentAmount: 100,
    location: locations.surgicalCenter,
  }),
};

const createOffer = (
  options: {
    id?: string;
    status?: OfferStatus;
    notes?: string | null;
    expiresAt?: Date | null;
    organization?: OrganizationStructure;
    provider?: ProviderStructure;
    job?: JobStructure;
    thread?: ThreadStructure | undefined;
  } = {},
): OfferItemStructure => {
  const now = new Date();
  const oneMonthLater = new Date(now);
  oneMonthLater.setMonth(now.getMonth() + 1);

  return {
    id: options.id ?? faker.string.uuid(),
    status: options.status ?? "PENDING",
    notes: options.notes ?? null,
    expiresAt: options.expiresAt ?? oneMonthLater,
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    organizationId:
      options.organization?.id ?? organizations.memorialHospital.id,
    providerId: options.provider?.id ?? providers.drSmith.id,
    jobId: options.job?.id ?? jobs.emergencyPhysician.id,
    organization: options.organization ?? organizations.memorialHospital,
    provider: options.provider ?? providers.drSmith,
    job: options.job ?? jobs.emergencyPhysician,
    thread: options.thread,
  } as OfferItemStructure;
};

const createOffers = (
  count = 4,
  options: {
    status?: OfferStatus;
  } = {},
): OfferGetManyQuery => {
  const defaultOffers = [
    createOffer({
      status: "PENDING",
      job: jobs.emergencyPhysician,
      organization: organizations.memorialHospital,
      provider: providers.drSmith,
    }),
    createOffer({
      status: "ACCEPTED",
      notes: "Accepted for immediate start",
      job: jobs.icuSpecialist,
      organization: organizations.cityHospital,
      provider: providers.drJohnson,
    }),
    createOffer({
      status: "REJECTED",
      notes: "Position already filled",
      job: jobs.generalPractitioner,
      organization: organizations.countyMedical,
      provider: providers.nurseWilliams,
    }),
    createOffer({
      status: "WITHDRAWN",
      notes: "Withdrawn by provider",
      job: jobs.pediatricNurse,
      organization: organizations.childrensHospital,
      provider: providers.drPatel,
    }),
  ];

  if (options.status) {
    return {
      items: Array.from({ length: count }, () =>
        createOffer({ status: options.status }),
      ),
      total: count,
    };
  }

  return {
    items: defaultOffers,
    total: defaultOffers.length,
  };
};

// Create story variants
export const Default: Story = {
  args: {
    loading: false,
    offers: createOffers(),
    error: undefined,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    offers: undefined,
    error: undefined,
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    offers: {
      items: [],
      total: 0,
    },
    error: undefined,
  },
};

export const WithError: Story = {
  args: {
    loading: false,
    offers: undefined,
    error: {
      message: "Failed to load offers",
    } as RouterError,
  },
};

export const WithAcceptMutation: Story = {
  args: {
    ...Default.args,
    accept: createMockMutation<any, any>(),
  },
};

export const WithRejectMutation: Story = {
  args: {
    ...Default.args,
    reject: createMockMutation<any, any>(),
  },
};

export const MutationsInProgress: Story = {
  args: {
    ...Default.args,
    accept: createMockMutation<any, any>({ isPending: true }),
    reject: createMockMutation<any, any>({ isPending: true }),
  },
};

export const PendingOffers: Story = {
  args: {
    ...Default.args,
    offers: createOffers(5, { status: "PENDING" }),
  },
};

export const AcceptedOffers: Story = {
  args: {
    ...Default.args,
    offers: createOffers(5, { status: "ACCEPTED" }),
  },
};

export const RejectedOffers: Story = {
  args: {
    ...Default.args,
    offers: createOffers(5, { status: "REJECTED" }),
  },
};

export const WithdrawnOffers: Story = {
  args: {
    ...Default.args,
    offers: createOffers(5, { status: "WITHDRAWN" }),
  },
};

export const ManyOffers: Story = {
  args: {
    ...Default.args,
    offers: createOffers(10),
  },
};
