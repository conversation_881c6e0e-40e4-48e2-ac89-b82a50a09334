/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import {
  ApplicationStatus,
  JobPostStatus,
  JobPostType,
  PaymentType,
  PersonRole,
  ProviderStatus,
} from "@/api";
import Applications from "@/www/providers/applications/Applications";

const meta = {
  title: "Pages/Providers/Applications/page",
  component: Applications,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/applications",
        query: {
          status: "PENDING",
        },
      },
    },
  },
} satisfies Meta<typeof Applications>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on the API
type ApplicationsQuery = RouterOutputs["applications"]["getMany"];
type ApplicationStructure = NonNullable<ApplicationsQuery["items"]>[number];
type JobStructure = NonNullable<ApplicationStructure["job"]>;
type OrganizationStructure = NonNullable<ApplicationStructure["organization"]>;
type ProviderStructure = NonNullable<ApplicationStructure["provider"]>;
type ThreadStructure = NonNullable<ApplicationStructure["thread"]>;

// Helper function to create a location
const createLocation = (name?: string) => ({
  id: faker.string.uuid(),
  name: name || faker.location.city(),
  address: {
    formatted: faker.location.streetAddress(),
    timeZone: "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  },
});

// Helper function to create an organization
const createOrganization = (name?: string): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: name || faker.company.name(),
  avatar: null,
});

// Helper function to create a job
const createJob = (
  options: {
    status?: JobPostStatus;
    type?: JobPostType;
    summary?: string;
    organization?: OrganizationStructure;
  } = {},
): JobStructure => ({
  id: faker.string.uuid(),
  type: options.type || JobPostType.PER_DIEM,
  summary: options.summary || "Emergency Room Physician",
  role: "Physician",
  scope: "Emergency Medicine",
  status: options.status || JobPostStatus.PUBLISHED,
  paymentType: PaymentType.HOURLY,
  paymentAmount: 150,
  location: createLocation(),
});

// Helper function to create a provider
const createProvider = (
  options: {
    status?: ProviderStatus;
    title?: string;
    firstName?: string;
    lastName?: string;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title || "MD",
  status: options.status || ProviderStatus.ACTIVE,
  person: {
    id: faker.string.uuid(),
    role: PersonRole.PROVIDER,
    title: options.title || "MD",
    firstName: options.firstName || faker.person.firstName(),
    lastName: options.lastName || faker.person.lastName(),
    avatar: null,
  },
});

// Helper function to create a thread
const createThread = (): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: [
    {
      id: faker.string.uuid(),
      content: faker.lorem.paragraph(),
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      author: {
        id: faker.string.uuid(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: null,
      },
    },
  ],
});

// Helper function to create an application
const createApplication = (
  status: ApplicationStatus = ApplicationStatus.PENDING,
  options: {
    notes?: string | null;
    job?: JobStructure;
    organization?: OrganizationStructure;
    provider?: ProviderStructure;
    thread?: ThreadStructure;
    createdAt?: Date;
  } = {},
): ApplicationStructure => ({
  id: faker.string.uuid(),
  status,
  createdAt: options.createdAt || faker.date.recent(),
  updatedAt: faker.date.recent(),
  organizationId: options.organization?.id || faker.string.uuid(),
  jobId: options.job?.id || faker.string.uuid(),
  providerId: options.provider?.id || faker.string.uuid(),
  job: options.job || createJob(),
  organization: options.organization || createOrganization(),
  provider: options.provider || createProvider(),
  thread: options.thread,
  notes: options.notes || null,
});

// Create mock data
const mockOrganization = createOrganization("Memorial Hospital");
const mockJob = createJob({
  summary: "Emergency Room Physician",
});
const mockProvider = createProvider();
const mockThread = createThread();

// Create applications with different statuses
const createApplications = (): ApplicationsQuery => {
  return {
    items: [
      createApplication(ApplicationStatus.PENDING, {
        job: mockJob,
        organization: mockOrganization,
        provider: mockProvider,
        thread: mockThread,
        createdAt: new Date("2024-01-20"),
      }),
      createApplication(ApplicationStatus.ACCEPTED, {
        job: createJob({ summary: "ICU Specialist" }),
        organization: createOrganization("City Hospital"),
        provider: createProvider(),
        thread: createThread(),
        notes: "Accepted for immediate start",
        createdAt: new Date("2024-01-15"),
      }),
      createApplication(ApplicationStatus.REJECTED, {
        job: createJob({ summary: "General Practitioner" }),
        organization: createOrganization("County Medical"),
        provider: createProvider(),
        thread: createThread(),
        notes: "Position already filled",
        createdAt: new Date("2024-01-10"),
      }),
      createApplication(ApplicationStatus.WITHDRAWN, {
        job: createJob({ summary: "Pediatric Nurse" }),
        organization: createOrganization("Children's Hospital"),
        provider: createProvider(),
        thread: createThread(),
        notes: "Withdrawn by applicant",
        createdAt: new Date("2024-01-05"),
      }),
    ],
    total: 4,
  };
};

const mockApplications = createApplications();

export const Default: Story = {
  args: {
    applications: mockApplications,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    applications: {
      items: [],
      total: 0,
    },
  },
};

export const WithError: Story = {
  args: {
    applications: mockApplications,
    error: {
      message: "Failed to load applications",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "applications.getMany",
        zodError: null,
      },
    } as RouterError,
  },
};

export const WithWithdrawMutation: Story = {
  args: {
    applications: mockApplications,
    withdraw: {
      mutate: async () => {},
      isPending: false,
    } as any,
  },
};

export const WithdrawingInProgress: Story = {
  args: {
    applications: mockApplications,
    withdraw: {
      mutate: async () => {},
      isPending: true,
    } as any,
  },
};
