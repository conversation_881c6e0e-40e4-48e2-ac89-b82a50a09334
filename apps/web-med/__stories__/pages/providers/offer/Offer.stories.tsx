import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  JobPostStatus,
  JobPostType,
  OfferStatus,
  PayType,
  ProviderStatus,
} from "@axa/database-medical";

import type { RouterError, RouterOutputs } from "@/api";

import Offer from "@/www/providers/offer/Offer";

import { createMockMutation } from "../../../helpers";

const meta = {
  title: "Pages/Providers/Offer/page",
  component: Offer,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/offers/abc-123",
      },
    },
  },
} satisfies Meta<typeof Offer>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type OfferGetQuery = RouterOutputs["offers"]["get"];
type OrganizationStructure = NonNullable<OfferGetQuery["organization"]>;
type ProviderStructure = NonNullable<OfferGetQuery["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type JobStructure = NonNullable<OfferGetQuery["job"]>;
type LocationStructure = NonNullable<JobStructure["location"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type ThreadStructure = NonNullable<OfferGetQuery["thread"]>;
type MessageStructure = NonNullable<ThreadStructure["messages"]>[number];

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createAddress = (
  options: {
    formatted?: string;
  } = {},
): AddressStructure => ({
  formatted:
    options.formatted ??
    `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`,
});

const createLocation = (
  options: {
    name?: string;
    address?: AddressStructure;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  address: options.address ?? createAddress(),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

const createJob = (
  options: {
    type?: JobPostType;
    status?: JobPostStatus;
    summary?: string;
    role?: string;
    scope?: string;
    paymentType?: PayType;
    paymentAmount?: number;
    location?: LocationStructure;
  } = {},
): JobStructure => ({
  id: faker.string.uuid(),
  type: options.type ?? "PER_DIEM",
  status: options.status ?? "PUBLISHED",
  summary: options.summary ?? faker.company.catchPhrase(),
  role: options.role ?? faker.person.jobTitle(),
  scope: options.scope ?? faker.lorem.paragraph(),
  paymentType: options.paymentType ?? "HOURLY",
  paymentAmount:
    options.paymentAmount ?? faker.number.int({ min: 50, max: 200 }),
  location: options.location ?? createLocation(),
});

const createMessage = (
  options: {
    content?: string;
    author?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  } = {},
): MessageStructure => ({
  id: faker.string.uuid(),
  content: options.content ?? faker.lorem.paragraph(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  author: options.author ?? {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

const createThread = (
  options: {
    messages?: MessageStructure[];
  } = {},
): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: options.messages ?? [
    createMessage(),
    createMessage(),
    createMessage(),
  ],
});

// Create common mock data
const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
};

const locations = {
  mainHospital: createLocation({ name: "Main Hospital" }),
  emergencyCenter: createLocation({ name: "Emergency Care Center" }),
  outpatientClinic: createLocation({ name: "Outpatient Clinic" }),
};

const providers = {
  drSmith: createProvider({
    title: "MD",
    person: createPerson({ firstName: "John", lastName: "Smith" }),
  }),
  drJohnson: createProvider({
    title: "DO",
    person: createPerson({ firstName: "Sarah", lastName: "Johnson" }),
  }),
  nurseWilliams: createProvider({
    title: "RN",
    person: createPerson({ firstName: "Michael", lastName: "Williams" }),
  }),
};

const jobs = {
  emergencyPhysician: createJob({
    summary: "Emergency Room Physician",
    role: "Physician",
    scope: "Emergency Medicine",
    paymentType: "HOURLY",
    paymentAmount: 150,
    location: locations.mainHospital,
  }),
  pediatrician: createJob({
    summary: "Pediatrician",
    role: "Pediatrician",
    scope: "Pediatric Care",
    paymentType: "HOURLY",
    paymentAmount: 125,
    location: locations.outpatientClinic,
  }),
  nurse: createJob({
    summary: "Registered Nurse",
    role: "Nurse",
    scope: "General Nursing",
    paymentType: "HOURLY",
    paymentAmount: 75,
    location: locations.emergencyCenter,
  }),
};

const createOffer = (
  options: {
    id?: string;
    status?: OfferStatus;
    notes?: string | null;
    expiresAt?: Date | null;
    organization?: OrganizationStructure;
    provider?: ProviderStructure;
    job?: JobStructure;
    thread?: ThreadStructure | undefined;
  } = {},
): OfferGetQuery => {
  const now = new Date();
  const oneMonthLater = new Date(now);
  oneMonthLater.setMonth(now.getMonth() + 1);

  return {
    id: options.id ?? faker.string.uuid(),
    status: options.status ?? "PENDING",
    notes: options.notes ?? null,
    expiresAt: options.expiresAt ?? oneMonthLater,
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    organizationId: options.organization?.id ?? organizations.cityGeneral.id,
    providerId: options.provider?.id ?? providers.drSmith.id,
    jobId: options.job?.id ?? jobs.emergencyPhysician.id,
    organization: options.organization ?? organizations.cityGeneral,
    provider: options.provider ?? providers.drSmith,
    job: options.job ?? jobs.emergencyPhysician,
    thread: options.thread,
  } as OfferGetQuery;
};

// Create story variants
export const Default: Story = {
  args: {
    loading: false,
    offer: createOffer(),
    error: undefined,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    offer: undefined,
    error: undefined,
  },
};

export const WithError: Story = {
  args: {
    loading: false,
    offer: undefined,
    error: {
      message: "Failed to load offer",
    } as RouterError,
  },
};

export const WithAcceptMutation: Story = {
  args: {
    ...Default.args,
    accept: createMockMutation<any, any>(),
  },
};

export const WithRejectMutation: Story = {
  args: {
    ...Default.args,
    reject: createMockMutation<any, any>(),
  },
};

export const MutationsInProgress: Story = {
  args: {
    ...Default.args,
    accept: createMockMutation<any, any>({ isPending: true }),
    reject: createMockMutation<any, any>({ isPending: true }),
  },
};

export const Accepted: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      status: "ACCEPTED",
      notes: "Accepted for immediate start",
    }),
  },
};

export const Rejected: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      status: "REJECTED",
      notes: "Position already filled",
    }),
  },
};

export const Withdrawn: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      status: "WITHDRAWN",
      notes: "Withdrawn by provider",
    }),
  },
};

export const WithThread: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      thread: createThread(),
    }),
  },
};

export const Expiring: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day from now
    }),
  },
};

export const PediatricianOffer: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      provider: providers.drJohnson,
      job: jobs.pediatrician,
      organization: organizations.memorialHealth,
    }),
  },
};

export const NurseOffer: Story = {
  args: {
    ...Default.args,
    offer: createOffer({
      provider: providers.nurseWilliams,
      job: jobs.nurse,
      organization: organizations.universityMedical,
    }),
  },
};
