/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import {
  DepartmentType,
  JobPostMode,
  JobPostPriority,
  JobPostStatus,
  JobPostType,
  LocationType,
  PayType,
  PersonRole,
  TimeBlockType,
} from "@axa/database-medical";

import type { RouterError, RouterOutputs, TimeBlockRecurrence } from "@/api";

import JobListing from "@/www/providers/job-listing/Listing";

const meta = {
  title: "Pages/Providers/JobListing/page",
  component: JobListing,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
} satisfies Meta<typeof JobListing>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type JobGetQuery = RouterOutputs["jobs"]["get"];
type OrganizationStructure = NonNullable<JobGetQuery["organization"]>;
type LocationStructure = NonNullable<JobGetQuery["location"]>;
type DepartmentStructure = NonNullable<JobGetQuery["department"]>;
type SpecialtyStructure = NonNullable<JobGetQuery["specialties"]>[number];
type ScheduleStructure = NonNullable<JobGetQuery["schedule"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type PersonStructure = NonNullable<
  DepartmentStructure["contacts"]
>[number]["person"];
type ContactStructure = NonNullable<DepartmentStructure["contacts"]>[number];
type TimeBlockStructure = NonNullable<ScheduleStructure["blocks"]>[number];
type ThreadStructure = NonNullable<JobGetQuery["thread"]>;

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.company.name(),
  avatar: options.avatar ?? null,
});

const createAddress = (
  options: {
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): AddressStructure => {
  const city = options.city || faker.location.city();
  const state = options.state || faker.location.state();

  return {
    formatted: `${faker.location.streetAddress()}, ${city}, ${state} ${faker.location.zipCode()}`,
    timeZone: options.timeZone || "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  };
};

const createLocation = (
  options: {
    name?: string;
    type?: LocationType;
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name:
    options.name || `${options.city || faker.location.city()} Medical Center`,
  type: options.type || LocationType.HOSPITAL,
  address: createAddress({
    city: options.city,
    state: options.state,
    timeZone: options.timeZone,
  }),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    title?: string | null;
    phone?: string | null;
    email?: string | null;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  role: options.role || PersonRole.PROVIDER,
  title: options.title ?? null,
  firstName: options.firstName || faker.person.firstName(),
  lastName: options.lastName || faker.person.lastName(),
  phone: options.phone ?? null,
  email: options.email ?? null,
  avatar: options.avatar ?? null,
});

const createContact = (
  options: {
    role?: string;
    person?: PersonStructure;
  } = {},
): ContactStructure => ({
  id: faker.string.uuid(),
  role: options.role || "MANAGER",
  person: options.person || createPerson(),
});

const createDepartment = (
  options: {
    name?: string;
    type?: DepartmentType;
    description?: string | null;
    contacts?: ContactStructure[];
  } = {},
): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.commerce.department(),
  type: options.type || DepartmentType.DEPARTMENT,
  description: options.description ?? null,
  contacts: options.contacts || [createContact()],
});

const createSpecialty = (
  options: {
    name?: string;
  } = {},
): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.commerce.productName(),
});

const createTimeBlock = (
  options: {
    type?: TimeBlockType;
    startsAt?: Date | null;
    endsAt?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
    startTime?: number | null;
    endTime?: number | null;
    hours?: number | null;
    recurrence?: TimeBlockRecurrence | null;
    timeZone?: string | null;
  } = {},
): TimeBlockStructure => ({
  id: faker.string.uuid(),
  type: options.type || TimeBlockType.SHIFT,
  startsAt: options.startsAt || faker.date.soon(),
  endsAt:
    options.endsAt ||
    faker.date.future({ refDate: options.startsAt || faker.date.soon() }),
  startDate: options.startDate || null,
  endDate: options.endDate || null,
  startTime: options.startTime || 9,
  endTime: options.endTime || 17,
  hours: options.hours || 8,
  recurrence: options.recurrence || null,
  timeZone: options.timeZone || "America/New_York",
});

const createSchedule = (
  options: {
    startsAt?: Date | null;
    endsAt?: Date | null;
    blocks?: TimeBlockStructure[];
  } = {},
): ScheduleStructure => ({
  id: faker.string.uuid(),
  startsAt: options.startsAt || faker.date.soon(),
  endsAt:
    options.endsAt ||
    faker.date.future({ refDate: options.startsAt || faker.date.soon() }),
  blocks: options.blocks || [createTimeBlock()],
});

const createMessage = (
  options: {
    content?: string;
    author?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  } = {},
) => ({
  id: faker.string.uuid(),
  content: options.content || faker.lorem.paragraph(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  author: options.author || {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: null,
  },
});

const createThread = (
  options: {
    messages?: ReturnType<typeof createMessage>[];
  } = {},
): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: options.messages || [createMessage()],
});

// Create a job with all required fields
const createJob = (
  options: {
    id?: string;
    status?: JobPostStatus;
    mode?: JobPostMode;
    type?: JobPostType;
    priority?: JobPostPriority;
    summary?: string;
    scope?: string | null;
    role?: string;
    paymentType?: PayType;
    paymentAmount?: number;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    schedule?: ScheduleStructure;
    publishedAt?: Date | null;
    expiresAt?: Date | null;
    filledAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    expiredAt?: Date | null;
    isApplied?: boolean;
    isOffered?: boolean;
  } = {},
): JobGetQuery => {
  const status = options.status || JobPostStatus.PUBLISHED;
  const now = new Date();

  // Set appropriate dates based on status
  let publishedAt = options.publishedAt;
  let filledAt = options.filledAt;
  let completedAt = options.completedAt;
  let cancelledAt = options.cancelledAt;
  let expiredAt = options.expiredAt;

  if (publishedAt === undefined) {
    publishedAt = status !== JobPostStatus.DRAFT ? faker.date.past() : null;
  }

  if (filledAt === undefined) {
    filledAt =
      status === JobPostStatus.FILLED || status === JobPostStatus.COMPLETED
        ? faker.date.recent()
        : null;
  }

  if (completedAt === undefined) {
    completedAt =
      status === JobPostStatus.COMPLETED ? faker.date.recent() : null;
  }

  if (cancelledAt === undefined) {
    cancelledAt =
      status === JobPostStatus.CANCELLED ? faker.date.recent() : null;
  }

  if (expiredAt === undefined) {
    expiredAt = status === JobPostStatus.EXPIRED ? faker.date.recent() : null;
  }

  const isApplied = options.isApplied ?? false;
  const isOffered = options.isOffered ?? false;

  return {
    id: options.id || faker.string.uuid(),
    createdAt: now,
    updatedAt: now,
    expiresAt: options.expiresAt || faker.date.future(),
    publishedAt,
    filledAt,
    completedAt,
    cancelledAt,
    expiredAt,
    status,
    mode: options.mode || JobPostMode.INDEPENDENT,
    type: options.type || JobPostType.PER_DIEM,
    priority: options.priority || JobPostPriority.MEDIUM,
    summary: options.summary || "Emergency Room Physician",
    scope:
      options.scope ??
      "Looking for an experienced Emergency Room Physician for night shifts. Must be board certified and have at least 5 years of experience in a high-volume ER setting.",
    role: options.role || "Physician",
    paymentType: options.paymentType || PayType.HOURLY,
    paymentAmount: options.paymentAmount || 150,
    paymentRate: 1,
    nightRate: 1.25,
    overtimeRate: 1.5,
    holidayRate: 2,
    bonusRate: 1,
    billingType: PayType.HOURLY,
    billingRate: 1.15,
    isBillable: true,
    organization:
      options.organization ||
      createOrganization({ name: "City General Hospital" }),
    location:
      options.location || createLocation({ name: "Downtown Medical Center" }),
    department:
      options.department || createDepartment({ name: "Emergency Department" }),
    specialties: options.specialties || [
      createSpecialty({ name: "Emergency Medicine" }),
    ],
    schedule: options.schedule || createSchedule(),
    analytics: {
      applicants: faker.number.int({ min: 0, max: 20 }),
      offers: faker.number.int({ min: 0, max: 5 }),
      payment: {
        min: faker.number.int({ min: 100, max: 130 }),
        max: faker.number.int({ min: 170, max: 200 }),
        avg: 150,
      },
    },
    context: {
      application: isApplied
        ? {
            id: faker.string.uuid(),
            status: "PENDING",
            createdAt: faker.date.recent(),
          }
        : null,
      offer: isOffered
        ? {
            id: faker.string.uuid(),
            status: "PENDING",
            createdAt: faker.date.recent(),
          }
        : null,
      position: null,
    },
    thread: createThread(),
    offers: [],
    applications: [],
    contacts: [],
    actions: [],
    position: undefined,
  } satisfies JobGetQuery;
};

// Create reusable entities
const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  pediatricCenter: createOrganization({ name: "Children's Pediatric Center" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
  ruralHealth: createOrganization({ name: "Rural Health Cooperative" }),
  veteransAffairs: createOrganization({
    name: "Veterans Affairs Medical Center",
  }),
};

const locations = {
  downtown: createLocation({
    name: "Downtown Medical Center",
    city: "New York",
    state: "NY",
  }),
  westSide: createLocation({
    name: "West Side Hospital",
    city: "Chicago",
    state: "IL",
  }),
  southBay: createLocation({
    name: "South Bay Medical",
    city: "San Francisco",
    state: "CA",
  }),
  eastEnd: createLocation({
    name: "East End Hospital",
    city: "Boston",
    state: "MA",
  }),
  midtown: createLocation({
    name: "Midtown Health Center",
    city: "Atlanta",
    state: "GA",
  }),
  northside: createLocation({
    name: "Northside Medical Plaza",
    city: "Seattle",
    state: "WA",
  }),
  ruralClinic: createLocation({
    name: "Rural Community Clinic",
    city: "Bozeman",
    state: "MT",
  }),
};

const departments = {
  emergency: createDepartment({ name: "Emergency Department" }),
  pediatrics: createDepartment({ name: "Pediatrics" }),
  cardiology: createDepartment({ name: "Cardiology" }),
  neurology: createDepartment({ name: "Neurology" }),
  oncology: createDepartment({ name: "Oncology" }),
  surgery: createDepartment({ name: "Surgery" }),
  radiology: createDepartment({ name: "Radiology" }),
  anesthesiology: createDepartment({ name: "Anesthesiology" }),
  psychiatry: createDepartment({ name: "Psychiatry" }),
  familyMedicine: createDepartment({ name: "Family Medicine" }),
};

const specialties = {
  emergencyMedicine: createSpecialty({ name: "Emergency Medicine" }),
  pediatrics: createSpecialty({ name: "Pediatrics" }),
  cardiology: createSpecialty({ name: "Cardiology" }),
  neurology: createSpecialty({ name: "Neurology" }),
  oncology: createSpecialty({ name: "Oncology" }),
  generalSurgery: createSpecialty({ name: "General Surgery" }),
  radiology: createSpecialty({ name: "Radiology" }),
  anesthesiology: createSpecialty({ name: "Anesthesiology" }),
  psychiatry: createSpecialty({ name: "Psychiatry" }),
  familyMedicine: createSpecialty({ name: "Family Medicine" }),
  internalMedicine: createSpecialty({ name: "Internal Medicine" }),
  orthopedics: createSpecialty({ name: "Orthopedics" }),
  urology: createSpecialty({ name: "Urology" }),
  dermatology: createSpecialty({ name: "Dermatology" }),
};

// Create different schedule types
const createDayShiftSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 7,
        endTime: 15,
        hours: 8,
        timeZone: "America/New_York",
      }),
    ],
  });

const createNightShiftSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 19,
        endTime: 3,
        hours: 8,
        timeZone: "America/New_York",
      }),
    ],
  });

const createWeekendSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 7,
        endTime: 19,
        hours: 12,
        timeZone: "America/New_York",
        recurrence: "WEEKEND" as TimeBlockRecurrence,
      }),
    ],
  });

const createOnCallSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.BLOCK,
        startTime: 0,
        endTime: 24,
        hours: 24,
        timeZone: "America/New_York",
      }),
    ],
  });

const createRotatingSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 7,
        endTime: 15,
        hours: 8,
        timeZone: "America/New_York",
        recurrence: "WEEKDAY" as TimeBlockRecurrence,
      }),
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 15,
        endTime: 23,
        hours: 8,
        timeZone: "America/New_York",
        recurrence: "WEEKDAY" as TimeBlockRecurrence,
      }),
    ],
  });

const createFlexibleSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.BLOCK,
        hours: 40,
        timeZone: "America/New_York",
      }),
    ],
  });

// Create mock jobs for different scenarios
const defaultJob = createJob({
  summary: "Emergency Room Physician",
  role: "Physician",
  scope:
    "Looking for an experienced Emergency Room Physician for night shifts. Must be board certified and have at least 5 years of experience in a high-volume ER setting.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200,
  schedule: createNightShiftSchedule(),
});

const appliedJob = createJob({
  summary: "Applied Job - Pending",
  role: "Physician",
  scope: "This job has been applied to and is pending review.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200,
  isApplied: true,
  schedule: createDayShiftSchedule(),
});

const offeredJob = createJob({
  summary: "Offered Job - Pending Decision",
  role: "Specialist",
  scope: "This job has been offered and is pending your decision.",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 300,
  isOffered: true,
  schedule: createWeekendSchedule(),
});

const filledJob = createJob({
  status: JobPostStatus.FILLED,
  summary: "Filled Job",
  role: "Nurse",
  scope: "This job has been filled.",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 150,
  schedule: createDayShiftSchedule(),
});

const expiredJob = createJob({
  status: JobPostStatus.EXPIRED,
  summary: "Expired Job",
  role: "Physician",
  scope: "This job has expired.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 180,
  schedule: createNightShiftSchedule(),
});

const draftJob = createJob({
  status: JobPostStatus.DRAFT,
  summary: "Draft Job - Not Yet Published",
  role: "Neurologist",
  scope: "This job is still in draft mode and not yet published.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.neurology,
  specialties: [specialties.neurology],
  paymentAmount: 250,
  schedule: createDayShiftSchedule(),
});

const cancelledJob = createJob({
  status: JobPostStatus.CANCELLED,
  summary: "Cancelled Job",
  role: "Radiologist",
  scope: "This job has been cancelled.",
  organization: organizations.memorialHealth,
  location: locations.midtown,
  department: departments.radiology,
  specialties: [specialties.radiology],
  paymentAmount: 220,
  schedule: createDayShiftSchedule(),
});

const completedJob = createJob({
  status: JobPostStatus.COMPLETED,
  summary: "Completed Job",
  role: "Surgeon",
  scope: "This job has been completed.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.surgery,
  specialties: [specialties.generalSurgery],
  paymentAmount: 350,
  schedule: createRotatingSchedule(),
});

// Different job types
const perDiemJob = createJob({
  type: JobPostType.PER_DIEM,
  summary: "Per Diem Anesthesiologist",
  role: "Anesthesiologist",
  scope: "Looking for a per diem anesthesiologist to cover occasional shifts.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.anesthesiology,
  specialties: [specialties.anesthesiology],
  paymentAmount: 275,
  schedule: createFlexibleSchedule(),
});

const locumTenensJob = createJob({
  type: JobPostType.TEMPORARY,
  summary: "Locum Tenens Psychiatrist",
  role: "Psychiatrist",
  scope: "Seeking a locum tenens psychiatrist for a 3-month coverage.",
  organization: organizations.veteransAffairs,
  location: locations.northside,
  department: departments.psychiatry,
  specialties: [specialties.psychiatry],
  paymentAmount: 230,
  schedule: createDayShiftSchedule(),
});

const permanentJob = createJob({
  type: JobPostType.PERMANENT,
  summary: "Permanent Family Physician",
  role: "Family Physician",
  scope: "Seeking a permanent family physician to join our growing practice.",
  organization: organizations.ruralHealth,
  location: locations.ruralClinic,
  department: departments.familyMedicine,
  specialties: [specialties.familyMedicine],
  paymentAmount: 190,
  schedule: createDayShiftSchedule(),
});

const contractJob = createJob({
  type: JobPostType.PERMANENT,
  summary: "Contract Oncologist",
  role: "Oncologist",
  scope: "Seeking a contract oncologist for a 1-year project.",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.oncology,
  specialties: [specialties.oncology],
  paymentAmount: 280,
  schedule: createDayShiftSchedule(),
});

// Different payment types
const hourlyJob = createJob({
  paymentType: PayType.HOURLY,
  summary: "Hourly Paid ER Physician",
  role: "Physician",
  scope: "Hourly paid position for an experienced ER physician.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200, // $200/hour
  schedule: createNightShiftSchedule(),
});

const dailyJob = createJob({
  paymentType: PayType.FIXED,
  summary: "Daily Rate Surgeon",
  role: "Surgeon",
  scope: "Daily rate for an experienced general surgeon.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.surgery,
  specialties: [specialties.generalSurgery],
  paymentAmount: 1500, // $1500/day
  schedule: createDayShiftSchedule(),
});

const weeklyJob = createJob({
  paymentType: PayType.FIXED,
  summary: "Weekly Rate Pediatrician",
  role: "Pediatrician",
  scope: "Weekly rate for a pediatrician to cover our clinic.",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 7500, // $7500/week
  schedule: createDayShiftSchedule(),
});

const monthlyJob = createJob({
  paymentType: PayType.FIXED,
  summary: "Monthly Rate Cardiologist",
  role: "Cardiologist",
  scope: "Monthly rate for a cardiologist to join our team.",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 30000, // $30,000/month
  schedule: createDayShiftSchedule(),
});

// Different job modes
const independentJob = createJob({
  mode: JobPostMode.INDEPENDENT,
  summary: "Independent Contractor Neurologist",
  role: "Neurologist",
  scope: "Independent contractor position for a neurologist.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.neurology,
  specialties: [specialties.neurology],
  paymentAmount: 250,
  schedule: createDayShiftSchedule(),
});

const agencyJob = createJob({
  mode: JobPostMode.ASSISTED,
  summary: "Agency Placed Radiologist",
  role: "Radiologist",
  scope: "Agency placement for a radiologist position.",
  organization: organizations.memorialHealth,
  location: locations.midtown,
  department: departments.radiology,
  specialties: [specialties.radiology],
  paymentAmount: 220,
  schedule: createDayShiftSchedule(),
});

// Different priorities
const highPriorityJob = createJob({
  priority: JobPostPriority.HIGH,
  summary: "URGENT: ER Physician Needed",
  role: "Physician",
  scope: "Urgent need for an ER physician to start immediately.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 225,
  schedule: createNightShiftSchedule(),
});

const mediumPriorityJob = createJob({
  priority: JobPostPriority.MEDIUM,
  summary: "Pediatrician Needed",
  role: "Pediatrician",
  scope: "Seeking a pediatrician to join our team within the next month.",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 180,
  schedule: createDayShiftSchedule(),
});

const lowPriorityJob = createJob({
  priority: JobPostPriority.LOW,
  summary: "Family Physician - Future Opening",
  role: "Family Physician",
  scope: "Planning for a future opening for a family physician.",
  organization: organizations.ruralHealth,
  location: locations.ruralClinic,
  department: departments.familyMedicine,
  specialties: [specialties.familyMedicine],
  paymentAmount: 170,
  schedule: createDayShiftSchedule(),
});

// Different schedule types
const dayShiftJob = createJob({
  summary: "Day Shift Nurse",
  role: "Nurse",
  scope: "Day shift position for an experienced nurse.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 120,
  schedule: createDayShiftSchedule(),
});

const nightShiftJob = createJob({
  summary: "Night Shift Physician",
  role: "Physician",
  scope: "Night shift position for an experienced physician.",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 220,
  schedule: createNightShiftSchedule(),
});

const weekendJob = createJob({
  summary: "Weekend Only Pediatrician",
  role: "Pediatrician",
  scope: "Weekend only position for a pediatrician.",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 200,
  schedule: createWeekendSchedule(),
});

const onCallJob = createJob({
  summary: "On-Call Surgeon",
  role: "Surgeon",
  scope: "On-call position for a general surgeon.",
  organization: organizations.universityMedical,
  location: locations.eastEnd,
  department: departments.surgery,
  specialties: [specialties.generalSurgery],
  paymentAmount: 300,
  schedule: createOnCallSchedule(),
});

const rotatingShiftJob = createJob({
  summary: "Rotating Shift Nurse",
  role: "Nurse",
  scope: "Rotating shift position for an experienced nurse.",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 130,
  schedule: createRotatingSchedule(),
});

const flexibleScheduleJob = createJob({
  summary: "Flexible Schedule Psychiatrist",
  role: "Psychiatrist",
  scope: "Flexible schedule position for a psychiatrist.",
  organization: organizations.veteransAffairs,
  location: locations.northside,
  department: departments.psychiatry,
  specialties: [specialties.psychiatry],
  paymentAmount: 210,
  schedule: createFlexibleSchedule(),
});

// Create a mock mutation that can be cast to the expected type
const createMockMutation = () => ({
  mutate: (params: any) => {
    console.log("Mutation called with params:", params);
  },
  mutateAsync: async (params: any) => {
    console.log("Mutation called with params:", params);
    return { id: "mock-id", status: "PENDING" };
  },
  isPending: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {},
  trpc: { path: "applications.provider.create" },
  context: {},
  // Add missing properties to match the expected type
  data: undefined,
  variables: undefined,
  isIdle: true,
  status: "idle",
  isLoading: false,
  failureCount: 0,
  failureReason: null,
  onMutate: async () => ({}),
  onSuccess: () => {},
  onError: () => {},
  onSettled: () => {},
});

// Basic story
export const Default: Story = {
  args: {
    job: defaultJob,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    job: defaultJob,
    error: new Error("Failed to load job") as RouterError,
  },
};

export const WithApplyMutation: Story = {
  args: {
    job: defaultJob,
    apply: createMockMutation() as any,
  },
};

export const ApplyingInProgress: Story = {
  args: {
    job: defaultJob,
    apply: {
      ...createMockMutation(),
      isPending: true,
    } as any,
  },
};

export const WithApplication: Story = {
  args: {
    job: appliedJob,
    withdraw: createMockMutation() as any,
  },
};

export const WithOffer: Story = {
  args: {
    job: offeredJob,
    accept: createMockMutation() as any,
    reject: createMockMutation() as any,
  },
};

export const Filled: Story = {
  args: {
    job: filledJob,
  },
};

export const Expired: Story = {
  args: {
    job: expiredJob,
  },
};

export const NoSpecialties: Story = {
  args: {
    job: {
      ...defaultJob,
      specialties: [],
    },
  },
};

export const NoDescription: Story = {
  args: {
    job: {
      ...defaultJob,
      scope: "",
    },
  },
};

// Additional stories for different job types
export const PerDiemJob: Story = {
  args: {
    job: perDiemJob,
  },
};

export const LocumTenensJob: Story = {
  args: {
    job: locumTenensJob,
  },
};

export const PermanentJob: Story = {
  args: {
    job: permanentJob,
  },
};

export const ContractJob: Story = {
  args: {
    job: contractJob,
  },
};

// Stories for different payment types
export const HourlyPayment: Story = {
  args: {
    job: hourlyJob,
  },
};

export const DailyPayment: Story = {
  args: {
    job: dailyJob,
  },
};

export const WeeklyPayment: Story = {
  args: {
    job: weeklyJob,
  },
};

export const MonthlyPayment: Story = {
  args: {
    job: monthlyJob,
  },
};

// Stories for different job modes
export const IndependentMode: Story = {
  args: {
    job: independentJob,
  },
};

export const AgencyMode: Story = {
  args: {
    job: agencyJob,
  },
};

// Stories for different priorities
export const HighPriority: Story = {
  args: {
    job: highPriorityJob,
  },
};

export const MediumPriority: Story = {
  args: {
    job: mediumPriorityJob,
  },
};

export const LowPriority: Story = {
  args: {
    job: lowPriorityJob,
  },
};

// Stories for different schedule types
export const DayShift: Story = {
  args: {
    job: dayShiftJob,
  },
};

export const NightShift: Story = {
  args: {
    job: nightShiftJob,
  },
};

export const WeekendShift: Story = {
  args: {
    job: weekendJob,
  },
};

export const OnCall: Story = {
  args: {
    job: onCallJob,
  },
};

export const RotatingShift: Story = {
  args: {
    job: rotatingShiftJob,
  },
};

export const FlexibleSchedule: Story = {
  args: {
    job: flexibleScheduleJob,
  },
};

// Additional job status stories
export const DraftStatus: Story = {
  args: {
    job: draftJob,
  },
};

export const CancelledStatus: Story = {
  args: {
    job: cancelledJob,
  },
};

export const CompletedStatus: Story = {
  args: {
    job: completedJob,
  },
};
