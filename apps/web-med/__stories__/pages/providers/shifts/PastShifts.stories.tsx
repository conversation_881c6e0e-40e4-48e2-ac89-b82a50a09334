import type { <PERSON>a, StoryObj } from "@storybook/react";

import { db } from "@axa/database-medical/generated";

import type { RouterOutputs } from "@/api";

import PastShifts from "@/www/providers/shifts/PastShifts";

const meta = {
  title: "Pages/Providers/Shifts/PastShifts",
  component: PastShifts,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/shifts",
      },
    },
  },
} satisfies Meta<typeof PastShifts>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    shifts: {
      items: [],
      total: 0,
    },
  },
};
