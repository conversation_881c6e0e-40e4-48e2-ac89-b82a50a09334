import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { db } from "@axa/database-medical/generated";

import type { RouterOutputs } from "@/api";

import ProviderShiftsPage from "@/www/providers/shifts/Shifts";

const meta = {
  title: "Pages/Providers/Shifts/page",
  component: ProviderShiftsPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/shifts",
      },
    },
  },
} satisfies Meta<typeof ProviderShiftsPage>;

export default meta;
type Story = StoryObj<typeof meta>;

const specialties = [
  db.specialty.create({
    name: "Emergency Medicine",
  }),
  db.specialty.create({
    name: "Cardiology",
  }),
  db.specialty.create({
    name: "Neurology",
  }),
  db.specialty.create({
    name: "Oncology",
  }),
];

const organization = db.organization.create({
  name: "City Healthcare Network",
  avatar: "/placeholder.svg",
});

const address = db.address.create({
  formatted: "123 Medical Center Dr, New York, NY 10001",
  timeZone: "America/New_York",
});

const department = db.department.create({
  type: "DEPARTMENT",
  name: "Emergency Room",
});

const location = db.location.create({
  name: "City General Hospital",
  type: "HOSPITAL",
  address,
  organization,
  departments: [department],
});

const person = db.person.create({
  firstName: "John",
  lastName: "Doe",
  title: "MD",
});

const provider = db.provider.create({
  status: "PENDING",
  createdAt: new Date("2024-01-01"),
  person,
  specialties,
});

const job = db.jobpost.create({
  status: "COMPLETED" as const,
  provider,
  location,
  organization,
  specialties,
});

const thread = db.thread.create({
  messages: [
    db.message.create({
      content: "Hello, I'm interested in the shift.",
      author: person,
    }),
    db.message.create({
      content: "Hello, I'm interested in the shift.",
      author: person,
    }),
    db.message.create({
      content: "Hello, I'm interested in the shift.",
      author: person,
    }),
  ],
});

const shift = {
  summary: "Morning Shift - Emergency Room",
  status: "PENDING" as const,
  startDate: new Date("2024-01-01T07:00:00Z"),
  endDate: new Date("2024-01-01T15:00:00Z"),
  cancelledAt: null,
  confirmedAt: null,
  startedAt: null,
  completedAt: null,
  location,
  organization,
  provider,
  specialties,
  department,
  job,
  thread,
};

const shift1 = db.shift.create({
  ...shift,
});

const shift2 = db.shift.create({
  ...shift,
  summary: "Afternoon Shift - Emergency Room",
  startDate: new Date("2024-01-04T12:00:00Z"),
  endDate: new Date("2024-01-04T19:00:00Z"),
  status: "CONFIRMED" as const,
  confirmedAt: new Date("2024-01-02T07:00:00Z"),
});

const shift3 = db.shift.create({
  ...shift,
  summary: "Afternoon Shift - Emergency Room",
  startDate: new Date("2024-01-08T21:00:00Z"),
  endDate: new Date("2024-01-08T23:00:00Z"),
  status: "COMPLETED" as const,
  completedAt: new Date("2024-01-08T23:00:00Z"),
});

const shift4 = db.shift.create({
  ...shift,
  summary: "Afternoon Shift - Emergency Room",
  startDate: new Date("2024-01-21T00:00:00Z"),
  endDate: new Date("2024-01-21T04:00:59Z"),
  status: "ACTIVE" as const,
  startedAt: new Date("2024-01-21T00:00:00Z"),
});
const shift5 = db.shift.create({
  ...shift,
  summary: "Afternoon Shift - Emergency Room",
  startDate: new Date("2024-01-21T00:00:00Z"),
  endDate: new Date("2024-01-21T04:00:59Z"),
  status: "CANCELLED" as const,

  cancelledAt: new Date("2024-01-21T04:00:59Z"),
});

const items = [
  shift1,
  shift2,
  shift3,
  shift4,
  shift5,
] as RouterOutputs["shifts"]["getMany"]["items"];

export const Default: Story = {
  args: {
    currentShifts: {
      data: {
        items: [shift4],
        total: 1,
      },
      loading: false,
      error: null,
    },
    upcomingShifts: {
      data: {
        items,
        total: items.length,
      },
      loading: false,
      error: null,
    },
    pastShifts: {
      data: {
        items: [shift3, shift2],
        total: 2,
      },
      loading: false,
      error: null,
    },
  },
};
export const NoUpcomingShifts: Story = {
  args: {
    upcomingShifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};

export const NoPastShifts: Story = {
  args: {
    upcomingShifts: {
      data: {
        items,
        total: items.length,
      },
      loading: false,
      error: null,
    },
    pastShifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};

export const EmptyState: Story = {
  args: {
    upcomingShifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
    pastShifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};
