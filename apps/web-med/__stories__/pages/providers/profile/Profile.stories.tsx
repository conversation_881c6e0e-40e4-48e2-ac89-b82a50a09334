import type { <PERSON>a, StoryObj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import Profile from "@/www/providers/profile/Profile";

const meta = {
  title: "Pages/Providers/Profile/page",
  component: Profile,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/profile",
      },
    },
    msw: {
      handlers: [
        trpcMsw.values.getMany.query(({ input }) => {
          return {
            items: [
              {
                id: "1",
                type: "MEDICAL_ROLE",
                value: "Physician",
              },
              {
                id: "2",
                type: "MEDICAL_ROLE",
                value: "Registered Nurse",
              },
              {
                id: "3",
                type: "MEDICAL_ROLE",
                value: "Physiotherapist",
              },
            ],
            total: 3,
          };
        }),
      ],
    },
  },
} satisfies Meta<typeof Profile>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    provider: {
      data: {
        id: "1",
      },
    },
  },
};
