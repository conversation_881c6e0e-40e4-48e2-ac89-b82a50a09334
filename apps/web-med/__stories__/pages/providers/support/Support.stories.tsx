import type { <PERSON>a, StoryObj } from "@storybook/react";

import Support from "@/www/providers/support";

const meta = {
  title: "Pages/Providers/Support/page",
  component: Support,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/support",
      },
    },
  },
} satisfies Meta<typeof Support>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};
