import type { <PERSON><PERSON>, <PERSON>Ob<PERSON> } from "@storybook/react";

import { ShiftStatus, TimeBlockRecurrence, TimeBlockType } from "@/api";
import Schedule from "@/www/providers/schedule/Schedule";

const meta = {
  title: "Pages/Providers/Schedule/page",
  component: Schedule,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/schedule",
      },
    },
  },
} satisfies Meta<typeof Schedule>;

export default meta;
type Story = StoryObj<typeof meta>;

// Use type assertion to handle complex types
const mockData = {
  shifts: {
    items: [
      {
        id: "shift_1",
        summary: "Emergency Room Coverage",
        scope: "Emergency Medicine",
        role: "Physician",
        timeZone: "America/Los_Angeles",
        startDate: new Date("2025-03-21T07:02:00"),
        endDate: new Date("2025-03-21T15:08:00"),
        status: ShiftStatus.CONFIRMED,
        paymentAmount: 150,
        organization: {
          id: "org_1",
          name: "City General Hospital",
          avatar: null,
        },
        location: {
          id: "loc_1",
          name: "Main Campus",
          type: "HOSPITAL" as any,
          address: {
            formatted: "123 Hospital Way",
            timeZone: "America/Los_Angeles",
            latitude: 37.7749,
            longitude: -122.4194,
          },
        },
      },
    ],
    total: 1,
  },
  provider: {
    data: {
      id: "provider_1",
      person: {
        id: "person_1",
        firstName: "Jane",
        lastName: "Smith",
        email: null,
        phone: null,
        avatar: null,
      },
      schedule: {
        id: "schedule_1",
        startsAt: null,
        endsAt: null,
        blocks: [
          {
            id: "block_1",
            type: TimeBlockType.AVAILABILITY,
            startDate: new Date("2025-03-07T09:00:00"),
            endDate: new Date("2025-03-21T17:00:00"),
            timeZone: "America/Los_Angeles",
            startTime: 600,
            endTime: 1700,
            dayOfWeek: 4,
            startsAt: null,
            endsAt: null,
            recurrence: TimeBlockRecurrence.WEEKLY,
          },
        ],
      },
      calendar: {
        id: "calendar_1",
        startsAt: null,
        endsAt: null,
        blocks: [
          {
            id: "cal_block_1",
            type: TimeBlockType.BLOCK,
            startDate: new Date("2025-02-23T09:00:00"),
            endDate: new Date("2025-03-24T17:00:00"),
            timeZone: "America/Los_Angeles",
            startTime: 600,
            endTime: 1700,
            dayOfWeek: 1,
            startsAt: null,
            endsAt: null,
            recurrence: TimeBlockRecurrence.WEEKLY,
          },
        ],
      },
    },
    loading: false,
  },
};

export const Default: Story = {
  args: mockData,
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const NoShifts: Story = {
  args: {
    ...mockData,
    shifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: undefined,
    },
    provider: {
      data: {
        ...mockData.provider.data,
        schedule: {
          ...mockData.provider.data.schedule,
          blocks: [],
        },
        calendar: {
          ...mockData.provider.data.calendar,
          blocks: [],
        },
      },
      loading: false,
      error: undefined,
    },
  },
};

export const NoBlocks: Story = {
  args: {
    ...mockData,
    shifts: {
      ...mockData.shifts,
      data: {
        ...mockData.shifts.data,
        items: [],
      },
    },
    provider: {
      ...mockData.provider,
      data: {
        ...mockData.provider.data,
        schedule: {
          id: "schedule_1",
          startsAt: null,
          endsAt: null,
          blocks: [],
        },
        calendar: {
          id: "calendar_1",
          startsAt: null,
          endsAt: null,
          blocks: [],
        },
      },
    },
  },
};
