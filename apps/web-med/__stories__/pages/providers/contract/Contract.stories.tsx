/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import {
  AgreementStatus,
  ContractStatus,
  ContractType,
  JobPositionStatus,
  JobPostStatus,
  SignatureStatus,
} from "@/api";
import Contract from "@/www/providers/contract/Contract";

import "@axa/database-medical";

const meta = {
  title: "Pages/Providers/Contract/page",
  component: Contract,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/contracts/123456789",
        query: {
          status: "PENDING",
        },
      },
    },
  },
} satisfies Meta<typeof Contract>;

export default meta;
type Story = StoryObj<typeof meta>;

type ContractQuery = RouterOutputs["contracts"]["get"];
type AgreementStructure = NonNullable<ContractQuery["agreements"]>[number];
type SignatureStructure = NonNullable<AgreementStructure["signatures"]>[number];

// Helper function to create a person
const createPerson = (role = "Provider") => ({
  id: faker.string.uuid(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  title: role,
  avatar: "/placeholder.svg",
});

// Helper function to create a signature
const createSignature = (
  status: SignatureStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
  } = {},
): SignatureStructure => ({
  id: faker.string.uuid(),
  status,
  createdAt: faker.date.recent(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  documensoToken: null,
  person: createPerson(
    status === SignatureStatus.SIGNED ? "Provider" : "Administrator",
  ),
});

// Helper function to create an agreement
const createAgreement = (
  status: AgreementStatus,
  options: {
    signedAt?: Date | null;
    rejectedAt?: Date | null;
    expiresAt?: Date;
    signatures?: SignatureStructure[];
  } = {},
): AgreementStructure => ({
  id: faker.string.uuid(),
  status,
  documensoId: faker.string.alphanumeric(10),
  documensoUrl: faker.internet.url(),
  createdAt: faker.date.recent(),
  expiresAt: options.expiresAt || faker.date.future(),
  signedAt: options.signedAt || null,
  rejectedAt: options.rejectedAt || null,
  signatures: options.signatures || [],
});

// Create provider signatures
const providerSignature = createSignature(SignatureStatus.SIGNED, {
  signedAt: faker.date.recent(),
});

const adminSignature = createSignature(SignatureStatus.PENDING);

// Create signatures for different states
const pendingSignatures = [
  createSignature(SignatureStatus.PENDING),
  createSignature(SignatureStatus.PENDING),
];

const signedSignatures = [
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
  createSignature(SignatureStatus.SIGNED, { signedAt: faker.date.recent() }),
];

const rejectedSignatures = [
  createSignature(SignatureStatus.REJECTED, {
    rejectedAt: faker.date.recent(),
  }),
  createSignature(SignatureStatus.PENDING),
];

// Create agreements for different states
const agreements = [
  createAgreement(AgreementStatus.SIGNED, {
    signedAt: faker.date.recent(),
    signatures: [providerSignature, adminSignature],
  }),
];

const pendingAgreements = [
  createAgreement(AgreementStatus.PENDING, {
    signatures: pendingSignatures,
  }),
];

const signedAgreements = [
  createAgreement(AgreementStatus.SIGNED, {
    signedAt: faker.date.recent(),
    signatures: signedSignatures,
  }),
];

const rejectedAgreements = [
  createAgreement(AgreementStatus.REJECTED, {
    rejectedAt: faker.date.recent(),
    signatures: rejectedSignatures,
  }),
];

const expiredAgreements = [
  createAgreement(AgreementStatus.EXPIRED, {
    expiresAt: faker.date.past(),
  }),
];

// Create a provider
const provider = {
  id: faker.string.uuid(),
  title: "Registered Nurse",
  person: {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: "/placeholder.svg",
  },
};

// Create an organization
const organization = {
  id: faker.string.uuid(),
  name: "City General Hospital",
  avatar: "/placeholder.svg",
};

// Create a job position
const position = {
  id: faker.string.uuid(),
  summary: "Emergency Room Physician - Night Shift",
  status: JobPositionStatus.ACTIVE,
  provider,
  job: {
    id: faker.string.uuid(),
    summary: "Emergency Room Physician - Night Shift",
    status: JobPostStatus.PUBLISHED,
    scope:
      "Provide emergency medical care during night shifts (8pm-8am). Responsibilities include patient assessment, diagnosis, treatment, and coordination with other healthcare professionals.",
  },
};

// Create the base contract
const baseContract: ContractQuery = {
  id: faker.string.uuid(),
  title: "Emergency Room Physician - Night Shift",
  type: ContractType.EMPLOYMENT,
  status: ContractStatus.PENDING,
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  deletedAt: null,
  expiresAt: faker.date.future(),
  agreements,
  provider,
  organization,
  position,
};

export const Default: Story = {
  args: {
    contract: baseContract,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: {
      message: "Failed to load contract",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "contracts.get",
        zodError: null,
      },
    } as RouterError,
  },
};

export const Draft: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.DRAFT,
      agreements: [],
    } as ContractQuery,
  },
};

export const Pending: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.PENDING,
      agreements: pendingAgreements,
    } as ContractQuery,
  },
};

export const Signed: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.SIGNED,
      agreements: signedAgreements,
    } as ContractQuery,
  },
};

export const Rejected: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.REJECTED,
      agreements: rejectedAgreements,
    } as ContractQuery,
  },
};

export const Expired: Story = {
  args: {
    contract: {
      ...baseContract,
      status: ContractStatus.EXPIRED,
      agreements: expiredAgreements,
    } as ContractQuery,
  },
};

export const NonCompete: Story = {
  args: {
    contract: {
      ...baseContract,
      title: "Non-Compete Agreement",
      type: ContractType.NON_COMPETE,
    } as ContractQuery,
  },
};

export const NonDisclosure: Story = {
  args: {
    contract: {
      ...baseContract,
      title: "Non-Disclosure Agreement",
      type: ContractType.NON_DISCLOSURE,
    } as ContractQuery,
  },
};

export const ServiceRate: Story = {
  args: {
    contract: {
      ...baseContract,
      title: "Service Rate Agreement",
      type: ContractType.SERVICE_RATE,
    } as ContractQuery,
  },
};

export const ServiceAgreement: Story = {
  args: {
    contract: {
      ...baseContract,
      title: "Service Agreement",
      type: ContractType.SERVICE_AGREEMENT,
    } as ContractQuery,
  },
};

export const Other: Story = {
  args: {
    contract: {
      ...baseContract,
      title: "Miscellaneous Contract",
      type: ContractType.OTHER,
    } as ContractQuery,
  },
};

export const NoAgreements: Story = {
  args: {
    contract: {
      ...baseContract,
      agreements: [],
    } as ContractQuery,
  },
};

export const NoProvider: Story = {
  args: {
    contract: {
      ...baseContract,
      provider: undefined,
    } as ContractQuery,
  },
};

export const NoPosition: Story = {
  args: {
    contract: {
      ...baseContract,
      position: undefined,
    } as ContractQuery,
  },
};
