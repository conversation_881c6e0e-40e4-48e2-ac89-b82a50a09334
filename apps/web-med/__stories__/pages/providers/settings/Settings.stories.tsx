import type { <PERSON>a, StoryObj } from "@storybook/react";

import Settings from "@/www/providers/settings";

const meta = {
  title: "Pages/Providers/Settings/page",
  component: Settings,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/settings",
      },
    },
  },
} satisfies Meta<typeof Settings>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};
