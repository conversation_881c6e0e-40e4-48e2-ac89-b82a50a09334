import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  PersonRole,
  ProviderStatus,
  RouterError,
  RouterOutputs,
  ShiftStatus,
} from "@/api";

import { PayoutStatus } from "@/api";
import Earnings from "@/www/providers/earnings/Earnings";

const meta = {
  title: "Pages/Providers/Earnings/page",
  component: Earnings,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/earnings",
      },
    },
  },
} satisfies Meta<typeof Earnings>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type AccountGetQuery = RouterOutputs["billing"]["accounts"]["provider"]["get"];
type PayoutsGetManyQuery = RouterOutputs["billing"]["payouts"]["getMany"];
type PayoutStructure = PayoutsGetManyQuery["items"][number];
type ProviderStructure = NonNullable<PayoutStructure["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type ShiftStructure = NonNullable<PayoutStructure["shifts"]>[number];
type EarningsAnalyticsQuery = RouterOutputs["billing"]["earnings"]["analytics"];
type UpcomingPaymentStructure = NonNullable<
  EarningsAnalyticsQuery["upcomingPayment"]
>;

// Helper functions to create mock data
const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  role: options.role ?? "PROVIDER",
  avatar: options.avatar ?? faker.image.avatar(),
});

const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: NonNullable<PersonStructure>;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

const createShift = (
  options: {
    id?: string;
    status?: ShiftStatus;
    summary?: string;
    scope?: string;
    startDate?: Date;
    endDate?: Date;
    hours?: number;
    paymentRate?: number;
    paymentAmount?: number;
    overtimeAmount?: number;
    holidayAmount?: number;
    nightAmount?: number;
    paymentTotal?: number;
    provider?: NonNullable<ProviderStructure>;
  } = {},
): ShiftStructure => ({
  id: options.id ?? faker.string.uuid(),
  status: options.status ?? "COMPLETED",
  summary:
    options.summary ??
    faker.helpers.arrayElement([
      "Morning Shift - Emergency Room",
      "Afternoon Shift - Cardiology",
      "Night Shift - Pediatrics",
    ]),
  scope: options.scope ?? "General duties in the department",
  startDate: options.startDate ?? faker.date.recent(),
  endDate: options.endDate ?? faker.date.soon(),
  hours: options.hours ?? faker.number.int({ min: 4, max: 12 }),
  paymentRate: options.paymentRate ?? faker.number.int({ min: 50, max: 200 }),
  paymentAmount:
    options.paymentAmount ?? faker.number.int({ min: 200, max: 2000 }),
  overtimeAmount:
    options.overtimeAmount ?? faker.number.int({ min: 0, max: 500 }),
  holidayAmount:
    options.holidayAmount ?? faker.number.int({ min: 0, max: 500 }),
  nightAmount: options.nightAmount ?? faker.number.int({ min: 0, max: 500 }),
  paymentTotal:
    options.paymentTotal ?? faker.number.int({ min: 200, max: 3000 }),
  provider: options.provider ?? createProvider(),
});

const createPayout = (
  options: {
    id?: string;
    amount?: number;
    status?: PayoutStatus;
    providerId?: string;
    overtimeAmount?: number;
    holidayAmount?: number;
    nightAmount?: number;
    paidAt?: Date | null;
    provider?: ProviderStructure;
    shifts?: ShiftStructure[];
  } = {},
): PayoutStructure => {
  const provider = options.provider ?? createProvider();

  return {
    id: options.id ?? faker.string.uuid(),
    amount: options.amount ?? faker.number.int({ min: 500, max: 5000 }),
    status:
      options.status ??
      faker.helpers.arrayElement([
        PayoutStatus.COMPLETED,
        PayoutStatus.PENDING,
        PayoutStatus.FAILED,
      ]),
    providerId: options.providerId ?? provider.id,
    overtimeAmount:
      options.overtimeAmount ?? faker.number.int({ min: 50, max: 500 }),
    holidayAmount:
      options.holidayAmount ?? faker.number.int({ min: 50, max: 500 }),
    nightAmount: options.nightAmount ?? faker.number.int({ min: 50, max: 500 }),
    paidAt:
      options.paidAt ?? faker.helpers.arrayElement([faker.date.recent(), null]),
    provider: provider,
    shifts:
      options.shifts ??
      Array.from({ length: 2 }, () => createShift({ provider })),
  };
};

const createAccount = (
  options: {
    id?: string;
    enabled?: boolean;
    submitted?: boolean;
    requirements?: NonNullable<AccountGetQuery>["requirements"];
  } = {},
): AccountGetQuery => ({
  id: options.id ?? faker.string.uuid(),
  enabled: options.enabled ?? true,
  submitted: options.submitted ?? true,
  requirements: options.requirements ?? {
    currently_due: [],
    eventually_due: [],
    past_due: [],
    pending_verification: [],
    alternatives: [],
    current_deadline: null,
    disabled_reason: null,
    errors: [],
  },
});

const createUpcomingPayment = (
  options: {
    paymentDate?: Date;
    shiftsCount?: number;
    totalHours?: number;
    totalAmount?: number;
    hoursByCategory?: {
      regularHours: number;
      overtimeHours: number;
      holidayHours: number;
    };
  } = {},
): UpcomingPaymentStructure => ({
  paymentDate: options.paymentDate ?? faker.date.future(),
  shiftsCount: options.shiftsCount ?? faker.number.int({ min: 3, max: 10 }),
  totalHours: options.totalHours ?? faker.number.int({ min: 24, max: 80 }),
  totalAmount:
    options.totalAmount ?? faker.number.int({ min: 2000, max: 10000 }),
  hoursByCategory: options.hoursByCategory ?? {
    regularHours: faker.number.int({ min: 20, max: 40 }),
    overtimeHours: faker.number.int({ min: 0, max: 10 }),
    holidayHours: faker.number.int({ min: 0, max: 8 }),
  },
});

const createEarningsAnalytics = (
  options: {
    totalEarnings?: number;
    totalEarningsChange?: number;
    hoursWorked?: number;
    hoursWorkedChange?: number;
    averageRate?: number;
    averageRateChange?: number;
    paymentsDateRange?: {
      min: Date;
      max: Date;
    };
    upcomingPayment?: UpcomingPaymentStructure | null;
  } = {},
): EarningsAnalyticsQuery => ({
  totalEarnings:
    options.totalEarnings ?? faker.number.int({ min: 5000, max: 50000 }),
  totalEarningsChange:
    options.totalEarningsChange ?? faker.number.int({ min: -20, max: 20 }),
  hoursWorked: options.hoursWorked ?? faker.number.int({ min: 20, max: 60 }),
  hoursWorkedChange:
    options.hoursWorkedChange ?? faker.number.int({ min: -10, max: 10 }),
  averageRate: options.averageRate ?? faker.number.int({ min: 100, max: 200 }),
  averageRateChange:
    options.averageRateChange ?? faker.number.int({ min: -5, max: 5 }),
  paymentsDateRange: options.paymentsDateRange ?? {
    min: faker.date.past(),
    max: faker.date.future(),
  },
  upcomingPayment: options.upcomingPayment ?? createUpcomingPayment(),
});

// Create common mock data
const providers = {
  drSmith: createProvider({
    title: "MD",
    status: "ACTIVE",
    person: createPerson({
      firstName: "John",
      lastName: "Smith",
      role: "PROVIDER",
    }),
  }),
  nurseJohnson: createProvider({
    title: "RN",
    status: "ACTIVE",
    person: createPerson({
      firstName: "Sarah",
      lastName: "Johnson",
      role: "PROVIDER",
    }),
  }),
};

// Create mock data for stories
const mockAccount = createAccount();

const mockPayouts = {
  items: [
    createPayout({
      status: PayoutStatus.COMPLETED,
      provider: providers.drSmith,
      paidAt: faker.date.recent(),
      amount: 3500,
    }),
    createPayout({
      status: PayoutStatus.PENDING,
      provider: providers.drSmith,
      paidAt: null,
      amount: 4200,
    }),
    createPayout({
      status: PayoutStatus.FAILED,
      provider: providers.drSmith,
      paidAt: faker.date.recent(),
      amount: 1800,
    }),
  ],
  total: 3,
};

const mockEarnings = createEarningsAnalytics({
  totalEarnings: 12500,
  totalEarningsChange: 15,
  hoursWorked: 42,
  hoursWorkedChange: 5,
  averageRate: 150,
  averageRateChange: 3,
});

const mockGenerateTaxReport = {
  mutate: () => Promise.resolve(),
  mutateAsync: () => Promise.resolve(null),
  isLoading: false,
  isError: false,
  error: null,
  reset: () => {},
  context: undefined,
  failureCount: 0,
  failureReason: null,
  isPaused: false,
  status: "idle",
  variables: undefined,
  isSuccess: false,
  data: undefined,
  isIdle: true,
  isPending: false,
  submittedAt: Date.now(),
  trpc: { path: "billing.earnings.generateTaxReport" },
} as const;

// Create more detailed account scenarios
const accountScenarios = {
  complete: createAccount({
    enabled: true,
    submitted: true,
    requirements: {
      currently_due: [],
      eventually_due: [],
      past_due: [],
      pending_verification: [],
      alternatives: [],
      current_deadline: null,
      disabled_reason: null,
      errors: [],
    },
  }),

  needsExternalAccount: createAccount({
    enabled: false,
    submitted: false,
    requirements: {
      currently_due: ["external_account"],
      eventually_due: ["external_account"],
      past_due: [],
      pending_verification: [],
      alternatives: [],
      current_deadline: null,
      disabled_reason: "requirements.past_due",
      errors: [],
    },
  }),

  needsIdentityVerification: createAccount({
    enabled: false,
    submitted: true,
    requirements: {
      currently_due: ["person.verification.document"],
      eventually_due: ["person.verification.document"],
      past_due: ["person.verification.document"],
      pending_verification: [],
      alternatives: [],
      current_deadline: faker.date.future().getTime(),
      disabled_reason: "requirements.past_due",
      errors: [],
    },
  }),

  pendingVerification: createAccount({
    enabled: false,
    submitted: true,
    requirements: {
      currently_due: [],
      eventually_due: [],
      past_due: [],
      pending_verification: ["person.verification.document"],
      alternatives: [],
      current_deadline: faker.date.future().getTime(),
      disabled_reason: "requirements.pending_verification",
      errors: [],
    },
  }),

  missingTaxInfo: createAccount({
    enabled: false,
    submitted: true,
    requirements: {
      currently_due: ["person.tax_id"],
      eventually_due: ["person.tax_id"],
      past_due: ["person.tax_id"],
      pending_verification: [],
      alternatives: [],
      current_deadline: faker.date.future().getTime(),
      disabled_reason: "requirements.past_due",
      errors: [],
    },
  }),

  multipleRequirements: createAccount({
    enabled: false,
    submitted: true,
    requirements: {
      currently_due: [
        "external_account",
        "person.tax_id",
        "person.address.city",
      ],
      eventually_due: [
        "external_account",
        "person.tax_id",
        "person.address.city",
      ],
      past_due: ["external_account", "person.tax_id"],
      pending_verification: [],
      alternatives: [],
      current_deadline: faker.date.future().getTime(),
      disabled_reason: "requirements.past_due",
      errors: [],
    },
  }),

  withErrors: createAccount({
    enabled: false,
    submitted: true,
    requirements: {
      currently_due: ["external_account"],
      eventually_due: ["external_account"],
      past_due: ["external_account"],
      pending_verification: [],
      alternatives: [],
      current_deadline: faker.date.future().getTime(),
      disabled_reason: "requirements.past_due",
      errors: [
        {
          code: "invalid_bank_account",
          reason: "The bank account provided is invalid.",
          requirement: "external_account",
        },
      ],
    },
  }),
};

// Create more detailed earnings scenarios
const earningsScenarios = {
  standard: createEarningsAnalytics({
    totalEarnings: 12500,
    totalEarningsChange: 15,
    hoursWorked: 42,
    hoursWorkedChange: 5,
    averageRate: 150,
    averageRateChange: 3,
  }),

  noUpcomingPayment: createEarningsAnalytics({
    totalEarnings: 8750,
    totalEarningsChange: -5,
    hoursWorked: 35,
    hoursWorkedChange: -10,
    averageRate: 125,
    averageRateChange: 0,
    upcomingPayment: null,
  }),

  highEarnings: createEarningsAnalytics({
    totalEarnings: 45000,
    totalEarningsChange: 35,
    hoursWorked: 60,
    hoursWorkedChange: 20,
    averageRate: 200,
    averageRateChange: 15,
    upcomingPayment: createUpcomingPayment({
      totalHours: 75,
      totalAmount: 15000,
      shiftsCount: 8,
      hoursByCategory: {
        regularHours: 40,
        overtimeHours: 25,
        holidayHours: 10,
      },
    }),
  }),

  lowEarnings: createEarningsAnalytics({
    totalEarnings: 3200,
    totalEarningsChange: -25,
    hoursWorked: 20,
    hoursWorkedChange: -30,
    averageRate: 80,
    averageRateChange: -10,
    upcomingPayment: createUpcomingPayment({
      totalHours: 15,
      totalAmount: 1200,
      shiftsCount: 2,
      hoursByCategory: {
        regularHours: 15,
        overtimeHours: 0,
        holidayHours: 0,
      },
    }),
  }),

  firstMonth: createEarningsAnalytics({
    totalEarnings: 5000,
    totalEarningsChange: 100,
    hoursWorked: 25,
    hoursWorkedChange: 100,
    averageRate: 100,
    averageRateChange: 0,
  }),
};

// Update existing story variants and add new ones
export const Default: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    account: {
      data: undefined,
      loading: true,
      error: null,
    },
    payouts: {
      data: undefined,
      loading: true,
      error: null,
    },
    earnings: {
      data: undefined,
      loading: true,
      error: null,
    },
  },
};

export const WithError: Story = {
  args: {
    account: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load account") as RouterError,
    },
    payouts: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load payouts") as RouterError,
    },
    earnings: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load earnings") as RouterError,
    },
  },
};

export const Empty: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: { items: [], total: 0 },
      loading: false,
      error: null,
    },
    earnings: {
      data: createEarningsAnalytics({
        totalEarnings: 0,
        totalEarningsChange: 0,
        hoursWorked: 0,
        hoursWorkedChange: 0,
        averageRate: 0,
        averageRateChange: 0,
        upcomingPayment: null,
      }),
      loading: false,
      error: null,
    },
  },
};

export const NeedsBankAccount: Story = {
  args: {
    account: {
      data: accountScenarios.needsExternalAccount,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const NeedsIdentityVerification: Story = {
  args: {
    account: {
      data: accountScenarios.needsIdentityVerification,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const PendingVerification: Story = {
  args: {
    account: {
      data: accountScenarios.pendingVerification,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const MissingTaxInfo: Story = {
  args: {
    account: {
      data: accountScenarios.missingTaxInfo,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const MultipleRequirements: Story = {
  args: {
    account: {
      data: accountScenarios.multipleRequirements,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const WithAccountErrors: Story = {
  args: {
    account: {
      data: accountScenarios.withErrors,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};

export const NoUpcomingPayment: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.noUpcomingPayment,
      loading: false,
      error: null,
    },
  },
};

export const HighEarnings: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.highEarnings,
      loading: false,
      error: null,
    },
  },
};

export const LowEarnings: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.lowEarnings,
      loading: false,
      error: null,
    },
  },
};

export const FirstMonth: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: mockPayouts,
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.firstMonth,
      loading: false,
      error: null,
    },
  },
};

export const WithManyPayouts: Story = {
  args: {
    account: {
      data: accountScenarios.complete,
      loading: false,
      error: null,
    },
    payouts: {
      data: {
        items: Array.from({ length: 10 }, (_, i) =>
          createPayout({
            id: `payout-${i + 1}`,
            status: ["COMPLETED", "PENDING", "FAILED"][i % 3] as PayoutStatus,
            amount: faker.number.int({ min: 1000, max: 6000 }),
            paidAt: i % 3 === 0 ? faker.date.recent() : null,
            provider: providers.drSmith,
          }),
        ),
        total: 10,
      },
      loading: false,
      error: null,
    },
    earnings: {
      data: earningsScenarios.standard,
      loading: false,
      error: null,
    },
  },
};
