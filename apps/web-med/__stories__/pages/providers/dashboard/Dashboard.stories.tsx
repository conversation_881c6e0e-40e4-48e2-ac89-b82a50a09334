import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import {
  ApplicationStatus,
  DepartmentType,
  JobPostMode,
  JobPostPriority,
  JobPostStatus,
  JobPostType,
  LocationType,
  OfferStatus,
  PayType,
  PersonRole,
  ProviderStatus,
  ShiftStatus,
  TimeBlockType,
  VerificationStatus,
} from "@axa/database-medical";

import type { ProviderDashboardProps } from "@/www/providers/dashboard/Dashboard";

import { RouterOutputs } from "@/api";
import Dashboard from "@/www/providers/dashboard/Dashboard";

const meta = {
  title: "Pages/Providers/Dashboard/page",
  component: Dashboard,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app",
      },
    },
  },
} satisfies Meta<typeof Dashboard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Create mock data that matches the expected types
const mockProvider: ProviderDashboardProps["provider"] = {
  data: {
    id: "provider_1",
    title: "<PERSON><PERSON> <PERSON>",
    status: ProviderStatus.ACTIVE,
    score: 100,
    spokenLanguages: ["English"],
    person: {
      id: "person_1",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      phone: null,
      avatar: null,
      role: PersonRole.PROVIDER,
    },
    address: {
      id: "address_1",
      formatted: "123 Medical Dr",
      latitude: 37.7749,
      longitude: -122.4194,
      timeZone: "America/Los_Angeles",
    },
    specialties: [],
    qualifications: [],
    experiences: [],
    reviews: [],
    shifts: [],
    verification: {
      id: "verification_1",
      status: VerificationStatus.APPROVED,
      backgroundCheckStatus: VerificationStatus.APPROVED,
      i9VerificationStatus: VerificationStatus.APPROVED,
      identityVerificationStatus: VerificationStatus.APPROVED,
      verifiedAt: new Date(),
      i9VerifiedAt: new Date(),
      identityVerifiedAt: new Date(),
      backgroundVerifiedAt: new Date(),
    },
    settings: {
      id: "settings_1",
      openToWork: true,
      openToOnCall: false,
      openToRelocate: false,
      openToTravel: false,
    },
    accountId: null,
    accountStatus: null,
    calendarId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  },
  loading: false,
};

const mockAnalytics: ProviderDashboardProps["analytics"] = {
  data: {
    shiftsByStatus: {
      ACTIVE: 1,
      APPROVED: 2,
      CANCELLED: 0,
      COMPLETED: 5,
      CONFIRMED: 2,
      PENDING: 0,
      REJECTED: 0,
    },
    applicationsByStatus: {
      ACCEPTED: 3,
      PENDING: 1,
      REJECTED: 0,
      WITHDRAWN: 0,
      CLOSED: 0,
    },
    offersByStatus: {
      ACCEPTED: 3,
      PENDING: 0,
      REJECTED: 0,
      WITHDRAWN: 0,
      CLOSED: 0,
    },
    shiftsByDate: {},
    earningsByDate: {},
    totalEarnings: 10000,
    upcomingShifts: [],
    recentReviews: [],
    verification: null,
    stats: {
      totalShifts: 10,
      completedShifts: 5,
      pendingApplications: 1,
      pendingOffers: 0,
      averageRating: 4.5,
    },
  },
  loading: false,
};

const mockShifts: ProviderDashboardProps["shifts"] = {
  data: {
    items: [
      {
        id: "shift_1",
        summary: "Emergency Room Coverage",
        scope: "Emergency Medicine",
        role: "Physician",
        timeZone: "America/Los_Angeles",
        startDate: new Date("2024-03-20T19:00:00"),
        endDate: new Date("2024-03-21T07:00:00"),
        hours: 12,
        overtimeHours: 0,
        nightTimeHours: 12,
        holidayTimeHours: 0,
        status: ShiftStatus.CONFIRMED,
        paymentTotal: 1200,
        paymentType: PayType.HOURLY,
        paymentRate: 150,
        paymentAmount: 1200,
        overtimeRate: 1.5,
        overtimeAmount: 0,
        nightRate: 1.25,
        nightAmount: 0,
        holidayRate: 2,
        holidayAmount: 0,
        bonusRate: 1,
        bonusAmount: 0,
        organization: {
          id: "org_1",
          name: "City General Hospital",
          avatar: null,
        },
        location: {
          id: "loc_1",
          name: "Main Campus",
          type: LocationType.HOSPITAL,
          address: {
            formatted: "123 Hospital Way",
            timeZone: "America/Los_Angeles",
            latitude: 37.7749,
            longitude: -122.4194,
          },
        },
        provider: {
          id: "provider_1",
          person: {
            id: "person_1",
            firstName: "Jane",
            lastName: "Smith",
            avatar: null,
            email: null,
            phone: null,
            role: PersonRole.PROVIDER,
          },
        },
        department: undefined,
        specialties: [],
        contacts: [],
        job: {
          id: "job_1",
        },
        invoice: undefined,
        review: undefined,
        organizationId: "org_1",
        locationId: "loc_1",
        jobId: "job_1",
        scheduleId: "schedule_1",
        departmentId: null,
        providerId: "provider_1",
        payoutId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      },
    ],
    total: 1,
  },
  loading: false,
};

const mockJobs: ProviderDashboardProps["jobs"] = {
  data: {
    items: [
      {
        id: "job_1",
        summary: "Night Shift ER Physician",
        role: "Physician",
        scope: "Emergency Medicine",
        status: JobPostStatus.PUBLISHED,
        type: JobPostType.PER_DIEM,
        organization: {
          id: "org_1",
          name: "City General Hospital",
          avatar: null,
        },
        location: {
          id: "loc_1",
          name: "Main Campus",
          type: LocationType.HOSPITAL,
          address: {
            formatted: "123 Hospital Way",
            timeZone: "America/Los_Angeles",
            latitude: 37.7749,
            longitude: -122.4194,
          },
        },
        specialties: [],
        department: undefined,
        schedule: undefined,
        thread: undefined,
        contacts: [],
        documents: [],
        shifts: [],
        applications: [],
        offers: [],
        contracts: [],
        actions: [],
        provider: undefined,
        organizationId: "org_1",
        locationId: "loc_1",
        departmentId: null,
        providerId: null,
        scheduleId: null,
        mode: JobPostMode.INDEPENDENT,
        priority: JobPostPriority.MEDIUM,
        paymentType: PayType.HOURLY,
        paymentAmount: 150,
        paymentRate: 1,
        nightRate: 1.25,
        overtimeRate: 1.5,
        holidayRate: 2,
        bonusRate: 1,
        billingType: PayType.HOURLY,
        billingRate: 1.15,
        isBillable: true,
        publishedAt: new Date("2024-03-18"),
        filledAt: null,
        cancelledAt: null,
        completedAt: null,
        expiredAt: null,
        expiresAt: null,
        startsAt: new Date("2024-03-20"),
        endsAt: new Date("2024-03-21"),
        createdAt: new Date("2024-03-18"),
        updatedAt: new Date("2024-03-18"),
        deletedAt: null,
      },
    ],
    total: 1,
  },
  loading: false,
};

// Mock mutation functions
const mockMutation = {
  mutate: () => {},
  isPending: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {},
  context: null,
  failureCount: 0,
  failureReason: null,
  isPaused: false,
  status: "idle",
  submittedAt: 0,
  variables: undefined,
  data: undefined,
  isIdle: true,
  mutateAsync: async () => undefined,
};

// Create base args that can be reused
const baseArgs: ProviderDashboardProps = {
  loading: false,
  provider: mockProvider,
  shifts: mockShifts,
  jobs: mockJobs,
  offers: { data: { items: [], total: 0 }, loading: false },
  analytics: mockAnalytics,
  apply: mockMutation,
  checkIn: mockMutation,
  checkOut: mockMutation,
  confirm: mockMutation,
  cancel: mockMutation,
};

export const Loading: Story = {
  args: {
    ...baseArgs,
    loading: true,
  },
};

export const NewProvider: Story = {
  args: {
    ...baseArgs,
    provider: {
      ...mockProvider,
      data: {
        ...mockProvider.data!,
        status: ProviderStatus.PENDING,
        verification: {
          ...mockProvider.data!.verification!,
          status: VerificationStatus.PENDING,
          backgroundCheckStatus: VerificationStatus.PENDING,
          i9VerificationStatus: VerificationStatus.PENDING,
          identityVerificationStatus: VerificationStatus.PENDING,
          verifiedAt: null,
          i9VerifiedAt: null,
          identityVerifiedAt: null,
          backgroundVerifiedAt: null,
        },
      },
    },
    shifts: { data: { items: [], total: 0 }, loading: false },
    jobs: { data: { items: [], total: 0 }, loading: false },
  },
};

export const PendingVerification: Story = {
  args: {
    ...baseArgs,
    provider: {
      ...mockProvider,
      data: {
        ...mockProvider.data!,
        status: ProviderStatus.PENDING,
        verification: {
          ...mockProvider.data!.verification!,
          status: VerificationStatus.PENDING,
          backgroundCheckStatus: VerificationStatus.PENDING,
          i9VerificationStatus: VerificationStatus.APPROVED,
          identityVerificationStatus: VerificationStatus.APPROVED,
          verifiedAt: null,
          backgroundVerifiedAt: null,
        },
      },
    },
    shifts: { data: { items: [], total: 0 }, loading: false },
    jobs: { data: { items: [], total: 0 }, loading: false },
  },
};

export const Active: Story = {
  args: baseArgs,
};
