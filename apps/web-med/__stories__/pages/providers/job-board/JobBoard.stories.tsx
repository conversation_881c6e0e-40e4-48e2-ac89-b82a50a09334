import type { <PERSON><PERSON>, <PERSON><PERSON>b<PERSON> } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { FacilityType, RouterOutputs, ScheduleType } from "@/api";
import type { api } from "@/api/client";

import {
  DepartmentType,
  JobPostMode,
  JobPostPriority,
  JobPostStatus,
  JobPostType,
  PaymentType,
  PersonRole,
  TimeBlockType,
} from "@/api";
import { trpcMsw } from "@/api/mock";
import JobBoard from "@/www/providers/job-board/JobBoard";

import { createMockMutation } from "../../../helpers";

const meta = {
  title: "Pages/Providers/JobBoard/page",
  component: JobBoard,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/jobs",
        query: {
          status: "PUBLISHED",
          type: "PER_DIEM",
          priority: "MEDIUM",
          schedule: "FULL_TIME",
          experience: "MID",
        },
      },
    },
    msw: {
      handlers: [
        trpcMsw.values.getMany.query(({ input }) => {
          if (input.type === "MEDICAL_ROLE") {
            return {
              items: [
                {
                  id: "1",
                  value: "Cardiologist",
                  type: "MEDICAL_ROLE",
                },
                {
                  id: "2",
                  value: "Pediatrician",
                  type: "MEDICAL_ROLE",
                },
                {
                  id: "3",
                  value: "Neurologist",
                  type: "MEDICAL_ROLE",
                },
                {
                  id: "4",
                  value: "General Practitioner",
                  type: "MEDICAL_ROLE",
                },
                {
                  id: "5",
                  value: "Orthopedic Surgeon",
                  type: "MEDICAL_ROLE",
                },
              ],
              total: 5,
            };
          }
        }),
        trpcMsw.specialties.getMany.query(({ input }) => {
          return {
            items: [
              {
                id: "1",
                name: "Cardiology",
                description:
                  "Specializing in the heart and blood vessels, including heart disease, heart failure, and heart surgery.",
              },
              {
                id: "2",
                name: "Pediatrics",
                description:
                  "Specializing in children's health, including newborn care, pediatric surgery, and pediatric intensive care.",
              },
              {
                id: "3",
                name: "Neurology",
                description:
                  "Specializing in the nervous system, including brain disorders, stroke, and epilepsy.",
              },
            ],
            total: 3,
          };
        }),
        trpcMsw.locations.getMany.query(({ input }) => {
          return {
            items: [
              {
                id: "1",
                type: "HOSPITAL",
                name: "Hospital 1",
                address: {
                  formatted: "123 Main St, Anytown, USA",
                },
              },
            ],
            total: 1,
          };
        }),
      ],
    },
  },
} satisfies Meta<typeof JobBoard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type JobPostQuery = RouterOutputs["jobs"]["getMany"];
type JobPostStructure = NonNullable<JobPostQuery["items"]>[number];
type OrganizationStructure = NonNullable<JobPostStructure["organization"]>;
type LocationStructure = NonNullable<JobPostStructure["location"]>;
type DepartmentStructure = NonNullable<JobPostStructure["department"]>;
type SpecialtyStructure = NonNullable<JobPostStructure["specialties"]>[number];
type ScheduleStructure = NonNullable<JobPostStructure["schedule"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type PersonStructure = NonNullable<
  DepartmentStructure["contacts"]
>[number]["person"];
type ContactStructure = NonNullable<DepartmentStructure["contacts"]>[number];
type TimeBlockStructure = NonNullable<ScheduleStructure["blocks"]>[number];

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.company.name(),
  avatar: options.avatar ?? null,
});

const createAddress = (
  options: {
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): AddressStructure => {
  const city = options.city || faker.location.city();
  const state = options.state || faker.location.state();

  return {
    formatted: `${faker.location.streetAddress()}, ${city}, ${state} ${faker.location.zipCode()}`,
    timeZone: options.timeZone || "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  };
};

const createLocation = (
  options: {
    name?: string;
    type?: FacilityType;
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name:
    options.name || `${options.city || faker.location.city()} Medical Center`,
  type: options.type || "HOSPITAL",
  address: createAddress({
    city: options.city,
    state: options.state,
    timeZone: options.timeZone,
  }),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    title?: string | null;
    phone?: string | null;
    email?: string | null;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  role: options.role || PersonRole.PROVIDER,
  title: options.title ?? null,
  firstName: options.firstName || faker.person.firstName(),
  lastName: options.lastName || faker.person.lastName(),
  phone: options.phone ?? null,
  email: options.email ?? null,
  avatar: options.avatar ?? null,
});

const createContact = (
  options: {
    role?: string;
    person?: PersonStructure;
  } = {},
): ContactStructure => ({
  id: faker.string.uuid(),
  role: options.role || "MANAGER",
  person: options.person || createPerson(),
});

const createDepartment = (
  options: {
    name?: string;
    type?: DepartmentType;
    description?: string | null;
    contacts?: ContactStructure[];
  } = {},
): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.commerce.department(),
  type: options.type || DepartmentType.DEPARTMENT,
  description: options.description ?? null,
  contacts: options.contacts || [createContact()],
});

const createSpecialty = (
  options: {
    name?: string;
  } = {},
): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.commerce.productName(),
});

const createTimeBlock = (
  options: {
    type?: TimeBlockType;
    startTime?: number;
    endTime?: number;
  } = {},
): TimeBlockStructure => ({
  id: faker.string.uuid(),
  type: options.type || TimeBlockType.SHIFT,
  startTime: options.startTime || 9,
  endTime: options.endTime || 17,
  startsAt: faker.date.recent(),
  endsAt: faker.date.recent(),
  startDate: faker.date.recent(),
  endDate: faker.date.recent(),
  recurrence: null,
  timeZone: "America/New_York",
  hours: 8,
});

const createSchedule = (
  options: {
    type?: ScheduleType;
    timeBlocks?: TimeBlockStructure[];
  } = {},
): ScheduleStructure => ({
  id: faker.string.uuid(),
  startsAt: faker.date.recent(),
  endsAt: faker.date.recent(),
  blocks: options.timeBlocks || [createTimeBlock()],
});

// Create a job post with all required fields
const createJob = (
  options: {
    id?: string;
    status?: JobPostStatus;
    mode?: JobPostMode;
    type?: JobPostType;
    priority?: JobPostPriority;
    summary?: string;
    scope?: string;
    role?: string;
    paymentType?: PaymentType;
    paymentAmount?: number;
    experience?: string;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    schedule?: ScheduleStructure;
    publishedAt?: Date | null;
    expiresAt?: Date;
    filledAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    expiredAt?: Date | null;
    isApplied?: boolean;
    isOffered?: boolean;
    isSaved?: boolean;
  } = {},
) => {
  const status = options.status || JobPostStatus.PUBLISHED;
  const now = new Date();

  // Set appropriate dates based on status
  let publishedAt = options.publishedAt;
  let filledAt = options.filledAt;
  let completedAt = options.completedAt;
  let cancelledAt = options.cancelledAt;
  let expiredAt = options.expiredAt;

  if (publishedAt === undefined) {
    publishedAt = status !== JobPostStatus.DRAFT ? faker.date.past() : null;
  }

  if (filledAt === undefined) {
    filledAt =
      status === JobPostStatus.FILLED || status === JobPostStatus.COMPLETED
        ? faker.date.recent()
        : null;
  }

  if (completedAt === undefined) {
    completedAt =
      status === JobPostStatus.COMPLETED ? faker.date.recent() : null;
  }

  if (cancelledAt === undefined) {
    cancelledAt =
      status === JobPostStatus.CANCELLED ? faker.date.recent() : null;
  }

  if (expiredAt === undefined) {
    expiredAt = status === JobPostStatus.EXPIRED ? faker.date.recent() : null;
  }

  const isApplied = options.isApplied ?? false;
  const isOffered = options.isOffered ?? false;
  const isSaved = options.isSaved ?? false;

  return {
    id: options.id || faker.string.uuid(),
    createdAt: now,
    updatedAt: now,
    // deletedAt: null,
    expiresAt: options.expiresAt || faker.date.future(),
    publishedAt,
    filledAt,
    completedAt,
    cancelledAt,
    expiredAt,
    status,
    mode: options.mode || JobPostMode.INDEPENDENT,
    type: options.type || JobPostType.PER_DIEM,
    priority: options.priority || JobPostPriority.MEDIUM,
    summary: options.summary || "Emergency Room Physician",
    scope: options.scope || "Emergency Medicine",
    role: options.role || "Physician",
    paymentType: options.paymentType || PaymentType.HOURLY,
    paymentAmount: options.paymentAmount || 150,
    paymentRate: 1,
    nightRate: 1.25,
    overtimeRate: 1.5,
    holidayRate: 2,
    bonusRate: 1,
    billingType: PaymentType.HOURLY,
    billingRate: 1.15,
    isBillable: true,
    // experience: options.experience || "MID",
    organization:
      options.organization || createOrganization({ name: "Test Hospital" }),
    location: options.location || createLocation({ name: "Main Hospital" }),
    department:
      options.department || createDepartment({ name: "Emergency Department" }),
    specialties: options.specialties || [
      createSpecialty({ name: "Emergency Medicine" }),
    ],
    schedule: options.schedule || createSchedule(),
    analytics: {
      applicants: faker.number.int({ min: 0, max: 20 }),
      offers: faker.number.int({ min: 0, max: 5 }),
      payment: {
        min: faker.number.int({ min: 100, max: 130 }),
        max: faker.number.int({ min: 170, max: 200 }),
        avg: 150,
      },
    },
    context: {
      application: isApplied
        ? {
            id: faker.string.uuid(),
            status: "PENDING",
            createdAt: faker.date.recent(),
          }
        : null,
      offer: isOffered
        ? {
            id: faker.string.uuid(),
            status: "PENDING",
            createdAt: faker.date.recent(),
          }
        : null,
      position: null,
    },
    position: undefined,
    thread: undefined,
    offers: [],
    applications: [],
    contacts: [],
    actions: [],
  } satisfies JobPostStructure;
};

// Create reusable entities
const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  pediatricCenter: createOrganization({ name: "Children's Pediatric Center" }),
  urgentCare: createOrganization({ name: "Urgent Care Network" }),
  ruralHealth: createOrganization({ name: "Rural Health Clinic" }),
};

const locations = {
  downtown: createLocation({
    name: "Downtown Medical Center",
    city: "New York",
    state: "NY",
  }),
  westSide: createLocation({
    name: "West Side Hospital",
    city: "Chicago",
    state: "IL",
  }),
  southBay: createLocation({
    name: "South Bay Medical",
    city: "San Francisco",
    state: "CA",
  }),
  eastEnd: createLocation({
    name: "East End Clinic",
    city: "Boston",
    state: "MA",
  }),
  ruralClinic: createLocation({
    name: "Rural Health Clinic",
    city: "Springfield",
    state: "MO",
  }),
};

const departments = {
  emergency: createDepartment({ name: "Emergency Department" }),
  pediatrics: createDepartment({ name: "Pediatrics" }),
  cardiology: createDepartment({ name: "Cardiology" }),
  neurology: createDepartment({ name: "Neurology" }),
  surgery: createDepartment({ name: "Surgery" }),
};

const specialties = {
  emergencyMedicine: createSpecialty({ name: "Emergency Medicine" }),
  pediatrics: createSpecialty({ name: "Pediatrics" }),
  cardiology: createSpecialty({ name: "Cardiology" }),
  neurology: createSpecialty({ name: "Neurology" }),
  anesthesiology: createSpecialty({ name: "Anesthesiology" }),
};

// Create jobs with different statuses
const publishedJob = createJob({
  status: JobPostStatus.PUBLISHED,
  summary: "Emergency Room Physician",
  role: "Physician",
  scope: "Emergency Medicine",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200,
});

const filledJob = createJob({
  status: JobPostStatus.FILLED,
  summary: "Pediatric Nurse",
  role: "Nurse",
  scope: "Pediatric Care",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 150,
});

const completedJob = createJob({
  status: JobPostStatus.COMPLETED,
  summary: "Cardiologist - Temporary",
  role: "Specialist",
  scope: "Cardiology",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 300,
});

const cancelledJob = createJob({
  status: JobPostStatus.CANCELLED,
  summary: "Neurologist - Consultant",
  role: "Consultant",
  scope: "Neurology",
  organization: organizations.urgentCare,
  location: locations.eastEnd,
  department: departments.neurology,
  specialties: [specialties.neurology],
  paymentAmount: 250,
});

const expiredJob = createJob({
  status: JobPostStatus.EXPIRED,
  summary: "Rural Health Physician",
  role: "Physician",
  scope: "General Practice",
  organization: organizations.ruralHealth,
  location: locations.ruralClinic,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 180,
});

const draftJob = createJob({
  status: JobPostStatus.DRAFT,
  summary: "Anesthesiologist - Surgery",
  role: "Anesthesiologist",
  scope: "Surgery Support",
  organization: organizations.memorialHealth,
  location: locations.downtown,
  department: departments.surgery,
  specialties: [specialties.anesthesiology],
  paymentAmount: 275,
});

// Create jobs with different payment amounts
const highPayJob = createJob({
  summary: "High-Paying Specialist",
  role: "Specialist",
  scope: "Specialized Care",
  organization: organizations.memorialHealth,
  location: locations.downtown,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 350,
});

const lowPayJob = createJob({
  summary: "Entry-Level Nurse",
  role: "Nurse",
  scope: "General Care",
  organization: organizations.cityGeneral,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 100,
});

// Create jobs with different types
const permanentJob = createJob({
  type: JobPostType.PERMANENT,
  summary: "Permanent Cardiologist",
  role: "Cardiologist",
  scope: "Cardiology",
  organization: organizations.memorialHealth,
  location: locations.downtown,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 280,
});

const temporaryJob = createJob({
  type: JobPostType.TEMPORARY,
  summary: "Temporary Pediatrician",
  role: "Pediatrician",
  scope: "Pediatric Care",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 200,
});

const perDiemJob = createJob({
  type: JobPostType.PER_DIEM,
  summary: "Per Diem Anesthesiologist",
  role: "Anesthesiologist",
  scope: "Surgery Support",
  organization: organizations.pediatricCenter,
  location: locations.southBay,
  department: departments.surgery,
  specialties: [specialties.anesthesiology],
  paymentAmount: 220,
});

// Create jobs with different application statuses
const appliedJob = createJob({
  summary: "Applied Job - Pending",
  role: "Physician",
  scope: "Emergency Medicine",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200,
  isApplied: true,
});

const offeredJob = createJob({
  summary: "Offered Job - Pending Decision",
  role: "Specialist",
  scope: "Cardiology",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 300,
  isOffered: true,
});

const savedJob = createJob({
  summary: "Saved Job - For Later",
  role: "Nurse",
  scope: "Pediatric Care",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 150,
  isSaved: true,
});

// Create the mock data objects with different variations
const createMockJobs = (): JobPostQuery => ({
  items: [
    publishedJob,
    filledJob,
    completedJob,
    cancelledJob,
    expiredJob,
    draftJob,
    highPayJob,
    lowPayJob,
    permanentJob,
    temporaryJob,
    perDiemJob,
    appliedJob,
    offeredJob,
    savedJob,
  ],
  total: 14,
});

// Mock filter options
const mockFilterOptions = {
  specialties: [],
  locations: [],
  loading: false,
};

// Mock functions for save/unsave
const mockSaveFunction = (jobId: string) => {
  console.log(`Saving job ${jobId}`);
};

const mockUnsaveFunction = (jobId: string) => {
  console.log(`Unsaving job ${jobId}`);
};

const apply = createMockMutation<
  typeof api.applications.provider.create.useMutation
>() as unknown as ReturnType<
  typeof api.applications.provider.create.useMutation
>;
const withdraw = createMockMutation<
  typeof api.applications.provider.withdraw.useMutation
>() as unknown as ReturnType<
  typeof api.applications.provider.withdraw.useMutation
>;
const reject = createMockMutation<
  typeof api.offers.provider.reject.useMutation
>() as unknown as ReturnType<typeof api.offers.provider.reject.useMutation>;
const accept = createMockMutation<
  typeof api.offers.provider.accept.useMutation
>() as unknown as ReturnType<typeof api.offers.provider.accept.useMutation>;

// Basic story
export const Default: Story = {
  args: {
    jobs: {
      data: createMockJobs(),
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

// Loading state
export const Loading: Story = {
  args: {
    jobs: {
      data: undefined,
      loading: true,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

// Empty state
export const Empty: Story = {
  args: {
    jobs: {
      data: { items: [], total: 0 },
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

// Error state
export const error: Story = {
  args: {
    jobs: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load jobs"),
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

// Different job status variations
export const PublishedJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [publishedJob],
        total: 1,
      },
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

export const FilledAndCompletedJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [filledJob, completedJob],
        total: 2,
      },
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

export const CancelledAndExpiredJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [cancelledJob, expiredJob],
        total: 2,
      },
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};

export const DraftJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [draftJob],
        total: 1,
      },
      loading: false,
      error: undefined,
    },
    filterOptions: mockFilterOptions,
    apply,
    withdraw,
    reject,
    accept,
    save: mockSaveFunction,
    unsave: mockUnsaveFunction,
  },
};
