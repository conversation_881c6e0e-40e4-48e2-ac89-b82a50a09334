import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import {
  DepartmentType,
  FacilityType,
  IncidentSeverity,
  IncidentStatus,
  IncidentType,
  PaymentType,
  PersonRole,
  ProviderStatus,
  ShiftStatus,
} from "@/api";
import Shift from "@/www/providers/shift/Shift";

const meta = {
  title: "Pages/Providers/Shift/page",
  component: Shift,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/shifts/abc-123",
      },
    },
  },
} satisfies Meta<typeof Shift>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to create a person
const createPerson = (role: PersonRole = PersonRole.PROVIDER) => ({
  id: faker.string.uuid(),
  role,
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  avatar: faker.image.avatar(),
  email: faker.internet.email(),
  phone: faker.phone.number(),
});

// Helper function to create a mock shift with faker data
const createMockShift = (
  status: ShiftStatus = ShiftStatus.PENDING,
): RouterOutputs["shifts"]["get"] => {
  // Generate random start and end dates
  const startDate = faker.date.future();
  const endDate = new Date(startDate);
  endDate.setHours(
    startDate.getHours() + faker.number.int({ min: 4, max: 12 }),
  );

  // Calculate hours
  const hours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);
  const paymentRate = faker.number.int({ min: 100, max: 300 });
  const paymentAmount = Math.round(hours * paymentRate);

  return {
    id: faker.string.uuid(),
    status,
    summary: faker.helpers.arrayElement([
      "Emergency Room Coverage",
      "ICU Shift",
      "Pediatric Department Coverage",
      "Surgery Assistance",
      "Outpatient Clinic",
    ]),
    scope: faker.helpers.arrayElement([
      "Full shift coverage",
      "Department supervision",
      "Patient rounds",
      "Procedure assistance",
      "On-call coverage",
    ]),
    role: faker.helpers.arrayElement([
      "Physician",
      "Nurse Practitioner",
      "Registered Nurse",
      "Physician Assistant",
      "Specialist",
    ]),
    timeZone: faker.location.timeZone(),
    startDate,
    endDate,
    paymentType: faker.helpers.arrayElement([
      PaymentType.HOURLY,
      PaymentType.FIXED,
    ]),
    paymentRate,
    paymentAmount,
    holidayAmount: faker.number.int({ min: 0, max: 200 }),
    nightAmount: faker.number.int({ min: 0, max: 150 }),
    overtimeAmount: faker.number.int({ min: 0, max: 300 }),
    paymentTotal: paymentAmount + faker.number.int({ min: 0, max: 500 }),
    hours,
    overtimeHours: faker.number.float({ min: 0, max: 2, precision: 0.5 }),
    nightTimeHours: faker.number.float({ min: 0, max: 4, precision: 0.5 }),
    holidayTimeHours: faker.number.float({ min: 0, max: 2, precision: 0.5 }),
    organizationId: faker.string.uuid(),
    paidAt: status === ShiftStatus.COMPLETED ? faker.date.recent() : null,

    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      avatar: faker.image.avatar(),
    },

    location: {
      id: faker.string.uuid(),
      name: `${faker.location.city()} ${faker.helpers.arrayElement(["Medical Center", "Hospital", "Clinic", "Health Center"])}`,
      type: faker.helpers.arrayElement([
        FacilityType.HOSPITAL,
        FacilityType.CLINIC,
        FacilityType.OFFICE,
        FacilityType.LAB,
        FacilityType.IMAGING,
        FacilityType.REHABILITATION,
        FacilityType.OTHER,
      ]),
      address: {
        formatted: `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`,
        timeZone: faker.location.timeZone(),
        latitude: faker.location.latitude(),
        longitude: faker.location.longitude(),
      },
    },

    provider: {
      id: faker.string.uuid(),
      title: faker.person.jobTitle(),
      status: ProviderStatus.ACTIVE,
      person: createPerson(PersonRole.PROVIDER),
    },

    department: {
      id: faker.string.uuid(),
      name: faker.helpers.arrayElement([
        "Emergency Department",
        "Intensive Care Unit",
        "Pediatrics",
        "Surgery",
        "Cardiology",
        "Neurology",
        "Oncology",
      ]),
      type: faker.helpers.arrayElement([
        DepartmentType.DEPARTMENT,
        DepartmentType.UNIT,
        DepartmentType.WARD,
      ]),
      contacts: Array.from(
        { length: faker.number.int({ min: 1, max: 3 }) },
        () => ({
          id: faker.string.uuid(),
          role: faker.helpers.arrayElement([
            "Department Head",
            "Nurse Manager",
            "Administrative Assistant",
            "Coordinator",
          ]),
          person: createPerson(PersonRole.INTERNAL),
        }),
      ),
    },

    specialties: Array.from(
      { length: faker.number.int({ min: 1, max: 3 }) },
      () => ({
        id: faker.string.uuid(),
        name: faker.helpers.arrayElement([
          "Emergency Medicine",
          "Trauma Care",
          "Pediatrics",
          "Cardiology",
          "Neurology",
          "Oncology",
          "General Surgery",
          "Orthopedics",
          "Internal Medicine",
        ]),
      }),
    ),

    actions:
      status !== ShiftStatus.PENDING
        ? Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () => ({
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement([
              "CONFIRM",
              "CHECK_IN",
              "CHECK_OUT",
              "CANCEL",
              "REVIEW",
            ]),
            createdAt: faker.date.recent(),
            actor: createPerson(
              faker.helpers.arrayElement([
                PersonRole.PROVIDER,
                PersonRole.CLIENT,
              ]),
            ),
          }))
        : [],

    incidents: faker.datatype.boolean()
      ? Array.from({ length: faker.number.int({ min: 1, max: 2 }) }, () => ({
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement([
            IncidentType.SAFETY,
            IncidentType.HEALTH,
            IncidentType.ENVIRONMENT,
            IncidentType.OTHER,
          ]),
          status: faker.helpers.arrayElement([
            IncidentStatus.OPEN,
            IncidentStatus.IN_PROGRESS,
            IncidentStatus.RESOLVED,
          ]),
          severity: faker.helpers.arrayElement([
            IncidentSeverity.MINOR,
            IncidentSeverity.MAJOR,
            IncidentSeverity.CRITICAL,
          ]),
          title: faker.helpers.arrayElement([
            "Patient Fall",
            "Medication Error",
            "Equipment Malfunction",
            "Staff Injury",
            "Security Breach",
            "Documentation Issue",
          ]),
          description: faker.lorem.paragraph(),
          createdAt: faker.date.recent(),
        }))
      : [],
  };
};

// Create different story variants using the helper function
export const Default: Story = {
  args: {
    shift: createMockShift(ShiftStatus.PENDING),
  },
};

export const Confirmed: Story = {
  args: {
    shift: createMockShift(ShiftStatus.CONFIRMED),
  },
};

export const Active: Story = {
  args: {
    shift: createMockShift(ShiftStatus.ACTIVE),
  },
};

export const Completed: Story = {
  args: {
    shift: createMockShift(ShiftStatus.COMPLETED),
  },
};

export const Cancelled: Story = {
  args: {
    shift: createMockShift(ShiftStatus.CANCELLED),
  },
};

export const WithIncidents: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.ACTIVE),
      incidents: Array.from({ length: 3 }, () => ({
        id: faker.string.uuid(),
        type: faker.helpers.arrayElement(Object.values(IncidentType)),
        status: faker.helpers.arrayElement(Object.values(IncidentStatus)),
        severity: faker.helpers.arrayElement(Object.values(IncidentSeverity)),
        title: faker.helpers.arrayElement([
          "Patient Fall",
          "Medication Error",
          "Equipment Malfunction",
          "Staff Injury",
          "Security Breach",
        ]),
        description: faker.lorem.paragraph(),
        createdAt: faker.date.recent(),
      })),
    },
  },
};

export const Empty: Story = {
  args: {
    shift: undefined,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: {
      message: faker.helpers.arrayElement([
        "Shift not found",
        "You don't have access to this shift",
        "An error occurred while fetching the shift",
        "Network error",
      ]),
      code: faker.helpers.arrayElement([
        "NOT_FOUND",
        "FORBIDDEN",
        "INTERNAL_SERVER_ERROR",
        "BAD_REQUEST",
      ]),
      data: { httpStatus: faker.helpers.arrayElement([404, 403, 500, 400]) },
    } as RouterError,
  },
};

export const PendingWithMultipleSpecialties: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.PENDING),
      specialties: Array.from({ length: 4 }, () => ({
        id: faker.string.uuid(),
        name: faker.helpers.arrayElement([
          "Emergency Medicine",
          "Trauma Care",
          "Critical Care",
          "Pediatric Emergency",
          "Cardiology",
          "Neurology",
          "Orthopedics",
          "Anesthesiology",
        ]),
      })),
    },
  },
};

export const ConfirmedUpcoming: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CONFIRMED),
      startDate: faker.date.soon({ days: 1 }),
      endDate: faker.date.soon({ days: 2 }),
    },
  },
};

export const ConfirmedImminentStart: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CONFIRMED),
      startDate: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
      endDate: new Date(Date.now() + 8.5 * 60 * 60 * 1000), // 8.5 hours from now
    },
  },
};

export const ActiveWithLongDuration: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.ACTIVE),
      startDate: new Date(Date.now() - 4 * 60 * 60 * 1000), // Started 4 hours ago
      endDate: new Date(Date.now() + 8 * 60 * 60 * 1000), // Ends 8 hours from now
      hours: 12,
    },
  },
};

export const ActiveNearingCompletion: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.ACTIVE),
      startDate: new Date(Date.now() - 7.5 * 60 * 60 * 1000), // Started 7.5 hours ago
      endDate: new Date(Date.now() + 30 * 60 * 1000), // Ends in 30 minutes
      hours: 8,
    },
  },
};

export const CompletedWithOvertime: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.COMPLETED),
      hours: 8,
      overtimeHours: 2.5,
      paymentAmount: 1200,
      overtimeAmount: 450,
      paymentTotal: 1650,
      paidAt: faker.date.recent(),
    },
  },
};

export const CompletedWithNightHours: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.COMPLETED),
      hours: 8,
      nightTimeHours: 4,
      paymentAmount: 1200,
      nightAmount: 300,
      paymentTotal: 1500,
      paidAt: faker.date.recent(),
    },
  },
};

export const CompletedWithHolidayPay: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.COMPLETED),
      hours: 8,
      holidayTimeHours: 8,
      paymentAmount: 1200,
      holidayAmount: 600,
      paymentTotal: 1800,
      paidAt: faker.date.recent(),
    },
  },
};

export const CancelledByProvider: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CANCELLED),
      actions: [
        {
          id: faker.string.uuid(),
          type: "CANCEL",
          createdAt: faker.date.recent(),
          actor: {
            id: faker.string.uuid(),
            role: PersonRole.PROVIDER,
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            avatar: faker.image.avatar(),
            email: faker.internet.email(),
            phone: faker.phone.number(),
          },
        },
      ],
    },
  },
};

export const CancelledByOrganization: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CANCELLED),
      actions: [
        {
          id: faker.string.uuid(),
          type: "CANCEL",
          createdAt: faker.date.recent(),
          actor: {
            id: faker.string.uuid(),
            role: PersonRole.STAFF,
            firstName: faker.person.firstName(),
            lastName: faker.person.lastName(),
            avatar: faker.image.avatar(),
            email: faker.internet.email(),
            phone: faker.phone.number(),
          },
        },
      ],
    },
  },
};

export const WithCriticalIncident: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.ACTIVE),
      incidents: [
        {
          id: faker.string.uuid(),
          type: IncidentType.HEALTH,
          status: IncidentStatus.IN_PROGRESS,
          severity: IncidentSeverity.CRITICAL,
          title: "Severe Medication Error",
          description:
            "Patient administered incorrect medication dosage requiring immediate intervention",
          createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
        },
      ],
    },
  },
};

export const WithMultipleIncidents: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.ACTIVE),
      incidents: [
        {
          id: faker.string.uuid(),
          type: IncidentType.SAFETY,
          status: IncidentStatus.REPORTED,
          severity: IncidentSeverity.MEDIUM,
          title: "Patient Fall",
          description:
            "Patient fell while attempting to get out of bed unassisted",
          createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
        },
        {
          id: faker.string.uuid(),
          type: IncidentType.ENVIRONMENT,
          status: IncidentStatus.RESOLVED,
          severity: IncidentSeverity.MINOR,
          title: "Equipment Malfunction",
          description:
            "IV pump malfunctioned but was quickly replaced with no patient impact",
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        },
        {
          id: faker.string.uuid(),
          type: IncidentType.SAFETY,
          status: IncidentStatus.IN_PROGRESS,
          severity: IncidentSeverity.MAJOR,
          title: "Aggressive Patient Behavior",
          description:
            "Patient exhibited aggressive behavior toward staff requiring security intervention",
          createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        },
      ],
    },
  },
};

export const FixedRateShift: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CONFIRMED),
      paymentType: PaymentType.FIXED,
      paymentRate: 0,
      paymentAmount: 1500,
      paymentTotal: 1500,
      hours: 8,
    },
  },
};

export const DailyRateShift: Story = {
  args: {
    shift: {
      ...createMockShift(ShiftStatus.CONFIRMED),
      paymentType: PaymentType.HOURLY,
      paymentRate: 65,
      paymentAmount: 520,
      paymentTotal: 520,
      hours: 8,
    },
  },
};
