import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import { PayoutStatus, ShiftStatus } from "@/api";
import { EarningView } from "@/www/providers/earning";

const meta = {
  title: "Pages/Providers/Earning/PayoutDetail",
  component: EarningView,
  parameters: {
    layout: "fullscreen",
  },
} satisfies Meta<typeof EarningView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Generate a mock payout with shifts
const generateMockPayout = (options?: {
  status?: PayoutStatus;
  shiftsCount?: number;
  paidAt?: Date | null;
}) => {
  const status = options?.status || PayoutStatus.COMPLETED;
  const shiftsCount =
    options?.shiftsCount || faker.number.int({ min: 3, max: 10 });
  const paidAt =
    options?.paidAt !== undefined ? options.paidAt : faker.date.recent();

  // Generate shifts
  const shifts = Array.from({ length: shiftsCount }).map(() => {
    const startDate = faker.date.recent();
    const endDate = new Date(startDate);
    endDate.setHours(
      startDate.getHours() + faker.number.int({ min: 4, max: 12 }),
    );

    const hours = faker.number.float({ min: 4, max: 12, precision: 0.5 });
    const paymentRate = faker.number.float({
      min: 50,
      max: 150,
      precision: 0.01,
    });
    const paymentAmount = hours * paymentRate;

    const overtimeAmount = faker.datatype.boolean()
      ? faker.number.float({ min: 20, max: 100, precision: 0.01 })
      : 0;

    const holidayAmount = faker.datatype.boolean()
      ? faker.number.float({ min: 20, max: 100, precision: 0.01 })
      : 0;

    const nightAmount = faker.datatype.boolean()
      ? faker.number.float({ min: 20, max: 100, precision: 0.01 })
      : 0;

    const paymentTotal =
      paymentAmount + overtimeAmount + holidayAmount + nightAmount;

    return {
      id: faker.string.uuid(),
      status: faker.helpers.arrayElement(Object.values(ShiftStatus)),
      summary: faker.helpers.arrayElement([
        "Emergency Room Coverage",
        "General Practice Shift",
        "Specialist Consultation",
        "Surgery Assistance",
        "Overnight Ward Coverage",
      ]),
      scope: faker.helpers.arrayElement([
        "Emergency Medicine",
        "General Practice",
        "Cardiology",
        "Pediatrics",
        "Surgery",
      ]),
      startDate,
      endDate,
      hours,
      paymentRate,
      paymentAmount,
      overtimeAmount,
      holidayAmount,
      nightAmount,
      paymentTotal,
      provider: {
        id: faker.string.uuid(),
        status: "ACTIVE",
        title: faker.person.jobTitle(),
        person: {
          id: faker.string.uuid(),
          role: "PROVIDER",
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          avatar: faker.image.avatar(),
        },
      },
    };
  });

  // Calculate total amounts
  const amount = shifts.reduce((sum, shift) => sum + shift.paymentAmount, 0);
  const overtimeAmount = shifts.reduce(
    (sum, shift) => sum + (shift.overtimeAmount || 0),
    0,
  );
  const holidayAmount = shifts.reduce(
    (sum, shift) => sum + (shift.holidayAmount || 0),
    0,
  );
  const nightAmount = shifts.reduce(
    (sum, shift) => sum + (shift.nightAmount || 0),
    0,
  );

  return {
    id: faker.string.uuid(),
    status,
    amount,
    overtimeAmount,
    holidayAmount,
    nightAmount,
    paidAt,
    providerId: faker.string.uuid(),
    shifts,
    provider: {
      id: faker.string.uuid(),
      status: "ACTIVE",
      title: faker.person.jobTitle(),
      person: {
        id: faker.string.uuid(),
        role: "PROVIDER",
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: faker.image.avatar(),
      },
    },
  };
};

// Default story with completed payout
export const Default: Story = {
  args: {
    payoutId: "mock-payout-id",
    payout: Promise.resolve(generateMockPayout()),
  },
};

// Pending payout story
export const PendingPayout: Story = {
  args: {
    payoutId: "mock-payout-id",
    payout: Promise.resolve(
      generateMockPayout({
        status: PayoutStatus.PENDING,
        paidAt: null,
      }),
    ),
  },
};

// Processing payout story
export const ProcessingPayout: Story = {
  args: {
    payoutId: "mock-payout-id",
    payout: Promise.resolve(
      generateMockPayout({
        status: PayoutStatus.PROCESSING,
        paidAt: null,
      }),
    ),
  },
};

// No shifts story
export const NoShifts: Story = {
  args: {
    payoutId: "mock-payout-id",
    payout: Promise.resolve(
      generateMockPayout({
        shiftsCount: 0,
      }),
    ),
  },
};

// Loading state story
export const Loading: Story = {
  args: {
    payoutId: "mock-payout-id",
    loading: true,
  },
};

// Error state story
export const error: Story = {
  args: {
    payoutId: "mock-payout-id",
    payout: Promise.reject(new Error("Failed to load payout data")),
  },
};
