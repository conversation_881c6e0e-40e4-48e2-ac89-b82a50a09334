import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import Earning from "@/www/providers/earning/Earning";

const meta = {
  title: "Pages/Providers/Earning/page",
  component: Earning,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/earning",
      },
    },
  },
} satisfies Meta<typeof Earning>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type EarningsAnalyticsQuery = RouterOutputs["billing"]["earnings"]["analytics"];
type UpcomingPaymentStructure = NonNullable<
  EarningsAnalyticsQuery["upcomingPayment"]
>;

// Helper functions to create mock data
const createUpcomingPayment = (
  options: {
    paymentDate?: Date;
    shiftsCount?: number;
    totalHours?: number;
    totalAmount?: number;
    hoursByCategory?: {
      regularHours: number;
      overtimeHours: number;
      holidayHours: number;
    };
  } = {},
): UpcomingPaymentStructure => ({
  paymentDate: options.paymentDate ?? faker.date.future(),
  shiftsCount: options.shiftsCount ?? faker.number.int({ min: 1, max: 10 }),
  totalHours:
    options.totalHours ??
    faker.number.float({ min: 10, max: 80, precision: 0.5 }),
  totalAmount:
    options.totalAmount ??
    faker.number.float({ min: 500, max: 5000, precision: 0.01 }),
  hoursByCategory: options.hoursByCategory ?? {
    regularHours: faker.number.float({ min: 8, max: 40, precision: 0.5 }),
    overtimeHours: faker.number.float({ min: 0, max: 20, precision: 0.5 }),
    holidayHours: faker.number.float({ min: 0, max: 16, precision: 0.5 }),
  },
});

const createEarningsAnalytics = (
  options: {
    totalEarnings?: number;
    totalEarningsChange?: number;
    hoursWorked?: number;
    hoursWorkedChange?: number;
    averageRate?: number;
    averageRateChange?: number;
    paymentsDateRange?: {
      min: Date;
      max: Date;
    };
    upcomingPayment?: UpcomingPaymentStructure | null;
  } = {},
): EarningsAnalyticsQuery => ({
  totalEarnings:
    options.totalEarnings ??
    faker.number.float({ min: 1000, max: 20000, precision: 0.01 }),
  totalEarningsChange:
    options.totalEarningsChange ??
    faker.number.float({ min: -20, max: 30, precision: 0.1 }),
  hoursWorked:
    options.hoursWorked ??
    faker.number.float({ min: 20, max: 200, precision: 0.5 }),
  hoursWorkedChange:
    options.hoursWorkedChange ??
    faker.number.float({ min: -15, max: 25, precision: 0.1 }),
  averageRate:
    options.averageRate ??
    faker.number.float({ min: 20, max: 100, precision: 0.01 }),
  averageRateChange:
    options.averageRateChange ??
    faker.number.float({ min: -10, max: 15, precision: 0.1 }),
  paymentsDateRange: options.paymentsDateRange ?? {
    min: faker.date.past({ years: 1 }),
    max: faker.date.recent(),
  },
  upcomingPayment: options.upcomingPayment ?? createUpcomingPayment(),
});

// Create scenarios for stories
const earningScenarios = {
  standard: createEarningsAnalytics({
    totalEarnings: 12500,
    totalEarningsChange: 15,
    hoursWorked: 42,
    hoursWorkedChange: 8,
    averageRate: 75.25,
    averageRateChange: 5.5,
    upcomingPayment: createUpcomingPayment({
      paymentDate: new Date(2025, 3, 15),
      shiftsCount: 5,
      totalHours: 40,
      totalAmount: 3200,
      hoursByCategory: {
        regularHours: 32,
        overtimeHours: 6,
        holidayHours: 2,
      },
    }),
  }),
  negative: createEarningsAnalytics({
    totalEarnings: 8700,
    totalEarningsChange: -12,
    hoursWorked: 36,
    hoursWorkedChange: -15,
    averageRate: 65.5,
    averageRateChange: -8.2,
    upcomingPayment: createUpcomingPayment({
      paymentDate: new Date(2025, 3, 15),
      shiftsCount: 4,
      totalHours: 32,
      totalAmount: 2100,
      hoursByCategory: {
        regularHours: 28,
        overtimeHours: 4,
        holidayHours: 0,
      },
    }),
  }),
  noUpcomingPayment: createEarningsAnalytics({
    totalEarnings: 10200,
    totalEarningsChange: 5,
    hoursWorked: 38,
    hoursWorkedChange: 2,
    averageRate: 70.15,
    averageRateChange: 3.1,
    upcomingPayment: null,
  }),
};

// Stories
export const Default: Story = {
  args: {
    loading: false,
    earning: {
      data: earningScenarios.standard,
      loading: false,
      error: undefined,
    },
  },
};

export const NegativeChange: Story = {
  args: {
    loading: false,
    earning: {
      data: earningScenarios.negative,
      loading: false,
      error: undefined,
    },
  },
};

export const NoUpcomingPayment: Story = {
  args: {
    loading: false,
    earning: {
      data: earningScenarios.noUpcomingPayment,
      loading: false,
      error: undefined,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    earning: {
      data: undefined,
      loading: true,
      error: undefined,
    },
  },
};

export const Error: Story = {
  args: {
    loading: false,
    earning: {
      data: undefined,
      loading: false,
      error: {
        message: "Failed to fetch earning data",
        code: "INTERNAL_SERVER_ERROR",
      } as RouterError,
    },
  },
};
