/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import {
  ApplicationStatus,
  JobPostStatus,
  JobPostType,
  PaymentType,
} from "@/api";
import Application from "@/www/providers/application/Application";

const meta = {
  title: "Pages/Providers/Application/page",
  component: Application,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/applications/abc-123/application",
      },
    },
  },
} satisfies Meta<typeof Application>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on the API
type ApplicationQuery = RouterOutputs["applications"]["get"];
type JobStructure = NonNullable<ApplicationQuery["job"]>;
type OrganizationStructure = NonNullable<ApplicationQuery["organization"]>;
type ThreadStructure = NonNullable<ApplicationQuery["thread"]>;

// Helper function to create a location
const createLocation = (name?: string) => ({
  id: faker.string.uuid(),
  name: name || faker.location.city(),
  type: "HOSPITAL",
  address: {
    formatted: faker.location.streetAddress(),
    timeZone: "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  },
});

// Helper function to create an organization
const createOrganization = (name?: string): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: name || faker.company.name(),
  avatar: null,
});

// Helper function to create a job
const createJob = (
  options: {
    status?: JobPostStatus;
    type?: JobPostType;
    summary?: string;
    organization?: OrganizationStructure;
  } = {},
): JobStructure => ({
  id: faker.string.uuid(),
  type: options.type || JobPostType.PER_DIEM,
  summary: options.summary || "Emergency Room Physician",
  role: "Physician",
  scope: "Emergency Medicine",
  status: options.status || JobPostStatus.PUBLISHED,
  paymentType: PaymentType.HOURLY,
  paymentAmount: 150,
  location: createLocation(),
});

// Helper function to create a thread
const createThread = (): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: [
    {
      id: faker.string.uuid(),
      content: faker.lorem.paragraph(),
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent(),
      author: {
        id: faker.string.uuid(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: null,
      },
    },
  ],
});

// Helper function to create an application
const createApplication = (
  status: ApplicationStatus = ApplicationStatus.PENDING,
  options: {
    notes?: string | null;
    job?: JobStructure;
    organization?: OrganizationStructure;
    thread?: ThreadStructure | undefined;
  } = {},
): ApplicationQuery => ({
  id: faker.string.uuid(),
  status,
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  organizationId: options.organization?.id || faker.string.uuid(),
  jobId: options.job?.id || faker.string.uuid(),
  providerId: faker.string.uuid(),
  job: options.job || createJob(),
  organization: options.organization || createOrganization(),
  notes: options.notes || null,
  thread: options.thread,
  provider: {
    id: faker.string.uuid(),
    title: "MD",
    status: "ACTIVE",
    person: {
      id: faker.string.uuid(),
      role: "PROVIDER",
      title: "MD",
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      avatar: null,
    },
  },
});

// Create the mock application
const mockOrganization = createOrganization("Test Hospital");
const mockJob = createJob({
  summary: "Emergency Room Physician",
  organization: mockOrganization,
});
const mockApplication = createApplication(ApplicationStatus.PENDING, {
  job: mockJob,
  organization: mockOrganization,
});

export const Default: Story = {
  args: {
    application: mockApplication,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    application: mockApplication,
    error: {
      message: "Failed to load application",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "applications.get",
        zodError: null,
      },
    } as RouterError,
  },
};

export const WithWithdrawMutation: Story = {
  args: {
    application: mockApplication,
    withdraw: {
      mutate: async () => {},
      isPending: false,
    } as any,
  },
};

export const WithdrawingInProgress: Story = {
  args: {
    application: mockApplication,
    withdraw: {
      mutate: async () => {},
      isPending: true,
    } as any,
  },
};

export const Accepted: Story = {
  args: {
    application: createApplication(ApplicationStatus.ACCEPTED, {
      notes: "Application accepted for immediate start",
      job: mockJob,
      organization: mockOrganization,
    }),
  },
};

export const Rejected: Story = {
  args: {
    application: createApplication(ApplicationStatus.REJECTED, {
      notes: "We have selected another candidate",
      job: mockJob,
      organization: mockOrganization,
    }),
  },
};

export const Withdrawn: Story = {
  args: {
    application: createApplication(ApplicationStatus.WITHDRAWN, {
      notes: "Candidate withdrew application",
      job: mockJob,
      organization: mockOrganization,
    }),
  },
};

export const WithNotes: Story = {
  args: {
    application: createApplication(ApplicationStatus.PENDING, {
      notes:
        "I am very interested in this position and have 5 years of experience in emergency medicine.",
      job: mockJob,
      organization: mockOrganization,
    }),
  },
};

export const WithThread: Story = {
  args: {
    application: createApplication(ApplicationStatus.PENDING, {
      job: mockJob,
      organization: mockOrganization,
      thread: createThread(),
    }),
  },
};
