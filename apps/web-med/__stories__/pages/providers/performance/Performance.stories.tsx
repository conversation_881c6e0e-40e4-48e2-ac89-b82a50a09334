import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import Performance from "@/www/providers/performance/Performance";

const meta = {
  title: "Pages/Providers/Performance/page",
  component: Performance,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/performance",
      },
    },
  },
} satisfies Meta<typeof Performance>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper to generate realistic review data
const generateReviews = (count: number, rating = 4) => {
  return Array.from({ length: count }, () => ({
    id: faker.string.uuid(),
    reviewer: `${faker.person.firstName()} ${faker.person.lastName()}`,
    date: faker.date.recent().toLocaleDateString(),
    rating: faker.number.float({
      min: rating - 0.3,
      max: rating + 0.3,
      precision: 0.1,
    }),
    comment: faker.helpers.arrayElement([
      "Great work ethic and very professional.",
      "Excellent patient care and communication.",
      "Very knowledgeable and punctual.",
      "Patients really appreciate their bedside manner.",
      "Outstanding performance on all shifts.",
      "Could improve on communication, but overall good work.",
      "Very reliable and thorough in their work.",
    ]),
  }));
};

// Generate realistic metrics based on the API structure
const generateMetrics = (rating = 4, shiftsCount = 50) => ({
  overallRating: rating,
  totalShifts: shiftsCount,
  onTimePercentage: faker.number.int({ min: 85, max: 100 }),
  patientSatisfaction: rating,
  completedShifts: shiftsCount,
  cancelledShifts: Math.max(0, Math.floor(shiftsCount * 0.1)), // 10% cancellation rate max
  shiftsThisMonth: Math.min(15, Math.floor(shiftsCount * 0.2)), // 20% of total shifts this month
  shiftsLastMonth: Math.min(15, Math.floor(shiftsCount * 0.15)), // 15% of total shifts last month
  totalHoursWorked: shiftsCount * 8, // 8 hours per shift
  averageShiftDuration: 8, // 8 hours average
  reviewsByMonth: {
    [new Date().toISOString().slice(0, 7)]: Math.min(
      5,
      Math.floor(shiftsCount * 0.1),
    ),
  },
});

export const BrandNewProvider: Story = {
  args: {
    reviews: [], // No reviews yet
    metrics: {
      overallRating: 0,
      totalShifts: 0,
      onTimePercentage: 0,
      patientSatisfaction: 0,
      completedShifts: 0,
      cancelledShifts: 0,
      shiftsThisMonth: 0,
      shiftsLastMonth: 0,
      totalHoursWorked: 0,
      averageShiftDuration: 0,
      reviewsByMonth: {},
    },
  },
};

export const NewProviderWithFewShifts: Story = {
  args: {
    reviews: generateReviews(2, 4.2), // Only 2 reviews so far
    metrics: generateMetrics(4.2, 2), // Only 2 shifts completed
  },
};

export const ExceptionalProvider: Story = {
  args: {
    reviews: generateReviews(15, 4.8), // Lots of consistently high reviews
    metrics: generateMetrics(4.8, 100), // Many successful shifts
  },
};

export const ExperiencedGoodProvider: Story = {
  args: {
    reviews: generateReviews(25, 3.8), // Many solid reviews
    metrics: generateMetrics(3.8, 75), // Significant number of shifts
  },
};

export const StruggleToImprove: Story = {
  args: {
    reviews: generateReviews(8, 2.5), // Several below-average reviews
    metrics: {
      ...generateMetrics(2.5, 20),
      onTimePercentage: 75, // Lower punctuality
      cancelledShifts: 5, // Higher cancellation rate
      patientSatisfaction: 2.3,
    },
  },
};

export const InconsistentProvider: Story = {
  args: {
    // Mix of good and concerning reviews
    reviews: [
      ...generateReviews(3, 4.5), // Some excellent reviews
      ...generateReviews(3, 2.5), // Some concerning reviews
    ],
    metrics: {
      ...generateMetrics(3.5, 30),
      onTimePercentage: 82,
      patientSatisfaction: 3.2,
      cancelledShifts: 6, // Higher cancellation rate
    },
  },
};
