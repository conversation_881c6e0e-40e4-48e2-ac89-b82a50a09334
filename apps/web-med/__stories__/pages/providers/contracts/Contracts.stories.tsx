/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import { ContractStatus, ContractType } from "@/api";
import Contracts from "@/www/providers/contracts/Contracts";

const meta = {
  title: "Pages/Providers/Contracts/page",
  component: Contracts,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers/app/contracts",
        query: {
          status: "PENDING",
        },
      },
    },
  },
} satisfies Meta<typeof Contracts>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on the API
type ContractsQuery = RouterOutputs["contracts"]["getMany"];
type ContractStructure = NonNullable<ContractsQuery["items"]>[number];

// Helper function to create an organization
const createOrganization = (name?: string) => ({
  id: faker.string.uuid(),
  name: name || faker.company.name(),
  avatar: "/placeholder.svg",
});

// Helper function to create a contract
const createContract = (
  status: ContractStatus = ContractStatus.PENDING,
  options: {
    title?: string;
    type?: ContractType;
    organization?: { id: string; name: string; avatar?: string };
  } = {},
) =>
  ({
    id: faker.string.uuid(),
    title: options.title || faker.person.jobTitle(),
    type: options.type || ContractType.EMPLOYMENT,
    status,
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    expiresAt: faker.date.future(),
    organization: {
      ...(options.organization || createOrganization()),
      avatar: options.organization?.avatar || "/placeholder.svg",
    },
    position: undefined,
    provider: undefined,
    agreements: [],
  }) satisfies ContractStructure;

// Create contracts for different statuses
const pendingContract = createContract(ContractStatus.PENDING, {
  title: "Emergency Room Physician - Night Shift",
  organization: createOrganization("City General Hospital"),
});

const signedContract = createContract(ContractStatus.SIGNED, {
  title: "ICU Specialist - Weekend Coverage",
  organization: createOrganization("Memorial Medical Center"),
});

const rejectedContract = createContract(ContractStatus.REJECTED, {
  title: "Pediatric Emergency - Full Time",
  organization: createOrganization("Children's Hospital"),
});

// Create the mock contracts data
const mockContracts: ContractsQuery = {
  items: [pendingContract, signedContract, rejectedContract],
  total: 3,
};

export const Default: Story = {
  args: {
    contracts: mockContracts,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    contracts: { items: [], total: 0 },
  },
};

export const WithError: Story = {
  args: {
    error: {
      message: "Failed to load contracts",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "contracts.getMany",
        zodError: null,
      },
    } as RouterError,
  },
};

export const OnlyPending: Story = {
  args: {
    contracts: {
      items: mockContracts.items.filter(
        (c: ContractStructure) => c.status === ContractStatus.PENDING,
      ),
      total: 1,
    },
  },
};

export const OnlySigned: Story = {
  args: {
    contracts: {
      items: mockContracts.items.filter(
        (c: ContractStructure) => c.status === ContractStatus.SIGNED,
      ),
      total: 1,
    },
  },
};

export const OnlyRejected: Story = {
  args: {
    contracts: {
      items: mockContracts.items.filter(
        (c: ContractStructure) => c.status === ContractStatus.REJECTED,
      ),
      total: 1,
    },
  },
};

// Example of creating multiple contracts with Faker
export const ManyContracts: Story = {
  args: {
    contracts: {
      items: Array.from({ length: 10 }, (_, i) => {
        // Distribute statuses evenly
        const statusIndex = i % 3;
        const status = [
          ContractStatus.PENDING,
          ContractStatus.SIGNED,
          ContractStatus.REJECTED,
        ][statusIndex];

        return createContract(status, {
          title: faker.person.jobTitle(),
          organization: createOrganization(),
        });
      }),
      total: 10,
    },
  },
};
