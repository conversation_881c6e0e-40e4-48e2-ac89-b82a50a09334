import type { Meta, StoryObj } from "@storybook/react";

import UsersPage from "@/www/admin/users/Users";

const meta = {
  title: "Pages/Admin/Users/<USER>",
  component: UsersPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/admin/users",
      },
    },
  },
} satisfies Meta<typeof UsersPage>;

export default meta;
type Story = StoryObj<typeof meta>;

const mutation = {
  mutate: () => {
    console.log("mutate");
  },
  mutateAsync: () => Promise.resolve(),
  isPending: false,
};

export const Default: Story = {
  args: {
    users: {
      isLoading: false,
      error: null,
      data: {
        total: 0,
        items: [],
      },
    },
    invitations: {
      isLoading: false,
      error: null,
      data: {
        count: 0,
        invites: [],
      },
    },
    syncPeopleMutation: mutation,
    syncOrganizationsMutation: mutation,
    revokeUserInvitation: mutation,
    sendUserInvitation: mutation,
  },
};
