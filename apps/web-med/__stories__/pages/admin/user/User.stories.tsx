import type { <PERSON>a, StoryObj } from "@storybook/react";

import UserPage from "@/www/admin/users/details/User";

const meta = {
  title: "Pages/Admin/User/page",
  component: UserPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/admin/users/abc-123",
      },
    },
  },
} satisfies Meta<typeof UserPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    loading: false,
    error: null,
    user: {
      id: "1",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User",
      role: "ADMIN",
      status: "ACTIVE",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  },
};
