import type { Meta, StoryObj } from "@storybook/react";

import SystemPage from "@/www/admin/system/System";

const meta = {
  title: "Pages/Admin/System/page",
  component: SystemPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/admin/system",
      },
    },
  },
} satisfies Meta<typeof SystemPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    values: {
      loading: false,
      error: null,
      values: {
        items: [],
        total: 0,
      },
    },
    specialties: {
      loading: false,
      error: null,
      specialties: {
        items: [],
        total: 0,
      },
    },
  },
};
