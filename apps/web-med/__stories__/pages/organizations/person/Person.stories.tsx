import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { PersonQueryResult } from "@/www/organizations/person/Person";

import { PersonRole } from "@/api";
import Person from "@/www/organizations/person/Person";

const meta = {
  title: "Pages/Organizations/Person/page",
  component: Person,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/people/abc-123",
      },
    },
  },
} satisfies Meta<typeof Person>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for person
const mockPerson: PersonQueryResult = {
  id: faker.string.uuid(),
  role: faker.helpers.arrayElement([
    PersonRole.ADMIN,
    PersonRole.BILLING,
    PersonRole.INTERNAL,
    PersonRole.CLIENT,
  ]),
  title: faker.person.jobTitle(),
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  email: faker.internet.email(),
  phone: faker.phone.number(),
  avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
  organization: {
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
  },
};

export const Default: Story = {
  args: {
    person: mockPerson,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: {
      message: "Failed to load person data",
    },
  },
};
