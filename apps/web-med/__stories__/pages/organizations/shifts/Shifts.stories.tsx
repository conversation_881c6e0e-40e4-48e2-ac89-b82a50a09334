import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  DepartmentType,
  IncidentSeverity,
  IncidentStatus,
  IncidentType,
  LocationType,
  PayType,
  PersonRole,
  ProviderStatus,
  ShiftStatus,
} from "@axa/database-medical";

import type { RouterError, RouterOutputs } from "@/api";

import Shifts from "@/www/organizations/shifts/Shifts";

const meta = {
  title: "Pages/Organizations/Shifts/page",
  component: Shifts,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/shifts",
      },
    },
  },
} satisfies Meta<typeof Shifts>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type ShiftsGetManyQuery = RouterOutputs["shifts"]["getMany"];
type ShiftStructure = ShiftsGetManyQuery["items"][number];
type OrganizationStructure = NonNullable<ShiftStructure["organization"]>;
type LocationStructure = NonNullable<ShiftStructure["location"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type ProviderStructure = NonNullable<ShiftStructure["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type DepartmentStructure = NonNullable<ShiftStructure["department"]>;
type SpecialtyStructure = NonNullable<ShiftStructure["specialties"]>[number];
type ContactStructure = NonNullable<DepartmentStructure["contacts"]>[number];

type IncidentsGetManyQuery = RouterOutputs["incidents"]["getMany"];
type IncidentStructure = IncidentsGetManyQuery["items"][number];

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createAddress = (
  options: {
    formatted?: string;
    timeZone?: string;
    latitude?: number;
    longitude?: number;
  } = {},
): AddressStructure => ({
  formatted:
    options.formatted ??
    `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`,
  timeZone: options.timeZone ?? "America/New_York",
  latitude: options.latitude ?? faker.location.latitude(),
  longitude: options.longitude ?? faker.location.longitude(),
});

const createLocation = (
  options: {
    name?: string;
    type?: LocationType;
    address?: AddressStructure;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  type: options.type ?? "HOSPITAL",
  address: options.address ?? createAddress(),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    avatar?: string | null;
    email?: string | null;
    phone?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  role: options.role ?? "USER",
  avatar: options.avatar ?? faker.image.avatar(),
  email: options.email ?? faker.internet.email(),
  phone: options.phone ?? faker.phone.number(),
});

const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

const createDepartment = (
  options: {
    name?: string;
    description?: string;
    type?: DepartmentType;
    contacts?: ContactStructure[];
  } = {},
): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.commerce.department(),
  type: options.type ?? "DEPARTMENT",
  contacts: options.contacts ?? [],
});

const createSpecialty = (
  options: {
    name?: string;
  } = {},
): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.person.jobType(),
});

const createShift = (
  options: {
    id?: string;
    summary?: string;
    scope?: string;
    role?: string;
    status?: ShiftStatus;
    startDate?: Date;
    endDate?: Date;
    paymentType?: PayType;
    paymentRate?: number;
    paymentAmount?: number;
    paymentTotal?: number;
    hours?: number;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    provider?: ProviderStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    confirmedAt?: Date | null;
    startedAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    timeZone?: string;
    holidayAmount?: number;
    nightAmount?: number;
    overtimeAmount?: number;
    overtimeHours?: number;
    nightTimeHours?: number;
    holidayTimeHours?: number;
  } = {},
): ShiftStructure => {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);

  return {
    id: options.id ?? faker.string.uuid(),
    summary: options.summary ?? "Morning Shift - Emergency Room",
    scope:
      options.scope ?? "General morning duties in the emergency department",
    job: undefined,
    invoice: undefined,
    review: undefined,
    timeZone: options.timeZone ?? "America/New_York",
    holidayAmount: options.holidayAmount ?? 0,
    nightAmount: options.nightAmount ?? 0,
    overtimeAmount: options.overtimeAmount ?? 0,
    paymentTotal: options.paymentTotal ?? 1200,
    contacts: [],
    role: options.role ?? "Physician",
    status: options.status ?? "PENDING",
    startDate: options.startDate ?? now,
    endDate: options.endDate ?? tomorrow,
    paymentType: options.paymentType ?? "HOURLY",
    paymentRate: options.paymentRate ?? 1.0,
    paymentAmount: options.paymentAmount ?? 150,
    hours: options.hours ?? 8,
    organizationId: options.organization?.id ?? organizations.cityGeneral.id,
    organization: options.organization ?? organizations.cityGeneral,
    location: options.location ?? locations.mainHospital,
    provider: options.provider ?? providers.drSmith,
    department: options.department ?? departments.emergency,
    specialties: options.specialties ?? [specialties.emergencyMedicine],
    confirmedAt: options.confirmedAt ?? null,
    startedAt: options.startedAt ?? null,
    completedAt: options.completedAt ?? null,
    cancelledAt: options.cancelledAt ?? null,
    overtimeHours: options.overtimeHours ?? 0,
    nightTimeHours: options.nightTimeHours ?? 0,
    holidayTimeHours: options.holidayTimeHours ?? 0,
  } satisfies ShiftStructure;
};

const createIncident = (
  options: {
    id?: string;
    title?: string;
    description?: string | null;
    severity?: IncidentSeverity;
    status?: IncidentStatus;
    type?: IncidentType;
    createdAt?: Date;
    updatedAt?: Date;
    provider?: ProviderStructure;
    organization?: OrganizationStructure;
    shift?: ShiftStructure;
  } = {},
): IncidentStructure => ({
  id: options.id ?? faker.string.uuid(),
  title: options.title ?? faker.lorem.sentence(),
  description: options.description ?? faker.lorem.paragraph(),
  severity: options.severity ?? "MINOR",
  status: options.status ?? "OPEN",
  type: options.type ?? "HEALTH",
  createdAt: options.createdAt ?? faker.date.recent(),
  updatedAt: options.updatedAt ?? faker.date.recent(),
  providerId: options.provider?.id ?? null,
  organizationId: options.organization?.id ?? null,
  shiftId: options.shift?.id ?? null,
  provider: options.provider,
  organization: options.organization,
  shift: options.shift,
});

// Create common mock data
const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
};

const locations = {
  mainHospital: createLocation({
    name: "Main Hospital",
    type: "HOSPITAL",
    address: createAddress({
      formatted: "123 Medical Center Dr, New York, NY 10001",
      timeZone: "America/New_York",
    }),
  }),
  emergencyCenter: createLocation({
    name: "Emergency Care Center",
    type: "CLINIC",
    address: createAddress({
      formatted: "456 Emergency Blvd, Boston, MA 02108",
      timeZone: "America/New_York",
    }),
  }),
  outpatientClinic: createLocation({
    name: "Outpatient Clinic",
    type: "CLINIC",
    address: createAddress({
      formatted: "789 Clinic Ave, Chicago, IL 60601",
      timeZone: "America/Chicago",
    }),
  }),
};

const departments = {
  emergency: createDepartment({
    name: "Emergency Department",
    type: "DEPARTMENT",
  }),
  cardiology: createDepartment({
    name: "Cardiology Department",
    type: "DEPARTMENT",
  }),
  pediatrics: createDepartment({
    name: "Pediatrics Department",
    type: "DEPARTMENT",
  }),
};

const providers = {
  drSmith: createProvider({
    title: "MD",
    status: "ACTIVE",
    person: createPerson({
      firstName: "John",
      lastName: "Smith",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
  nurseJohnson: createProvider({
    title: "RN",
    status: "ACTIVE",
    person: createPerson({
      firstName: "Sarah",
      lastName: "Johnson",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
  drPatel: createProvider({
    title: "MD",
    status: "ACTIVE",
    person: createPerson({
      firstName: "Raj",
      lastName: "Patel",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
};

const specialties = {
  emergencyMedicine: createSpecialty({ name: "Emergency Medicine" }),
  cardiology: createSpecialty({ name: "Cardiology" }),
  pediatrics: createSpecialty({ name: "Pediatrics" }),
  neurology: createSpecialty({ name: "Neurology" }),
};

// Create mock shifts for different scenarios
const mockShifts = [
  createShift({
    id: "1",
    summary: "Morning Shift - Emergency Room",
    status: "PENDING",
    startDate: new Date("2024-01-01T07:00:00Z"),
    endDate: new Date("2024-01-01T15:00:00Z"),
    organization: organizations.cityGeneral,
    location: locations.mainHospital,
    provider: providers.drSmith,
    department: departments.emergency,
    specialties: [specialties.emergencyMedicine],
  }),
  createShift({
    id: "2",
    summary: "Afternoon Shift - Cardiology",
    status: "CONFIRMED",
    startDate: new Date("2024-01-02T15:00:00Z"),
    endDate: new Date("2024-01-02T23:00:00Z"),
    confirmedAt: new Date("2024-01-01T10:00:00Z"),
    organization: organizations.memorialHealth,
    location: locations.mainHospital,
    provider: providers.drPatel,
    department: departments.cardiology,
    specialties: [specialties.cardiology],
  }),
  createShift({
    id: "3",
    summary: "Night Shift - Emergency Room",
    status: "COMPLETED",
    startDate: new Date("2024-01-03T23:00:00Z"),
    endDate: new Date("2024-01-04T07:00:00Z"),
    confirmedAt: new Date("2024-01-02T10:00:00Z"),
    startedAt: new Date("2024-01-03T23:00:00Z"),
    completedAt: new Date("2024-01-04T07:00:00Z"),
    organization: organizations.cityGeneral,
    location: locations.emergencyCenter,
    provider: providers.drSmith,
    department: departments.emergency,
    specialties: [specialties.emergencyMedicine],
  }),
  createShift({
    id: "4",
    summary: "Morning Shift - Pediatrics",
    status: "CANCELLED",
    startDate: new Date("2024-01-04T07:00:00Z"),
    endDate: new Date("2024-01-04T15:00:00Z"),
    cancelledAt: new Date("2024-01-03T18:00:00Z"),
    organization: organizations.universityMedical,
    location: locations.outpatientClinic,
    provider: providers.nurseJohnson,
    department: departments.pediatrics,
    specialties: [specialties.pediatrics],
  }),
];

// Create mock incidents
const mockIncidents = [
  createIncident({
    id: "1",
    title: "Patient Fall",
    description: "Patient fell while getting out of bed",
    severity: "MINOR",
    status: "OPEN",
    type: "HEALTH",
    createdAt: new Date("2024-01-03T09:30:00Z"),
    provider: providers.nurseJohnson,
    organization: organizations.cityGeneral,
    shift: mockShifts[0],
  }),
  createIncident({
    id: "2",
    title: "Medication Error",
    description: "Wrong medication administered to patient",
    severity: "MAJOR",
    status: "IN_PROGRESS",
    type: "HEALTH",
    createdAt: new Date("2024-01-04T01:15:00Z"),
    provider: providers.drSmith,
    organization: organizations.cityGeneral,
    shift: mockShifts[2],
  }),
];

// Create story variants
export const Default: Story = {
  args: {
    shifts: {
      data: {
        items: mockShifts,
        total: mockShifts.length,
      },
      loading: false,
      error: null,
    },
    incidents: {
      data: {
        items: mockIncidents,
        total: mockIncidents.length,
      },
      loading: false,
      error: null,
    },
  },
};

export const Empty: Story = {
  args: {
    shifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
    incidents: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    shifts: {
      data: undefined,
      loading: true,
      error: null,
    },
    incidents: {
      data: undefined,
      loading: true,
      error: null,
    },
  },
};

export const WithError: Story = {
  args: {
    shifts: {
      data: undefined,
      loading: false,
      error: {
        message: "Failed to load shifts",
      } as RouterError,
    },
    incidents: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};

export const WithIncidentsError: Story = {
  args: {
    shifts: {
      data: {
        items: mockShifts,
        total: mockShifts.length,
      },
      loading: false,
      error: null,
    },
    incidents: {
      data: undefined,
      loading: false,
      error: {
        message: "Failed to load incidents",
      } as RouterError,
    },
  },
};

export const WithManyShifts: Story = {
  args: {
    shifts: {
      data: {
        items: Array.from({ length: 20 }, (_, i) =>
          createShift({
            id: `shift-${i + 1}`,
            summary: `Shift ${i + 1} - ${faker.commerce.department()}`,
            status: ["PENDING", "CONFIRMED", "COMPLETED", "CANCELLED"][
              i % 4
            ] as ShiftStatus,
            startDate: faker.date.soon({ days: (i % 7) + 1 }),
            endDate: faker.date.soon({ days: (i % 7) + 2 }),
            organization: Object.values(organizations)[i % 3],
            location: Object.values(locations)[i % 3],
            provider: Object.values(providers)[i % 3],
            department: Object.values(departments)[i % 3],
            specialties: [
              Object.values(specialties)[i % 4] || { id: "", name: "" },
            ],
          }),
        ),
        total: 20,
      },
      loading: false,
      error: null,
    },
    incidents: {
      data: {
        items: mockIncidents,
        total: mockIncidents.length,
      },
      loading: false,
      error: null,
    },
  },
};

export const WithManyIncidents: Story = {
  args: {
    shifts: {
      data: {
        items: mockShifts,
        total: mockShifts.length,
      },
      loading: false,
      error: null,
    },
    incidents: {
      data: {
        items: Array.from({ length: 15 }, (_, i) =>
          createIncident({
            id: `incident-${i + 1}`,
            title: `Incident ${i + 1}: ${faker.lorem.words(3)}`,
            description: faker.lorem.paragraph(),
            severity: ["MINOR", "MAJOR", "CRITICAL"][i % 3] as IncidentSeverity,
            status: ["OPEN", "IN_PROGRESS", "RESOLVED"][
              i % 3
            ] as IncidentStatus,
            type: ["HEALTH", "SAFETY", "SECURITY"][i % 3] as IncidentType,
            createdAt: faker.date.recent({ days: (i % 10) + 1 }),
            provider: Object.values(providers)[i % 3],
            organization: Object.values(organizations)[i % 3],
            shift: mockShifts[i % 4],
          }),
        ),
        total: 15,
      },
      loading: false,
      error: null,
    },
  },
};
