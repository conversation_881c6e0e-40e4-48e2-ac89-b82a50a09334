import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import Facility from "@/www/organizations/facility/Facility";

const meta = {
  title: "Pages/Organizations/Facility/page",
  component: Facility,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/facilities/abc-123",
      },
    },
  },
} satisfies Meta<typeof Facility>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockFacility: RouterOutputs["locations"]["get"] = {
  id: "loc_1",
  name: "City General Hospital",
  type: "HOSPITAL",
  description:
    "A major hospital in the city center with state-of-the-art facilities and equipment. We provide comprehensive medical care 24/7 with a team of experienced healthcare professionals.",
  address: {
    id: "addr_1",
    formatted: "123 Medical Center Dr, New York, NY 10001",
    timeZone: "America/New_York",
    latitude: 40.7128,
    longitude: -74.006,
  },
  organization: {
    id: "org_1",
    name: "City Healthcare Network",
    avatar: "/placeholder.svg",
    path: "/organizations/org_1",
    type: "CLIENT",
    status: "ACTIVE",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    deletedAt: null,
  },
};

export const Default: Story = {
  args: {
    facility: mockFacility,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: {
      message: "Failed to load facility details",
      code: "INTERNAL_SERVER_ERROR",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "locations.get",
        zodError: null,
      },
    },
  },
};

export const NoDescription: Story = {
  args: {
    facility: {
      ...mockFacility,
      description: "",
    },
  },
};
