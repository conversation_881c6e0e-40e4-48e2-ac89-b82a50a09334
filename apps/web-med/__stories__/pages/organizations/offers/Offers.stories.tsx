import type { <PERSON>a, StoryObj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import OffersView from "@/www/organizations/offers/Offers";

import { offers } from "../offer/data";

const meta = {
  title: "Pages/Organizations/Offers/Offers",
  component: OffersView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/offers/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.offers.get.query(({ input }) => {
          // Return the appropriate contract based on the ID
          const id = input.id;

          if (id === "abc-123") {
            return offers.pending;
          } else if (id === "abc-124") {
            return offers.pending;
          } else if (id === "abc-125") {
            return offers.accepted;
          } else if (id === "abc-126") {
            return offers.rejected;
          }

          // Default fallback
          return offers.pending;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the contract with the matching thread ID
          const offer = Object.values(offers).find((c) => c.thread?.id === id);

          if (offer?.thread) {
            return {
              ...offer.thread,
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof OffersView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Draft: Story = {
  args: {
    loading: false,
    offers: {
      data: {
        items: [offers.pending, offers.accepted, offers.rejected],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Pending: Story = {
  args: {
    loading: false,
    offers: {
      data: {
        items: [offers.pending, offers.accepted, offers.rejected],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Active: Story = {
  args: {
    loading: false,
    offers: {
      data: {
        items: [offers.pending, offers.accepted, offers.rejected],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Expired: Story = {
  args: {
    loading: false,
    offers: {
      data: {
        items: [offers.pending, offers.accepted, offers.rejected],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Terminated: Story = {
  args: {
    loading: false,
    offers: {
      data: {
        items: [offers.pending, offers.accepted, offers.rejected],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    offers: {
      loading: true,
    },
  },
};
