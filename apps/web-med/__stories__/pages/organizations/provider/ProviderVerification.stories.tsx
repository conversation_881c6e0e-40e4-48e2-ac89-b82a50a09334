import type { Meta, StoryObj } from "@storybook/react";

import { VerificationStatus } from "@/api";
import ProviderVerification, {
  ProviderVerificationSkeleton,
} from "@/www/organizations/provider/ProviderVerification";

import { baseProvider } from "./data";

// Simple inline mock mutation helper
const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
  },
  mutateAsync: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate Async]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return { id: "mock-verify-id" } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {
    console.log("[Storybook Mock Reset]");
  },
});

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderVerification",
  component: ProviderVerification,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "status", "internal"],
} satisfies Meta<typeof ProviderVerification>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define default verification with required fields
const defaultVerificationData = {
  id: baseProvider.verification?.id ?? "mock-verification-id",
  providerId: baseProvider.id,
  status: baseProvider.verification?.status ?? VerificationStatus.PENDING,
  verifiedAt: baseProvider.verification?.verifiedAt ?? null,
  i9VerifiedAt: baseProvider.verification?.i9VerifiedAt ?? null,
  identityVerifiedAt: baseProvider.verification?.identityVerifiedAt ?? null,
  backgroundVerifiedAt: baseProvider.verification?.backgroundVerifiedAt ?? null,
  backgroundCheckStatus:
    baseProvider.verification?.backgroundCheckStatus ?? null,
  i9VerificationStatus: baseProvider.verification?.i9VerificationStatus ?? null,
  identityVerificationStatus:
    baseProvider.verification?.identityVerificationStatus ?? null,
};

// --- Default Story (Pending) ---

export const Default: Story = {
  args: {
    loading: false,
    error: undefined,
    provider: baseProvider,
    verification: {
      ...defaultVerificationData,
      status: VerificationStatus.PENDING,
    },
    verify: createMockMutation(),
  } as any, // Cast args
};

// --- Loading State ---

export const Loading: Story = {
  args: {
    loading: true,
    error: undefined,
    provider: baseProvider, // Provider data might still be available
    verification: undefined,
    verify: createMockMutation(),
  } as any,
  render: () => <ProviderVerificationSkeleton />,
};

// --- Error State ---

export const WithError: Story = {
  args: {
    loading: false,
    error: { message: "Failed to load verification data" },
    provider: baseProvider,
    verification: undefined,
    verify: { ...createMockMutation(), isError: true },
  } as any,
};

// --- Verified State ---

export const Verified: Story = {
  args: {
    loading: false,
    error: undefined,
    provider: baseProvider,
    verification: {
      ...defaultVerificationData,
      status: VerificationStatus.APPROVED,
      backgroundCheckStatus: VerificationStatus.APPROVED,
      i9VerificationStatus: VerificationStatus.APPROVED,
      identityVerificationStatus: VerificationStatus.APPROVED,
      verifiedAt: new Date(),
    },
    verify: createMockMutation(),
  } as any,
};

// --- Rejected State ---

export const Rejected: Story = {
  args: {
    loading: false,
    error: undefined,
    provider: baseProvider,
    verification: {
      ...defaultVerificationData,
      status: VerificationStatus.REJECTED,
      identityVerificationStatus: VerificationStatus.REJECTED,
    },
    verify: createMockMutation(),
  } as any,
};
