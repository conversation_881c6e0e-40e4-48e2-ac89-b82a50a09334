import type { Meta, StoryObj } from "@storybook/react";

import {
  ProviderStatus as ProviderStatusEnum,
  VerificationStatus,
} from "@/api";
import ProviderStatus, {
  ProviderStatusSkeleton,
} from "@/www/organizations/provider/ProviderStatus";

import { baseProvider } from "./data";

// Simple inline mock mutation helper
const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
  },
  mutateAsync: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate Async]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return { id: "mock-op-id" } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {
    console.log("[Storybook Mock Reset]");
  },
});

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderStatus",
  component: ProviderStatus,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "status", "internal"],
} satisfies Meta<typeof ProviderStatus>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define default verification with required fields
const defaultVerificationData = {
  id: baseProvider.verification?.id ?? "mock-verification-id",
  providerId: baseProvider.id,
  status: baseProvider.verification?.status ?? VerificationStatus.PENDING,
  verifiedAt: baseProvider.verification?.verifiedAt ?? null,
  i9VerifiedAt: baseProvider.verification?.i9VerifiedAt ?? null,
  identityVerifiedAt: baseProvider.verification?.identityVerifiedAt ?? null,
  backgroundVerifiedAt: baseProvider.verification?.backgroundVerifiedAt ?? null,
  backgroundCheckStatus:
    baseProvider.verification?.backgroundCheckStatus ?? null,
  i9VerificationStatus: baseProvider.verification?.i9VerificationStatus ?? null,
  identityVerificationStatus:
    baseProvider.verification?.identityVerificationStatus ?? null,
};

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
    verification: defaultVerificationData,
    updateStatus: createMockMutation(),
  } as any, // Cast args
};

// --- Loading State ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
    verification: undefined,
    updateStatus: createMockMutation(),
  } as any,
  render: () => <ProviderStatusSkeleton />,
};

// --- Error State ---
// Assuming component shows minimal info or error state if provider load fails
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined,
    verification: defaultVerificationData, // Verification might still load
    updateStatus: {
      ...createMockMutation(),
      isError: true,
      error: { message: "Failed to update status" },
    },
  } as any,
  // Optional: render specific error message or rely on component internal handling
};

// --- Different Provider Statuses ---

export const StatusPending: Story = {
  args: {
    loading: false,
    provider: { ...baseProvider, status: ProviderStatusEnum.PENDING },
    verification: defaultVerificationData,
    updateStatus: createMockMutation(),
  } as any,
  name: "Status: Pending",
};

export const StatusActive: Story = {
  args: {
    loading: false,
    provider: { ...baseProvider, status: ProviderStatusEnum.ACTIVE },
    verification: {
      ...defaultVerificationData,
      status: VerificationStatus.APPROVED,
    },
    updateStatus: createMockMutation(),
  } as any,
  name: "Status: Active",
};

export const StatusInactive: Story = {
  args: {
    loading: false,
    provider: { ...baseProvider, status: ProviderStatusEnum.INACTIVE },
    verification: defaultVerificationData,
    updateStatus: createMockMutation(),
  } as any,
  name: "Status: Inactive",
};

export const StatusSuspended: Story = {
  args: {
    loading: false,
    provider: { ...baseProvider, status: ProviderStatusEnum.SUSPENDED },
    verification: defaultVerificationData,
    updateStatus: createMockMutation(),
  } as any,
  name: "Status: Suspended",
};
