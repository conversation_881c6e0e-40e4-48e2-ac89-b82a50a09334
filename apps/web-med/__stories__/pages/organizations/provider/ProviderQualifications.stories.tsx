import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  AccountStatus,
  ProviderStatus,
  QualificationStatus,
  QualificationType,
  ShiftStatus,
  TimeBlockRecurrence,
  TimeBlockType,
  VerificationStatus,
} from "@/api";
import ProviderQualifications from "@/www/organizations/provider/ProviderQualifications";

// Import mocks from data.ts
import { baseProvider, createMockQualification } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/Qualifications", // Updated title
  component: ProviderQualifications,
  parameters: {
    layout: "centered", // Centered layout might be better for a single card
  },
  tags: ["autodocs"],
  argTypes: {
    loading: { control: "boolean" },
    provider: { control: "object" },
  },
} satisfies Meta<typeof ProviderQualifications>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story: Default state with multiple qualifications
export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
  },
};

// Story: Loading state
export const Loading: Story = {
  args: {
    loading: true,
    provider: baseProvider,
  },
  parameters: {
    docs: {
      description: {
        story: "Displays the skeleton loader for the qualifications section.",
      },
    },
  },
};

// Story: Empty state (provider exists but no qualifications)
export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      qualifications: [],
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays the section when the provider has no qualifications listed.",
      },
    },
  },
};

// Story: Provider data is undefined (should render null or handle gracefully)
export const NoProviderData: Story = {
  args: {
    loading: false,
    provider: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Tests the component's behavior when the provider data is undefined. It should ideally render nothing.",
      },
    },
  },
};

// Story: Specific qualification statuses
export const SpecificStatuses: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      qualifications: [
        createMockQualification(
          QualificationStatus.PENDING,
          QualificationType.LICENSE,
        ),
        createMockQualification(
          QualificationStatus.APPROVED,
          QualificationType.DEGREE,
        ),
        createMockQualification(
          QualificationStatus.REJECTED,
          QualificationType.CERTIFICATE,
        ),
        createMockQualification(
          QualificationStatus.EXPIRED,
          QualificationType.LICENSE,
        ),
        createMockQualification(
          QualificationStatus.APPROVED,
          QualificationType.OTHER,
        ),
      ],
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays qualifications with a specific mix of statuses and types.",
      },
    },
  },
};
