import type { <PERSON>a, StoryObj } from "@storybook/react";

import ProviderJobExperiences from "@/www/organizations/provider/ProviderJobExperiences";

// Import shared mocks
import { baseProvider } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/JobExperiences", // Updated title
  component: ProviderJobExperiences,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    loading: { control: "boolean" },
    provider: { control: "object" },
  },
} satisfies Meta<typeof ProviderJobExperiences>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story: Default state with experiences
export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
  },
};

// Story: Loading state
export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined, // Provide no data when loading
  },
};

// Story: Empty state (provider exists but no experiences)
export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      experiences: [], // Override with empty array
    },
  },
};

// Story: Provider data is undefined (should render null or handle gracefully)
export const NoProviderData: Story = {
  args: {
    loading: false,
    provider: undefined,
  },
};
