import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ProviderSchedule, {
  ProviderScheduleSkeleton, // Assuming skeleton exists
} from "@/www/organizations/provider/ProviderSchedule";

import { baseProvider } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderSchedule",
  component: ProviderSchedule,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "schedule"],
} satisfies Meta<typeof ProviderSchedule>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
  },
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
  },
  render: () => <ProviderScheduleSkeleton />,
};

// --- Error Story ---
// Assuming component handles missing provider data gracefully
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined,
  },
};

// --- Empty State (No Schedule/Blocks) ---

export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      schedules: [], // Empty schedules array
      calendar: {
        ...baseProvider.calendar!,
        blocks: [], // Empty blocks in calendar
      },
    },
  },
};
