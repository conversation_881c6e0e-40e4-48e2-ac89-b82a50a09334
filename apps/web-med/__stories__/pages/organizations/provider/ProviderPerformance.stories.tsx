import type { <PERSON>a, StoryObj } from "@storybook/react";

import ProviderPerformance, {
  ProviderPerformanceSkeleton, // Assuming skeleton exists
} from "@/www/organizations/provider/ProviderPerformance";

import { baseProvider } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderPerformance",
  component: ProviderPerformance,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "performance"],
} satisfies Meta<typeof ProviderPerformance>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
  },
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
  },
  render: () => <ProviderPerformanceSkeleton />,
};

// --- Error Story ---
// Assuming component handles missing provider data gracefully
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined,
  },
  render: () => <div>Error loading performance data.</div>,
};

// --- Empty State (Simulated) ---
// Performance metrics are likely within the provider object.
// Modify baseProvider if specific fields being null/zero affects display.
export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      score: 0, // Example: simulate zero score
      // Simulate empty shifts if that affects performance metrics
      shifts: [],
    },
  },
};

// --- High Score Story ---
export const HighScore: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      score: 98, // Set a high score
    },
  },
};

// --- Low Score Story ---
export const LowScore: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      score: 62, // Set a low score
    },
  },
};
