import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ProviderReviews, {
  ProviderReviewsSkeleton, // Assuming skeleton exists
} from "@/www/organizations/provider/ProviderReviews";

import { baseProvider } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderReviews",
  component: ProviderReviews,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "reviews"],
} satisfies Meta<typeof ProviderReviews>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
  },
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
  },
  render: () => <ProviderReviewsSkeleton />,
};

// --- Error Story ---
// Assuming component handles missing provider data gracefully or shows error
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined,
    // If component takes an error prop:
    // error: { message: "Failed to load reviews data" }
  },
};

// --- Empty State (No Reviews) ---

export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      reviews: [], // Explicitly empty reviews
    },
  },
};

// --- One Review ---

export const OneReview: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      reviews: baseProvider.reviews?.slice(0, 1) ?? [], // Take only one review
    },
  },
};
