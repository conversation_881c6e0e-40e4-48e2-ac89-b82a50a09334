import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  AccountStatus,
  ProviderStatus,
  QualificationStatus,
  QualificationType,
  ShiftStatus,
  TimeBlockRecurrence,
  TimeBlockType,
  VerificationStatus,
} from "@/api";

// --- Refactored Qualification Mock Generation ---

// Define some realistic templates
const qualificationTemplates = [
  {
    type: QualificationType.DEGREE,
    namePattern: () => `${faker.person.jobArea()} Degree`,
    institutionPattern: () => `${faker.company.name()} University`,
    stateRequired: false,
    expires: false,
  },
  {
    type: QualificationType.LICENSE,
    namePattern: () => `Medical License`,
    institutionPattern: (state: string) => `Board of Medicine - ${state}`,
    stateRequired: true,
    expires: true,
  },
  {
    type: QualificationType.CERTIFICATE,
    namePattern: () =>
      `${faker.commerce.productAdjective()} Board Certification`,
    institutionPattern: () => `${faker.company.name()} Association`,
    stateRequired: false,
    expires: true,
  },
  {
    type: QualificationType.CERTIFICATE,
    namePattern: () =>
      `Advanced ${faker.hacker.adjective()} Life Support (A${faker.hacker.abbreviation()}LS)`,
    institutionPattern: () =>
      `${faker.company.name()} College of ${faker.person.jobArea()}s`,
    stateRequired: false,
    expires: true,
  },
  {
    type: QualificationType.OTHER,
    namePattern: () => `Special ${faker.word.adjective()} Training`,
    institutionPattern: () => `${faker.company.name()} Institute`,
    stateRequired: false,
    expires: faker.datatype.boolean(), // Other types might or might not expire
  },
];

// Helper function to generate logical dates based on status and expiration
function generateQualificationDates(
  status: QualificationStatus,
  expires: boolean,
) {
  let startDate: Date | null = null;
  let endDate: Date | null = null;

  switch (status) {
    case QualificationStatus.APPROVED:
      startDate = faker.date.past({ years: 5 });
      if (expires) {
        // If it expires, set end date in the future
        endDate = faker.date.future({ years: 3, refDate: startDate });
      } else {
        // Degrees typically don't expire in this context
        endDate = null;
      }
      break;
    case QualificationStatus.PENDING:
      // Pending might not have dates yet, or start date is near future
      if (faker.datatype.boolean(0.5)) {
        startDate = faker.date.soon({ days: 30 });
        endDate = expires
          ? faker.date.future({ years: 3, refDate: startDate })
          : null;
      }
      break;
    case QualificationStatus.EXPIRED:
      // Both dates must be in the past
      endDate = faker.date.past({ years: 1 });
      startDate = faker.date.past({ years: 5, refDate: endDate });
      break;
    case QualificationStatus.REJECTED:
      // Might have past dates reflecting application period, or null
      if (faker.datatype.boolean(0.6)) {
        startDate = faker.date.past({ years: 1 });
        // Reject usually means no end date relevant
        endDate = null;
      } else {
        startDate = null;
        endDate = null;
      }
      break;
  }

  return { startDate, endDate };
}

export const createMockQualification = (
  statusOverride?: QualificationStatus,
  typeOverride?: QualificationType,
) => {
  // 1. Select Template
  let template;
  if (typeOverride) {
    const possibleTemplates = qualificationTemplates.filter(
      (t) => t.type === typeOverride,
    );
    template =
      faker.helpers.arrayElement(possibleTemplates) ??
      faker.helpers.arrayElement(qualificationTemplates); // Fallback if type not found
  } else {
    template = faker.helpers.arrayElement(qualificationTemplates);
  }

  // 2. Determine Status & State
  const status =
    statusOverride ??
    faker.helpers.arrayElement(Object.values(QualificationStatus));
  const state = template.stateRequired
    ? faker.location.state({ abbreviated: true })
    : null;

  // 3. Generate Name & Institution
  const name = template.namePattern();
  const institution = template.institutionPattern(state ?? ""); // Pass state if needed

  // 4. Generate Dates
  const { startDate, endDate } = generateQualificationDates(
    status,
    template.expires,
  );

  // 5. Combine
  return {
    id: faker.string.uuid(),
    name,
    type: template.type,
    identifier: faker.string.alphanumeric(10).toUpperCase(),
    institution,
    status,
    state,
    country: "United States",
    startDate,
    endDate,
  };
};

// --- Refactored Experience Mock Generation ---

// Takes the end date of the *previous* experience to ensure no overlap
export const createMockExperience = (previousEndDate: Date | null = null) => {
  let startDate: Date;

  // Determine start date based on previous end date
  if (previousEndDate === null) {
    // First job generated (will be furthest in the past after reversal)
    startDate = faker.date.past({ years: 10 }); // Start it further back
  } else {
    // Subsequent jobs start after a gap from the previous one
    const gapDays = faker.number.int({ min: 30, max: 180 });
    startDate = faker.date.soon({ days: gapDays, refDate: previousEndDate });
  }

  // ALWAYS generate an end date relative to the start date
  const endDate = faker.date.future({
    years: faker.number.int({ min: 1, max: 4 }), // Job duration 1-4 years
    refDate: startDate,
  });

  // Generate specialties for this role
  const specialties = Array.from(
    { length: faker.number.int({ min: 1, max: 3 }) },
    () => ({
      id: faker.string.uuid(),
      name: faker.person.jobArea(),
    }),
  );

  // Generate facility details
  const state = faker.location.state({ abbreviated: true });
  const facility = {
    id: faker.string.uuid(),
    name: `${faker.company.name()} Medical Center`,
    address: {
      state: state,
    },
  };

  return {
    id: faker.string.uuid(),
    role: faker.person.jobTitle(),
    company: facility.name,
    // Generate a longer description
    description: faker.lorem.paragraphs(8), // Use paragraphs for longer text
    startDate,
    endDate,
    facility,
    specialties,
  };
};

// Helper to generate a *list* of sequential experiences
const generateSequentialExperiences = (count: number) => {
  const experiences = [];
  let lastEndDate: Date | null = null;
  for (let i = 0; i < count; i++) {
    const newExperience = createMockExperience(lastEndDate);
    experiences.push(newExperience);
    // The new end date becomes the 'previous' for the next iteration
    // If current job (endDate is null), keep lastEndDate null for subsequent calls
    if (newExperience.endDate !== null) {
      lastEndDate = newExperience.endDate;
    }
  }
  // Reverse the array so the most recent job (potentially current) is first
  return experiences.reverse();
};

// --- Base Provider Mock ---
export const baseProvider: RouterOutputs["providers"]["get"] = {
  id: faker.string.uuid(),
  accountId: faker.string.uuid(),
  accountStatus: AccountStatus.COMPLETED,
  title: faker.person.jobTitle(),
  spokenLanguages: [faker.location.country(), faker.location.country()],
  score: faker.number.int({ min: 70, max: 100 }),
  person: {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    avatar: faker.image.avatar(),
  },
  address: {
    id: faker.string.uuid(),
    formatted: faker.location.streetAddress(true),
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
    timeZone: faker.location.timeZone(),
  },
  verification: {
    id: faker.string.uuid(),
    status: faker.helpers.arrayElement(Object.values(VerificationStatus)),
    backgroundCheckStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    i9VerificationStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    identityVerificationStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    verifiedAt: faker.date.past(),
    backgroundVerifiedAt: faker.date.past(),
    i9VerifiedAt: faker.date.past(),
    identityVerifiedAt: faker.date.past(),
  },
  specialties: Array.from(
    { length: faker.number.int({ min: 1, max: 3 }) },
    () => ({
      id: faker.string.uuid(),
      name: faker.person.jobArea(),
      description: faker.lorem.sentence(),
    }),
  ),
  qualifications: Array.from(
    { length: faker.number.int({ min: 3, max: 5 }) },
    () => createMockQualification(), // Use the helper function here
  ),
  experiences: generateSequentialExperiences(
    faker.number.int({ min: 2, max: 5 }),
  ),
  reviews: [],
  shifts: [],
  schedules: [],
  calendar: undefined,
  settings: {
    id: faker.string.uuid(),
    openToWork: faker.datatype.boolean(),
    openToOnCall: faker.datatype.boolean(),
    openToRelocate: faker.datatype.boolean(),
    openToTravel: faker.datatype.boolean(),
  },
  createdAt: faker.date.past({ years: 2 }),
  updatedAt: faker.date.recent(),
  status: faker.helpers.arrayElement(Object.values(ProviderStatus)),
};
