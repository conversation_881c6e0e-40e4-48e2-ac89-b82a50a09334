import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ProviderBanking, {
  ProviderBankingSkeleton, // Assuming skeleton exists
} from "@/www/organizations/provider/ProviderBanking";

import { baseProvider } from "./data";

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderBanking",
  component: ProviderBanking,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "banking"],
} satisfies Meta<typeof ProviderBanking>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

export const Default: Story = {
  args: {
    // Assuming it doesn't need a loading prop directly, just the data
    provider: baseProvider,
  },
};

// --- Loading Story ---
// The parent component (`InternalStatusPanel`) passes the provider data.
// If the *provider* data is loading, this component likely isn't rendered or gets undefined.
// We simulate the loading state via the skeleton.
export const Loading: Story = {
  args: {
    provider: undefined,
  },
  render: () => <ProviderBankingSkeleton />,
};

// --- Error Story ---
// If provider data fails to load in parent, this component receives undefined
export const WithError: Story = {
  args: {
    provider: undefined,
  },
  render: () => <div>Error loading banking information.</div>, // Or similar message
};

// --- Empty State (Simulated) ---
// Relevant banking info is likely part of the main provider object
// Modify baseProvider if specific fields being null/empty changes the display
export const Empty: Story = {
  args: {
    provider: {
      ...baseProvider,
      // Example: Simulate missing account ID or status if relevant
      accountId: null,
      accountStatus: null,
    },
  },
};
