import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import ProviderInfo, {
  ProviderInfoSkeleton, // Import skeleton for loading state
} from "@/www/organizations/provider/ProviderInfo";

import { baseProvider } from "./data"; // Only import baseProvider

// No shared mock helper, define inline or in this file
// import { createMockMutation } from "./data.common";

// Simple inline mock mutation helper for stories
const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate]", vars);
    // Simulate async operation
    await new Promise((resolve) => setTimeout(resolve, 500));
    // You could potentially add logic here to show toast/feedback in Storybook
  },
  mutateAsync: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate Async]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
    // Return a minimal success-like response
    return { id: "mock-updated-id" } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {
    console.log("[Storybook Mock Reset]");
  },
});

const meta = {
  title: "Pages/Organizations/Provider/Components/ProviderInfo",
  component: ProviderInfo,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider"],
} satisfies Meta<typeof ProviderInfo>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider, // Use the baseProvider directly
    updateProvider: createMockMutation(), // Create a mock mutation
  } as any, // Cast entire args to any
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
    // No need to pass mutation if component isn't rendered or uses skeleton
  },
  // Render the skeleton directly for a better loading preview
  render: () => <ProviderInfoSkeleton />,
};

// --- Error Story ---
// Assuming parent component handles error display, so we show the component with no data
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined, // No data on parent error
    updateProvider: {
      ...createMockMutation(),
      isError: true,
      error: { message: "Failed to update provider" },
    }, // Error state mutation
  } as any, // Cast entire args to any
};

// --- Empty State Story (Simulated) ---
// Create a version of baseProvider with potentially empty/null fields if needed
// For now, we just use the default baseProvider, assuming "empty" isn't visually distinct for *this* component
// unless specific fields are checked.
export const Empty: Story = {
  args: {
    loading: false,
    provider: {
      ...baseProvider,
      title: null, // Example: Simulate a provider with no title
      spokenLanguages: [],
      person: {
        // Ensure required fields are present even when others are null
        id: baseProvider.person?.id ?? "mock-person-id",
        firstName: baseProvider.person?.firstName ?? "-",
        lastName: baseProvider.person?.lastName ?? "-",
        email: null,
        phone: null,
        avatar: null, // Also make avatar null for empty state
      },
      // Simulate missing address
      address: undefined,
    },
    updateProvider: createMockMutation(),
  } as any, // Cast entire args to any
};
