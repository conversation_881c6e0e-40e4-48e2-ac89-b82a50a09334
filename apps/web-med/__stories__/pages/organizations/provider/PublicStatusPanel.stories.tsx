import type { Meta, StoryObj } from "@storybook/react";

import { VerificationStatus } from "@/api"; // Import the enum
import PublicStatusPanel, {
  PublicStatusPanelSkeleton, // Import skeleton
} from "@/www/organizations/provider/PublicStatusPanel";

import { baseProvider } from "./data"; // Assuming verification is part of baseProvider

const meta = {
  title: "Pages/Organizations/Provider/Components/PublicStatusPanel",
  component: PublicStatusPanel,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "status"],
} satisfies Meta<typeof PublicStatusPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define all required fields explicitly, providing defaults for potentially undefined ones
const defaultVerification = {
  id: baseProvider.verification?.id ?? "mock-verification-id",
  providerId: baseProvider.id,
  status: baseProvider.verification?.status ?? VerificationStatus.PENDING,
  verifiedAt: baseProvider.verification?.verifiedAt ?? null,
  i9VerifiedAt: baseProvider.verification?.i9VerifiedAt ?? null,
  identityVerifiedAt: baseProvider.verification?.identityVerifiedAt ?? null,
  backgroundVerifiedAt: baseProvider.verification?.backgroundVerifiedAt ?? null,
  backgroundCheckStatus:
    baseProvider.verification?.backgroundCheckStatus ?? null,
  i9VerificationStatus: baseProvider.verification?.i9VerificationStatus ?? null,
  identityVerificationStatus:
    baseProvider.verification?.identityVerificationStatus ?? null,
};

// --- Default Story ---

export const Default: Story = {
  args: {
    loading: false,
    provider: baseProvider,
    verification: defaultVerification,
  },
};

// --- Loading Story ---

export const Loading: Story = {
  args: {
    loading: true,
    provider: undefined,
    verification: undefined,
  },
  render: () => <PublicStatusPanelSkeleton />,
};

// --- Error Story ---
// Assuming component doesn't render on error in parent
export const WithError: Story = {
  args: {
    loading: false,
    provider: undefined,
    verification: undefined,
  },
  render: () => (
    <div className="text-destructive">Error loading status panel data.</div>
  ), // Or render nothing
};

// --- Different Verification Statuses ---

export const VerificationPending: Story = {
  args: {
    loading: false,
    provider: baseProvider,
    verification: {
      ...defaultVerification,
      status: VerificationStatus.PENDING,
      backgroundCheckStatus: VerificationStatus.PENDING,
      i9VerificationStatus: VerificationStatus.PENDING,
      identityVerificationStatus: VerificationStatus.PENDING,
      verifiedAt: null,
      backgroundVerifiedAt: null,
      i9VerifiedAt: null,
      identityVerifiedAt: null,
    },
  },
  name: "Verification Pending",
};

export const VerificationFailed: Story = {
  args: {
    loading: false,
    provider: baseProvider,
    verification: {
      ...defaultVerification,
      status: VerificationStatus.REJECTED,
      identityVerificationStatus: VerificationStatus.REJECTED,
      backgroundCheckStatus: VerificationStatus.APPROVED,
      i9VerificationStatus: VerificationStatus.PENDING,
      verifiedAt: null,
      backgroundVerifiedAt: new Date(),
      // Ensure other date fields are null if not applicable to this state
      i9VerifiedAt: null,
      identityVerifiedAt: null,
    },
  },
  name: "Verification Failed/Rejected",
};

export const VerificationVerified: Story = {
  args: {
    loading: false,
    provider: baseProvider,
    verification: {
      ...defaultVerification,
      status: VerificationStatus.APPROVED,
      backgroundCheckStatus: VerificationStatus.APPROVED,
      i9VerificationStatus: VerificationStatus.APPROVED,
      identityVerificationStatus: VerificationStatus.APPROVED,
      verifiedAt: new Date(),
      backgroundVerifiedAt: new Date(Date.now() - 86400000),
      i9VerifiedAt: new Date(Date.now() - 172800000),
      identityVerifiedAt: new Date(Date.now() - 259200000),
    },
  },
  name: "Verification Verified/Approved",
};
