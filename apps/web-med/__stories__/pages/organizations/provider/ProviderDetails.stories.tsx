import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import {
  AccountStatus,
  ProviderStatus,
  QualificationStatus,
  QualificationType,
  ShiftStatus,
  TimeBlockRecurrence,
  TimeBlockType,
  VerificationStatus,
} from "@/api";
import Provider from "@/www/organizations/provider/Provider";

const meta = {
  title: "Pages/Organizations/Provider/page",
  component: Provider,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/providers/abc-123",
      },
    },
  },
} satisfies Meta<typeof Provider>;

export default meta;
type Story = StoryObj<typeof meta>;

const baseProvider = {
  id: faker.string.uuid(),
  accountId: faker.string.uuid(),
  accountStatus: AccountStatus.COMPLETED,
  title: faker.person.jobTitle(),
  spokenLanguages: [faker.location.country(), faker.location.country()],
  score: faker.number.int({ min: 70, max: 100 }),
  person: {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    avatar: faker.image.avatar(),
  },
  address: {
    id: faker.string.uuid(),
    formatted: faker.location.streetAddress(true),
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
    timeZone: faker.location.timeZone(),
  },
  verification: {
    id: faker.string.uuid(),
    status: faker.helpers.arrayElement(Object.values(VerificationStatus)),
    backgroundCheckStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    i9VerificationStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    identityVerificationStatus: faker.helpers.arrayElement(
      Object.values(VerificationStatus),
    ),
    verifiedAt: faker.date.past(),
    backgroundVerifiedAt: faker.date.past(),
    i9VerifiedAt: faker.date.past(),
    identityVerifiedAt: faker.date.past(),
  },
  specialties: Array.from(
    { length: faker.number.int({ min: 1, max: 3 }) },
    () => ({
      id: faker.string.uuid(),
      name: faker.person.jobArea(),
      description: faker.lorem.sentence(),
    }),
  ),
  qualifications: [
    {
      id: faker.string.uuid(),
      name: faker.person.jobDescriptor() + " Degree",
      type: QualificationType.DEGREE,
      identifier: faker.string.alphanumeric(10).toUpperCase(),
      institution: faker.company.name() + " University",
      status: QualificationStatus.APPROVED,
      state: faker.location.state({ abbreviated: true }),
      country: "United States",
      startDate: faker.date.past({ years: 10 }),
      endDate: faker.date.past({ years: 6 }),
    },
    {
      id: faker.string.uuid(),
      name: "Medical License",
      type: QualificationType.LICENSE,
      identifier: faker.string.alphanumeric(8).toUpperCase(),
      institution: `Board of Medicine - ${faker.location.state()}`,
      status: faker.helpers.arrayElement(Object.values(QualificationStatus)),
      state: faker.location.state({ abbreviated: true }),
      country: "United States",
      startDate: faker.date.past({ years: 5 }),
      endDate: faker.date.future({ years: 2 }),
    },
    {
      id: faker.string.uuid(),
      name: `${faker.commerce.productAdjective()} Certification`,
      type: QualificationType.CERTIFICATE,
      identifier: faker.string.alphanumeric(12).toUpperCase(),
      institution: faker.company.name() + " Association",
      status: faker.helpers.arrayElement(Object.values(QualificationStatus)),
      state: null,
      country: "United States",
      startDate: faker.date.past({ years: 3 }),
      endDate: faker.date.future({ years: 1 }),
    },
  ],
  experiences: Array.from(
    { length: faker.number.int({ min: 1, max: 4 }) },
    () => ({
      id: faker.string.uuid(),
      role: faker.person.jobTitle(),
      company: faker.company.name(),
      description: faker.lorem.paragraph(),
      startDate: faker.date.past({ years: 8 }),
      endDate: faker.datatype.boolean(0.7)
        ? faker.date.past({ years: 1 })
        : null,
    }),
  ),
  reviews: [],
  shifts: [],
  schedules: [],
  calendar: undefined,
  settings: {
    id: faker.string.uuid(),
    openToWork: faker.datatype.boolean(),
    openToOnCall: faker.datatype.boolean(),
    openToRelocate: faker.datatype.boolean(),
    openToTravel: faker.datatype.boolean(),
  },
  createdAt: faker.date.past({ years: 2 }),
  updatedAt: faker.date.recent(),
  status: faker.helpers.arrayElement(Object.values(ProviderStatus)),
};

// 1. New Provider - Initial State
export const NewProvider: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.PENDING,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.PENDING,
        backgroundCheckStatus: VerificationStatus.PENDING,
        i9VerificationStatus: VerificationStatus.PENDING,
        identityVerificationStatus: VerificationStatus.PENDING,
        verifiedAt: null,
        backgroundVerifiedAt: null,
        i9VerifiedAt: null,
        identityVerifiedAt: null,
      },
      loading: false,
    },
  },
};

// 2. Partial Verification
export const PartialVerification: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.PENDING,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.PENDING,
        backgroundCheckStatus: VerificationStatus.PENDING,
        i9VerificationStatus: VerificationStatus.PENDING,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: null,
        backgroundVerifiedAt: null,
        i9VerifiedAt: null,
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};

// 3. Failed Verification
export const FailedVerification: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.PENDING,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.REJECTED,
        backgroundCheckStatus: VerificationStatus.REJECTED,
        i9VerificationStatus: VerificationStatus.PENDING,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: null,
        backgroundVerifiedAt: new Date("2024-03-16"),
        i9VerifiedAt: null,
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};

// 4. Complete Verification - Ready for Approval
export const ReadyForApproval: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.PENDING,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.APPROVED,
        backgroundCheckStatus: VerificationStatus.APPROVED,
        i9VerificationStatus: VerificationStatus.APPROVED,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: new Date("2024-03-17"),
        backgroundVerifiedAt: new Date("2024-03-16"),
        i9VerifiedAt: new Date("2024-03-17"),
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};

// 5. Active Provider
export const ActiveProvider: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.ACTIVE,
        score: 95,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.APPROVED,
        backgroundCheckStatus: VerificationStatus.APPROVED,
        i9VerificationStatus: VerificationStatus.APPROVED,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: new Date("2024-03-17"),
        backgroundVerifiedAt: new Date("2024-03-16"),
        i9VerifiedAt: new Date("2024-03-17"),
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};

// 6. Suspended Provider
export const SuspendedProvider: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.SUSPENDED,
        score: 24,
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.APPROVED,
        backgroundCheckStatus: VerificationStatus.APPROVED,
        i9VerificationStatus: VerificationStatus.APPROVED,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: new Date("2024-03-17"),
        backgroundVerifiedAt: new Date("2024-03-16"),
        i9VerifiedAt: new Date("2024-03-17"),
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};

export const Loading: Story = {
  args: {
    provider: {
      loading: true,
    },
    verification: {
      loading: true,
    },
  },
};

export const WithError: Story = {
  args: {
    provider: {
      data: undefined,
      error: new Error("Failed to load provider"),
      loading: false,
    },
    verification: {
      data: undefined,
      error: new Error("Failed to load verification"),
      loading: false,
    },
  },
};

// Complex Provider with Rich History
export const ComplexProvider: Story = {
  args: {
    provider: {
      data: {
        ...baseProvider,
        status: ProviderStatus.ACTIVE,
        score: 98,
        qualifications: [
          ...baseProvider.qualifications,
          {
            id: faker.string.uuid(),
            name: "Advanced Cardiac Life Support (ACLS)",
            type: QualificationType.CERTIFICATE,
            identifier: faker.string.alphanumeric(10).toUpperCase(),
            institution: "American Heart Association",
            status: QualificationStatus.APPROVED,
            state: null,
            country: "United States",
            startDate: faker.date.past({ years: 2 }),
            endDate: faker.date.future({ years: 1 }),
          },
        ],
      },
      loading: false,
    },
    verification: {
      data: {
        id: "verification_1",
        providerId: "provider_1",
        status: VerificationStatus.APPROVED,
        backgroundCheckStatus: VerificationStatus.APPROVED,
        i9VerificationStatus: VerificationStatus.APPROVED,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: new Date("2024-03-17"),
        backgroundVerifiedAt: new Date("2024-03-16"),
        i9VerifiedAt: new Date("2024-03-17"),
        identityVerifiedAt: new Date("2024-03-15"),
      },
      loading: false,
    },
  },
};
