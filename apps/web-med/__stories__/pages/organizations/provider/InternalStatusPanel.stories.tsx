import type { Meta, StoryObj } from "@storybook/react";

import { VerificationStatus } from "@/api"; // Import for status variations
import InternalStatusPanel from "@/www/organizations/provider/InternalStatusPanel";

import { baseProvider } from "./data";

// Simple inline mock mutation helper
const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
  },
  mutateAsync: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate Async]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return { id: "mock-op-id" } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {
    console.log("[Storybook Mock Reset]");
  },
});

const meta = {
  title: "Pages/Organizations/Provider/Components/InternalStatusPanel",
  component: InternalStatusPanel,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "provider", "status", "internal"],
} satisfies Meta<typeof InternalStatusPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define default verification with required fields
const defaultVerificationData = {
  id: baseProvider.verification?.id ?? "mock-verification-id",
  providerId: baseProvider.id,
  status: baseProvider.verification?.status ?? VerificationStatus.PENDING,
  verifiedAt: baseProvider.verification?.verifiedAt ?? null,
  i9VerifiedAt: baseProvider.verification?.i9VerifiedAt ?? null,
  identityVerifiedAt: baseProvider.verification?.identityVerifiedAt ?? null,
  backgroundVerifiedAt: baseProvider.verification?.backgroundVerifiedAt ?? null,
  backgroundCheckStatus:
    baseProvider.verification?.backgroundCheckStatus ?? null,
  i9VerificationStatus: baseProvider.verification?.i9VerificationStatus ?? null,
  identityVerificationStatus:
    baseProvider.verification?.identityVerificationStatus ?? null,
};

// --- Default Story (Often Pending Verification) ---

export const Default: Story = {
  args: {
    provider: {
      data: baseProvider,
      loading: false,
      error: undefined,
    },
    verification: {
      data: {
        ...defaultVerificationData,
        status: VerificationStatus.PENDING,
      },
      loading: false,
      error: undefined,
    },
    verify: createMockMutation(),
    update: createMockMutation(),
  } as any, // Cast args
};

// --- Loading State ---

export const LoadingProvider: Story = {
  args: {
    provider: { data: undefined, loading: true, error: undefined },
    verification: {
      data: defaultVerificationData,
      loading: false,
      error: undefined,
    },
    verify: createMockMutation(),
    update: createMockMutation(),
  } as any,
  name: "Loading Provider Data",
};

export const LoadingVerification: Story = {
  args: {
    provider: {
      data: baseProvider,
      loading: false,
      error: undefined,
    },
    verification: { data: undefined, loading: true, error: undefined },
    verify: createMockMutation(),
    update: createMockMutation(),
  } as any,
  name: "Loading Verification Data",
};

// --- Error State ---

export const ErrorProvider: Story = {
  args: {
    provider: {
      data: undefined,
      loading: false,
      error: { message: "Failed to load provider" },
    },
    verification: {
      data: defaultVerificationData,
      loading: false,
      error: undefined,
    },
    verify: { ...createMockMutation(), isError: true },
    update: { ...createMockMutation(), isError: true },
  } as any,
  name: "Error Loading Provider",
};

// --- Verified State ---

export const Verified: Story = {
  args: {
    provider: {
      data: baseProvider,
      loading: false,
      error: undefined,
    },
    verification: {
      data: {
        ...defaultVerificationData,
        status: VerificationStatus.APPROVED,
        backgroundCheckStatus: VerificationStatus.APPROVED,
        i9VerificationStatus: VerificationStatus.APPROVED,
        identityVerificationStatus: VerificationStatus.APPROVED,
        verifiedAt: new Date(),
      },
      loading: false,
      error: undefined,
    },
    verify: createMockMutation(),
    update: createMockMutation(),
  } as any,
};

// --- Rejected State ---

export const Rejected: Story = {
  args: {
    provider: {
      data: baseProvider,
      loading: false,
      error: undefined,
    },
    verification: {
      data: {
        ...defaultVerificationData,
        status: VerificationStatus.REJECTED,
        identityVerificationStatus: VerificationStatus.REJECTED, // Example
      },
      loading: false,
      error: undefined,
    },
    verify: createMockMutation(),
    update: createMockMutation(),
  } as any,
};
