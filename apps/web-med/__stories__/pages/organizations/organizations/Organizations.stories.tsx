import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import Organizations from "@/www/organizations/organizations/Organizations";

const meta = {
  title: "Pages/Organizations/Organizations/page",
  component: Organizations,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/organizations",
      },
    },
  },
} satisfies Meta<typeof Organizations>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockOrganizations: RouterOutputs["organizations"]["getMany"] = {
  items: [
    {
      id: "org_1",
      name: "City Healthcare Network",
      avatar: "/placeholder.svg",
      type: "CLIENT",
      status: "ACTIVE",
      class: "PRIVATE",
      mode: "INDEPENDENT",
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date("2024-01-01"),
      deletedAt: null,
      approvedAt: new Date("2024-01-01"),
      rejectedAt: null,
      phone: "+****************",
      email: "<EMAIL>",
      members: [],
      manager: null,
      parent: null,
      address: null,
    },
    {
      id: "org_2",
      name: "Westside Medical Group",
      avatar: "/placeholder.svg",
      type: "CLIENT",
      status: "ACTIVE",
      class: "GOVERNMENT",
      mode: "INDEPENDENT",
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date("2024-01-01"),
      deletedAt: null,
      approvedAt: new Date("2024-01-01"),
      rejectedAt: null,
      phone: "+****************",
      email: "<EMAIL>",
      members: [],
      manager: null,
      parent: null,
      address: null,
    },
  ],
  total: 2,
};

export const Default: Story = {
  args: {
    organizations: mockOrganizations,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: {
      message: "Failed to load organizations",
    } as Error,
  },
};

export const Empty: Story = {
  args: {
    organizations: {
      items: [],
      total: 0,
    },
  },
};
