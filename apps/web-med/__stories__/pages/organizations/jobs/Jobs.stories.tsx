/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import type { Meta, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  DepartmentType,
  FacilityType,
  JobPostStatus,
  JobPostType,
  PaymentType,
  PersonRole,
} from "@/api";
import Jobs from "@/www/organizations/jobs/Jobs";

const meta = {
  title: "Pages/Organizations/Jobs/page",
  component: Jobs,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/jobs",
      },
    },
  },
} satisfies Meta<typeof Jobs>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on the API
type JobsQuery = RouterOutputs["jobs"]["getMany"];
type JobStructure = NonNullable<JobsQuery["items"]>[number];

// Define types for related entities
interface OrganizationStructure {
  id: string;
  name: string;
  avatar: string | null;
}

interface AddressStructure {
  formatted: string;
  timeZone: string;
  latitude: number;
  longitude: number;
}

interface LocationStructure {
  id: string;
  name: string;
  type: FacilityType;
  address: AddressStructure;
}

interface PersonStructure {
  id: string;
  role: PersonRole;
  title: string | null;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  avatar: string | null;
}

interface ContactStructure {
  id: string;
  role: string;
  person: PersonStructure;
}

interface DepartmentStructure {
  id: string;
  name: string;
  description: string | null;
  type: DepartmentType;
  contacts: ContactStructure[];
}

interface SpecialtyStructure {
  id: string;
  name: string;
}

// Basic creator functions
const createOrganization = (name?: string): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: name || faker.company.name(),
  avatar: null,
});

const createAddress = (
  options: {
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): AddressStructure => {
  const city = options.city || faker.location.city();
  const state = options.state || faker.location.state();
  return {
    formatted: `${faker.location.streetAddress()}, ${city}, ${state}`,
    timeZone: options.timeZone || "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  };
};

const createLocation = (
  options: {
    name?: string;
    type?: FacilityType;
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name || faker.location.city(),
  type: options.type || FacilityType.HOSPITAL,
  address: createAddress({
    city: options.city,
    state: options.state,
    timeZone: options.timeZone,
  }),
});

const createPerson = (): PersonStructure => ({
  id: faker.string.uuid(),
  role: PersonRole.CLIENT,
  title: "Department Head",
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  phone: faker.phone.number(),
  email: faker.internet.email(),
  avatar: null,
});

const createContact = (): ContactStructure => ({
  id: faker.string.uuid(),
  role: "Manager",
  person: createPerson(),
});

const createDepartment = (name?: string): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: name || faker.commerce.department(),
  description: faker.lorem.sentence(),
  type: DepartmentType.DEPARTMENT,
  contacts: [createContact()],
});

const createSpecialty = (name?: string): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: name || faker.lorem.word(),
});

// Create a basic job with only the required fields from the API selector
const createJob = (
  options: {
    status?: JobPostStatus;
    type?: JobPostType;
    summary?: string;
    role?: string;
    scope?: string;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    paymentAmount?: number;
    publishedAt?: Date | null;
    filledAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    expiredAt?: Date | null;
    expiresAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
  } = {},
) => {
  const status = options.status || JobPostStatus.PUBLISHED;

  // Set dates based on status
  const publishedAt: Date | null =
    options.publishedAt !== undefined
      ? options.publishedAt
      : status === JobPostStatus.DRAFT
        ? null
        : faker.date.recent();
  let filledAt: Date | null =
    options.filledAt !== undefined ? options.filledAt : null;
  let completedAt: Date | null =
    options.completedAt !== undefined ? options.completedAt : null;
  let cancelledAt: Date | null =
    options.cancelledAt !== undefined ? options.cancelledAt : null;
  let expiredAt: Date | null =
    options.expiredAt !== undefined ? options.expiredAt : null;

  if (status === JobPostStatus.FILLED && filledAt === null) {
    filledAt = faker.date.recent();
  }

  if (status === JobPostStatus.COMPLETED && completedAt === null) {
    completedAt = faker.date.recent();
    filledAt = filledAt || faker.date.past({ refDate: completedAt });
  }

  if (status === JobPostStatus.CANCELLED && cancelledAt === null) {
    cancelledAt = faker.date.recent();
  }

  if (status === JobPostStatus.EXPIRED && expiredAt === null) {
    expiredAt = faker.date.recent();
  }

  return {
    id: faker.string.uuid(),
    status,
    mode: "INDEPENDENT",
    role: options.role || "Physician",
    type: options.type || JobPostType.PER_DIEM,
    summary: options.summary || "Emergency Room Physician",
    scope: options.scope || "Emergency Medicine",
    priority: "MEDIUM",
    paymentType: PaymentType.HOURLY,
    paymentAmount:
      options.paymentAmount || faker.number.int({ min: 100, max: 300 }),
    paymentRate: 1,
    nightRate: 1.25,
    overtimeRate: 1.5,
    holidayRate: 2,
    bonusRate: 1,
    billingType: PaymentType.HOURLY,
    billingRate: 1.15,
    isBillable: true,
    publishedAt,
    filledAt,
    completedAt,
    cancelledAt,
    expiredAt,
    expiresAt: options.expiresAt || faker.date.future(),
    createdAt: options.createdAt || faker.date.recent(),
    updatedAt: options.updatedAt || faker.date.recent(),
    organization: options.organization || createOrganization(),
    location: options.location || createLocation(),
    department: options.department || createDepartment(),
    specialties: options.specialties || [],
    schedule: undefined,
    thread: undefined,
    offers: [],
    applications: [],
    position: undefined,
    contacts: [],
    actions: [],
    context: null,
    analytics: null,
  } satisfies JobStructure;
};

// Create a set of organizations
const organizations = {
  cityGeneral: createOrganization("City General Hospital"),
  memorialHealth: createOrganization("Memorial Health System"),
  pediatricCenter: createOrganization("Children's Pediatric Center"),
  urgentCare: createOrganization("Urgent Care Clinic"),
  ruralHealth: createOrganization("Rural Health Network"),
};

// Create a set of locations
const locations = {
  downtown: createLocation({
    name: "Downtown Medical Center",
    city: "New York",
    state: "NY",
    timeZone: "America/New_York",
  }),
  westSide: createLocation({
    name: "West Side Hospital",
    city: "Chicago",
    state: "IL",
    timeZone: "America/Chicago",
  }),
  southBay: createLocation({
    name: "South Bay Medical",
    city: "San Francisco",
    state: "CA",
    timeZone: "America/Los_Angeles",
  }),
  eastEnd: createLocation({
    name: "East End Clinic",
    city: "Boston",
    state: "MA",
    timeZone: "America/New_York",
  }),
  ruralClinic: createLocation({
    name: "Rural Health Clinic",
    city: "Bozeman",
    state: "MT",
    timeZone: "America/Denver",
    type: FacilityType.CLINIC,
  }),
};

// Create a set of departments
const departments = {
  emergency: createDepartment("Emergency"),
  pediatrics: createDepartment("Pediatrics"),
  surgery: createDepartment("Surgery"),
  cardiology: createDepartment("Cardiology"),
  neurology: createDepartment("Neurology"),
};

// Create a set of specialties
const specialties = {
  emergencyMedicine: createSpecialty("Emergency Medicine"),
  pediatrics: createSpecialty("Pediatrics"),
  cardiology: createSpecialty("Cardiology"),
  neurology: createSpecialty("Neurology"),
  surgery: createSpecialty("Surgery"),
  anesthesiology: createSpecialty("Anesthesiology"),
  radiology: createSpecialty("Radiology"),
};

// Create jobs with different statuses
const publishedJob = createJob({
  status: JobPostStatus.PUBLISHED,
  summary: "Emergency Room Physician",
  role: "Physician",
  scope: "Emergency Medicine",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 200,
  publishedAt: faker.date.recent(),
  expiresAt: faker.date.future(),
});

const filledJob = createJob({
  status: JobPostStatus.FILLED,
  summary: "Pediatric Nurse",
  role: "Nurse",
  scope: "Pediatric Care",
  organization: organizations.pediatricCenter,
  location: locations.westSide,
  department: departments.pediatrics,
  specialties: [specialties.pediatrics],
  paymentAmount: 150,
  publishedAt: faker.date.past(),
  filledAt: faker.date.recent(),
});

const completedJob = createJob({
  status: JobPostStatus.COMPLETED,
  summary: "Cardiologist - Temporary",
  role: "Specialist",
  scope: "Cardiology",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.cardiology,
  specialties: [specialties.cardiology],
  paymentAmount: 300,
  publishedAt: faker.date.past(),
  filledAt: faker.date.past(),
  completedAt: faker.date.recent(),
});

const cancelledJob = createJob({
  status: JobPostStatus.CANCELLED,
  summary: "Neurologist - Consultant",
  role: "Consultant",
  scope: "Neurology",
  organization: organizations.urgentCare,
  location: locations.eastEnd,
  department: departments.neurology,
  specialties: [specialties.neurology],
  paymentAmount: 250,
  publishedAt: faker.date.past(),
  cancelledAt: faker.date.recent(),
});

const expiredJob = createJob({
  status: JobPostStatus.EXPIRED,
  summary: "Rural Health Physician",
  role: "Physician",
  scope: "General Practice",
  organization: organizations.ruralHealth,
  location: locations.ruralClinic,
  department: departments.emergency,
  specialties: [specialties.emergencyMedicine],
  paymentAmount: 180,
  publishedAt: faker.date.past(),
  expiredAt: faker.date.recent(),
});

const draftJob = createJob({
  status: JobPostStatus.DRAFT,
  summary: "Surgical Assistant",
  role: "Assistant",
  scope: "Surgery",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.surgery,
  specialties: [specialties.surgery, specialties.anesthesiology],
  paymentAmount: 120,
  publishedAt: undefined,
});

// Create a set of jobs with different payment amounts
const highPayJob = createJob({
  summary: "Specialized Radiologist",
  role: "Specialist",
  scope: "Radiology",
  organization: organizations.memorialHealth,
  location: locations.southBay,
  department: departments.surgery,
  specialties: [specialties.radiology],
  paymentAmount: 350,
});

const lowPayJob = createJob({
  summary: "Medical Assistant",
  role: "Assistant",
  scope: "General Practice",
  organization: organizations.urgentCare,
  location: locations.eastEnd,
  department: departments.emergency,
  specialties: [],
  paymentAmount: 80,
});

// Create a set of jobs with different types
const permanentJob = createJob({
  type: JobPostType.PERMANENT,
  summary: "Chief of Surgery",
  role: "Chief",
  scope: "Surgery Department",
  organization: organizations.cityGeneral,
  location: locations.downtown,
  department: departments.surgery,
  specialties: [specialties.surgery],
  paymentAmount: 280,
});

const temporaryJob = createJob({
  type: JobPostType.TEMPORARY,
  summary: "Temporary Nurse Practitioner",
  role: "Nurse Practitioner",
  scope: "General Care",
  organization: organizations.memorialHealth,
  location: locations.westSide,
  department: departments.emergency,
  specialties: [],
  paymentAmount: 160,
});

const perDiemJob = createJob({
  type: JobPostType.PER_DIEM,
  summary: "Per Diem Anesthesiologist",
  role: "Anesthesiologist",
  scope: "Surgery Support",
  organization: organizations.pediatricCenter,
  location: locations.southBay,
  department: departments.surgery,
  specialties: [specialties.anesthesiology],
  paymentAmount: 220,
});

// Create the mock data objects with different variations
const createMockJobs = (): JobsQuery => ({
  items: [
    publishedJob,
    filledJob,
    completedJob,
    cancelledJob,
    expiredJob,
    draftJob,
    highPayJob,
    lowPayJob,
    permanentJob,
    temporaryJob,
    perDiemJob,
  ],
  total: 11,
});

// Basic story
export const Default: Story = {
  args: {
    jobs: {
      data: createMockJobs(),
    },
    applications: {
      data: { items: [], total: 0 },
    },
    offers: {
      data: { items: [], total: 0 },
    },
  },
};

// Story with only published jobs
export const PublishedJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [
          publishedJob,
          highPayJob,
          lowPayJob,
          permanentJob,
          temporaryJob,
          perDiemJob,
        ],
        total: 6,
      },
    },
    applications: {
      data: { items: [], total: 0 },
    },
    offers: {
      data: { items: [], total: 0 },
    },
  },
};

// Story with only filled and completed jobs
export const FilledAndCompletedJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [filledJob, completedJob],
        total: 2,
      },
    },
    applications: {
      data: { items: [], total: 0 },
    },
    offers: {
      data: { items: [], total: 0 },
    },
  },
};

// Story with only cancelled and expired jobs
export const CancelledAndExpiredJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [cancelledJob, expiredJob],
        total: 2,
      },
    },
    applications: {
      data: { items: [], total: 0 },
    },
    offers: {
      data: { items: [], total: 0 },
    },
  },
};

// Story with only draft jobs
export const DraftJobs: Story = {
  args: {
    jobs: {
      data: {
        items: [draftJob],
        total: 1,
      },
    },
    applications: {
      data: { items: [], total: 0 },
    },
    offers: {
      data: { items: [], total: 0 },
    },
  },
};

export const Loading: Story = {
  args: {
    jobs: {
      loading: true,
    },
    applications: {
      loading: true,
    },
    offers: {
      loading: true,
    },
  },
};

export const Empty: Story = {
  args: {
    jobs: {
      data: {
        items: [],
        total: 0,
      },
    },
    applications: {
      data: {
        items: [],
        total: 0,
      },
    },
    offers: {
      data: {
        items: [],
        total: 0,
      },
    },
  },
};
