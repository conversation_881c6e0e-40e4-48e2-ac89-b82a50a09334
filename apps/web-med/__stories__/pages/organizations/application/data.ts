import { faker } from "@faker-js/faker";

import type {
  ContractStatus,
  ContractType,
  PersonRole,
  RouterOutputs,
} from "@/api";

import {
  ApplicationStatus,
  JobPostStatus,
  JobPostType,
  ProviderStatus,
} from "@/api";

// Set a seed for reproducible results
faker.seed(456);

// Define types based on API
export type ApplicationGetQuery = RouterOutputs["applications"]["get"];
export type OrganizationStructure = NonNullable<
  ApplicationGetQuery["organization"]
>;
export type ProviderStructure = NonNullable<ApplicationGetQuery["provider"]>;
export type JobStructure = NonNullable<ApplicationGetQuery["job"]>;
export type ThreadStructure = NonNullable<ApplicationGetQuery["thread"]>;
export type MessageStructure = NonNullable<ThreadStructure["messages"]>[number];
export type PersonStructure = NonNullable<ProviderStructure["person"]>;
export type PositionStructure = NonNullable<ApplicationGetQuery["position"]>;
export type ContractStructure = NonNullable<ApplicationGetQuery["contract"]>;

// Helper functions to create mock data
export const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

export const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    title?: string | null;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  role: options.role ?? "USER",
  title: options.title ?? faker.person.jobTitle(),
  avatar: options.avatar ?? faker.image.avatar(),
});

export const createProvider = (
  options: {
    title?: string;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? ProviderStatus.ACTIVE,
  person: options.person ?? createPerson(),
});

export const createJob = (
  options: {
    summary?: string;
    type?: JobPostType;
    role?: string;
    scope?: string;
    status?: JobPostStatus;
    paymentType?: string;
    paymentAmount?: number;
  } = {},
): JobStructure => ({
  id: faker.string.uuid(),
  summary: options.summary ?? faker.company.catchPhrase(),
  type: options.type ?? JobPostType.PER_DIEM,
  role: options.role ?? faker.person.jobTitle(),
  scope: options.scope ?? faker.lorem.paragraph(),
  status: options.status ?? JobPostStatus.PUBLISHED,
  paymentType: options.paymentType ?? "HOURLY",
  paymentAmount:
    options.paymentAmount ?? faker.number.int({ min: 50, max: 200 }),
  location: {
    name: faker.company.name(),
    address: {
      formatted: faker.location.streetAddress(),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
    },
  },
});

export const createMessage = (
  options: {
    content?: string;
    author?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  } = {},
): MessageStructure => ({
  id: faker.string.uuid(),
  content: options.content ?? faker.lorem.paragraph(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  author: options.author ?? {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

export const createThread = (
  options: {
    messages?: MessageStructure[];
  } = {},
): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: options.messages ?? [
    createMessage(),
    createMessage(),
    createMessage(),
  ],
});

export const createPosition = (
  options: {
    role?: string;
    summary?: string;
    type?: string;
    status?: string;
    paymentType?: string;
    paymentAmount?: number;
  } = {},
): PositionStructure => ({
  id: faker.string.uuid(),
  role: options.role ?? faker.person.jobTitle(),
  summary: options.summary ?? faker.company.catchPhrase(),
  type: options.type ?? "FULL_TIME",
  status: options.status ?? "ACTIVE",
  paymentType: options.paymentType ?? "HOURLY",
  paymentAmount:
    options.paymentAmount ?? faker.number.int({ min: 50, max: 200 }),
});

export const createContract = (
  options: {
    title?: string;
    status?: ContractStatus;
    type?: ContractType;
    expiresAt?: Date;
    organizationId?: string;
    providerId?: string;
    positionId?: string;
  } = {},
): ContractStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? `Contract for ${faker.company.name()}`,
  status: options.status ?? "DRAFT",
  type: options.type ?? "EMPLOYMENT",
  expiresAt: options.expiresAt ?? faker.date.future(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  organizationId: options.organizationId ?? faker.string.uuid(),
  providerId: options.providerId ?? faker.string.uuid(),
  positionId: options.positionId ?? faker.string.uuid(),
});

export const createApplication = (
  options: {
    id?: string;
    status?: ApplicationStatus;
    notes?: string;
    provider?: ProviderStructure;
    organization?: OrganizationStructure;
    job?: JobStructure;
    thread?: ThreadStructure;
    position?: PositionStructure;
    contract?: ContractStructure;
    contractId?: string;
    threadId?: string;
  } = {},
): ApplicationGetQuery => {
  const provider = options.provider ?? createProvider();
  const organization = options.organization ?? createOrganization();
  const position = options.position ?? createPosition();
  const contract =
    options.contract ??
    createContract({
      organizationId: organization.id,
      providerId: provider.id,
      positionId: position.id,
    });

  return {
    id: options.id ?? faker.string.uuid(),
    status: options.status ?? ApplicationStatus.PENDING,
    notes: options.notes ?? faker.lorem.paragraph(),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    provider,
    organization,
    job: options.job ?? createJob(),
    thread: options.thread ?? createThread(),
    position: options.position,
    contract: options.contract,
    contractId: options.contractId ?? options.contract?.id ?? null,
    threadId: options.threadId ?? options.thread?.id ?? faker.string.uuid(),
    organizationId: organization.id,
    providerId: provider.id,
    jobId: options.job?.id ?? faker.string.uuid(),
    positionId: options.position?.id ?? null,
  };
};

// Create specific application scenarios
export const applications = {
  pending: createApplication({
    status: ApplicationStatus.PENDING,
    notes:
      "I am very interested in this position and believe my experience in emergency medicine makes me a strong candidate.",
    provider: createProvider({
      title: "Emergency Physician",
      person: createPerson({
        firstName: "Robert",
        lastName: "Chen",
        title: "MD",
      }),
    }),
    organization: createOrganization({
      name: "City General Hospital",
    }),
    job: createJob({
      summary: "Emergency Room Physician",
      role: "Physician",
      paymentType: "HOURLY",
      paymentAmount: 150,
    }),
  }),

  accepted: createApplication({
    status: ApplicationStatus.ACCEPTED,
    notes:
      "I have 10+ years of experience in cardiology and am board certified in interventional cardiology.",
    provider: createProvider({
      title: "Cardiologist",
      person: createPerson({
        firstName: "Emily",
        lastName: "Rodriguez",
        title: "MD",
      }),
    }),
    organization: createOrganization({
      name: "Memorial Health System",
    }),
    job: createJob({
      summary: "Senior Cardiologist",
      role: "Specialist",
      paymentType: "SALARY",
      paymentAmount: 250000,
    }),
    contract: createContract({
      title: "Employment Contract - Cardiology Department",
      status: "ACTIVE",
      type: "EMPLOYMENT",
    }),
    position: createPosition({
      role: "Senior Cardiologist",
      summary: "Lead cardiology specialist",
      paymentType: "SALARY",
      paymentAmount: 250000,
    }),
  }),

  withPosition: createApplication({
    status: ApplicationStatus.ACCEPTED,
    notes:
      "I'm interested in joining your pediatric team and have specialized in neonatal care for the past 5 years.",
    provider: createProvider({
      title: "Pediatrician",
      person: createPerson({
        firstName: "David",
        lastName: "Kim",
        title: "MD",
      }),
    }),
    organization: createOrganization({
      name: "Children's Medical Center",
    }),
    job: createJob({
      summary: "Pediatric Specialist",
      role: "Pediatrician",
      paymentType: "HOURLY",
      paymentAmount: 120,
    }),
    position: createPosition({
      role: "Pediatric Specialist",
      summary: "Neonatal care specialist",
      paymentType: "HOURLY",
      paymentAmount: 125,
    }),
  }),

  rejected: createApplication({
    status: ApplicationStatus.REJECTED,
    notes:
      "I have 3 years of experience in neurology and am interested in joining your team.",
    provider: createProvider({
      title: "Neurologist",
      person: createPerson({
        firstName: "Jessica",
        lastName: "Taylor",
        title: "MD",
      }),
    }),
    organization: createOrganization({
      name: "University Medical Center",
    }),
    job: createJob({
      summary: "Senior Neurologist",
      role: "Specialist",
      paymentType: "HOURLY",
      paymentAmount: 140,
    }),
  }),
};
