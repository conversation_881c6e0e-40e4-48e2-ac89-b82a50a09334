import type { Meta, StoryObj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import ApplicationView from "@/www/organizations/application/Application";

import { applications } from "./data";

const meta = {
  title: "Pages/Organizations/Application/Details",
  component: ApplicationView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/organizations/application/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.contracts.get.query(({ input }) => {
          // Return contract data if the application has a contract
          const id = input.id;

          if (applications.accepted.contractId === id) {
            return applications.accepted.contract;
          }

          // Default fallback to null if no contract found
          return null;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the application with the matching thread ID
          const application = Object.values(applications).find(
            (app) => app.thread?.id === id,
          );

          if (application?.thread) {
            return {
              ...application.thread,
              author: {
                id: "author-id",
                firstName: "System",
                lastName: "User",
                avatar: null,
              },
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof ApplicationView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Default: Story = {
  args: {
    loading: false,
    application: {
      data: applications.pending,
      loading: false,
      error: null,
    },
  },
};

export const WithContract: Story = {
  args: {
    loading: false,
    application: {
      data: applications.accepted,
      loading: false,
      error: null,
    },
  },
};

export const WithPosition: Story = {
  args: {
    loading: false,
    application: {
      data: applications.withPosition,
      loading: false,
      error: null,
    },
  },
};

export const Rejected: Story = {
  args: {
    loading: false,
    application: {
      data: applications.rejected,
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    application: {
      data: applications.pending,
      loading: true,
      error: null,
    },
  },
};
