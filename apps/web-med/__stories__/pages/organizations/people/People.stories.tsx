import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { PeopleQueryResult } from "@/www/organizations/people/People";

import { PersonRole } from "@/api";
import People from "@/www/organizations/people/People";

const meta = {
  title: "Pages/Organizations/People/page",
  component: People,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/people",
      },
    },
  },
} satisfies Meta<typeof People>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for people
const mockPeople: PeopleQueryResult = {
  items: Array.from({ length: 10 }, () => ({
    id: faker.string.uuid(),
    role: faker.helpers.arrayElement([
      PersonRole.ADMIN,
      PersonRole.BILLING,
      PersonRole.INTERNAL,
      PersonRole.CLIENT,
      PersonRole.NONE,
    ]),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    organization: faker.helpers.arrayElement([
      {
        id: faker.string.uuid(),
        name: faker.company.name(),
        avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
      },
      null,
    ]),
    address: faker.helpers.arrayElement([
      {
        formatted: faker.location.streetAddress(),
        timeZone: faker.location.timeZone(),
      },
    ]),
  })),
  total: 10,
};

export const Default: Story = {
  args: {
    people: mockPeople,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: {
      message: "Failed to load people data",
    },
  },
};
