import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { PayoutStatus, PersonRole, ProviderStatus, ShiftStatus } from "@/api";
import Payout from "@/www/organizations/payout/Payout";

const meta = {
  title: "Pages/Organizations/Payout/page",
  component: Payout,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/billing/payouts/abc-123",
      },
    },
  },
} satisfies Meta<typeof Payout>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPayout: RouterOutputs["billing"]["payouts"]["get"] = {
  id: faker.string.uuid(),
  amount: faker.number.int({ min: 500, max: 5000 }),
  status: faker.helpers.arrayElement([
    PayoutStatus.COMPLETED,
    PayoutStatus.PENDING,
    PayoutStatus.FAILED,
  ]),
  providerId: faker.string.uuid(),
  overtimeAmount: faker.number.int({ min: 50, max: 500 }),
  holidayAmount: faker.number.int({ min: 50, max: 500 }),
  nightAmount: faker.number.int({ min: 50, max: 500 }),
  paidAt: faker.helpers.arrayElement([faker.date.recent(), null]),
  provider: {
    id: faker.string.uuid(),
    status: ProviderStatus.ACTIVE,
    title: null,
    person: {
      id: faker.string.uuid(),
      role: PersonRole.PROVIDER,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      avatar: null,
    },
  },
  shifts: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    status: ShiftStatus.COMPLETED,
    summary: faker.helpers.arrayElement([
      "Morning Shift",
      "Evening Shift",
      "Night Shift",
    ]),
    scope: faker.helpers.arrayElement([
      "Primary Care",
      "Emergency",
      "Specialized Care",
    ]),
    startDate: faker.date.recent(),
    endDate: faker.date.soon(),
    hours: faker.number.float({ min: 4, max: 12, precision: 0.1 }),
    paymentRate: faker.number.float({ min: 50, max: 200, precision: 0.01 }),
    paymentAmount: faker.number.float({ min: 200, max: 2000, precision: 0.01 }),
    overtimeAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    holidayAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    nightAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    paymentTotal: faker.number.float({ min: 500, max: 3000, precision: 0.01 }),
    provider: {
      id: faker.string.uuid(),
      status: ProviderStatus.ACTIVE,
      title: null,
      person: {
        id: faker.string.uuid(),
        role: PersonRole.PROVIDER,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: null,
      },
    },
  })),
};

export const Default: Story = {
  args: {
    payout: mockPayout,
  },
};

export const Loading: Story = {
  args: {
    payout: undefined,
  },
};

export const WithError: Story = {
  args: {
    error: new Error("Failed to load payout"),
  },
};
