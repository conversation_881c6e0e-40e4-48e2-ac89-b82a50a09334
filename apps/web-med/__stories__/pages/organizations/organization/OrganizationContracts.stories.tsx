import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationContracts from "@/www/organizations/organization/OrganizationContracts";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationContracts",
  component: OrganizationContracts,
  parameters: {
    layout: "centered",
    nextjs: {
      // Add router context if the component uses next/link or similar
      appDirectory: true,
      router: {
        path: `/app/organizations/${defaultOrganizationData.organization.data.organization.id ?? "mock-org-id"}/contracts`,
      },
    },
  },
  tags: ["component", "organization", "table", "contracts"],
} satisfies Meta<typeof OrganizationContracts>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs = {
  contracts: defaultOrganizationData.contracts,
  organization: defaultOrganizationData.organization.data.organization,
};

export const Default: Story = {
  args: defaultArgs,
};

// --- Loading Story ---

const loadingArgs = {
  contracts: loadingOrganizationData.contracts,
  organization: defaultOrganizationData.organization.data.organization,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationContractsSkeleton />
};

// --- Error Story ---

const errorArgs = {
  contracts: errorOrganizationData.contracts,
  organization: defaultOrganizationData.organization.data.organization,
};

export const WithError: Story = {
  args: errorArgs,
};

// --- Empty State Story ---

const emptyArgs = {
  contracts: emptyOrganizationData.contracts, // Use empty contracts data
  organization: defaultOrganizationData.organization.data.organization,
};

export const Empty: Story = {
  args: emptyArgs,
};

// --- One Contract Story ---

const oneContractArgs = {
  ...defaultArgs,
  contracts: {
    ...defaultArgs.contracts,
    data: {
      total: 1,
      items: defaultArgs.contracts.data.items.slice(0, 1) ?? [],
    },
  },
};

export const OneContract: Story = {
  args: oneContractArgs,
};
