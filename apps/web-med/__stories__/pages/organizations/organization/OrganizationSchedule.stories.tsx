import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationSchedule from "@/www/organizations/organization/OrganizationSchedule";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationSchedule",
  component: OrganizationSchedule,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "organization", "schedule"],
} satisfies Meta<typeof OrganizationSchedule>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs = {
  loading: defaultOrganizationData.organization.loading,
  organization: defaultOrganizationData.organization.data.organization,
  // Extract mutation props
  createTimeBlock: defaultOrganizationData.createTimeBlock,
  updateTimeBlock: defaultOrganizationData.updateTimeBlock,
  deleteTimeBlock: defaultOrganizationData.deleteTimeBlock,
};

export const Default: Story = {
  args: defaultArgs as any, // Cast for mock mutations
};

// --- Loading Story ---

const loadingArgs = {
  loading: true,
  organization: undefined,
  createTimeBlock: loadingOrganizationData.createTimeBlock as any,
  updateTimeBlock: loadingOrganizationData.updateTimeBlock as any,
  deleteTimeBlock: loadingOrganizationData.deleteTimeBlock as any,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationScheduleSkeleton />
};

// --- Error Story ---
// Similar to OrganizationInfo, the parent handles the main error display.
// We show the component with no data and error-state mutations.
const errorArgs = {
  loading: false,
  organization: undefined, // No data on parent error
  createTimeBlock: errorOrganizationData.createTimeBlock as any,
  updateTimeBlock: errorOrganizationData.updateTimeBlock as any,
  deleteTimeBlock: errorOrganizationData.deleteTimeBlock as any,
};

export const WithError: Story = {
  args: errorArgs,
};

// --- Empty Schedule Story ---

const emptyScheduleArgs = {
  loading: false,
  // Use organization data from the empty state which specifically clears schedule blocks
  organization: emptyOrganizationData.organization.data.organization,
  createTimeBlock: emptyOrganizationData.createTimeBlock,
  updateTimeBlock: emptyOrganizationData.updateTimeBlock,
  deleteTimeBlock: emptyOrganizationData.deleteTimeBlock,
};

export const EmptySchedule: Story = {
  args: emptyScheduleArgs as any,
};
