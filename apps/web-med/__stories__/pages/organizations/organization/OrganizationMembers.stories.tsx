import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationMembers from "@/www/organizations/organization/OrganizationMembers";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationMembers",
  component: OrganizationMembers,
  parameters: {
    layout: "centered",
    nextjs: {
      // Add router context if the component uses next/navigation
      appDirectory: true,
      router: {
        // Provide a base path, adjust if specific paths are needed for actions
        path: `/app/organizations/${defaultOrganizationData.organization.data.organization.id ?? "mock-org-id"}/members`,
        // You might need to mock searchParams if the component uses them
        // searchParams: new URLSearchParams({ page: '1' }),
      },
    },
  },
  tags: ["component", "organization", "table"],
} satisfies Meta<typeof OrganizationMembers>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs = {
  loading: defaultOrganizationData.members.loading,
  error: defaultOrganizationData.members.error,
  organization: defaultOrganizationData.organization.data.organization,
  members: defaultOrganizationData.members.data,
};

export const Default: Story = {
  args: defaultArgs,
};

// --- Loading Story ---

const loadingArgs = {
  loading: true,
  error: undefined,
  organization: defaultOrganizationData.organization.data.organization, // Org info might still be available
  members: undefined,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationMembersSkeleton />
};

// --- Error Story ---

const errorArgs = {
  loading: false,
  error: errorOrganizationData.members.error,
  organization: defaultOrganizationData.organization.data.organization, // Org info might still be available
  members: errorOrganizationData.members.data, // Usually empty on error
};

export const WithError: Story = {
  args: errorArgs,
};

// --- Empty State Story ---

const emptyArgs = {
  loading: false,
  error: undefined,
  organization: defaultOrganizationData.organization.data.organization,
  members: emptyOrganizationData.members.data, // Use empty members data
};

export const Empty: Story = {
  args: emptyArgs,
};

// --- One Member Story ---

const oneMemberArgs = {
  ...defaultArgs,
  members: {
    total: 1,
    items: defaultArgs.members.items.slice(0, 1) ?? [],
  },
};

export const OneMember: Story = {
  args: oneMemberArgs,
};
