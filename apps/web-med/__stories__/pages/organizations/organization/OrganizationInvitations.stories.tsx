import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { OrganizationInvitationsProps } from "@/www/organizations/organization/OrganizationInvitations";

import OrganizationInvitations from "@/www/organizations/organization/OrganizationInvitations";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationInvitations",
  component: OrganizationInvitations,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
      router: {
        path: `/app/organizations/${defaultOrganizationData.organization.data.organization.id ?? "mock-org-id"}/invitations`,
      },
    },
  },
  tags: ["component", "organization", "table"],
} satisfies Meta<typeof OrganizationInvitations>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs: OrganizationInvitationsProps = {
  loading: defaultOrganizationData.invites.loading,
  error: defaultOrganizationData.invites.error,
  organization: defaultOrganizationData.organization.data.organization,
  invites: defaultOrganizationData.invites.data,
};

export const Default: Story = {
  args: defaultArgs,
};

// --- Loading Story ---

const loadingArgs: OrganizationInvitationsProps = {
  loading: true,
  error: undefined,
  organization: defaultOrganizationData.organization.data.organization,
  invites: undefined,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationInvitationsSkeleton />
};

// --- Error Story ---

const errorArgs: OrganizationInvitationsProps = {
  loading: false,
  error: errorOrganizationData.invites.error,
  organization: defaultOrganizationData.organization.data.organization,
  invites: errorOrganizationData.invites.data, // Usually empty on error
};

export const WithError: Story = {
  args: errorArgs,
};

// --- Empty State Story ---

const emptyArgs: OrganizationInvitationsProps = {
  loading: false,
  error: undefined,
  organization: defaultOrganizationData.organization.data.organization,
  invites: emptyOrganizationData.invites.data, // Use empty invites data
};

export const Empty: Story = {
  args: emptyArgs,
};

// --- One Invite Story ---

const oneInviteArgs: OrganizationInvitationsProps = {
  ...defaultArgs,
  invites: {
    total: 1,
    items: defaultArgs.invites?.items.slice(0, 1) ?? [],
  },
};

export const OneInvite: Story = {
  args: oneInviteArgs,
};
