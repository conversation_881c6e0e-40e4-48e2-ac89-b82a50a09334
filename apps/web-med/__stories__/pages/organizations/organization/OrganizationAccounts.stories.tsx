import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationAccounts from "@/www/organizations/organization/OrganizationAccounts";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationAccounts",
  component: OrganizationAccounts,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
      router: {
        path: `/app/organizations/${defaultOrganizationData.organization.data.organization.id ?? "mock-org-id"}/accounts`,
      },
    },
  },
  tags: ["component", "organization", "table", "accounts"],
} satisfies Meta<typeof OrganizationAccounts>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs = {
  // Note: This component seems to take the *entire* organization query result
  loading: defaultOrganizationData.organization.loading,
  organization: defaultOrganizationData.organization.data,
};

export const Default: Story = {
  args: defaultArgs,
};

// --- Loading Story ---

const loadingArgs = {
  loading: true,
  organization: undefined,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationAccountsSkeleton />
};

// --- Error Story ---
// Component likely doesn't render if the main org query fails
const errorArgs = {
  loading: false,
  organization: undefined,
  // Optionally pass error if the component handles it
  // error: errorOrganizationData.organization.error
};

export const WithError: Story = {
  args: errorArgs,
  // Maybe render nothing or a specific error message if applicable
  // render: () => <div>Error loading accounts.</div>
};

// --- Empty State Story ---

const emptyArgs = {
  loading: false,
  organization: emptyOrganizationData.organization.data, // Use empty accounts data from this state
};

export const Empty: Story = {
  args: emptyArgs,
};

// --- One Account Story ---

const oneAccountArgs = {
  ...defaultArgs,
  organization: {
    ...defaultArgs.organization,
    accounts: defaultArgs.organization.accounts.slice(0, 1) ?? [],
  },
};

export const OneAccount: Story = {
  args: oneAccountArgs,
};
