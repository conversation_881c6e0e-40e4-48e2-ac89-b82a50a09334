import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationInfo from "@/www/organizations/organization/OrganizationInfo";

import {
  defaultOrganizationData,
  emptyOrganizationData, // Assuming empty might affect info display
  errorOrganizationData, // Assuming error might affect info display
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationInfo",
  component: OrganizationInfo,
  parameters: {
    layout: "centered", // Center component since it's not a full page
  },
  tags: ["component", "organization"],
} satisfies Meta<typeof OrganizationInfo>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

// Extract props relevant to OrganizationInfo from default data
const defaultArgs = {
  loading: defaultOrganizationData.organization.loading,
  organization: defaultOrganizationData.organization.data.organization,
  updateOrganization: defaultOrganizationData.updateOrganization,
  setStatus: defaultOrganizationData.setStatus,
};

export const Default: Story = {
  args: defaultArgs as any, // Cast as any for simplified mock mutations
};

// --- Loading Story ---

const loadingArgs = {
  loading: true,
  organization: undefined,
  // Pass undefined for mutations in loading state, or mocked disabled ones
  updateOrganization: loadingOrganizationData.updateOrganization as any,
  setStatus: loadingOrganizationData.setStatus as any,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally, you can render the skeleton directly if the component doesn't handle loading internally
  // render: () => <OrganizationInfoSkeleton />,
};

// --- Error Story ---

const errorArgs = {
  loading: false,
  // Pass error state (component might render an error message or nothing)
  organization: undefined,
  error: errorOrganizationData.organization.error, // Pass the error object if needed by the component
  // Pass error state mutations
  updateOrganization: errorOrganizationData.updateOrganization as any,
  setStatus: errorOrganizationData.setStatus as any,
};

// Note: OrganizationInfo itself doesn't seem to take an `error` prop directly based on Organization.tsx usage.
// It relies on the parent to handle the error rendering. We might adjust this if needed,
// but for now, we simulate the parent providing no data on error.
export const WithError: Story = {
  args: {
    loading: false,
    organization: undefined, // No org data when there's an error at the parent level
    updateOrganization: errorOrganizationData.updateOrganization as any,
    setStatus: errorOrganizationData.setStatus as any,
  },
};

// --- Empty State Story ---

// Example: If some fields being empty affects the Info display
const emptyArgs = {
  loading: false,
  organization: emptyOrganizationData.organization.data.organization, // Use organization data from empty state
  updateOrganization: emptyOrganizationData.updateOrganization,
  setStatus: emptyOrganizationData.setStatus,
};

export const Empty: Story = {
  args: emptyArgs as any,
};
