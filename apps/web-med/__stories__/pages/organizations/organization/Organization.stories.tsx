import type { <PERSON>a, StoryObj } from "@storybook/react";

import Organization from "@/www/organizations/organization/Organization";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/page",
  component: Organization,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: `/app/organizations/${defaultOrganizationData.organization.data.organization.id ?? "mock-org-id"}`,
      },
    },
  },
  tags: ["page", "organization"],
} satisfies Meta<typeof Organization>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: defaultOrganizationData as any,
};

export const Loading: Story = {
  args: loadingOrganizationData as any,
};

export const WithError: Story = {
  args: errorOrganizationData as any,
};

export const Empty: Story = {
  args: emptyOrganizationData as any,
};
