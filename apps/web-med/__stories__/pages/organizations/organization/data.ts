import type { TRPCClientErrorLike } from "@trpc/client";

import { faker } from "@faker-js/faker";

import type { RouterError, RouterOutputs } from "@/api";

import {
  AccountStatus,
  ContractStatus,
  ContractType,
  OrganizationBillingType,
  OrganizationClass,
  OrganizationMode,
  OrganizationStatus,
  OrganizationType,
  ScheduleType,
} from "@/api";

// --- Mock Data Generation ---

const createMockOrganization =
  (): RouterOutputs["organizations"]["get"]["organization"] => ({
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: faker.image.avatar(),
    type: faker.helpers.enumValue(OrganizationType),
    class: faker.helpers.enumValue(OrganizationClass),
    status: faker.helpers.enumValue(OrganizationStatus),
    mode: faker.helpers.enumValue(OrganizationMode),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    approvedAt: faker.date.past(),
    rejectedAt: null,
    phone: faker.phone.number(),
    email: faker.internet.email(),
    balance: faker.number.float({ min: -1000, max: 10000, fractionDigits: 2 }),
    score: faker.number.int({ min: 100, max: 500 }),
    billingFrequency: "MONTHLY",
    assistPercentage: faker.number.float({ min: 1, max: 2, fractionDigits: 2 }),
    basePercentage: faker.number.float({ min: 1, max: 2, fractionDigits: 2 }),
    cancellationFeePercentage: faker.number.float({
      min: 1,
      max: 2,
      fractionDigits: 2,
    }),
    accountId: null, // Simplified for mock
    customerId: null, // Simplified for mock
    parent: null, // Simplified for mock
    manager: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      type: OrganizationType.INTERNAL,
      avatar: faker.image.avatar(),
    },
    address: {
      id: faker.string.uuid(),
      formatted: faker.location.streetAddress(true),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
      country: faker.location.countryCode(),
      timeZone: faker.location.timeZone(),
    },
    schedule: {
      id: faker.string.uuid(),
      name: faker.lorem.words(2),
      type: ScheduleType.ORGANIZATION,
      blocks: Array.from(
        { length: faker.number.int({ min: 1, max: 3 }) },
        () => ({
          id: faker.string.uuid(),
          dayOfWeek: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.number.int({ min: 6 * 60, max: 12 * 60 }), // 6 AM to 12 PM in minutes
          endTime: faker.number.int({ min: 13 * 60, max: 18 * 60 }), // 1 PM to 6 PM in minutes
        }),
      ),
      startsAt: null,
      endsAt: null,
    },
    contracts: Array.from(
      { length: faker.number.int({ min: 0, max: 5 }) },
      () => ({
        id: faker.string.uuid(),
        title: faker.lorem.sentence(3),
        type: faker.helpers.enumValue(ContractType),
        status: faker.helpers.enumValue(ContractStatus),
        createdAt: faker.date.past(),
        where: undefined as never,
      }),
    ),
  });

const createMockAccounts = (
  count: number,
): RouterOutputs["organizations"]["get"]["accounts"] =>
  Array.from({ length: count }, () => ({
    id: faker.string.uuid(),
    name: faker.company.name(),
    path: faker.company.name().toLowerCase().replace(/ /g, "-"),
    avatar: faker.image.avatar(),
    type: OrganizationType.ACCOUNT,
    status: faker.helpers.enumValue(OrganizationStatus),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    addressId: null,
    scheduleId: null,
    deletedAt: null,
    email: faker.internet.email(),
    phone: faker.phone.number(),
    depth: 1,
    numchild: 0,
    approvedAt: faker.date.past(),
    rejectedAt: null,
    customerId: `cus_${faker.string.alphanumeric(14)}`,
    accountId: `acct_${faker.string.alphanumeric(14)}`,
    accountStatus: faker.helpers.enumValue(AccountStatus),
    taxId: faker.finance.routingNumber(),
    vatId: null,
    structure: "LLC",
    balance: faker.number.float({ min: -500, max: 5000, fractionDigits: 2 }),
    threshold: 1000,
    assistPercentage: faker.number.int({ min: 1, max: 5 }),
    basePercentage: faker.number.int({ min: 10, max: 25 }),
    cancellationFeePercentage: faker.number.int({ min: 0, max: 10 }),
    billingFrequency: "MONTHLY",
    autoPay: faker.datatype.boolean(),
    score: faker.number.float({ min: 100, max: 500, fractionDigits: 1 }),
    class: faker.helpers.enumValue(OrganizationClass),
    billing: faker.helpers.enumValue(OrganizationBillingType),
    mode: faker.helpers.enumValue(OrganizationMode),
    managerId: null,
  }));

const createMockMember =
  (): RouterOutputs["organizations"]["memberships"]["getMany"]["items"][number] => ({
    id: faker.string.uuid(),
    role: faker.helpers.arrayElement(["OWNER", "ADMIN", "MEMBER"]), // Use realistic roles
    user: {
      id: faker.string.uuid(),
      avatar: faker.image.avatar(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
    },
  });

const createMockInvite =
  (): RouterOutputs["organizations"]["invitations"]["getMany"]["items"][number] => ({
    id: faker.string.uuid(),
    emailAddress: faker.internet.email(),
    role: faker.helpers.arrayElement(["org:admin", "org:member"]), // Use realistic roles
    status: faker.helpers.arrayElement(["pending", "accepted"]), // Example statuses
    createdAt: faker.date.past().getTime(), // Use number for timestamp consistency
    updatedAt: faker.date.recent().getTime(),
  });

const createMockContract =
  (): RouterOutputs["contracts"]["getMany"]["items"][number] => ({
    id: faker.string.uuid(),
    title: faker.lorem.sentence(4),
    type: faker.helpers.arrayElement([
      ContractType.SERVICE_RATE,
      ContractType.SERVICE_AGREEMENT,
    ]),
    status: faker.helpers.enumValue(ContractStatus),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    expiresAt: faker.date.future(),
    agreements: undefined, // Keep related fields undefined for base mock unless needed
    position: undefined,
    provider: undefined,
    organization: undefined,
    thread: undefined,
  });

// Mock Mutation Function (simplified)
const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (_vars: TVariables) => {
    console.log("Mock mutate called", _vars);
  },
  mutateAsync: async (_vars: TVariables) => {
    console.log("Mock mutateAsync called", _vars);
    // Return a minimal success-like response or adjust as needed for tests
    return { id: faker.string.uuid() } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {},
  // Add other properties if your component relies on them (e.g., status)
});

// --- Exported Data Structures ---

const baseOrganizationData = createMockOrganization();
const baseAccountsData = createMockAccounts(
  faker.number.int({ min: 1, max: 5 }),
);
const baseMembersData = Array.from(
  { length: faker.number.int({ min: 1, max: 7 }) },
  createMockMember,
);
const baseInvitesData = Array.from(
  { length: faker.number.int({ min: 1, max: 4 }) },
  createMockInvite,
);
const baseContractsData = Array.from(
  { length: faker.number.int({ min: 2, max: 5 }) },
  createMockContract,
);

export const defaultOrganizationData = {
  loading: false,
  organization: {
    data: {
      organization: baseOrganizationData,
      accounts: baseAccountsData,
      members: null,
    }, // Adjust members based on actual API response if needed
    loading: false,
    error: undefined,
  },
  members: {
    data: { total: baseMembersData.length, items: baseMembersData },
    loading: false,
    error: undefined,
  },
  invites: {
    data: { total: baseInvitesData.length, items: baseInvitesData },
    loading: false,
    error: undefined,
  },
  contracts: {
    data: { total: baseContractsData.length, items: baseContractsData },
    loading: false,
    error: undefined,
  },
  // Provide mock mutations
  setStatus: createMockMutation<
    void,
    { status: OrganizationStatus; organizationId: string }
  >(),
  updateOrganization: createMockMutation<{ id: string }>(), // Add specific types if known
  createTimeBlock: createMockMutation<{ id: string }>(),
  updateTimeBlock: createMockMutation<{ id: string }>(),
  deleteTimeBlock: createMockMutation<{ id: string }>(),
  updateBanking: createMockMutation<{ id: string }>(), // Add specific types if known
};

export const loadingOrganizationData = {
  loading: true,
  organization: { data: undefined, loading: true, error: undefined },
  members: { data: undefined, loading: true, error: undefined },
  invites: { data: undefined, loading: true, error: undefined },
  contracts: { data: undefined, loading: true, error: undefined },
  // Mutations should probably be undefined or disabled in loading state
  setStatus: undefined,
  updateOrganization: undefined,
  createTimeBlock: undefined,
  updateTimeBlock: undefined,
  deleteTimeBlock: undefined,
  updateBanking: undefined,
};

export const errorOrganizationData = {
  loading: false,
  organization: {
    data: undefined,
    loading: false,
    error: { message: "Failed to load organization details" } as RouterError,
  },
  members: {
    data: { total: 0, items: [] }, // Show empty state for related data on error
    loading: false,
    error: { message: "Failed to load members" } as RouterError, // Can have specific errors
  },
  invites: {
    data: { total: 0, items: [] },
    loading: false,
    error: { message: "Failed to load invites" } as RouterError,
  },
  contracts: {
    data: { total: 0, items: [] },
    loading: false,
    error: { message: "Failed to load contracts" } as RouterError,
  },
  // Provide mock mutations, potentially in a disabled state or indicating error
  setStatus: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
  updateOrganization: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
  createTimeBlock: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
  updateTimeBlock: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
  deleteTimeBlock: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
  updateBanking: {
    ...createMockMutation(),
    isError: true,
    error: { message: "Mutation unavailable" } as RouterError,
  },
};

export const emptyOrganizationData = {
  ...defaultOrganizationData, // Start with default structure
  organization: {
    ...defaultOrganizationData.organization,
    data: {
      ...defaultOrganizationData.organization.data,
      organization: {
        ...defaultOrganizationData.organization.data.organization,
        schedule: {
          // Example of making a nested field empty
          ...(defaultOrganizationData.organization.data.organization
            .schedule ?? {
            id: faker.string.uuid(),
            type: ScheduleType.ORGANIZATION,
            name: null,
            startsAt: null,
            endsAt: null,
          }), // Ensure schedule exists
          blocks: [],
        },
        contracts: [], // Empty contracts array
      },
      accounts: [], // Empty accounts array
    },
  },
  members: {
    data: { total: 0, items: [] },
    loading: false,
    error: undefined,
  },
  invites: {
    data: { total: 0, items: [] },
    loading: false,
    error: undefined,
  },
  contracts: {
    data: { total: 0, items: [] },
    loading: false,
    error: undefined,
  },
  // Mutations remain the same as default
};
