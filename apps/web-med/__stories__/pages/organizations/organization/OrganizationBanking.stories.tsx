import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import OrganizationBanking from "@/www/organizations/organization/OrganizationBanking";

import {
  defaultOrganizationData,
  emptyOrganizationData,
  errorOrganizationData,
  loadingOrganizationData,
} from "./data";

const meta = {
  title: "Pages/Organizations/Organization/Components/OrganizationBanking",
  component: OrganizationBanking,
  parameters: {
    layout: "centered",
  },
  tags: ["component", "organization", "banking"],
} satisfies Meta<typeof OrganizationBanking>;

export default meta;
type Story = StoryObj<typeof meta>;

// --- Default Story ---

const defaultArgs = {
  organization: defaultOrganizationData.organization.data.organization,
  updateBanking: defaultOrganizationData.updateBanking,
};

export const Default: Story = {
  args: defaultArgs as any, // Cast for mock mutation
};

// --- Loading Story ---
// Banking info likely comes from the main organization query.
// If that query is loading, we likely show nothing or a skeleton.
const loadingArgs = {
  loading: true,
  organization: undefined,
  updateBanking: loadingOrganizationData.updateBanking as any,
};

export const Loading: Story = {
  args: loadingArgs,
  // Optionally render skeleton:
  // render: () => <OrganizationBankingSkeleton />
};

// --- Error Story ---
// Similar to other sections, parent error means no data here.
const errorArgs = {
  organization: undefined,
  updateBanking: errorOrganizationData.updateBanking as any,
};

export const WithError: Story = {
  args: errorArgs,
};

// --- Empty/Different State ---
// The "Empty" state might not be distinct for Banking itself,
// but depends on the fields within the organization object.
// We can use the `emptyOrganizationData` which might have different org details.
const emptyOrgArgs = {
  organization: emptyOrganizationData.organization.data.organization,
  updateBanking: emptyOrganizationData.updateBanking,
};

export const WithEmptyOrgData: Story = {
  args: emptyOrgArgs as any,
  name: "With Empty Org Data", // Rename story for clarity
};
