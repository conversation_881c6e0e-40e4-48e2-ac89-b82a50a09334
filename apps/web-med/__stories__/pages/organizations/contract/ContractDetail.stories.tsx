import type { <PERSON>a, StoryObj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import { ContractView } from "@/www/organizations/contract";

import { contracts } from "./data";

const meta = {
  title: "Pages/Organizations/Contract/Details",
  component: ContractView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/contracts/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.contracts.get.query(({ input }) => {
          // Return the appropriate contract based on the ID
          const id = input.id;

          if (id === "abc-123") {
            return contracts.draft;
          } else if (id === "abc-124") {
            return contracts.pending;
          } else if (id === "abc-125") {
            return contracts.signed;
          } else if (id === "abc-126") {
            return contracts.expired;
          } else if (id === "abc-127") {
            return contracts.terminated;
          }

          // Default fallback
          return contracts.draft;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the contract with the matching thread ID
          const contract = Object.values(contracts).find(
            (c) => c.thread?.id === id,
          );

          if (contract?.thread) {
            return {
              ...contract.thread,
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof ContractView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Draft: Story = {
  args: {
    loading: false,
    id: "abc-123",
  },
};

export const Pending: Story = {
  args: {
    loading: false,
    id: "abc-124",
  },
};

export const Active: Story = {
  args: {
    loading: false,
    id: "abc-125",
  },
};

export const Expired: Story = {
  args: {
    loading: false,
    id: "abc-126",
  },
};

export const Terminated: Story = {
  args: {
    loading: false,
    id: "abc-127",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    id: "abc-128",
  },
};
