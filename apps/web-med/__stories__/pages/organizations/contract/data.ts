import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  ContractStatus,
  ContractType,
  OrganizationClass,
  OrganizationStatus,
  OrganizationType,
} from "@/api";

// Set a seed for reproducible results
faker.seed(123);

// Factory function for creating mock organizations
export function createMockOrganization(overrides = {}) {
  return {
    id: faker.string.uuid(),
    name: faker.company.name(),
    path: faker.string.alphanumeric(8),
    avatar: faker.image.avatar(),
    type: faker.helpers.enumValue(OrganizationType),
    class: faker.helpers.enumValue(OrganizationClass),
    status: faker.helpers.enumValue(OrganizationStatus),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    deletedAt: null,
    approvedAt: faker.date.past().toISOString(),
    rejectedAt: null,
    ...overrides,
  };
}

// Factory function for creating mock providers
export function createMockProvider(overrides = {}) {
  return {
    id: faker.string.uuid(),
    title: faker.company.name(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    person: {
      id: faker.string.uuid(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      avatar: faker.image.avatar(),
    },
    ...overrides,
  };
}

// Factory function for creating mock messages
export function createMockMessage(overrides = {}) {
  return {
    id: faker.string.uuid(),
    content: faker.lorem.paragraph(),
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    deletedAt: null,
    author: {
      id: faker.string.uuid(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      avatar: faker.image.avatar(),
    },
    ...overrides,
  };
}

// Factory function for creating mock threads
export function createMockThread(overrides = {}) {
  const messages = Array.from(
    { length: faker.number.int({ min: 1, max: 5 }) },
    (_, index) =>
      createMockMessage({
        createdAt: faker.date.recent(index + 1).toISOString(),
      }),
  );

  return {
    id: faker.string.uuid(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    messages,
    author: {
      id: faker.string.uuid(),
      firstName: "System",
      lastName: "User",
      avatar: null,
    },
    ...overrides,
  };
}

// Factory function for creating mock agreements
export function createMockAgreement(overrides = {}) {
  return {
    id: faker.string.uuid(),
    title: faker.lorem.sentence(),
    content: faker.lorem.paragraphs(3),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides,
  };
}

// Factory function for creating mock signatures
export function createMockSignature(overrides = {}) {
  return {
    id: faker.string.uuid(),
    status: faker.helpers.arrayElement(["SIGNED", "PENDING", "REJECTED"]),
    createdAt: faker.date.recent().toISOString(),
    signedAt: faker.helpers.maybe(() => faker.date.recent().toISOString()),
    rejectedAt: null,
    documensoToken: faker.helpers.maybe(() => faker.string.alphanumeric(20)),
    person: createMockProvider(),
    ...overrides,
  };
}

// Factory function for creating mock contracts
export function createMockContract(
  overrides = {},
): RouterOutputs["contracts"]["get"] {
  const thread = createMockThread();

  return {
    id: faker.string.uuid(),
    title: `Contract for ${faker.company.name()}`,
    type: faker.helpers.enumValue(ContractType),
    status: faker.helpers.enumValue(ContractStatus),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    expiresAt: faker.date.future(),
    notes: faker.lorem.paragraph(),
    provider: createMockProvider(),
    organization: createMockOrganization(),
    thread: {
      id: thread.id,
    },
    agreements: Array.from(
      { length: faker.number.int({ min: 1, max: 3 }) },
      () => createMockAgreement(),
    ),
    signatures: Array.from(
      { length: faker.number.int({ min: 1, max: 2 }) },
      () => createMockSignature(),
    ),
    ...overrides,
  };
}

// Create specific contract scenarios
export const contracts = {
  // Draft contract
  draft: createMockContract({
    status: ContractStatus.DRAFT,
    title: "Draft Contract",
    notes: "This contract is still in draft mode and needs to be finalized.",
  }),

  // Pending contract
  pending: createMockContract({
    status: ContractStatus.PENDING,
    title: "Pending Contract",
    notes: "This contract is pending approval from all parties.",
  }),

  signed: createMockContract({
    status: ContractStatus.SIGNED,
    title: "Active Contract",
    notes: "This contract is currently active and in effect.",
    signatures: Array.from({ length: 2 }, () =>
      createMockSignature({
        status: ContractStatus.SIGNED,
        signedAt: faker.date.recent().toISOString(),
      }),
    ),
  }),

  // Expired contract
  expired: createMockContract({
    status: ContractStatus.EXPIRED,
    title: "Expired Contract",
    notes: "This contract has expired and is no longer in effect.",
    expiresAt: faker.date.past().toISOString(),
  }),

  // Terminated contract
  terminated: createMockContract({
    status: ContractStatus.REJECTED,
    title: "Terminated Contract",
    notes: "This contract was terminated before its expiration date.",
  }),
};
