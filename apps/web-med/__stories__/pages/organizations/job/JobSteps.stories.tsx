import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobPostStatus } from "@/api";
import JobSteps from "@/www/organizations/job/JobSteps";

const meta = {
  title: "Pages/Organizations/JobDetails/JobSteps",
  component: JobSteps,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof JobSteps>;

export default meta;
type Story = StoryObj<typeof meta>;

const baseArgs = {
  createdAt: new Date("2024-01-01"),
};

export const Draft: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.DRAFT,
  },
};

export const Published: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.PUBLISHED,
    publishedAt: new Date("2024-01-15"),
  },
};

export const Filled: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.FILLED,
    publishedAt: new Date("2024-01-15"),
    filledAt: new Date("2024-02-01"),
  },
};

export const Completed: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.COMPLETED,
    publishedAt: new Date("2024-01-15"),
    filledAt: new Date("2024-02-01"),
    completedAt: new Date("2024-02-15"),
  },
};

export const Cancelled: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.CANCELLED,
    publishedAt: new Date("2024-01-15"),
    cancelledAt: new Date("2024-01-20"),
  },
};

export const Expired: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.EXPIRED,
    publishedAt: new Date("2024-01-15"),
    expiredAt: new Date("2024-02-15"),
  },
};

export const Loading: Story = {
  args: {
    ...baseArgs,
    status: JobPostStatus.PUBLISHED,
    publishedAt: new Date("2024-01-15"),
    loading: true,
  },
};
