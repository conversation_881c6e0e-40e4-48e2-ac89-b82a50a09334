import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  DepartmentType,
  PaymentType,
  PersonRole,
  RouterOutputs,
} from "@/api";

import {
  ApplicationStatus,
  FacilityType,
  JobPostMode,
  JobPostPriority,
  JobPostStatus,
  JobPostType,
  OfferStatus,
  ProviderStatus,
  TimeBlockRecurrence,
  TimeBlockType,
} from "@/api";
import Job from "@/www/organizations/job/Job";

import { createMockMutation } from "../../../helpers";

const meta = {
  title: "Pages/Organizations/JobDetails/page",
  component: Job,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/jobs/abc-123",
      },
    },
  },
} satisfies Meta<typeof Job>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type JobGetQuery = RouterOutputs["jobs"]["get"];
type OrganizationStructure = NonNullable<JobGetQuery["organization"]>;
type LocationStructure = NonNullable<JobGetQuery["location"]>;
type DepartmentStructure = NonNullable<JobGetQuery["department"]>;
type SpecialtyStructure = NonNullable<JobGetQuery["specialties"]>[number];
type ScheduleStructure = NonNullable<JobGetQuery["schedule"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type PersonStructure = NonNullable<
  DepartmentStructure["contacts"]
>[number]["person"];
type ContactStructure = NonNullable<DepartmentStructure["contacts"]>[number];
type TimeBlockStructure = NonNullable<ScheduleStructure["blocks"]>[number];
type ThreadStructure = NonNullable<JobGetQuery["thread"]>;
type MessageStructure = NonNullable<ThreadStructure["messages"]>[number];
type ActionStructure = NonNullable<JobGetQuery["actions"]>[number];
type ApplicationStructure = NonNullable<JobGetQuery["applications"]>[number];
type OfferStructure = NonNullable<JobGetQuery["offers"]>[number];
type ProviderStructure = NonNullable<OfferStructure["provider"]>;
type PositionStructure = NonNullable<JobGetQuery["position"]>;

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createAddress = (
  options: {
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): AddressStructure => {
  return {
    formatted: `${faker.location.streetAddress()}, ${options.city ?? faker.location.city()}, ${options.state ?? faker.location.state()} ${faker.location.zipCode()}`,
    timeZone: options.timeZone ?? "America/New_York",
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
  };
};

const createLocation = (
  options: {
    name?: string;
    type?: FacilityType;
    city?: string;
    state?: string;
    timeZone?: string;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  type: options.type ?? "HOSPITAL",
  address: createAddress({
    city: options.city,
    state: options.state,
    timeZone: options.timeZone,
  }),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    title?: string | null;
    phone?: string | null;
    email?: string | null;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  role: options.role ?? "USER",
  title: options.title ?? faker.person.jobTitle(),
  phone: options.phone ?? faker.phone.number(),
  email: options.email ?? faker.internet.email(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createContact = (
  options: {
    role?: string;
    person?: PersonStructure;
  } = {},
): ContactStructure => ({
  id: faker.string.uuid(),
  role: options.role ?? "Primary Contact",
  person: options.person ?? createPerson(),
});

const createDepartment = (
  options: {
    name?: string;
    type?: DepartmentType;
    description?: string | null;
    contacts?: ContactStructure[];
  } = {},
): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.commerce.department(),
  type: options.type ?? "DEPARTMENT",
  description: options.description ?? faker.company.catchPhrase(),
  contacts: options.contacts ?? [createContact(), createContact()],
});

const createSpecialty = (
  options: {
    name?: string;
  } = {},
): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.person.jobType(),
});

const createTimeBlock = (
  options: {
    type?: TimeBlockType;
    startsAt?: Date | null;
    endsAt?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
    startTime?: number | null;
    endTime?: number | null;
    hours?: number | null;
    recurrence?: TimeBlockRecurrence | null;
    timeZone?: string | null;
  } = {},
): TimeBlockStructure => ({
  id: faker.string.uuid(),
  type: options.type ?? TimeBlockType.SHIFT,
  startsAt: options.startsAt ?? faker.date.future(),
  endsAt:
    options.endsAt ??
    faker.date.future({ refDate: options.startsAt ?? faker.date.future() }),
  startDate: options.startDate ?? faker.date.future(),
  endDate:
    options.endDate ??
    faker.date.future({ refDate: options.startDate ?? faker.date.future() }),
  startTime: options.startTime ?? 900, // 9:00 AM
  endTime: options.endTime ?? 1700, // 5:00 PM
  hours: options.hours ?? 8,
  recurrence: options.recurrence ?? TimeBlockRecurrence.WEEKLY,
  timeZone: options.timeZone ?? "America/New_York",
});

const createSchedule = (
  options: {
    startsAt?: Date | null;
    endsAt?: Date | null;
    blocks?: TimeBlockStructure[];
  } = {},
): ScheduleStructure => ({
  id: faker.string.uuid(),
  startsAt: options.startsAt ?? faker.date.future(),
  endsAt:
    options.endsAt ??
    faker.date.future({ refDate: options.startsAt ?? faker.date.future() }),
  blocks: options.blocks ?? [
    createTimeBlock(),
    createTimeBlock({
      startTime: 1700,
      endTime: 2300,
      hours: 6,
    }),
  ],
});

const createMessage = (
  options: {
    content?: string;
    author?: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string | null;
    };
  } = {},
): MessageStructure => ({
  id: faker.string.uuid(),
  content: options.content ?? faker.lorem.paragraph(),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  author: options.author ?? {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

const createThread = (
  options: {
    messages?: MessageStructure[];
  } = {},
): ThreadStructure => ({
  id: faker.string.uuid(),
  messages: options.messages ?? [
    createMessage(),
    createMessage(),
    createMessage(),
  ],
});

const createAction = (
  options: {
    type?: string;
    metadata?: Record<string, any>;
  } = {},
): ActionStructure => ({
  id: faker.string.uuid(),
  type: options.type ?? "JOB_CREATED",
  createdAt: faker.date.recent(),
  metadata: options.metadata ?? {},
  actor: {
    id: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

const createProvider = (
  options: {
    title?: string;
    status?: ProviderStatus;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? ProviderStatus.ACTIVE,
  person: {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    avatar: faker.image.avatar(),
  },
});

const createApplication = (
  options: {
    status?: ApplicationStatus;
    provider?: ProviderStructure;
    notes?: string;
  } = {},
): ApplicationStructure => ({
  id: faker.string.uuid(),
  status: options.status ?? ApplicationStatus.PENDING,
  notes: options.notes ?? faker.lorem.sentence(),
  createdAt: faker.date.recent(),
  provider: options.provider ?? createProvider(),
});

const createOffer = (
  options: {
    status?: OfferStatus;
    provider?: ProviderStructure;
    notes?: string;
  } = {},
): OfferStructure => ({
  id: faker.string.uuid(),
  status: options.status ?? OfferStatus.PENDING,
  notes: options.notes ?? faker.lorem.sentence(),
  createdAt: faker.date.recent(),
  provider: options.provider ?? createProvider(),
});

// Create common mock data
const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
  pediatricCenter: createOrganization({ name: "Pediatric Care Center" }),
  communityHealth: createOrganization({ name: "Community Health Network" }),
};

const locations = {
  mainHospital: createLocation({
    name: "Main Hospital",
    type: FacilityType.HOSPITAL,
    city: "New York",
    state: "NY",
  }),
  emergencyCenter: createLocation({
    name: "Emergency Care Center",
    type: FacilityType.CLINIC,
    city: "Boston",
    state: "MA",
  }),
  outpatientClinic: createLocation({
    name: "Outpatient Clinic",
    type: FacilityType.CLINIC,
    city: "Chicago",
    state: "IL",
  }),
  surgicalCenter: createLocation({
    name: "Surgical Center",
    type: FacilityType.CAMPUS,
    city: "Los Angeles",
    state: "CA",
  }),
  rehabilitationCenter: createLocation({
    name: "Rehabilitation Center",
    type: FacilityType.REHABILITATION,
    city: "Miami",
    state: "FL",
  }),
};

const departments = {
  emergency: createDepartment({
    name: "Emergency Department",
    type: "DEPARTMENT",
    description: "Level 1 Trauma Center",
    contacts: [
      createContact({ role: "Department Head" }),
      createContact({ role: "Nurse Manager" }),
    ],
  }),
  surgery: createDepartment({
    name: "Surgery Department",
    type: "DEPARTMENT",
    description: "General and specialized surgical services",
    contacts: [
      createContact({ role: "Chief of Surgery" }),
      createContact({ role: "Surgical Coordinator" }),
    ],
  }),
  pediatrics: createDepartment({
    name: "Pediatrics Department",
    type: "DEPARTMENT",
    description: "Comprehensive care for children and adolescents",
    contacts: [
      createContact({ role: "Pediatrics Director" }),
      createContact({ role: "Child Life Specialist" }),
    ],
  }),
  cardiology: createDepartment({
    name: "Cardiology Department",
    type: "DEPARTMENT",
    description: "Advanced cardiac care and diagnostics",
    contacts: [
      createContact({ role: "Cardiology Director" }),
      createContact({ role: "Cardiac Nurse Specialist" }),
    ],
  }),
  neurology: createDepartment({
    name: "Neurology Department",
    type: "DEPARTMENT",
    description: "Specialized care for neurological conditions",
    contacts: [
      createContact({ role: "Neurology Director" }),
      createContact({ role: "Neurological Specialist" }),
    ],
  }),
};

const specialties = {
  emergencyMedicine: createSpecialty({ name: "Emergency Medicine" }),
  generalSurgery: createSpecialty({ name: "General Surgery" }),
  pediatrics: createSpecialty({ name: "Pediatrics" }),
  cardiology: createSpecialty({ name: "Cardiology" }),
  neurology: createSpecialty({ name: "Neurology" }),
  criticalCare: createSpecialty({ name: "Critical Care" }),
  anesthesiology: createSpecialty({ name: "Anesthesiology" }),
  familyMedicine: createSpecialty({ name: "Family Medicine" }),
};

const createDayShiftSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 900, // 9:00 AM
        endTime: 1700, // 5:00 PM
        hours: 8,
        recurrence: TimeBlockRecurrence.WEEKLY,
      }),
    ],
  });

const createNightShiftSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 2300, // 11:00 PM
        endTime: 700, // 7:00 AM
        hours: 8,
        recurrence: TimeBlockRecurrence.WEEKLY,
      }),
    ],
  });

const createWeekendSchedule = () =>
  createSchedule({
    blocks: [
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 900, // 9:00 AM
        endTime: 1700, // 5:00 PM
        hours: 8,
        recurrence: TimeBlockRecurrence.WEEKLY,
      }),
      createTimeBlock({
        type: TimeBlockType.SHIFT,
        startTime: 900, // 9:00 AM
        endTime: 1700, // 5:00 PM
        hours: 8,
        recurrence: TimeBlockRecurrence.WEEKLY,
      }),
    ],
  });

const createPosition = (): PositionStructure => ({
  id: faker.string.uuid(),
  person: createPerson(),
  organization: organizations.cityGeneral,
  location: locations.mainHospital,
});

const createJob = (
  options: {
    id?: string;
    status?: JobPostStatus;
    mode?: JobPostMode;
    type?: JobPostType;
    priority?: JobPostPriority;
    summary?: string;
    scope?: string;
    role?: string;
    paymentType?: PaymentType;
    paymentAmount?: number;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    schedule?: ScheduleStructure;
    thread?: ThreadStructure;
    actions?: ActionStructure[];
    applications?: ApplicationStructure[];
    offers?: OfferStructure[];
    publishedAt?: Date | null;
    expiresAt?: Date | null;
    filledAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    position?: PositionStructure | null;
  } = {},
): JobGetQuery => {
  const now = new Date();
  const oneMonthLater = new Date(now);
  oneMonthLater.setMonth(now.getMonth() + 1);

  return {
    id: options.id ?? faker.string.uuid(),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    expiresAt: options.expiresAt ?? oneMonthLater,
    publishedAt:
      options.publishedAt ??
      (options.status === JobPostStatus.PUBLISHED ? now : null),
    filledAt:
      options.filledAt ??
      (options.status === JobPostStatus.FILLED ? now : null),
    cancelledAt:
      options.cancelledAt ??
      (options.status === JobPostStatus.CANCELLED ? now : null),
    completedAt:
      options.completedAt ??
      (options.status === JobPostStatus.COMPLETED ? now : null),
    expiredAt: null,
    // archivedAt: null,
    status: options.status ?? JobPostStatus.PUBLISHED,
    mode: options.mode ?? JobPostMode.INDEPENDENT,
    type: options.type ?? JobPostType.PER_DIEM,
    priority: options.priority ?? JobPostPriority.MEDIUM,
    summary: options.summary ?? faker.company.catchPhrase(),
    scope: options.scope ?? faker.lorem.paragraph(),
    role: options.role ?? faker.person.jobTitle(),
    paymentType: options.paymentType ?? "HOURLY",
    paymentAmount:
      options.paymentAmount ?? faker.number.int({ min: 50, max: 200 }),
    paymentRate: 1,
    nightRate: 1.25,
    overtimeRate: 1.5,
    holidayRate: 2,
    bonusRate: 0,
    billingType: "HOURLY",
    billingRate: 1.15,
    isBillable: true,
    organization: options.organization ?? organizations.cityGeneral,
    location: options.location ?? locations.mainHospital,
    department: options.department ?? departments.emergency,
    specialties: options.specialties ?? [
      specialties.emergencyMedicine,
      specialties.criticalCare,
    ],
    schedule: options.schedule ?? createDayShiftSchedule(),
    thread: options.thread ?? createThread(),
    actions: options.actions ?? [
      createAction({ type: "JOB_CREATED" }),
      createAction({ type: "JOB_PUBLISHED" }),
    ],
    applications: options.applications ?? [
      createApplication({ status: ApplicationStatus.PENDING }),
      createApplication({ status: ApplicationStatus.ACCEPTED }),
    ],
    offers: options.offers ?? [
      createOffer({ status: OfferStatus.PENDING }),
      createOffer({ status: OfferStatus.ACCEPTED }),
    ],
    contacts: [
      createContact({ role: "Hiring Manager" }),
      createContact({ role: "Department Head" }),
    ],
    position:
      options.position ??
      ([JobPostStatus.FILLED, JobPostStatus.COMPLETED].includes(options.status!)
        ? createPosition()
        : undefined),
    provider: null,
    analytics: null,
    context: null,
  };
};

// Fix the interface for BaseMutation to match the expected types
interface BaseMutation<TVariables = unknown, TData = unknown> {
  data: undefined;
  error: null;
  isError: false;
  isIdle: true;
  isLoading: false;
  isSuccess: false;
  mutate: (variables: TVariables) => void;
  mutateAsync: (variables: TVariables) => Promise<TData>;
  reset: () => void;
  status: "idle";
  variables: undefined;
  failureCount: 0;
  failureReason: null;
  isPaused: false;
  context: undefined;
  trpc: { path: string };
  isPending: false;
  submittedAt: number;
}

// Create mock mutations with proper typing

// Create story variants
export const Default: Story = {
  args: {
    loading: false,
    job: {
      loading: false,
      data: createJob({
        status: "PUBLISHED",
        summary: "Emergency Room Physician",
        role: "Physician",
        scope:
          "Looking for an experienced Emergency Room Physician for night shifts. Must be board certified and have at least 5 years of experience in a high-volume ER setting.",
        organization: organizations.cityGeneral,
        location: locations.mainHospital,
        department: departments.emergency,
        specialties: [specialties.emergencyMedicine, specialties.criticalCare],
        schedule: createNightShiftSchedule(),
        paymentType: "HOURLY",
        paymentAmount: 150,
      }),
      error: undefined,
      refetch: async () => {},
    },
    providers: {
      loading: false,
      data: {
        items: Array.from({ length: 5 }, () => createProvider()),
        total: 5,
        pageSize: 5,
        pageNumber: 0,
      },
      error: undefined,
      refetch: async () => {},
    },
    applications: {
      loading: false,
      data: {
        items: Array.from({ length: 3 }, () => createApplication()),
        total: 3,
        pageSize: 5,
        pageNumber: 0,
      },
      error: undefined,
      refetch: async () => {},
    },
    offers: {
      loading: false,
      data: {
        items: Array.from({ length: 2 }, () => createOffer()),
        total: 2,
        pageSize: 5,
        pageNumber: 0,
      },
      error: undefined,
      refetch: async () => {},
    },
    update: createMockMutation<any, any>("jobs.update"),
    publish: createMockMutation<any, any>("jobs.organization.publish"),
    unpublish: createMockMutation<any, any>("jobs.organization.unpublish"),
    cancel: createMockMutation<any, any>("jobs.organization.cancel"),
    linkDepartment: createMockMutation<any, any>("departments.link"),
    unlinkDepartment: createMockMutation<any, any>("departments.unlink"),
    linkLocation: createMockMutation<any, any>("locations.link"),
    unlinkLocation: createMockMutation<any, any>("locations.unlink"),
    createContact: createMockMutation<any, any>("contacts.create"),
    updateContact: createMockMutation<any, any>("contacts.update"),
    deleteContact: createMockMutation<any, any>("contacts.delete"),
    createSchedule: createMockMutation<any, any>("schedule.create"),
    updateSchedule: createMockMutation<any, any>("schedule.update"),
    createBlock: createMockMutation<any, any>("schedule.blocks.create"),
    updateBlock: createMockMutation<any, any>("schedule.blocks.update"),
    deleteBlock: createMockMutation<any, any>("schedule.blocks.delete"),
    sendOffer: createMockMutation<any, any>("offers.organization.send"),
    acceptApplication: createMockMutation<any, any>(
      "applications.organization.approve",
    ),
    rejectApplication: createMockMutation<any, any>(
      "applications.organization.reject",
    ),
    withdrawOffer: createMockMutation<any, any>("offers.organization.withdraw"),
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      loading: true,
      data: undefined,
    },
  },
};

export const Draft: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        status: "DRAFT",
        summary: "Pediatric Nurse - Draft",
        role: "Nurse",
        scope: "Draft job posting for a pediatric nurse position.",
        organization: organizations.pediatricCenter,
        location: locations.outpatientClinic,
        department: departments.pediatrics,
        specialties: [specialties.pediatrics],
        schedule: createDayShiftSchedule(),
        paymentType: "HOURLY",
        paymentAmount: 75,
        publishedAt: null,
      }),
    },
  },
};

export const Filled: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        status: "FILLED",
        summary: "Cardiologist - Filled Position",
        role: "Cardiologist",
        scope: "This position has been filled by a qualified cardiologist.",
        organization: organizations.memorialHealth,
        location: locations.mainHospital,
        department: departments.cardiology,
        specialties: [specialties.cardiology],
        schedule: createDayShiftSchedule(),
        paymentType: "FIXED",
        paymentAmount: 25000,
        filledAt: new Date(),
      }),
    },
  },
};

export const Cancelled: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        status: "CANCELLED",
        summary: "Neurologist - Cancelled Position",
        role: "Neurologist",
        scope: "This position has been cancelled and is no longer available.",
        organization: organizations.universityMedical,
        location: locations.mainHospital,
        department: departments.neurology,
        specialties: [specialties.neurology],
        schedule: createDayShiftSchedule(),
        paymentType: "HOURLY",
        paymentAmount: 180,
        cancelledAt: new Date(),
      }),
    },
  },
};

export const Completed: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        status: "COMPLETED",
        summary: "Family Physician - Completed Assignment",
        role: "Family Physician",
        scope: "This temporary assignment has been completed successfully.",
        organization: organizations.communityHealth,
        location: locations.outpatientClinic,
        department: departments.emergency,
        specialties: [specialties.familyMedicine],
        schedule: createDayShiftSchedule(),
        paymentType: "FIXED",
        paymentAmount: 15000,
        completedAt: new Date(),
      }),
    },
  },
};

export const WeekendShift: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        summary: "Weekend ER Physician",
        role: "Physician",
        scope: "Weekend coverage for our busy emergency department.",
        organization: organizations.cityGeneral,
        location: locations.emergencyCenter,
        department: departments.emergency,
        specialties: [specialties.emergencyMedicine],
        schedule: createWeekendSchedule(),
        paymentType: "HOURLY",
        paymentAmount: 175,
      }),
    },
  },
};

export const SurgicalPosition: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        summary: "General Surgeon - Immediate Opening",
        role: "Surgeon",
        scope:
          "Immediate opening for a general surgeon to join our surgical team.",
        organization: organizations.memorialHealth,
        location: locations.surgicalCenter,
        department: departments.surgery,
        specialties: [specialties.generalSurgery],
        schedule: createDayShiftSchedule(),
        paymentType: "FIXED",
        paymentAmount: 2000, // per day
      }),
    },
  },
};

export const AnesthesiologyPosition: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: createJob({
        summary: "Anesthesiologist - Surgical Center",
        role: "Anesthesiologist",
        scope:
          "Seeking an experienced anesthesiologist for our busy surgical center.",
        organization: organizations.universityMedical,
        location: locations.surgicalCenter,
        department: departments.surgery,
        specialties: [specialties.anesthesiology],
        schedule: createDayShiftSchedule(),
        paymentType: "HOURLY",
        paymentAmount: 200,
      }),
    },
  },
};

export const Error: Story = {
  args: {
    ...Default.args,
    job: {
      ...Default.args.job,
      data: undefined,
      error: {
        message: "Failed to load job details",
        data: { code: "NOT_FOUND", httpStatus: 404 },
        shape: { message: "Failed to load job details" },
      },
    },
  },
};
