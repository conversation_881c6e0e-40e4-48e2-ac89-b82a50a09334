import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { RouterOutputs, ScheduleType } from "@/api";
import type { api } from "@/api/client";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";
import JobSchedule from "@/www/organizations/job/schedule/JobSchedule";

const meta = {
  title: "Pages/Organizations/JobDetails/JobSchedule",
  component: JobSchedule,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof JobSchedule>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock schedule data
const mockSchedule = {
  id: "schedule_1",
  startsAt: new Date("2024-02-01"),
  endsAt: new Date("2024-03-01"),
  blocks: [
    {
      id: "block_1",
      type: TimeBlockType.SHIFT,
      startDate: new Date("2024-02-01"),
      endDate: new Date("2024-02-28"),
      startsAt: new Date("2024-02-01T09:00:00"),
      endsAt: new Date("2024-02-01T17:00:00"),
      hours: 8,
      timeZone: "America/New_York",
      dayOfWeek: 1,
      recurrence: TimeBlockRecurrence.WEEKLY,
    },
    {
      id: "block_2",
      type: TimeBlockType.SHIFT,
      startDate: new Date("2024-02-03"),
      endDate: new Date("2024-02-24"),
      startsAt: new Date("2024-02-03T10:00:00"),
      endsAt: new Date("2024-02-03T18:00:00"),
      hours: 8,
      timeZone: "America/New_York",
      dayOfWeek: 3,
      recurrence: TimeBlockRecurrence.WEEKLY,
    },
  ],
};

// Mock job data with all required fields to match RouterOutputs["jobs"]["get"]
const mockJob = {
  id: "job_1",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-15"),
  deletedAt: null,
  expiresAt: new Date("2024-04-01"),
  startsAt: new Date("2024-02-01"),
  endsAt: new Date("2024-03-01"),
  publishedAt: new Date("2024-01-15"),
  filledAt: null,
  cancelledAt: null,
  completedAt: null,
  expiredAt: null,
  status: "PUBLISHED",
  mode: "INDEPENDENT",
  type: "PER_DIEM",
  priority: "HIGH",
  summary: "Emergency Room Physician",
  scope: "Looking for an experienced Emergency Room Physician",
  role: "Physician",
  paymentType: "HOURLY",
  paymentAmount: 150,
  paymentRate: 1,
  nightRate: 1.25,
  overtimeRate: 1.5,
  holidayRate: 2,
  bonusRate: 1,
  billingType: "HOURLY",
  billingRate: 1.15,
  isBillable: true,
  organizationId: "org_1",
  locationId: "loc_1",
  scheduleId: "schedule_1",
  departmentId: "dept_1",
  providerId: null,
  schedule: mockSchedule,
  location: {
    id: "loc_1",
    name: "Main Hospital",
    address: {
      formatted: "123 Main St, City, State 12345",
      timeZone: "America/New_York",
    },
  },
  organization: {
    id: "org_1",
    name: "City General Hospital",
  },
  department: {
    id: "dept_1",
    name: "Emergency Department",
  },
  specialties: [{ id: "spec_1", name: "Emergency Medicine" }],
  analytics: {
    applicants: 5,
    offers: 2,
    contracts: 1,
    payment: {
      min: 140,
      max: 160,
      avg: 150,
    },
  },
  context: null,
  documents: [],
  contacts: [],
  actions: [],
  provider: null,
  address: null,
  manager: null,
  recruiter: null,
  supervisor: null,
  coordinator: null,
  thread: null,
  contracts: [],
  shifts: [],
  applications: [],
  offers: [],
} as unknown as RouterOutputs["jobs"]["get"];

// Mock mutation base
const baseMockMutation = {
  data: undefined,
  error: null,
  isError: false,
  isIdle: true,
  isLoading: false,
  isSuccess: false,
  mutate: () => {},
  reset: () => {},
  status: "idle" as const,
  variables: undefined,
  failureCount: 0,
  failureReason: null,
  isPaused: false,
  context: undefined,
  trpc: { path: "" },
  isPending: false,
  submittedAt: Date.now(),
} as const;

// Mock mutations
const mockCreateScheduleMutation = {
  ...baseMockMutation,
  mutateAsync: async (variables: { jobId: string; type: ScheduleType }) => ({
    id: "new_schedule_id",
    jobId: variables.jobId,
    type: variables.type,
  }),
};

const mockCreateBlockMutation = {
  ...baseMockMutation,
  mutateAsync: async (variables: {
    scheduleId: string;
    type: TimeBlockType;
    startDate: Date;
    endDate: Date;
    startsAt?: Date;
    endsAt?: Date;
    hours: number;
    timeZone: string;
    recurrence?: TimeBlockRecurrence;
  }) => ({
    id: "new_block_id",
    ...variables,
  }),
};

const mockUpdateBlockMutation = {
  ...baseMockMutation,
  mutateAsync: async (variables: {
    id: string;
    data: {
      type: TimeBlockType;
      startDate: Date;
      endDate: Date;
      startsAt?: Date;
      endsAt?: Date;
      hours: number;
      timeZone: string;
      recurrence?: TimeBlockRecurrence;
    };
  }) => ({
    id: variables.id,
    ...variables.data,
  }),
};

const mockDeleteBlockMutation = {
  ...baseMockMutation,
  mutateAsync: async (variables: { id: string }) => ({
    id: variables.id,
  }),
};

const mockUpdateScheduleMutation = {
  ...baseMockMutation,
  mutateAsync: async (variables: {
    id: string;
    data: {
      type?: ScheduleType;
      name?: string;
      startsAt?: Date;
      endsAt?: Date;
    };
  }) => ({
    id: variables.id,
    ...variables.data,
  }),
};

export const Default: Story = {
  args: {
    job: mockJob,
    createSchedule: mockCreateScheduleMutation as unknown as ReturnType<
      typeof api.schedule.create.useMutation
    >,
    createBlock: mockCreateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.create.useMutation
    >,
    updateBlock: mockUpdateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.update.useMutation
    >,
    deleteBlock: mockDeleteBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.delete.useMutation
    >,
    updateSchedule: mockUpdateScheduleMutation as unknown as ReturnType<
      typeof api.schedule.update.useMutation
    >,
  },
};

export const NoSchedule: Story = {
  args: {
    job: {
      ...mockJob,
      schedule: null,
    } as unknown as RouterOutputs["jobs"]["get"],
    createSchedule: mockCreateScheduleMutation as unknown as ReturnType<
      typeof api.schedule.create.useMutation
    >,
    createBlock: mockCreateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.create.useMutation
    >,
    updateBlock: mockUpdateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.update.useMutation
    >,
    deleteBlock: mockDeleteBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.delete.useMutation
    >,
    updateSchedule: mockUpdateScheduleMutation as unknown as ReturnType<
      typeof api.schedule.update.useMutation
    >,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const PendingMutation: Story = {
  args: {
    job: mockJob,
    createSchedule: {
      ...mockCreateScheduleMutation,
      isPending: true,
    } as unknown as ReturnType<typeof api.schedule.create.useMutation>,
    createBlock: mockCreateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.create.useMutation
    >,
    updateBlock: mockUpdateBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.update.useMutation
    >,
    deleteBlock: mockDeleteBlockMutation as unknown as ReturnType<
      typeof api.schedule.blocks.delete.useMutation
    >,
    updateSchedule: mockUpdateScheduleMutation as unknown as ReturnType<
      typeof api.schedule.update.useMutation
    >,
  },
};
