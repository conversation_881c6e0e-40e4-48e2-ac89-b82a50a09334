import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { OfferStatus, ProviderStatus, VerificationStatus } from "@/api";
import ProspectingOffers from "@/www/organizations/job/prospecting/ProspectingOffers";

import { createMockMutation } from "../../../helpers";

// Set a seed for reproducible results
faker.seed(123);

const meta = {
  title: "Pages/Organizations/Job/ProspectingOffers",
  component: ProspectingOffers,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof ProspectingOffers>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type OfferStructure = NonNullable<
  RouterOutputs["providers"]["prospecting"]["offers"]["items"]
>[number];

type ProviderStructure = NonNullable<OfferStructure["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type VerificationStructure = NonNullable<ProviderStructure["verification"]>;
type JobStructure = NonNullable<OfferStructure["job"]>;

// Helper functions to create mock data
function createPerson(
  options: {
    firstName?: string;
    lastName?: string;
    avatar?: string | null;
    email?: string | null;
    phone?: string | null;
  } = {},
): PersonStructure {
  return {
    id: faker.string.uuid(),
    firstName: options.firstName ?? faker.person.firstName(),
    lastName: options.lastName ?? faker.person.lastName(),
    avatar: options.avatar ?? faker.helpers.maybe(() => faker.image.avatar()),
    email: options.email ?? faker.internet.email(),
    phone: options.phone ?? faker.phone.number(),
  };
}

function createVerification(
  options: {
    status?: VerificationStatus;
  } = {},
): VerificationStructure {
  return {
    id: faker.string.uuid(),
    status:
      options.status ??
      faker.helpers.arrayElement(Object.values(VerificationStatus)),
    backgroundCheckStatus: faker.helpers.arrayElement([
      "VERIFIED",
      "PENDING",
      "FAILED",
    ]),
    i9VerificationStatus: faker.helpers.arrayElement([
      "VERIFIED",
      "PENDING",
      "FAILED",
    ]),
    identityVerificationStatus: faker.helpers.arrayElement([
      "VERIFIED",
      "PENDING",
      "FAILED",
    ]),
    verifiedAt: faker.helpers.maybe(() => faker.date.recent().toISOString()),
    backgroundVerifiedAt: faker.helpers.maybe(() =>
      faker.date.recent().toISOString(),
    ),
    i9VerifiedAt: faker.helpers.maybe(() => faker.date.recent().toISOString()),
    identityVerifiedAt: faker.helpers.maybe(() =>
      faker.date.recent().toISOString(),
    ),
  };
}

function createProvider(
  options: {
    title?: string;
    status?: ProviderStatus;
    score?: number;
    person?: PersonStructure;
    verification?: VerificationStructure;
  } = {},
): ProviderStructure {
  return {
    id: faker.string.uuid(),
    title:
      options.title ??
      faker.helpers.arrayElement([
        "Registered Nurse",
        "Physician",
        "Nurse Practitioner",
        "Physician Assistant",
        "Medical Assistant",
        "Surgeon",
        "Anesthesiologist",
      ]),
    status: options.status ?? ProviderStatus.ACTIVE,
    score:
      options.score ??
      faker.number.float({ min: 0.1, max: 1.0, precision: 0.01 }),
    spokenLanguages: faker.helpers.arrayElements(
      ["English", "Spanish", "French", "Mandarin", "Arabic", "Hindi"],
      faker.number.int({ min: 1, max: 3 }),
    ),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    person: options.person ?? createPerson(),
    verification: options.verification ?? createVerification(),
  };
}

function createJob(
  options: {
    id?: string;
    title?: string;
    status?: string;
  } = {},
): JobStructure {
  return {
    id: options.id ?? faker.string.uuid(),
  };
}

function createOffer(
  options: {
    status?: OfferStatus;
    provider?: ProviderStructure;
    notes?: string;
    createdAt?: string;
    updatedAt?: string;
    expiresAt?: string | null;
    job?: JobStructure;
  } = {},
): OfferStructure {
  const createdAt = options.createdAt ?? faker.date.recent().toISOString();

  return {
    id: faker.string.uuid(),
    status:
      options.status ?? faker.helpers.arrayElement(Object.values(OfferStatus)),
    notes: options.notes ?? faker.helpers.maybe(() => faker.lorem.paragraph()),
    createdAt,
    updatedAt:
      options.updatedAt ??
      faker.date
        .between({ from: new Date(createdAt), to: new Date() })
        .toISOString(),
    expiresAt:
      options.expiresAt ??
      faker.helpers.maybe(() => faker.date.future().toISOString()),
    provider: options.provider ?? createProvider(),
    job: options.job ?? createJob(),
    providerId: faker.string.uuid(),
    jobId: faker.string.uuid(),
    organizationId: faker.string.uuid(),
  };
}

// Create variants with different offer statuses
function createPendingOffer(): OfferStructure {
  return createOffer({
    status: OfferStatus.PENDING,
    expiresAt: faker.date.future().toISOString(),
    notes:
      "This is a pending offer that will expire soon. Please respond at your earliest convenience.",
    provider: createProvider({
      verification: createVerification({ status: VerificationStatus.VERIFIED }),
    }),
  });
}

function createAcceptedOffer(): OfferStructure {
  return createOffer({
    status: OfferStatus.ACCEPTED,
    notes: "Offer has been accepted. We look forward to working with you!",
    provider: createProvider({
      verification: createVerification({ status: VerificationStatus.VERIFIED }),
    }),
  });
}

function createRejectedOffer(): OfferStructure {
  return createOffer({
    status: OfferStatus.REJECTED,
    notes: "The provider has declined this offer.",
    provider: createProvider(),
  });
}

function createExpiredOffer(): OfferStructure {
  const pastDate = faker.date.past();
  return createOffer({
    status: OfferStatus.EXPIRED,
    createdAt: faker.date.past({ refDate: pastDate }).toISOString(),
    expiresAt: pastDate.toISOString(),
    notes: "This offer has expired without a response.",
    provider: createProvider(),
  });
}

function createWithdrawnOffer(): OfferStructure {
  return createOffer({
    status: OfferStatus.WITHDRAWN,
    notes: "This offer was withdrawn by the organization.",
    provider: createProvider(),
  });
}

// Create story variants
export const Default: Story = {
  args: {
    job: {
      loading: false,
      data: {
        id: faker.string.uuid(),
        title: "Emergency Room Physician",
        status: "ACTIVE",
      },
    },
    offers: {
      loading: false,
      data: {
        items: [
          createPendingOffer(),
          createAcceptedOffer(),
          createRejectedOffer(),
          createExpiredOffer(),
          createWithdrawnOffer(),
        ],
        total: 5,
      },
    },
    withdrawOffer: createMockMutation("offers.organization.withdraw"),
  },
};

export const PendingOffersOnly: Story = {
  args: {
    job: {
      loading: false,
      data: {
        id: faker.string.uuid(),
        title: "Nurse Practitioner",
        status: "ACTIVE",
      },
    },
    offers: {
      loading: false,
      data: {
        items: Array.from({ length: 3 }, () => createPendingOffer()),
        total: 3,
      },
    },
    withdrawOffer: createMockMutation("offers.organization.withdraw"),
  },
};

export const NoOffers: Story = {
  args: {
    job: {
      loading: false,
      data: {
        id: faker.string.uuid(),
        title: "Medical Assistant",
        status: "ACTIVE",
      },
    },
    offers: {
      loading: false,
      data: {
        items: [],
        total: 0,
      },
    },
    withdrawOffer: createMockMutation("offers.organization.withdraw"),
  },
};

export const Loading: Story = {
  args: {
    job: {
      loading: false,
      data: {
        id: faker.string.uuid(),
        title: "Surgeon",
        status: "ACTIVE",
      },
    },
    offers: {
      loading: true,
    },
    withdrawOffer: createMockMutation("offers.organization.withdraw"),
  },
};

export const WithError: Story = {
  args: {
    job: {
      loading: false,
      data: {
        id: faker.string.uuid(),
        title: "Cardiologist",
        status: "ACTIVE",
      },
    },
    offers: {
      loading: false,
      error: {
        message: "Failed to load offers",
        data: { code: "INTERNAL_SERVER_ERROR" },
      },
    },
    withdrawOffer: createMockMutation("offers.organization.withdraw"),
  },
};
