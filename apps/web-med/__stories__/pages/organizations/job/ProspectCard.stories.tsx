import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { ProviderStatus, VerificationStatus } from "@/api";
import { ProspectCard } from "@/www/organizations/job/prospecting/ProspectCard";

import { createMockMutation } from "../../../helpers";

const meta = {
  title: "Pages/Organizations/Job/ProspectCard",
  component: ProspectCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof ProspectCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type ProviderStructure = NonNullable<
  RouterOutputs["providers"]["prospecting"]["search"]["items"]
>[number];

type PersonStructure = NonNullable<ProviderStructure["person"]>;
type ExperienceStructure = NonNullable<
  ProviderStructure["experiences"]
>[number];
type SpecialtyStructure = NonNullable<ProviderStructure["specialties"]>[number];
type VerificationStructure = NonNullable<ProviderStructure["verification"]>;
type AddressStructure = NonNullable<ProviderStructure["address"]>;
type QualificationStructure = NonNullable<
  ProviderStructure["qualifications"]
>[number];
type ReviewStructure = NonNullable<ProviderStructure["reviews"]>[number];

// Helper functions to create mock data
function createPerson(
  options: {
    firstName?: string;
    lastName?: string;
    avatar?: string | null;
    email?: string | null;
    phone?: string | null;
  } = {},
): PersonStructure {
  return {
    id: faker.string.uuid(),
    firstName: options.firstName ?? faker.person.firstName(),
    lastName: options.lastName ?? faker.person.lastName(),
    avatar: options.avatar ?? faker.helpers.maybe(() => faker.image.avatar()),
    email: options.email ?? faker.internet.email(),
    phone: options.phone ?? faker.phone.number(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

function createExperience(
  options: {
    title?: string;
    company?: string;
    startDate?: string;
    endDate?: string | null;
  } = {},
): ExperienceStructure {
  const startDate =
    options.startDate ?? faker.date.past({ years: 10 }).toISOString();

  return {
    id: faker.string.uuid(),
    title: options.title ?? faker.person.jobTitle(),
    company: options.company ?? faker.company.name(),
    startDate,
    endDate:
      options.endDate ??
      faker.helpers.maybe(() =>
        faker.date.between({ from: startDate, to: new Date() }).toISOString(),
      ),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

function createSpecialty(
  options: {
    name?: string;
  } = {},
): SpecialtyStructure {
  return {
    id: faker.string.uuid(),
    name:
      options.name ??
      faker.helpers.arrayElement([
        "Cardiology",
        "Neurology",
        "Pediatrics",
        "Emergency Medicine",
        "Oncology",
        "Orthopedics",
        "Psychiatry",
        "Radiology",
        "Surgery",
        "Anesthesiology",
      ]),
  };
}

function createVerification(
  options: {
    status?: VerificationStatus;
  } = {},
): VerificationStructure {
  return {
    id: faker.string.uuid(),
    status:
      options.status ??
      faker.helpers.arrayElement(Object.values(VerificationStatus)),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

function createAddress(
  options: {
    timeZone?: string;
  } = {},
): AddressStructure {
  return {
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    country: faker.location.country(),
    postalCode: faker.location.zipCode(),
    timeZone:
      options.timeZone ??
      faker.helpers.arrayElement([
        "America/New_York",
        "America/Chicago",
        "America/Denver",
        "America/Los_Angeles",
        "America/Phoenix",
      ]),
    formatted: faker.location.streetAddress({ useFullAddress: true }),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

function createQualification(
  options: {
    name?: string;
    type?: string;
  } = {},
): QualificationStructure {
  return {
    id: faker.string.uuid(),
    name:
      options.name ??
      faker.helpers.arrayElement([
        "Board Certified",
        "ABPN",
        "ACLS",
        "BLS",
        "RN",
        "PALS",
        "NRP",
      ]),
    type:
      options.type ??
      faker.helpers.arrayElement(["CERTIFICATION", "LICENSE", "EDUCATION"]),
    identifier: faker.string.alphanumeric(8).toUpperCase(),
    institution: faker.company.name(),
    status: faker.helpers.arrayElement(["ACTIVE", "PENDING", "EXPIRED"]),
    state: faker.location.state(),
    country: faker.location.country(),
    startDate: faker.date.past().toISOString(),
    endDate: faker.date.future().toISOString(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

function createReview(
  options: {
    rating?: number;
    comment?: string;
  } = {},
): ReviewStructure {
  return {
    id: faker.string.uuid(),
    rating: options.rating ?? faker.number.int({ min: 1, max: 5 }),
    comment: options.comment ?? faker.lorem.paragraph(),
    createdAt: faker.date.past().toISOString(),
    reviewer: {
      id: faker.string.uuid(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      avatar: faker.helpers.maybe(() => faker.image.avatar()),
    },
  };
}

function createProvider(
  options: {
    title?: string;
    status?: ProviderStatus;
    score?: number;
    person?: PersonStructure;
    experiences?: ExperienceStructure[];
    specialties?: SpecialtyStructure[];
    verification?: VerificationStructure;
    address?: AddressStructure;
    qualifications?: QualificationStructure[];
    reviews?: ReviewStructure[];
    spokenLanguages?: string[];
  } = {},
): ProviderStructure {
  const person = options.person ?? createPerson();
  const experiences =
    options.experiences ??
    Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
      createExperience(),
    );
  const specialties =
    options.specialties ??
    Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
      createSpecialty(),
    );
  const qualifications =
    options.qualifications ??
    Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
      createQualification(),
    );
  const reviews =
    options.reviews ??
    Array.from({ length: faker.number.int({ min: 0, max: 5 }) }, () =>
      createReview(),
    );

  return {
    id: faker.string.uuid(),
    title:
      options.title ??
      faker.helpers.arrayElement(["MD", "DO", "NP", "PA", "RN", "CRNA"]),
    status:
      options.status ??
      faker.helpers.arrayElement(Object.values(ProviderStatus)),
    score:
      options.score ??
      faker.number.float({ min: 0.5, max: 1, precision: 0.01 }),
    person,
    experiences,
    specialties,
    verification: options.verification ?? createVerification(),
    address: options.address ?? createAddress(),
    qualifications,
    reviews,
    spokenLanguages:
      options.spokenLanguages ??
      faker.helpers.maybe(() =>
        faker.helpers.arrayElements(
          ["English", "Spanish", "French", "Mandarin", "Arabic"],
          { min: 1, max: 3 },
        ),
      ),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  };
}

// Create story variants
export const Default: Story = {
  args: {
    provider: createProvider({
      title: "RN",
      person: createPerson({
        firstName: "Michael",
        lastName: "Lee",
      }),
      experiences: [
        createExperience({
          title: "Registered Nurse",
          company: "City General Hospital",
          startDate: "2018-01-01T00:00:00.000Z",
          endDate: null,
        }),
      ],
      verification: createVerification({ status: VerificationStatus.VERIFIED }),
      qualifications: [
        createQualification({ name: "RN" }),
        createQualification({ name: "BLS" }),
      ],
      spokenLanguages: ["English", "Mandarin"],
      reviews: [
        createReview({ rating: 4, comment: "Great nurse, very attentive" }),
        createReview({ rating: 4, comment: "Professional and knowledgeable" }),
        createReview({ rating: 4, comment: "Would recommend" }),
      ],
    }),
    isLoading: false,
    isError: false,
    onSendOffer: async () => {
      console.log("Send offer clicked");
    },
    onViewProfile: () => {
      console.log("View profile clicked");
    },
    onRetry: () => {
      console.log("Retry clicked");
    },
  },
};

export const Verified: Story = {
  args: {
    ...Default.args,
    provider: createProvider({
      score: 0.95,
      person: createPerson({
        firstName: "Sarah",
        lastName: "Johnson",
      }),
      title: "MD",
      verification: createVerification({ status: VerificationStatus.VERIFIED }),
      qualifications: [
        createQualification({ name: "Board Certified" }),
        createQualification({ name: "ABPN" }),
      ],
      spokenLanguages: ["English", "Spanish"],
      experiences: [
        createExperience({
          title: "Cardiologist",
          company: "Mayo Clinic",
          startDate: "2015-01-01T00:00:00.000Z",
          endDate: null,
        }),
        createExperience({
          title: "Resident Physician",
          company: "Johns Hopkins Hospital",
          startDate: "2010-01-01T00:00:00.000Z",
          endDate: "2014-12-31T00:00:00.000Z",
        }),
      ],
      reviews: Array.from({ length: 5 }, () => createReview({ rating: 5 })),
    }),
  },
};

export const Unverified: Story = {
  args: {
    ...Default.args,
    provider: createProvider({
      score: 0.51,
      person: createPerson({
        firstName: "Michael",
        lastName: "Smith",
      }),
      title: "NP",
      verification: createVerification({ status: VerificationStatus.PENDING }),
      qualifications: [createQualification({ name: "NP" })],
      spokenLanguages: ["English"],
      experiences: [
        createExperience({
          title: "Nurse Practitioner",
          company: "Community Health Center",
          startDate: "2018-01-01T00:00:00.000Z",
          endDate: null,
        }),
      ],
      reviews: [
        createReview({ rating: 3, comment: "Average experience" }),
        createReview({ rating: 4, comment: "Good but could improve" }),
      ],
    }),
  },
};

export const ManyQualifications: Story = {
  args: {
    ...Default.args,
    provider: createProvider({
      person: createPerson({
        firstName: "Jessica",
        lastName: "Williams",
      }),
      title: "MD",
      qualifications: [
        createQualification({ name: "Board Certified" }),
        createQualification({ name: "ACLS" }),
        createQualification({ name: "PALS" }),
        createQualification({ name: "BLS" }),
      ],
      specialties: [
        createSpecialty({ name: "Emergency Medicine" }),
        createSpecialty({ name: "Critical Care" }),
        createSpecialty({ name: "Trauma Surgery" }),
      ],
    }),
  },
};

export const LongExperience: Story = {
  args: {
    ...Default.args,
    provider: createProvider({
      person: createPerson({
        firstName: "Robert",
        lastName: "Davis",
      }),
      title: "MD",
      experiences: [
        createExperience({
          title: "Chief of Surgery",
          company: "Mount Sinai Hospital",
          startDate: "2015-01-01T00:00:00.000Z",
          endDate: null,
        }),
        createExperience({
          title: "Attending Surgeon",
          company: "NYU Langone Health",
          startDate: "2005-01-01T00:00:00.000Z",
          endDate: "2014-12-31T00:00:00.000Z",
        }),
        createExperience({
          title: "Surgical Resident",
          company: "Massachusetts General Hospital",
          startDate: "2000-01-01T00:00:00.000Z",
          endDate: "2004-12-31T00:00:00.000Z",
        }),
      ],
    }),
  },
};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const Error: Story = {
  args: {
    isError: true,
    onRetry: () => {
      console.log("Retry clicked");
    },
  },
};
