import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  ContractsQueryResult,
  DocumentsQueryResult,
} from "@/www/organizations/documents/Documents";

import { ContractStatus } from "@/api";
import Documents from "@/www/organizations/documents/Documents";

const meta = {
  title: "Pages/Organizations/Documents/page",
  component: Documents,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/documents",
      },
    },
  },
} satisfies Meta<typeof Documents>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for documents
const mockDocuments: DocumentsQueryResult = {
  items: Array.from({ length: 5 }, () => ({
    id: faker.string.uuid(),
    name: faker.system.fileName(),
    url: faker.internet.url(),
    type: faker.lorem.word(),
    size: faker.number.int({ min: 1000, max: 10000000 }),
    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
    },
  })),
  total: 5,
};

// Mock data for contracts
const mockContracts: ContractsQueryResult = {
  items: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    name: faker.commerce.productName(),
    description: faker.lorem.sentence(),
    status: faker.helpers.arrayElement([
      ContractStatus.DRAFT,
      ContractStatus.PENDING,
      ContractStatus.EXPIRED,
      ContractStatus.REJECTED,
      ContractStatus.SIGNED,
    ]),
    startDate: faker.date.recent(),
    endDate: faker.date.future(),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
    },
  })),
  total: 3,
};

export const Default: Story = {
  args: {
    documents: {
      data: mockDocuments,
      loading: false,
      error: null,
    },
    contracts: {
      data: mockContracts,
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    documents: {
      loading: true,
    },
    contracts: {
      loading: true,
    },
  },
};

export const DocumentsError: Story = {
  args: {
    documents: {
      error: {
        message: "Failed to load documents",
      },
    },
    contracts: {
      data: mockContracts,
    },
  },
};

export const ContractsError: Story = {
  args: {
    documents: {
      data: mockDocuments,
    },
    contracts: {
      error: {
        message: "Failed to load contracts",
      },
    },
  },
};
