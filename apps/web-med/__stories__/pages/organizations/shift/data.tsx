import { faker } from "@faker-js/faker";

import type {
  DepartmentType,
  FacilityType,
  PaymentType,
  PersonRole,
  ProviderStatus,
  RouterOutputs,
  ShiftStatus,
} from "@/api";

import { IncidentSeverity, IncidentStatus, IncidentType } from "@/api";

// Define types based on API
type ShiftGetQuery = RouterOutputs["shifts"]["get"];
type OrganizationStructure = NonNullable<ShiftGetQuery["organization"]>;
type LocationStructure = NonNullable<ShiftGetQuery["location"]>;
type AddressStructure = NonNullable<LocationStructure["address"]>;
type ProviderStructure = NonNullable<ShiftGetQuery["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type DepartmentStructure = NonNullable<ShiftGetQuery["department"]>;
type ContactStructure = NonNullable<DepartmentStructure["contacts"]>[number];
type SpecialtyStructure = NonNullable<ShiftGetQuery["specialties"]>[number];
type ReviewStructure = NonNullable<ShiftGetQuery["review"]>;
type IncidentStructure = NonNullable<ShiftGetQuery["incidents"]>[number];

// --- Mock Mutation Helper ---
// (Consider moving to a shared storybook helper if used across many stories)
export const createMockMutation = <
  TData = unknown,
  TVariables = unknown,
>(): Partial<any> => ({
  mutate: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
  },
  mutateAsync: async (vars: TVariables) => {
    console.log("[Storybook Mock Mutate Async]", vars);
    await new Promise((resolve) => setTimeout(resolve, 500));
    return { id: "mock-op-id" } as TData;
  },
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
  reset: () => {
    console.log("[Storybook Mock Reset]");
  },
});

// --- Helper functions to create mock data ---

export const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

export const createAddress = (
  options: {
    formatted?: string;
    timeZone?: string;
    latitude?: number;
    longitude?: number;
  } = {},
): AddressStructure => ({
  formatted:
    options.formatted ??
    `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`,
  timeZone: options.timeZone ?? "America/New_York",
  latitude: options.latitude ?? faker.location.latitude(),
  longitude: options.longitude ?? faker.location.longitude(),
});

export const createLocation = (
  options: {
    name?: string;
    type?: FacilityType;
    address?: AddressStructure;
  } = {},
): LocationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  type: options.type ?? "HOSPITAL",
  address: options.address ?? createAddress(),
});

export const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    role?: PersonRole;
    avatar?: string | null;
    email?: string | null;
    phone?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  role: options.role ?? "USER",
  avatar: options.avatar ?? faker.image.avatar(),
  email: options.email ?? faker.internet.email(),
  phone: options.phone ?? faker.phone.number(),
});

export const createContact = (
  options: {
    role?: string;
    person?: PersonStructure;
  } = {},
): ContactStructure => ({
  id: faker.string.uuid(),
  role: options.role ?? "Primary Contact",
  person: options.person ?? createPerson(),
});

export const createDepartment = (
  options: {
    name?: string;
    type?: DepartmentType;
    contacts?: ContactStructure[];
  } = {},
): DepartmentStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.commerce.department(),
  type: options.type ?? "DEPARTMENT",
  contacts: options.contacts ?? [
    createContact({ role: "Department Head" }),
    createContact({ role: "Nurse Manager" }),
  ],
});

export const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

export const createSpecialty = (
  options: {
    name?: string;
  } = {},
): SpecialtyStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.person.jobType(),
});

export const createReview = (
  options: {
    rating?: number;
    comment?: string | null;
  } = {},
): ReviewStructure => ({
  id: faker.string.uuid(),
  rating: options.rating ?? faker.number.int({ min: 1, max: 5 }),
  comment: options.comment ?? faker.lorem.paragraph(),
});

export const createIncident = (
  options: {
    title?: string;
    description?: string | null;
    type?: IncidentType;
    status?: IncidentStatus;
    severity?: IncidentSeverity;
    createdAt?: Date;
  } = {},
): IncidentStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.lorem.sentence(),
  description: options.description ?? faker.lorem.paragraph(),
  type: options.type ?? IncidentType.HEALTH,
  status: options.status ?? IncidentStatus.OPEN,
  severity: options.severity ?? IncidentSeverity.MINOR,
  createdAt: options.createdAt ?? faker.date.recent(),
});

// --- Create common mock data instances ---

export const organizations = {
  cityGeneral: createOrganization({ name: "City General Hospital" }),
  memorialHealth: createOrganization({ name: "Memorial Health System" }),
  universityMedical: createOrganization({ name: "University Medical Center" }),
};

export const locations = {
  mainHospital: createLocation({
    name: "Main Hospital",
    type: "HOSPITAL",
    address: createAddress({
      formatted: "123 Medical Center Dr, New York, NY 10001",
      timeZone: "America/New_York",
    }),
  }),
  emergencyCenter: createLocation({
    name: "Emergency Care Center",
    type: "CLINIC",
    address: createAddress({
      formatted: "456 Emergency Blvd, Boston, MA 02108",
      timeZone: "America/New_York",
    }),
  }),
  outpatientClinic: createLocation({
    name: "Outpatient Clinic",
    type: "CLINIC",
    address: createAddress({
      formatted: "789 Clinic Ave, Chicago, IL 60601",
      timeZone: "America/Chicago",
    }),
  }),
};

export const departments = {
  emergency: createDepartment({
    name: "Emergency Department",
    type: "DEPARTMENT",
    contacts: [
      createContact({
        role: "Department Head",
        person: createPerson({
          firstName: "Robert",
          lastName: "Johnson",
          role: "CLIENT",
        }),
      }),
      createContact({
        role: "Nurse Manager",
        person: createPerson({
          firstName: "Emily",
          lastName: "Williams",
          role: "CLIENT",
        }),
      }),
    ],
  }),
  cardiology: createDepartment({
    name: "Cardiology Department",
    type: "DEPARTMENT",
    contacts: [
      createContact({
        role: "Department Head",
        person: createPerson({
          firstName: "Michael",
          lastName: "Brown",
          role: "CLIENT",
        }),
      }),
    ],
  }),
  pediatrics: createDepartment({
    name: "Pediatrics Department",
    type: "DEPARTMENT",
    contacts: [
      createContact({
        role: "Department Head",
        person: createPerson({
          firstName: "Sarah",
          lastName: "Davis",
          role: "CLIENT",
        }),
      }),
    ],
  }),
};

export const providers = {
  drSmith: createProvider({
    title: "MD",
    status: "ACTIVE",
    person: createPerson({
      firstName: "John",
      lastName: "Smith",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
  nurseJohnson: createProvider({
    title: "RN",
    status: "ACTIVE",
    person: createPerson({
      firstName: "Sarah",
      lastName: "Johnson",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
  drPatel: createProvider({
    title: "MD",
    status: "ACTIVE",
    person: createPerson({
      firstName: "Raj",
      lastName: "Patel",
      role: "PROVIDER",
      email: "<EMAIL>",
      phone: "************",
    }),
  }),
};

export const specialties = {
  emergencyMedicine: createSpecialty({ name: "Emergency Medicine" }),
  cardiology: createSpecialty({ name: "Cardiology" }),
  pediatrics: createSpecialty({ name: "Pediatrics" }),
  neurology: createSpecialty({ name: "Neurology" }),
  criticalCare: createSpecialty({ name: "Critical Care" }),
};

// --- Shift Creation Function ---

export const createShift = (
  options: {
    id?: string;
    status?: ShiftStatus;
    summary?: string;
    scope?: string;
    role?: string;
    timeZone?: string;
    startDate?: Date;
    endDate?: Date;
    paymentType?: PaymentType;
    paymentRate?: number;
    paymentAmount?: number;
    paymentTotal?: number;
    hours?: number;
    overtimeHours?: number;
    nightTimeHours?: number;
    holidayTimeHours?: number;
    organization?: OrganizationStructure;
    location?: LocationStructure;
    provider?: ProviderStructure;
    department?: DepartmentStructure;
    specialties?: SpecialtyStructure[];
    review?: ReviewStructure | null;
    incidents?: IncidentStructure[];
    confirmedAt?: Date | null;
    startedAt?: Date | null;
    completedAt?: Date | null;
    cancelledAt?: Date | null;
    approvedAt?: Date | null;
  } = {},
): ShiftGetQuery => {
  const now = new Date();
  const eightHoursLater = new Date(now);
  eightHoursLater.setHours(now.getHours() + 8);

  return {
    id: options.id ?? faker.string.uuid(),
    status: options.status ?? "PENDING",
    summary: options.summary ?? "Morning Shift - Emergency Room",
    scope:
      options.scope ?? "General morning duties in the emergency department",
    role: options.role ?? "Physician",
    timeZone: options.timeZone ?? "America/New_York",
    startDate: options.startDate ?? now,
    endDate: options.endDate ?? eightHoursLater,
    paymentType: options.paymentType ?? "HOURLY",
    paymentRate: options.paymentRate ?? 1.0,
    paymentAmount: options.paymentAmount ?? 150,
    paymentTotal: options.paymentTotal ?? 1200,
    hours: options.hours ?? 8,
    overtimeHours: options.overtimeHours ?? 0,
    nightTimeHours: options.nightTimeHours ?? 0,
    holidayTimeHours: options.holidayTimeHours ?? 0,
    organizationId: options.organization?.id ?? organizations.cityGeneral.id,
    organization: options.organization ?? organizations.cityGeneral,
    location: options.location ?? locations.mainHospital,
    provider: options.provider ?? providers.drSmith,
    department: options.department ?? departments.emergency,
    specialties: options.specialties ?? [
      specialties.emergencyMedicine,
      specialties.criticalCare,
    ],
    review: options.review ?? null,
    incidents: options.incidents ?? [],
    confirmedAt: options.confirmedAt ?? null,
    startedAt: options.startedAt ?? null,
    completedAt: options.completedAt ?? null,
    cancelledAt: options.cancelledAt ?? null,
    approvedAt: options.approvedAt ?? null,
    // Add missing properties that might be expected in ShiftGetQuery
    job: { id: faker.string.uuid() },
    invoice: { id: faker.string.uuid() },
    actions: [],
    contacts: [],
    holidayAmount: 0,
    nightAmount: 0,
    overtimeAmount: 0,
  } as unknown as ShiftGetQuery;
};

// --- Base Shift Export ---
export const baseShift = createShift();

// --- Export Type ---
// Exporting the main type might be useful
export type ShiftDataType = ShiftGetQuery;
