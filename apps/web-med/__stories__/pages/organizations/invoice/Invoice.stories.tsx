import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { InvoiceStatus, PersonRole, ProviderStatus, ShiftStatus } from "@/api";
import Invoice from "@/www/organizations/invoice/Invoice";

const meta = {
  title: "Pages/Organizations/Invoice/page",
  component: Invoice,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/billing/invoices/abc-123",
      },
    },
  },
} satisfies Meta<typeof Invoice>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockInvoice: RouterOutputs["billing"]["invoices"]["get"] = {
  id: faker.string.uuid(),
  number: faker.finance.accountNumber(),
  date: faker.date.recent(),
  due: faker.date.future(),
  total: faker.number.int({ min: 500, max: 5000 }),
  status: faker.helpers.arrayElement([
    InvoiceStatus.PAID,
    InvoiceStatus.DRAFT,
    InvoiceStatus.OPEN,
    InvoiceStatus.DUE,
    InvoiceStatus.VOID,
  ]),
  organization: {
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: faker.image.avatar(),
  },
  shifts: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    status: ShiftStatus.COMPLETED,
    summary: faker.helpers.arrayElement([
      "Morning Shift",
      "Evening Shift",
      "Night Shift",
    ]),
    scope: faker.helpers.arrayElement([
      "Primary Care",
      "Emergency",
      "Specialized Care",
    ]),
    startDate: faker.date.recent(),
    endDate: faker.date.soon(),
    hours: faker.number.float({ min: 4, max: 12, precision: 0.1 }),
    paymentRate: faker.number.float({ min: 50, max: 200, precision: 0.01 }),
    paymentAmount: faker.number.float({ min: 200, max: 2000, precision: 0.01 }),
    overtimeAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    holidayAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    nightAmount: faker.number.float({ min: 0, max: 500, precision: 0.01 }),
    paymentTotal: faker.number.float({ min: 500, max: 3000, precision: 0.01 }),
    provider: {
      id: faker.string.uuid(),
      status: ProviderStatus.ACTIVE,
      title: null,
      person: {
        id: faker.string.uuid(),
        role: PersonRole.PROVIDER,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: faker.image.avatar(),
      },
    },
  })),
};

export const Default: Story = {
  args: {
    invoice: mockInvoice,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: new Error("Failed to load invoice"),
  },
};

export const Empty: Story = {
  args: {
    invoice: {
      ...mockInvoice,
      shifts: [],
    },
  },
};
