import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import { OfferView } from "@/www/organizations/offer";

import { offers } from "./data";

const meta = {
  title: "Pages/Organizations/Offer/Details",
  component: OfferView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/organizations/offer/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.offers.get.query(({ input }) => {
          // Return the appropriate offer based on the ID
          const id = input.id;

          if (id === "abc-123") {
            return offers.pending;
          } else if (id === "abc-124") {
            return offers.accepted;
          } else if (id === "abc-125") {
            return offers.rejected;
          }

          // Default fallback
          return offers.pending;
        }),

        trpcMsw.contracts.get.query(({ input }) => {
          // Return contract data if the offer has a contract
          const id = input.id;

          if (offers.accepted.contractId === id) {
            return offers.accepted.contract;
          }

          // Default fallback to null if no contract found
          return null;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the offer with the matching thread ID
          const offer = Object.values(offers).find((o) => o.threadId === id);

          if (offer?.thread) {
            return {
              ...offer.thread,
              author: {
                id: "author-id",
                firstName: "System",
                lastName: "User",
                avatar: null,
              },
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof OfferView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Default: Story = {
  args: {
    loading: false,
    offerId: "abc-123",
  },
};

export const WithContract: Story = {
  args: {
    loading: false,
    offerId: "abc-124",
  },
};

export const Rejected: Story = {
  args: {
    loading: false,
    offerId: "abc-125",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    offerId: "abc-126",
  },
};
