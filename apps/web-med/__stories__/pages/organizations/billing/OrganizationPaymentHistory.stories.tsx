import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import OrganizationPaymentHistory from "@/www/organizations/billing/OrganizationPaymentHistory";

const meta = {
  title: "Pages/Organizations/Billing/OrganizationPaymentHistory",
  component: OrganizationPaymentHistory,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationPaymentHistory>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock organization
const mockOrganization: RouterOutputs["organizations"]["get"]["organization"] =
  {
    id: faker.string.uuid(),
    name: faker.company.name(),
    customerId: faker.string.uuid(),
    // Add other required properties as needed
  } as RouterOutputs["organizations"]["get"]["organization"];

// Mock payment history with medical-related items
const mockHistory: RouterOutputs["billing"]["history"] = [
  // Credit card payment for medical supplies - succeeded
  {
    id: faker.string.uuid(),
    amount: 12500,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Credit Card Payment for Medical Supplies - Invoice #MED-12345",
    duration: 0,
    date: faker.date.recent(5),
    price: "$125.00",
  },
  // Bank transfer for staff salaries - processing
  {
    id: faker.string.uuid(),
    amount: 450000,
    status: "processing",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Bank Transfer for Staff Salaries - Invoice #PAY-67890",
    duration: 0,
    date: faker.date.recent(10),
    price: "$4,500.00",
  },
  // Credit card payment for patient billing software - canceled
  {
    id: faker.string.uuid(),
    amount: 7500,
    status: "canceled",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Credit Card Payment for Patient Billing Software Subscription - Invoice #SFT-23456",
    duration: 0,
    date: faker.date.recent(15),
    price: "$75.00",
  },
  // Bank transfer for medical equipment - requires_payment_method
  {
    id: faker.string.uuid(),
    amount: 180000,
    status: "requires_payment_method",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Bank Transfer for Medical Equipment Purchase - Invoice #EQP-78901",
    duration: 0,
    date: faker.date.recent(20),
    price: "$1,800.00",
  },
  // Credit card payment for laboratory services - succeeded
  {
    id: faker.string.uuid(),
    amount: 32500,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Payment for Laboratory Services - Invoice #LAB-34567",
    duration: 0,
    date: faker.date.recent(25),
    price: "$325.00",
  },
  // Bank transfer for facility rent - succeeded
  {
    id: faker.string.uuid(),
    amount: 250000,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Transfer for Facility Rent - Invoice #RENT-89012",
    duration: 0,
    date: faker.date.recent(30),
    price: "$2,500.00",
  },
  // Credit card payment for medical insurance - processing
  {
    id: faker.string.uuid(),
    amount: 95000,
    status: "processing",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Payment for Medical Malpractice Insurance - Invoice #INS-45678",
    duration: 0,
    date: faker.date.recent(35),
    price: "$950.00",
  },
];

export const Default: Story = {
  args: {
    organization: mockOrganization,
    history: mockHistory,
  },
};

export const Loading: Story = {
  args: {
    organization: mockOrganization,
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    organization: mockOrganization,
    error: new Error("Failed to load payment history"),
  },
};

export const Empty: Story = {
  args: {
    organization: mockOrganization,
    history: [],
  },
};
