import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  OrganizationClass,
  OrganizationMode,
  OrganizationStatus,
  OrganizationType,
  ScheduleType,
} from "@/api";
import OrganizationPaymentMethods from "@/www/organizations/billing/OrganizationPaymentMethods";

const meta = {
  title: "Pages/Organizations/Billing/OrganizationPaymentMethods",
  component: OrganizationPaymentMethods,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationPaymentMethods>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockOrganization: RouterOutputs["organizations"]["get"]["organization"] =
  {
    id: faker.string.uuid(),
    name: faker.company.name(),
    type: OrganizationType.CLIENT,
    class: OrganizationClass.PRIVATE,
    status: OrganizationStatus.ACTIVE,
    mode: OrganizationMode.INDEPENDENT,
    phone: faker.phone.number(),
    email: faker.internet.email(),
    avatar: faker.image.avatar(),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    approvedAt: faker.date.past(),
    rejectedAt: null,
    // Finance fields
    balance: faker.number.int({ min: 1000, max: 10000 }),
    score: faker.number.int({ min: 50, max: 100 }),
    assistPercentage: faker.number.int({ min: 5, max: 15 }),
    basePercentage: faker.number.int({ min: 85, max: 95 }),
    billingFrequency: "MONTHLY",
    accountId: faker.string.uuid(),
    customerId: faker.string.uuid(),
    // Required fields from the API
    parent: null,
    manager: undefined,
    address: {
      id: faker.string.uuid(),
      formatted: faker.location.streetAddress(),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
      country: faker.location.country(),
      timeZone: faker.location.timeZone(),
    },
    schedule: {
      id: faker.string.uuid(),
      type: ScheduleType.ORGANIZATION,
      name: null,
      startsAt: null,
      endsAt: null,
      blocks: [],
    },
    contracts: [],
  };

const mockAccount: RouterOutputs["billing"]["accounts"]["organization"]["get"] =
  {
    id: faker.string.uuid(),
    enabled: true,
    submitted: true,
    requirements: {
      currently_due: [],
      eventually_due: [],
      past_due: [],
      pending_verification: [],
      alternatives: null,
      current_deadline: null,
      disabled_reason: null,
      errors: [],
    },
  };

const mockMethods: RouterOutputs["billing"]["methods"] = [
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
  },
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "mastercard",
    last4: "8888",
    expMonth: 3,
    expYear: 2026,
  },
  {
    id: faker.string.uuid(),
    type: "us_bank_account",
    brand: undefined,
    last4: "6789",
    expMonth: undefined,
    expYear: undefined,
  },
];

const mockHistory: RouterOutputs["billing"]["history"] = Array.from(
  { length: 5 },
  () => ({
    id: faker.string.uuid(),
    amount: faker.number.int({ min: 500, max: 5000 }),
    status: faker.helpers.arrayElement([
      "succeeded",
      "processing",
      "requires_payment_method",
      "canceled",
    ]),
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: faker.commerce.productName(),
    duration: faker.number.int({ min: 1, max: 30 }),
    date: faker.date.recent(),
    price: `$${faker.number.float({ min: 10, max: 500, precision: 2 })}`,
  }),
);

const mockCreateSetupIntent = {
  mutateAsync: async () => ({ clientSecret: "mock_client_secret" }),
  isPending: false,
} as any;

const mockRemovePaymentMethod = {
  mutateAsync: async () => ({}),
  isPending: false,
} as any;

export const Default: Story = {
  args: {
    organization: mockOrganization,
    account: mockAccount,
    methods: mockMethods,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const Loading: Story = {
  args: {
    organization: mockOrganization,
    loading: true,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const NoPaymentMethods: Story = {
  args: {
    organization: mockOrganization,
    account: mockAccount,
    methods: [],
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const NoPaymentHistory: Story = {
  args: {
    organization: mockOrganization,
    account: mockAccount,
    methods: mockMethods,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const RemovingPaymentMethod: Story = {
  args: {
    organization: mockOrganization,
    account: mockAccount,
    methods: mockMethods,
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: {
      ...mockRemovePaymentMethod,
      isPending: true,
    },
  },
};

export const WithError: Story = {
  args: {
    organization: mockOrganization,
    error: new Error("Failed to load payment methods"),
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const Empty: Story = {
  args: {
    organization: mockOrganization,
    account: mockAccount,
    methods: [],
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};
