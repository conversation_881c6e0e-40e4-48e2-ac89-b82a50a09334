import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import {
  InvoiceStatus,
  OrganizationClass,
  OrganizationMode,
  OrganizationStatus,
  OrganizationType,
  ScheduleType,
} from "@/api";
import Billing from "@/www/organizations/billing/Billing";

const meta = {
  title: "Pages/Organizations/Billing/page",
  component: Billing,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/billing",
      },
    },
  },
} satisfies Meta<typeof Billing>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockOrganization: RouterOutputs["organizations"]["get"] = {
  organization: {
    id: faker.string.uuid(),
    name: faker.company.name(),
    type: OrganizationType.CLIENT,
    class: OrganizationClass.PRIVATE,
    status: OrganizationStatus.ACTIVE,
    mode: OrganizationMode.INDEPENDENT,
    phone: faker.phone.number(),
    email: faker.internet.email(),
    avatar: faker.image.avatar(),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    approvedAt: faker.date.past(),
    rejectedAt: null,
    // Finance fields
    balance: faker.number.int({ min: 1000, max: 10000 }),
    score: faker.number.int({ min: 50, max: 100 }),
    assistPercentage: faker.number.int({ min: 5, max: 15 }),
    basePercentage: faker.number.int({ min: 85, max: 95 }),
    billingFrequency: "MONTHLY",
    accountId: faker.string.uuid(),
    customerId: null,
    // Required fields from the API
    parent: null,
    manager: undefined,
    address: {
      id: faker.string.uuid(),
      formatted: faker.location.streetAddress(),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
      country: faker.location.country(),
      timeZone: faker.location.timeZone(),
    },
    schedule: {
      id: faker.string.uuid(),
      type: ScheduleType.ORGANIZATION,
      name: null,
      startsAt: null,
      endsAt: null,
      blocks: [],
    },
    contracts: [],
  },
  members: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    role: "ADMIN",
    user: {
      id: faker.string.uuid(),
      avatar: undefined,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
    },
  })),
  accounts: [],
};

const mockInvoices: RouterOutputs["billing"]["invoices"]["getMany"] = {
  items: Array.from({ length: 5 }, () => ({
    id: faker.string.uuid(),
    number: faker.finance.accountNumber(),
    date: faker.date.recent(),
    due: faker.date.future(),
    total: faker.number.int({ min: 500, max: 5000 }),
    status: faker.helpers.arrayElement([
      InvoiceStatus.PAID,
      InvoiceStatus.DRAFT,
      InvoiceStatus.OPEN,
      InvoiceStatus.DUE,
      InvoiceStatus.VOID,
    ]),
    organization: {
      id: mockOrganization.organization.id,
      name: mockOrganization.organization.name,
      avatar: mockOrganization.organization.avatar,
    },
  })),
  total: 5,
};

// Create a more specific mock for the account requirements
const mockRequirements = {
  currently_due: [] as string[],
  eventually_due: [] as string[],
  past_due: [] as string[],
  pending_verification: [] as string[],
  alternatives: null,
  current_deadline: null,
  disabled_reason: null,
  errors: [] as any[],
};

const mockAccount: RouterOutputs["billing"]["accounts"]["organization"]["get"] =
  {
    id: faker.string.uuid(),
    enabled: true,
    submitted: true,
    requirements: mockRequirements,
  };

const mockMethods: RouterOutputs["billing"]["methods"] = [
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
  },
  {
    id: faker.string.uuid(),
    type: "card",
    brand: "mastercard",
    last4: "8888",
    expMonth: 3,
    expYear: 2026,
  },
];

// Mock payment history with medical-related items
const mockHistory: RouterOutputs["billing"]["history"] = [
  // Credit card payment for medical supplies - succeeded
  {
    id: faker.string.uuid(),
    amount: 12500,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Credit Card Payment for Medical Supplies - Invoice #MED-12345",
    duration: 0,
    date: faker.date.recent(5),
    price: "$125.00",
  },
  // Bank transfer for staff salaries - processing
  {
    id: faker.string.uuid(),
    amount: 450000,
    status: "processing",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Bank Transfer for Staff Salaries - Invoice #PAY-67890",
    duration: 0,
    date: faker.date.recent(10),
    price: "$4,500.00",
  },
  // Credit card payment for patient billing software - canceled
  {
    id: faker.string.uuid(),
    amount: 7500,
    status: "canceled",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Credit Card Payment for Patient Billing Software Subscription - Invoice #SFT-23456",
    duration: 0,
    date: faker.date.recent(15),
    price: "$75.00",
  },
  // Bank transfer for medical equipment - requires_payment_method
  {
    id: faker.string.uuid(),
    amount: 180000,
    status: "requires_payment_method",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Bank Transfer for Medical Equipment Purchase - Invoice #EQP-78901",
    duration: 0,
    date: faker.date.recent(20),
    price: "$1,800.00",
  },
  // Credit card payment for laboratory services - succeeded
  {
    id: faker.string.uuid(),
    amount: 32500,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Payment for Laboratory Services - Invoice #LAB-34567",
    duration: 0,
    date: faker.date.recent(25),
    price: "$325.00",
  },
  // Bank transfer for facility rent - succeeded
  {
    id: faker.string.uuid(),
    amount: 250000,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Transfer for Facility Rent - Invoice #RENT-89012",
    duration: 0,
    date: faker.date.recent(30),
    price: "$2,500.00",
  },
  // Credit card payment for medical insurance - processing
  {
    id: faker.string.uuid(),
    amount: 95000,
    status: "processing",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Payment for Medical Malpractice Insurance - Invoice #INS-45678",
    duration: 0,
    date: faker.date.recent(35),
    price: "$950.00",
  },
  // Bank transfer for pharmaceutical supplies - succeeded
  {
    id: faker.string.uuid(),
    amount: 87500,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Transfer for Pharmaceutical Supplies - Invoice #PHARM-90123",
    duration: 0,
    date: faker.date.recent(40),
    price: "$875.00",
  },
  // Credit card payment for staff training - requires_payment_method
  {
    id: faker.string.uuid(),
    amount: 42500,
    status: "requires_payment_method",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "Payment for Staff Medical Training - Invoice #TRN-56789",
    duration: 0,
    date: faker.date.recent(45),
    price: "$425.00",
  },
  // Bank transfer for utility bills - succeeded
  {
    id: faker.string.uuid(),
    amount: 35000,
    status: "succeeded",
    created: faker.date.recent().getTime() / 1000,
    currency: "usd",
    name: "ACH Transfer for Clinic Utilities - Invoice #UTIL-01234",
    duration: 0,
    date: faker.date.recent(50),
    price: "$350.00",
  },
];

const mockCreateSetupIntent = {
  mutateAsync: async () => ({ clientSecret: "mock_client_secret" }),
  isPending: false,
} as any;

const mockRemovePaymentMethod = {
  mutateAsync: async () => ({}),
  isPending: false,
} as any;

const args = {
  organization: {
    data: mockOrganization,
    loading: false,
    error: null,
  },
  invoices: {
    data: mockInvoices,
    loading: false,
    error: null,
  },
  account: {
    data: mockAccount,
    loading: false,
    error: null,
  },
  methods: {
    data: mockMethods,
    loading: false,
    error: null,
  },
  history: {
    data: mockHistory,
    loading: false,
    error: null,
  },
  createSetupIntent: mockCreateSetupIntent,
  removePaymentMethod: mockRemovePaymentMethod,
};

// Basic States
export const Default: Story = {
  args,
};

export const Loading: Story = {
  args: {
    organization: {
      data: undefined,
      loading: true,
      error: null,
    },
    invoices: {
      data: undefined,
      loading: true,
      error: null,
    },
    account: {
      data: undefined,
      loading: true,
      error: null,
    },
    methods: {
      data: undefined,
      loading: true,
      error: null,
    },
    history: {
      data: undefined,
      loading: true,
      error: null,
    },
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const WithError: Story = {
  args: {
    organization: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load organization"),
    },
    invoices: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load invoices"),
    },
    account: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load account"),
    },
    methods: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load payment methods"),
    },
    history: {
      data: undefined,
      loading: false,
      error: new Error("Failed to load payment history"),
    },
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

export const Empty: Story = {
  args: {
    organization: {
      data: mockOrganization,
      loading: false,
      error: null,
    },
    invoices: {
      data: { items: [], total: 0 },
      loading: false,
      error: null,
    },
    account: {
      data: mockAccount,
      loading: false,
      error: null,
    },
    methods: {
      data: [],
      loading: false,
      error: null,
    },
    history: {
      data: [],
      loading: false,
      error: null,
    },
    createSetupIntent: mockCreateSetupIntent,
    removePaymentMethod: mockRemovePaymentMethod,
  },
};

// Payment Method States
export const NoPaymentMethods: Story = {
  args: {
    ...args,
    methods: {
      data: [],
      loading: false,
      error: null,
    },
  },
};

export const SinglePaymentMethod: Story = {
  args: {
    ...args,
    methods: {
      data: mockMethods.slice(0, 1),
      loading: false,
      error: null,
    },
  },
};

export const MultiplePaymentMethods: Story = {
  args,
};

export const RemovingPaymentMethod: Story = {
  args: {
    ...args,
    removePaymentMethod: {
      ...mockRemovePaymentMethod,
      isPending: true,
    },
  },
};

// Payment History States
export const NoPaymentHistory: Story = {
  args: {
    ...args,
    history: {
      data: [],
      loading: false,
      error: null,
    },
  },
};

export const WithPaymentHistory: Story = {
  args,
};

// Combined States
export const NoAccountNoMethods: Story = {
  args: {
    ...args,
    account: {
      data: null,
      loading: false,
      error: null,
    },
    methods: {
      data: [],
      loading: false,
      error: null,
    },
  },
};

export const AccountNotEnabledNoMethods: Story = {
  args: {
    ...args,
    account: {
      data: {
        ...mockAccount,
        enabled: false,
        requirements: {
          ...mockRequirements,
          currently_due: [
            "individual.verification.document",
            "individual.verification.additional_document",
          ],
          eventually_due: ["individual.verification.document"],
        },
      },
      loading: false,
      error: null,
    },
    methods: {
      data: [],
      loading: false,
      error: null,
    },
  },
};

export const NoInvoices: Story = {
  args: {
    ...args,
    invoices: {
      data: { items: [], total: 0 },
      loading: false,
      error: null,
    },
  },
};
