import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { PayoutStatus, PersonRole, ProviderStatus, ShiftStatus } from "@/api";
import Payouts from "@/www/organizations/payouts/Payouts";

const meta = {
  title: "Pages/Organizations/Payouts/page",
  component: Payouts,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/billing/payouts",
      },
    },
  },
} satisfies Meta<typeof Payouts>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockPayouts: RouterOutputs["billing"]["payouts"]["getMany"] = {
  items: Array.from({ length: 5 }, () => ({
    id: faker.string.uuid(),
    amount: faker.number.int({ min: 500, max: 5000 }),
    status: faker.helpers.arrayElement([
      PayoutStatus.COMPLETED,
      PayoutStatus.PENDING,
      PayoutStatus.FAILED,
    ]),
    providerId: faker.string.uuid(),
    overtimeAmount: faker.number.int({ min: 50, max: 500 }),
    holidayAmount: faker.number.int({ min: 50, max: 500 }),
    nightAmount: faker.number.int({ min: 50, max: 500 }),
    paidAt: faker.helpers.arrayElement([faker.date.recent(), null]),
    provider: {
      id: faker.string.uuid(),
      status: ProviderStatus.ACTIVE,
      title: null,
      person: {
        id: faker.string.uuid(),
        role: PersonRole.PROVIDER,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: null,
      },
    },
    shifts: Array.from({ length: 2 }, () => ({
      id: faker.string.uuid(),
      status: ShiftStatus.COMPLETED,
      summary: faker.helpers.arrayElement([
        "Morning Shift",
        "Evening Shift",
        "Night Shift",
      ]),
    })),
  })),
  total: 5,
};

export const Default: Story = {
  args: {
    payouts: mockPayouts,
  },
};

export const Loading: Story = {
  args: {
    payouts: undefined,
  },
};

export const Empty: Story = {
  args: {
    payouts: {
      items: [],
      total: 0,
    },
  },
};
