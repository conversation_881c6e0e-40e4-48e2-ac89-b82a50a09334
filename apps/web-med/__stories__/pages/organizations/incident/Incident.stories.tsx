import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  IncidentSeverity,
  IncidentStatus,
  IncidentType,
  ProviderStatus,
  ShiftStatus,
} from "@axa/database-medical";

import type { RouterError, RouterOutputs } from "@/api";

import Incident from "@/www/organizations/incident/Incident";

const meta = {
  title: "Pages/Organizations/Incident/page",
  component: Incident,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/shifts/abc-123/incident",
      },
    },
  },
} satisfies Meta<typeof Incident>;

export default meta;
type Story = StoryObj<typeof meta>;

// Define types based on API
type IncidentGetQuery = RouterOutputs["incidents"]["get"];
type OrganizationStructure = NonNullable<IncidentGetQuery["organization"]>;
type ProviderStructure = NonNullable<IncidentGetQuery["provider"]>;
type PersonStructure = NonNullable<ProviderStructure["person"]>;
type ShiftStructure = NonNullable<IncidentGetQuery["shift"]>;

// Helper functions to create mock data
const createOrganization = (
  options: {
    name?: string;
    avatar?: string | null;
  } = {},
): OrganizationStructure => ({
  id: faker.string.uuid(),
  name: options.name ?? faker.company.name(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createPerson = (
  options: {
    firstName?: string;
    lastName?: string;
    avatar?: string | null;
  } = {},
): PersonStructure => ({
  id: faker.string.uuid(),
  firstName: options.firstName ?? faker.person.firstName(),
  lastName: options.lastName ?? faker.person.lastName(),
  avatar: options.avatar ?? faker.image.avatar(),
});

const createProvider = (
  options: {
    title?: string | null;
    status?: ProviderStatus;
    person?: PersonStructure;
  } = {},
): ProviderStructure => ({
  id: faker.string.uuid(),
  title: options.title ?? faker.person.jobTitle(),
  status: options.status ?? "ACTIVE",
  person: options.person ?? createPerson(),
});

const createShift = (
  options: {
    summary?: string;
    scope?: string;
    status?: ShiftStatus;
    startDate?: Date;
    endDate?: Date;
  } = {},
): ShiftStructure => ({
  id: faker.string.uuid(),
  summary: options.summary ?? faker.company.catchPhrase(),
  scope: options.scope ?? faker.lorem.paragraph(),
  status: options.status ?? "CONFIRMED",
  startDate: options.startDate ?? faker.date.recent(),
  endDate: options.endDate ?? faker.date.future(),
});

// Create common mock data
const organizations = {
  memorialHospital: createOrganization({ name: "Memorial Hospital" }),
  cityHospital: createOrganization({ name: "City Hospital" }),
  countyMedical: createOrganization({ name: "County Medical" }),
};

const providers = {
  drSmith: createProvider({
    title: "MD",
    person: createPerson({ firstName: "John", lastName: "Smith" }),
  }),
  nurseJohnson: createProvider({
    title: "RN",
    person: createPerson({ firstName: "Sarah", lastName: "Johnson" }),
  }),
};

const shifts = {
  morningShift: createShift({
    summary: "Morning Shift",
    scope: "General morning duties",
    status: "CONFIRMED",
    startDate: new Date(2023, 5, 1, 8, 0),
    endDate: new Date(2023, 5, 1, 16, 0),
  }),
  nightShift: createShift({
    summary: "Night Shift",
    scope: "Emergency department coverage",
    status: "CONFIRMED",
    startDate: new Date(2023, 5, 1, 20, 0),
    endDate: new Date(2023, 5, 2, 4, 0),
  }),
};

const createIncident = (
  options: {
    id?: string;
    title?: string;
    description?: string;
    severity?: IncidentSeverity;
    status?: IncidentStatus;
    type?: IncidentType;
    provider?: ProviderStructure;
    organization?: OrganizationStructure;
    shift?: ShiftStructure;
  } = {},
): IncidentGetQuery => ({
  id: options.id ?? faker.string.uuid(),
  title: options.title ?? faker.lorem.sentence(),
  description: options.description ?? faker.lorem.paragraph(),
  severity: options.severity ?? "MINOR",
  status: options.status ?? "OPEN",
  type: options.type ?? "HEALTH",
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent(),
  providerId: options.provider?.id ?? null,
  organizationId: options.organization?.id ?? null,
  shiftId: options.shift?.id ?? null,
  provider: options.provider,
  organization: options.organization,
  shift: options.shift,
});

// Create story variants
export const Default: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "Patient Fall",
        description: "Patient fell while getting out of bed",
        severity: "MINOR",
        status: "OPEN",
        type: "HEALTH",
        provider: providers.nurseJohnson,
        organization: organizations.memorialHospital,
        shift: shifts.morningShift,
      }),
    },
  },
};

export const Loading: Story = {
  args: {
    incident: {
      loading: true,
      error: undefined,
      data: undefined,
    },
  },
};

export const WithError: Story = {
  args: {
    incident: {
      loading: false,
      error: {
        message: "Failed to load incident",
      } as RouterError,
      data: undefined,
    },
  },
};

export const MajorIncident: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "Medication Error",
        description: "Wrong medication administered to patient",
        severity: "MAJOR",
        status: "IN_PROGRESS",
        type: "HEALTH",
        provider: providers.drSmith,
        organization: organizations.cityHospital,
        shift: shifts.nightShift,
      }),
    },
  },
};

export const CriticalIncident: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "Equipment Failure",
        description: "Ventilator failure during procedure",
        severity: "CRITICAL",
        status: "OPEN",
        type: "SAFETY",
        provider: providers.drSmith,
        organization: organizations.countyMedical,
        shift: shifts.nightShift,
      }),
    },
  },
};

export const ResolvedIncident: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "Spill in Hallway",
        description: "Liquid spill in main corridor",
        severity: "MINOR",
        status: "RESOLVED",
        type: "SAFETY",
        provider: providers.nurseJohnson,
        organization: organizations.memorialHospital,
        shift: shifts.morningShift,
      }),
    },
  },
};

export const EnvironmentalIncident: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "HVAC Malfunction",
        description: "Temperature control issue in patient rooms",
        severity: "MAJOR",
        status: "IN_PROGRESS",
        type: "ENVIRONMENT",
        provider: providers.nurseJohnson,
        organization: organizations.cityHospital,
        shift: shifts.morningShift,
      }),
    },
  },
};

export const OtherIncident: Story = {
  args: {
    incident: {
      loading: false,
      error: undefined,
      data: createIncident({
        title: "Missing Equipment",
        description: "Medical cart missing from storage area",
        severity: "MINOR",
        status: "OPEN",
        type: "OTHER",
        provider: providers.drSmith,
        organization: organizations.countyMedical,
        shift: shifts.nightShift,
      }),
    },
  },
};
