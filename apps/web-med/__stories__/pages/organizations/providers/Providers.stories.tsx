import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type {
  ProvidersQueryResult,
  ReviewsQueryResult,
} from "@/www/organizations/providers/Providers";

import { ProviderStatus, QualificationType, VerificationStatus } from "@/api";
import Providers from "@/www/organizations/providers/Providers";

const meta = {
  title: "Pages/Organizations/Providers/page",
  component: Providers,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/providers",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="container mx-auto p-4">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Providers>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for providers
const mockProvidersData: ProvidersQueryResult = {
  items: Array.from({ length: 10 }, () => ({
    id: faker.string.uuid(),

    status: faker.helpers.arrayElement([
      ProviderStatus.ACTIVE,
      ProviderStatus.PENDING,
      ProviderStatus.INACTIVE,
      ProviderStatus.SUSPENDED,
    ]) as ProviderStatus,
    title: faker.helpers.arrayElement(["MD", "RN", "PA", "NP", null]),
    verification: {
      id: faker.string.uuid(),
      status: faker.helpers.arrayElement([
        VerificationStatus.PENDING,
        VerificationStatus.APPROVED,
        VerificationStatus.REJECTED,
      ]) as VerificationStatus,
    },
    person: {
      id: faker.string.uuid(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
    },
    address: {
      id: faker.string.uuid(),
      formatted: faker.address.streetAddress(),
      latitude: faker.location.latitude(),
      longitude: faker.location.longitude(),
      timeZone: faker.location.timeZone(),
    },
    specialties: Array.from({ length: 3 }, () => ({
      id: faker.string.uuid(),
      name: faker.lorem.word(),
    })),
    qualifications: Array.from({ length: 2 }, () => ({
      id: faker.string.uuid(),
      name: faker.lorem.word(),
      identifier: faker.string.uuid(),
      expirationDate: faker.date.future(),
      state: faker.location.state(),
      type: faker.helpers.arrayElement([
        QualificationType.LICENSE,
        QualificationType.CERTIFICATE,
        QualificationType.DEGREE,
      ]),
    })),
    experiences: Array.from({ length: 2 }, () => ({
      id: faker.string.uuid(),
      company: faker.company.name(),
      startDate: faker.date.recent(),
      endDate: faker.date.recent(),
      role: faker.lorem.sentence(),
      description: faker.lorem.paragraph(),
    })),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
  })),
  total: 10,
};

// Mock data for reviews
const mockReviewsData: ReviewsQueryResult = {
  items: Array.from({ length: 5 }, () => ({
    id: faker.string.uuid(),
    rating: faker.number.int({ min: 1, max: 5 }),
    comment: faker.lorem.paragraph(),
    provider: {
      id: faker.string.uuid(),
      person: {
        id: faker.string.uuid(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
      },
    },
    reviewer: {
      id: faker.string.uuid(),
      person: {
        id: faker.string.uuid(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
      },
    },
    shift: {
      id: faker.string.uuid(),
      summary: faker.helpers.arrayElement([
        "Morning Shift",
        "Evening Shift",
        "Night Shift",
      ]),
      startDate: faker.date.recent(),
      endDate: faker.date.soon(),
    },
    createdAt: faker.date.recent(),
  })),
  total: 5,
};

export const Default: Story = {
  args: {
    loading: false,
    providers: {
      data: mockProvidersData,
      loading: false,
      error: null,
    },
    reviews: {
      data: mockReviewsData,
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};
