import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { InvoiceStatus } from "@/api";
import Invoices from "@/www/organizations/invoices/Invoices";

const meta = {
  title: "Pages/Organizations/Invoices/page",
  component: Invoices,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/billing/invoices",
      },
    },
  },
} satisfies Meta<typeof Invoices>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockInvoices: RouterOutputs["billing"]["invoices"]["getMany"] = {
  items: Array.from({ length: 5 }, () => ({
    id: faker.string.uuid(),
    number: faker.finance.accountNumber(),
    date: faker.date.recent(),
    due: faker.date.future(),
    total: faker.number.int({ min: 500, max: 5000 }),
    status: faker.helpers.arrayElement([
      InvoiceStatus.PAID,
      InvoiceStatus.DRAFT,
      InvoiceStatus.OPEN,
      InvoiceStatus.DUE,
      InvoiceStatus.VOID,
    ]),
    organization: {
      id: faker.string.uuid(),
      name: faker.company.name(),
      avatar: null,
    },
  })),
  total: 5,
};

export const Default: Story = {
  args: {
    invoices: mockInvoices,
  },
};

export const Loading: Story = {
  args: {
    invoices: undefined,
  },
};

export const Empty: Story = {
  args: {
    invoices: {
      items: [],
      total: 0,
    },
  },
};
