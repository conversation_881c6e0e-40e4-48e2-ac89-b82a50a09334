import type { Meta, StoryObj } from "@storybook/react";

import { trpcMsw } from "@/api/mock";
import ContractsView from "@/www/organizations/contracts/Contracts";

import { contracts } from "../contract/data";

const meta = {
  title: "Pages/Organizations/Contracts/Contracts",
  component: ContractsView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/contracts/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.contracts.get.query(({ input }) => {
          // Return the appropriate contract based on the ID
          const id = input.id;

          if (id === "abc-123") {
            return contracts.draft;
          } else if (id === "abc-124") {
            return contracts.pending;
          } else if (id === "abc-125") {
            return contracts.signed;
          } else if (id === "abc-126") {
            return contracts.expired;
          } else if (id === "abc-127") {
            return contracts.terminated;
          }

          // Default fallback
          return contracts.draft;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the contract with the matching thread ID
          const contract = Object.values(contracts).find(
            (c) => c.thread?.id === id,
          );

          if (contract?.thread) {
            return {
              ...contract.thread,
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof ContractsView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Draft: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: false,
  },
};

export const Pending: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: false,
  },
};

export const Active: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: false,
  },
};

export const Expired: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: false,
  },
};

export const Terminated: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: false,
  },
};

export const Loading: Story = {
  args: {
    contracts: {
      data: {
        items: [contracts.draft, contracts.pending, contracts.signed],
        total: 3,
      },
      loading: false,
      error: null,
    },
    loading: true,
  },
};
