import type { <PERSON>a, StoryObj } from "@storybook/react";

import type { RouterError } from "@/api"; // Import RouterError type

import { trpcMsw } from "@/api/mock";
import ApplicationsView from "@/www/organizations/applications/Applications";

import { applications } from "../application/data";

const meta = {
  title: "Pages/Organizations/Applications/Applications",
  component: ApplicationsView,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/organizations/application/abc-123",
      },
    },
    msw: {
      handlers: [
        trpcMsw.contracts.get.query(({ input }) => {
          // Return contract data if the application has a contract
          const id = input.id;

          if (applications.accepted.contractId === id) {
            return applications.accepted.contract;
          }

          // Default fallback to null if no contract found
          return null;
        }),

        trpcMsw.threads.get.query(({ input }) => {
          // Return thread data with messages
          const id = input.id;

          // Find the application with the matching thread ID
          const application = Object.values(applications).find(
            (app) => app.thread?.id === id,
          );

          if (application?.thread) {
            return {
              ...application.thread,
              author: {
                id: "author-id",
                firstName: "System",
                lastName: "User",
                avatar: null,
              },
            };
          }

          // Default fallback
          return null;
        }),
      ],
    },
  },
} satisfies Meta<typeof ApplicationsView>;

export default meta;
type Story = StoryObj<typeof meta>;

// Story variants
export const Default: Story = {
  args: {
    loading: false,
    applications: {
      data: {
        items: [
          applications.accepted,
          applications.withPosition,
          applications.pending,
        ],
        total: 3,
      },
      loading: false,
      error: null,
    },
  },
};

export const Empty: Story = {
  args: {
    loading: false,
    applications: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: null,
    },
  },
};

export const SingleItem: Story = {
  args: {
    loading: false,
    applications: {
      data: {
        items: [applications.withPosition], // Show only one
        total: 1,
      },
      loading: false,
      error: null,
    },
  },
};

export const OnlyRejected: Story = {
  args: {
    loading: false,
    applications: {
      data: {
        items: [applications.rejected],
        total: 1,
      },
      loading: false,
      error: null,
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    applications: {
      data: {
        items: [], // Typically empty during load
        total: 0,
      },
      loading: true,
      error: null,
    },
  },
};

export const ErrorState: Story = {
  args: {
    loading: false,
    applications: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
      error: new Error("Failed to load applications.") as RouterError,
    },
  },
};
