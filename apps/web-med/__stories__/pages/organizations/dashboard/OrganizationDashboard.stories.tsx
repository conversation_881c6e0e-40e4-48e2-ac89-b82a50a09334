import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import {
  ApplicationStatus,
  DepartmentType,
  FacilityType,
  JobPostStatus,
  OfferStatus,
  PaymentType,
  ShiftStatus,
} from "@/api";
import Dashboard from "@/www/organizations/dashboard/Dashboard";

const meta = {
  title: "Pages/Organizations/Dashboard/page",
  component: Dashboard,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="container mx-auto py-6">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Dashboard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Create a simple mock data version for storybook
export const Default: Story = {
  args: {
    loading: false,
    analytics: {
      data: {
        shiftsByStatus: {
          [ShiftStatus.PENDING]: 15,
          [ShiftStatus.CONFIRMED]: 22,
          [ShiftStatus.ACTIVE]: 8,
          [ShiftStatus.COMPLETED]: 35,
          [ShiftStatus.CANCELLED]: 5,
          [ShiftStatus.APPROVED]: 3,
          [ShiftStatus.REJECTED]: 1,
        },
        jobsByStatus: {
          [JobPostStatus.DRAFT]: 3,
          [JobPostStatus.PUBLISHED]: 10,
          [JobPostStatus.FILLED]: 5,
          [JobPostStatus.COMPLETED]: 15,
          [JobPostStatus.CANCELLED]: 2,
          [JobPostStatus.EXPIRED]: 1,
        },
        applicationsByStatus: {
          [ApplicationStatus.PENDING]: 20,
          [ApplicationStatus.ACCEPTED]: 10,
          [ApplicationStatus.REJECTED]: 5,
          [ApplicationStatus.WITHDRAWN]: 3,
          [ApplicationStatus.CLOSED]: 2,
        },
        offersByStatus: {
          [OfferStatus.PENDING]: 10,
          [OfferStatus.ACCEPTED]: 5,
          [OfferStatus.REJECTED]: 3,
          [OfferStatus.WITHDRAWN]: 2,
          ["EXPIRED" as OfferStatus]: 1,
        },
        shiftsByDate: Object.fromEntries(
          Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const day = date.getDate();
            const month = date.getMonth() + 1;
            const year = date.getFullYear();
            return [`${month}/${day}/${year}`, Math.floor(Math.random() * 10)];
          }),
        ),
        jobsByDate: Object.fromEntries(
          Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const day = date.getDate();
            const month = date.getMonth() + 1;
            const year = date.getFullYear();
            return [`${month}/${day}/${year}`, Math.floor(Math.random() * 5)];
          }),
        ),
        metrics: {
          activeStaff: {
            value: 248,
            change: 12,
            trend: "up" as const,
            period: "this month",
          },
          openShifts: {
            value: 32,
            change: 3,
            trend: "down" as const,
            period: "this week",
          },
          applications: {
            value: 156,
            change: 8,
            trend: "up" as const,
            period: "this week",
          },
          weeklyBudget: {
            value: 42500,
            total: 50000,
            period: "of $50k budget",
          },
        },
      },
      loading: false,
    },
    shifts: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
    },
    jobs: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
    },
    applications: {
      data: {
        items: [],
        total: 0,
      },
      loading: false,
    },
    mockData: {
      analytics: {
        metrics: {
          activeStaff: {
            value: 248,
            change: 12,
            trend: "up",
            period: "this month",
          },
          openShifts: {
            value: 32,
            change: 3,
            trend: "down",
            period: "this week",
          },
          applications: {
            value: 156,
            change: 8,
            trend: "up",
            period: "this week",
          },
          weeklyBudget: {
            value: 42500,
            total: 50000,
            period: "of $50k budget",
          },
        },
        coverageTrends: Array.from({ length: 30 }, (_, i) => {
          const date = new Date(2024, 2, i + 1).toISOString().split("T")[0];
          return {
            date,
            coverage: Math.floor(75 + Math.random() * 20),
            target: 95,
          };
        }),
      },
      schedule: {
        currentWeek: "March 18-24, 2025",
        schedule: [
          {
            day: "Mon",
            shifts: [
              {
                type: "Morning Shift",
                staff: 8,
                needed: 8,
                status: "complete",
              },
            ],
          },
          {
            day: "Tue",
            shifts: [
              { type: "Day Shift", staff: 12, needed: 12, status: "complete" },
            ],
          },
          {
            day: "Wed",
            shifts: [
              { type: "Night Shift", staff: 4, needed: 8, status: "warning" },
            ],
          },
          {
            day: "Thu",
            shifts: [
              {
                type: "Morning Shift",
                staff: 10,
                needed: 10,
                status: "complete",
              },
            ],
          },
          {
            day: "Fri",
            shifts: [
              { type: "Day Shift", staff: 15, needed: 15, status: "complete" },
            ],
          },
          {
            day: "Sat",
            shifts: [
              { type: "Night Shift", staff: 2, needed: 4, status: "critical" },
            ],
          },
          {
            day: "Sun",
            shifts: [
              {
                type: "Morning Shift",
                staff: 6,
                needed: 6,
                status: "complete",
              },
            ],
          },
        ],
      },
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    analytics: { loading: true },
    shifts: { loading: true },
    jobs: { loading: true },
    applications: { loading: true },
  },
};

// Scenario: High Activity Dashboard
export const HighActivity: Story = {
  args: {
    ...Default.args,
    analytics: {
      data: {
        ...Default.args.analytics.data,
        shiftsByStatus: {
          [ShiftStatus.PENDING]: 35,
          [ShiftStatus.CONFIRMED]: 42,
          [ShiftStatus.ACTIVE]: 28,
          [ShiftStatus.COMPLETED]: 85,
          [ShiftStatus.CANCELLED]: 15,
          [ShiftStatus.APPROVED]: 13,
          [ShiftStatus.REJECTED]: 5,
        },
        applicationsByStatus: {
          [ApplicationStatus.PENDING]: 50,
          [ApplicationStatus.ACCEPTED]: 30,
          [ApplicationStatus.REJECTED]: 15,
          [ApplicationStatus.WITHDRAWN]: 8,
          [ApplicationStatus.CLOSED]: 7,
        },
        metrics: {
          activeStaff: {
            value: 450,
            change: 25,
            trend: "up" as const,
            period: "this month",
          },
          openShifts: {
            value: 75,
            change: 15,
            trend: "up" as const,
            period: "this week",
          },
          applications: {
            value: 320,
            change: 40,
            trend: "up" as const,
            period: "this week",
          },
          weeklyBudget: {
            value: 85000,
            total: 100000,
            period: "of $100k budget",
          },
        },
      },
      loading: false,
    },
  },
};

// Scenario: Low Activity Dashboard
export const LowActivity: Story = {
  args: {
    ...Default.args,
    analytics: {
      data: {
        ...Default.args.analytics.data,
        shiftsByStatus: {
          [ShiftStatus.PENDING]: 5,
          [ShiftStatus.CONFIRMED]: 8,
          [ShiftStatus.ACTIVE]: 3,
          [ShiftStatus.COMPLETED]: 12,
          [ShiftStatus.CANCELLED]: 2,
          [ShiftStatus.APPROVED]: 1,
          [ShiftStatus.REJECTED]: 0,
        },
        applicationsByStatus: {
          [ApplicationStatus.PENDING]: 7,
          [ApplicationStatus.ACCEPTED]: 3,
          [ApplicationStatus.REJECTED]: 2,
          [ApplicationStatus.WITHDRAWN]: 1,
          [ApplicationStatus.CLOSED]: 0,
        },
        metrics: {
          activeStaff: {
            value: 85,
            change: 5,
            trend: "down" as const,
            period: "this month",
          },
          openShifts: {
            value: 12,
            change: 8,
            trend: "down" as const,
            period: "this week",
          },
          applications: {
            value: 45,
            change: 12,
            trend: "down" as const,
            period: "this week",
          },
          weeklyBudget: {
            value: 15000,
            total: 30000,
            period: "of $30k budget",
          },
        },
      },
      loading: false,
    },
  },
};

// Scenario: Staffing Crisis Dashboard
export const StaffingCrisis: Story = {
  args: {
    ...Default.args,
    analytics: {
      data: {
        ...Default.args.analytics.data,
        metrics: {
          activeStaff: {
            value: 120,
            change: 30,
            trend: "down" as const,
            period: "this month",
          },
          openShifts: {
            value: 85,
            change: 45,
            trend: "up" as const,
            period: "this week",
          },
          applications: {
            value: 25,
            change: 15,
            trend: "down" as const,
            period: "this week",
          },
          weeklyBudget: {
            value: 28000,
            total: 50000,
            period: "of $50k budget",
          },
        },
      },
      loading: false,
    },
    mockData: {
      ...Default.args.mockData,
      schedule: {
        currentWeek: "March 18-24, 2025",
        schedule: [
          {
            day: "Mon",
            shifts: [
              {
                type: "Morning Shift",
                staff: 4,
                needed: 8,
                status: "critical",
              },
            ],
          },
          {
            day: "Tue",
            shifts: [
              { type: "Day Shift", staff: 6, needed: 12, status: "critical" },
            ],
          },
          {
            day: "Wed",
            shifts: [
              { type: "Night Shift", staff: 2, needed: 8, status: "critical" },
            ],
          },
          {
            day: "Thu",
            shifts: [
              {
                type: "Morning Shift",
                staff: 5,
                needed: 10,
                status: "critical",
              },
            ],
          },
          {
            day: "Fri",
            shifts: [
              { type: "Day Shift", staff: 8, needed: 15, status: "warning" },
            ],
          },
          {
            day: "Sat",
            shifts: [
              { type: "Night Shift", staff: 1, needed: 4, status: "critical" },
            ],
          },
          {
            day: "Sun",
            shifts: [
              {
                type: "Morning Shift",
                staff: 3,
                needed: 6,
                status: "critical",
              },
            ],
          },
        ],
      },
    },
  },
};

// Scenario: Growth Dashboard
export const Growth: Story = {
  args: {
    ...Default.args,
    analytics: {
      data: {
        ...Default.args.analytics.data,
        jobsByStatus: {
          [JobPostStatus.DRAFT]: 8,
          [JobPostStatus.PUBLISHED]: 25,
          [JobPostStatus.FILLED]: 12,
          [JobPostStatus.COMPLETED]: 30,
          [JobPostStatus.CANCELLED]: 3,
          [JobPostStatus.EXPIRED]: 2,
        },
        applicationsByStatus: {
          [ApplicationStatus.PENDING]: 45,
          [ApplicationStatus.ACCEPTED]: 25,
          [ApplicationStatus.REJECTED]: 10,
          [ApplicationStatus.WITHDRAWN]: 5,
          [ApplicationStatus.CLOSED]: 3,
        },
        metrics: {
          activeStaff: {
            value: 320,
            change: 35,
            trend: "up" as const,
            period: "this month",
          },
          openShifts: {
            value: 45,
            change: 20,
            trend: "up" as const,
            period: "this week",
          },
          applications: {
            value: 220,
            change: 45,
            trend: "up" as const,
            period: "this week",
          },
          weeklyBudget: {
            value: 65000,
            total: 75000,
            period: "of $75k budget",
          },
        },
      },
      loading: false,
    },
  },
};
