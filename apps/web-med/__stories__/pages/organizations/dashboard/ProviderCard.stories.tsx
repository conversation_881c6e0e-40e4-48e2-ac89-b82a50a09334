import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { AccountStatus, ProviderStatus, VerificationStatus } from "@/api";
import ProviderCard from "@/www/organizations/dashboard/providers-panel/ProviderCard";

// Create mock provider data helper - matches the new search API structure
const createMockProvider = (
  overrides?: Partial<RouterOutputs["providers"]["search"]["items"][number]>,
): RouterOutputs["providers"]["search"]["items"][number] => {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const createdDate = faker.date.between({
    from: "2020-01-01",
    to: new Date(),
  });

  // Generate experiences for years calculation
  const experiences = Array.from(
    { length: faker.number.int({ min: 1, max: 5 }) },
    () => ({
      id: faker.string.uuid(),
      role: faker.person.jobTitle(),
      company:
        faker.company.name() +
        " " +
        faker.helpers.arrayElement([
          "Hospital",
          "Medical Center",
          "Clinic",
          "Health System",
        ]),
      startDate: faker.date.past({ years: 15 }),
      endDate:
        faker.helpers.maybe(() => faker.date.recent(), {
          probability: 0.4,
        }) ?? null,
    }),
  );

  // Calculate years of experience
  const yearsOfExperience = experiences.reduce((years, exp) => {
    const startDate = new Date(exp.startDate);
    const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
    const experienceYears =
      (endDate.getTime() - startDate.getTime()) /
      (1000 * 60 * 60 * 24 * 365.25);
    return years + Math.max(0, experienceYears);
  }, 0);

  // Get latest experience
  const sortedExperiences = experiences.sort(
    (a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime(),
  );
  const latestExperience = sortedExperiences[0]
    ? {
        role: sortedExperiences[0].role,
        company: sortedExperiences[0].company,
      }
    : null;

  return {
    id: faker.string.uuid(),
    personId: faker.string.uuid(),
    addressId: faker.string.uuid(),
    accountId: faker.string.uuid(),
    calendarId: faker.string.uuid(),
    score: faker.number.int({ min: 0, max: 100 }),
    title: faker.helpers.arrayElement([
      "Physician",
      "Nurse Practitioner",
      "Registered Nurse",
      "Physician Assistant",
      "Medical Technician",
      "Respiratory Therapist",
      "Physical Therapist",
      "Emergency Medicine Physician",
      "Cardiologist",
      "Neurologist",
    ]),
    status: faker.helpers.enumValue(ProviderStatus),
    verificationStatus: faker.helpers.enumValue(VerificationStatus),
    accountStatus: faker.helpers.enumValue(AccountStatus),
    spokenLanguages: faker.helpers.arrayElements(
      ["English", "Spanish", "French", "German", "Mandarin", "Portuguese"],
      { min: 1, max: 3 },
    ),
    gender:
      faker.helpers.maybe(() => faker.person.sex(), { probability: 0.7 }) ??
      null,
    createdAt: createdDate,
    updatedAt: faker.date.recent(),
    deletedAt: null,
    // Calculated fields from API
    yearsOfExperience: Math.round(yearsOfExperience * 10) / 10,
    latestExperience,
    joinedDate: createdDate,
    isRecentlyJoined:
      (new Date().getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24) <=
      90,
    // Related data (privacy-compliant)
    person: {
      id: faker.string.uuid(),
      firstName,
      lastName,
      avatar:
        faker.helpers.maybe(() => faker.image.avatar(), {
          probability: 0.7,
        }) ?? null,
    },
    address: {
      id: faker.string.uuid(),
      formatted: `${faker.location.city()}, ${faker.location.state()}`,
      timeZone: faker.location.timeZone(),
    },
    specialties: Array.from(
      { length: faker.number.int({ min: 1, max: 4 }) },
      () => ({
        id: faker.string.uuid(),
        name: faker.helpers.arrayElement([
          "Emergency Medicine",
          "Internal Medicine",
          "Cardiology",
          "Neurology",
          "Pediatrics",
          "Oncology",
          "Orthopedics",
          "Dermatology",
          "Psychiatry",
          "Radiology",
          "Anesthesiology",
          "Surgery",
          "Critical Care",
          "Family Medicine",
        ]),
        description:
          faker.helpers.maybe(() => faker.lorem.sentence(), {
            probability: 0.5,
          }) ?? null,
      }),
    ),
    experiences,
    verification: {
      id: faker.string.uuid(),
      status: faker.helpers.enumValue(VerificationStatus),
      verifiedAt: faker.date.recent(),
    },
    settings: {
      id: faker.string.uuid(),
      openToWork:
        faker.helpers.maybe(() => faker.datatype.boolean(), {
          probability: 0.8,
        }) ?? null,
      openToOnCall:
        faker.helpers.maybe(() => faker.datatype.boolean(), {
          probability: 0.6,
        }) ?? null,
    },
    ...overrides,
  };
};

const meta = {
  title: "Pages/Organizations/Dashboard/ProviderCard",
  component: ProviderCard,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A polished provider card component that displays medical provider information with loading, error, and various data states.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="max-w-sm">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof ProviderCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic States
export const Default: Story = {
  args: {
    provider: createMockProvider(),
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load provider information",
  },
};

export const NoProvider: Story = {
  args: {
    provider: undefined,
  },
};

// Verification States
export const VerifiedProvider: Story = {
  args: {
    provider: createMockProvider({
      verification: {
        id: faker.string.uuid(),
        status: VerificationStatus.APPROVED,
        verifiedAt: faker.date.recent(),
      },
    }),
  },
};

export const PendingVerification: Story = {
  args: {
    provider: createMockProvider({
      verification: {
        id: faker.string.uuid(),
        status: VerificationStatus.PENDING,
        verifiedAt: null,
      },
    }),
  },
};

// Availability States
export const AvailableForWork: Story = {
  args: {
    provider: createMockProvider({
      settings: {
        id: faker.string.uuid(),
        openToWork: true,
        openToOnCall: true,
      },
    }),
  },
};

export const NotAvailable: Story = {
  args: {
    provider: createMockProvider({
      settings: {
        id: faker.string.uuid(),
        openToWork: false,
        openToOnCall: false,
      },
    }),
  },
};

// Experience Levels
export const HighExperience: Story = {
  args: {
    provider: createMockProvider({
      experiences: Array.from({ length: 8 }, () => ({
        id: faker.string.uuid(),
        role: faker.person.jobTitle(),
        description: faker.lorem.paragraph(),
        company: faker.company.name() + " Medical Center",
        startDate: faker.date.past({ years: 20 }),
        endDate:
          faker.helpers.maybe(() => faker.date.recent(), {
            probability: 0.3,
          }) ?? null,
      })),
      score: faker.number.int({ min: 85, max: 100 }),
    }),
  },
};

export const NewProvider: Story = {
  args: {
    provider: createMockProvider({
      createdAt: faker.date.recent({ days: 7 }),
      experiences: [
        {
          id: faker.string.uuid(),
          role: "Recent Graduate",
          company: "Medical University",
          startDate: faker.date.recent({ days: 30 }),
          endDate: null,
        },
      ],
      score: faker.number.int({ min: 20, max: 50 }),
    }),
  },
};

// Avatar Variations
export const NoAvatar: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "John",
        lastName: "Doe",
        avatar: null,
      },
    }),
  },
};

export const WithAvatar: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "Sarah",
        lastName: "Wilson",
        avatar:
          "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face",
      },
    }),
  },
};

// Specialty Variations
export const ManySpecialties: Story = {
  args: {
    provider: createMockProvider({
      specialties: [
        { id: "1", name: "Emergency Medicine", description: null },
        { id: "2", name: "Critical Care", description: null },
        { id: "3", name: "Internal Medicine", description: null },
        { id: "4", name: "Cardiology", description: null },
        { id: "5", name: "Trauma Surgery", description: null },
        { id: "6", name: "Anesthesiology", description: null },
      ],
    }),
  },
};

export const NoSpecialties: Story = {
  args: {
    provider: createMockProvider({
      specialties: [],
    }),
  },
};

export const SingleSpecialty: Story = {
  args: {
    provider: createMockProvider({
      specialties: [
        { id: "1", name: "Family Medicine", description: "Primary care" },
      ],
    }),
  },
};

// Score Variations
export const HighScore: Story = {
  args: {
    provider: createMockProvider({
      score: 95,
    }),
  },
};

export const LowScore: Story = {
  args: {
    provider: createMockProvider({
      score: 15,
    }),
  },
};

export const NoScore: Story = {
  args: {
    provider: createMockProvider({
      score: 0,
    }),
  },
};

// Long Name Edge Case
export const LongName: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "Dr. Elizabeth Margaret Victoria",
        lastName: "van der Williamson-Smith-Johnson",
        avatar: null,
      },
      title: "Emergency Medicine Physician and Critical Care Specialist",
    }),
  },
};

// Stress Test Stories for Layout
export const StressTestVeryLongName: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "Dr. Maximilian Alexander Constantine",
        lastName: "von Habsburg-Rothschild-Wellington-Montgomery",
        avatar: null,
      },
      title:
        "Chief Emergency Medicine Physician, Critical Care Specialist, and Trauma Surgery Consultant",
    }),
  },
};

export const StressTestVeryLongSpecialties: Story = {
  args: {
    provider: createMockProvider({
      specialties: [
        {
          id: "1",
          name: "Emergency Medicine and Critical Care",
          description: null,
        },
        {
          id: "2",
          name: "Cardiovascular Surgery and Interventional Cardiology",
          description: null,
        },
        {
          id: "3",
          name: "Neurosurgery and Interventional Neuroradiology",
          description: null,
        },
        {
          id: "4",
          name: "Orthopedic Surgery and Sports Medicine",
          description: null,
        },
        {
          id: "5",
          name: "Plastic and Reconstructive Surgery",
          description: null,
        },
        {
          id: "6",
          name: "Anesthesiology and Pain Management",
          description: null,
        },
        {
          id: "7",
          name: "Internal Medicine and Infectious Diseases",
          description: null,
        },
        { id: "8", name: "Pediatric Emergency Medicine", description: null },
      ],
    }),
  },
};

export const StressTestMinimalData: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "A",
        lastName: "B",
        avatar: null,
      },
      title: null,
      specialties: [],
      yearsOfExperience: 0,
      score: 0,
      settings: {
        id: faker.string.uuid(),
        openToWork: null,
        openToOnCall: null,
      },
    }),
  },
};

export const StressTestAllMaxData: Story = {
  args: {
    provider: createMockProvider({
      person: {
        id: faker.string.uuid(),
        firstName: "Dr. Christopher Alexander Benjamin",
        lastName: "von Habsburg-Lorraine-Este-Medici",
        avatar:
          "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face",
      },
      title:
        "Senior Emergency Medicine Physician, Critical Care Specialist, Trauma Surgery Consultant, and Medical Director",
      specialties: [
        {
          id: "1",
          name: "Emergency Medicine and Acute Care",
          description: null,
        },
        {
          id: "2",
          name: "Critical Care and Intensive Care Medicine",
          description: null,
        },
        {
          id: "3",
          name: "Trauma Surgery and Emergency Operations",
          description: null,
        },
        {
          id: "4",
          name: "Cardiovascular Surgery and Interventions",
          description: null,
        },
        {
          id: "5",
          name: "Neurosurgery and Brain Interventions",
          description: null,
        },
        {
          id: "6",
          name: "Orthopedic Surgery and Joint Replacement",
          description: null,
        },
        {
          id: "7",
          name: "Plastic Surgery and Reconstructive Medicine",
          description: null,
        },
        {
          id: "8",
          name: "Anesthesiology and Pain Management",
          description: null,
        },
        {
          id: "9",
          name: "Internal Medicine and Diagnostics",
          description: null,
        },
        {
          id: "10",
          name: "Pediatric Emergency and Critical Care",
          description: null,
        },
      ],
      yearsOfExperience: 25.5,
      score: 98,
      settings: {
        id: faker.string.uuid(),
        openToWork: true,
        openToOnCall: true,
      },
      verification: {
        id: faker.string.uuid(),
        status: VerificationStatus.APPROVED,
        verifiedAt: faker.date.recent(),
      },
    }),
  },
};

export const StressTestLongLocation: Story = {
  args: {
    provider: createMockProvider({
      address: {
        id: faker.string.uuid(),
        formatted:
          "San Francisco-Oakland-Berkeley Metropolitan Area, California, United States",
        timeZone: "America/Los_Angeles",
      },
    }),
  },
};

// Grid stress test with mixed edge cases
export const StressTestGridMixed: Story = {
  args: {
    provider: createMockProvider(),
  },
  decorators: [
    (Story) => (
      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        {/* Normal card */}
        <Story />
        {/* Long name */}
        <ProviderCard
          provider={createMockProvider({
            person: {
              id: faker.string.uuid(),
              firstName: "Dr. Elizabeth Margaret Victoria",
              lastName: "van der Williamson-Smith-Johnson",
              avatar: null,
            },
          })}
        />
        {/* Many specialties */}
        <ProviderCard
          provider={createMockProvider({
            specialties: Array.from({ length: 8 }, (_, i) => ({
              id: `${i}`,
              name: `Very Long Specialty Name ${i + 1}`,
              description: null,
            })),
          })}
        />
        {/* Minimal data */}
        <ProviderCard
          provider={createMockProvider({
            person: {
              id: faker.string.uuid(),
              firstName: "A",
              lastName: "B",
              avatar: null,
            },
            title: null,
            specialties: [],
            yearsOfExperience: 0,
            score: 0,
          })}
        />
        {/* All max data */}
        <ProviderCard
          provider={createMockProvider({
            person: {
              id: faker.string.uuid(),
              firstName: "Dr. Christopher Alexander",
              lastName: "von Habsburg-Lorraine-Este",
              avatar: faker.image.avatar(),
            },
            title:
              "Senior Emergency Medicine Physician and Critical Care Specialist",
            specialties: Array.from({ length: 10 }, (_, i) => ({
              id: `${i}`,
              name: `Specialty ${i + 1}`,
              description: null,
            })),
            yearsOfExperience: 25.5,
            score: 98,
            settings: {
              id: faker.string.uuid(),
              openToWork: true,
              openToOnCall: true,
            },
          })}
        />
        {/* Loading */}
        <ProviderCard loading={true} />
      </div>
    ),
  ],
  parameters: {
    layout: "fullscreen",
  },
};

// Different Professional Roles
export const Physician: Story = {
  args: {
    provider: createMockProvider({
      title: "Emergency Medicine Physician",
      specialties: [
        { id: "1", name: "Emergency Medicine", description: null },
        { id: "2", name: "Critical Care", description: null },
      ],
    }),
  },
};

export const Nurse: Story = {
  args: {
    provider: createMockProvider({
      title: "Registered Nurse",
      specialties: [
        { id: "1", name: "Critical Care", description: null },
        { id: "2", name: "Emergency Medicine", description: null },
      ],
    }),
  },
};

export const NursePractitioner: Story = {
  args: {
    provider: createMockProvider({
      title: "Nurse Practitioner",
      specialties: [
        { id: "1", name: "Family Medicine", description: null },
        { id: "2", name: "Pediatrics", description: null },
      ],
    }),
  },
};

export const PhysicianAssistant: Story = {
  args: {
    provider: createMockProvider({
      title: "Physician Assistant",
      specialties: [
        { id: "1", name: "Orthopedics", description: null },
        { id: "2", name: "Sports Medicine", description: null },
      ],
    }),
  },
};

// Grid Layout Example
export const GridExample: Story = {
  args: {
    provider: createMockProvider(),
  },
  decorators: [
    (Story) => (
      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        <Story />
        <ProviderCard provider={createMockProvider()} />
        <ProviderCard provider={createMockProvider()} />
        <ProviderCard provider={createMockProvider()} />
        <ProviderCard provider={createMockProvider()} />
        <ProviderCard provider={createMockProvider()} />
      </div>
    ),
  ],
  parameters: {
    layout: "fullscreen",
  },
};
