import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { RouterOutputs } from "@/api";

import { AccountStatus, ProviderStatus, VerificationStatus } from "@/api";
import ProviderPanel from "@/www/organizations/dashboard/providers-panel/ProvidersPanel";

// Create mock provider data - matches the new search API structure
const createMockProvider = (
  overrides?: Partial<RouterOutputs["providers"]["search"]["items"][number]>,
): RouterOutputs["providers"]["search"]["items"][number] => {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const createdDate = faker.date.between({
    from: "2020-01-01",
    to: new Date(),
  });

  // Generate experiences for years calculation
  const experiences = Array.from(
    { length: faker.number.int({ min: 1, max: 5 }) },
    () => ({
      id: faker.string.uuid(),
      role: faker.person.jobTitle(),
      company:
        faker.company.name() +
        " " +
        faker.helpers.arrayElement([
          "Hospital",
          "Medical Center",
          "Clinic",
          "Health System",
        ]),
      startDate: faker.date.past({ years: 15 }),
      endDate:
        faker.helpers.maybe(() => faker.date.recent(), {
          probability: 0.4,
        }) ?? null,
    }),
  );

  // Calculate years of experience
  const yearsOfExperience = experiences.reduce((years, exp) => {
    const startDate = new Date(exp.startDate);
    const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
    const experienceYears =
      (endDate.getTime() - startDate.getTime()) /
      (1000 * 60 * 60 * 24 * 365.25);
    return years + Math.max(0, experienceYears);
  }, 0);

  // Get latest experience
  const sortedExperiences = experiences.sort(
    (a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime(),
  );
  const latestExperience = sortedExperiences[0]
    ? {
        role: sortedExperiences[0].role,
        company: sortedExperiences[0].company,
      }
    : null;

  return {
    id: faker.string.uuid(),
    personId: faker.string.uuid(),
    addressId: faker.string.uuid(),
    accountId: faker.string.uuid(),
    calendarId: faker.string.uuid(),
    score: faker.number.int({ min: 0, max: 100 }),
    title: faker.helpers.arrayElement([
      "Physician",
      "Nurse Practitioner",
      "Registered Nurse",
      "Physician Assistant",
      "Medical Technician",
      "Respiratory Therapist",
      "Physical Therapist",
      "Emergency Medicine Physician",
      "Cardiologist",
      "Neurologist",
    ]),
    status: faker.helpers.enumValue(ProviderStatus),
    verificationStatus: faker.helpers.enumValue(VerificationStatus),
    accountStatus: faker.helpers.enumValue(AccountStatus),
    spokenLanguages: faker.helpers.arrayElements(
      ["English", "Spanish", "French", "German", "Mandarin", "Portuguese"],
      { min: 1, max: 3 },
    ),
    gender:
      faker.helpers.maybe(() => faker.person.sex(), { probability: 0.7 }) ??
      null,
    createdAt: createdDate,
    updatedAt: faker.date.recent(),
    deletedAt: null,
    // Calculated fields from API
    yearsOfExperience: Math.round(yearsOfExperience * 10) / 10,
    latestExperience,
    joinedDate: createdDate,
    isRecentlyJoined:
      (new Date().getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24) <=
      90,
    // Related data (privacy-compliant)
    person: {
      id: faker.string.uuid(),
      firstName,
      lastName,
      avatar:
        faker.helpers.maybe(() => faker.image.avatar(), {
          probability: 0.7,
        }) ?? null,
    },
    address: {
      id: faker.string.uuid(),
      city: faker.location.city(),
      state: faker.location.state(),
      timeZone: faker.location.timeZone(),
    },
    specialties: Array.from(
      { length: faker.number.int({ min: 1, max: 4 }) },
      () => ({
        id: faker.string.uuid(),
        name: faker.helpers.arrayElement([
          "Emergency Medicine",
          "Internal Medicine",
          "Cardiology",
          "Neurology",
          "Pediatrics",
          "Oncology",
          "Orthopedics",
          "Dermatology",
          "Psychiatry",
          "Radiology",
          "Anesthesiology",
          "Surgery",
          "Critical Care",
          "Family Medicine",
        ]),
        description:
          faker.helpers.maybe(() => faker.lorem.sentence(), {
            probability: 0.5,
          }) ?? null,
      }),
    ),
    experiences,
    verification: {
      id: faker.string.uuid(),
      status: faker.helpers.enumValue(VerificationStatus),
      verifiedAt: faker.date.recent(),
    },
    settings: {
      id: faker.string.uuid(),
      openToWork:
        faker.helpers.maybe(() => faker.datatype.boolean(), {
          probability: 0.8,
        }) ?? null,
      openToOnCall:
        faker.helpers.maybe(() => faker.datatype.boolean(), {
          probability: 0.6,
        }) ?? null,
    },
    ...overrides,
  };
};

// Filter options for stories
const roleOptions = [
  { label: "All Positions", value: "" },
  { label: "Physician", value: "Physician" },
  { label: "Nurse", value: "Nurse" },
  { label: "Technician", value: "Technician" },
  { label: "Therapist", value: "Therapist" },
  { label: "Assistant", value: "Assistant" },
  { label: "Specialist", value: "Specialist" },
] as const;

const joinDateOptions = [
  { label: "All Time", value: "" },
  { label: "Last 30 Days", value: "30" },
  { label: "Last 90 Days", value: "90" },
  { label: "Last 6 Months", value: "180" },
] as const;

const criteriaOptions = [
  { label: "All Providers", value: "" },
  { label: "Verified Only", value: "verified" },
  { label: "Available for Work", value: "available" },
  { label: "High Rated", value: "high-rated" },
] as const;

// Container width utility for different grid configurations
const getContainerClassName = (columns: 2 | 3 | 4): string => {
  const baseClasses = "mx-auto max-h-screen";

  const widthClasses = {
    2: "max-w-4xl", // Narrower for 2 columns - better card proportions
    3: "max-w-screen-lg", // Original width for 3 columns
    4: "max-w-screen-xl", // Wider for 4 columns - accommodate more content
  };

  return `${baseClasses} ${widthClasses[columns]}`;
};

const meta = {
  title: "Pages/Organizations/Dashboard/ProviderPanel",
  component: ProviderPanel,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A comprehensive provider discovery and scouting interface for medical hiring managers.",
      },
    },
  },
} satisfies Meta<typeof ProviderPanel>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    providers: [],
    totalProviders: 0,
    loading: false,
    error: null,
    currentQuery: "",
    currentRole: "",
    currentJoinDate: "",
    currentCriteria: "",
    currentPage: 1,
    pageSize: 12, // Updated for responsive 2-column grid (6 rows × 2 columns)
    roleOptions,
    joinDateOptions,
    criteriaOptions,
    gridColumns: 2, // Responsive: Mobile(1) → Small+(2)
    className: getContainerClassName(2), // Dynamic container width
    onRefresh: () => {},
    searchNamespace: "providers",
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
    loading: true,
    pageSize: 12, // Show 12 skeleton cards for optimal 2-column responsive layout
  },
};

export const ErrorState: Story = {
  args: {
    ...Default.args,
    error: new Error(
      "Failed to load providers. Please check your connection and try again.",
    ),
  },
};

export const Empty: Story = {
  args: {
    ...Default.args,
    currentQuery: "nonexistent provider",
  },
};

export const WithProviders: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 12 }, () => createMockProvider()), // Perfect responsive 2-column grid
    totalProviders: 156,
    pageSize: 12,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Standard provider grid with responsive behavior: 1 column (mobile) → 2 columns (small screens+).",
      },
    },
  },
};

export const SmallDataset: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 3 }, () => createMockProvider()),
    totalProviders: 3,
    pageSize: 8,
  },
};

export const VerifiedProvidersOnly: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 6 }, () =>
      createMockProvider({
        verification: {
          id: faker.string.uuid(),
          status: VerificationStatus.APPROVED,
          verifiedAt: faker.date.recent(),
        },
      }),
    ),
    totalProviders: 47,
    currentCriteria: "verified",
    pageSize: 8,
  },
};

export const AvailableProviders: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 6 }, () =>
      createMockProvider({
        settings: {
          id: faker.string.uuid(),
          openToWork: true,
          openToOnCall: faker.datatype.boolean(),
        },
      }),
    ),
    totalProviders: 23,
    currentCriteria: "available",
    pageSize: 8,
  },
};

export const HighScoredProviders: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 8 }, () =>
      createMockProvider({
        score: faker.number.int({ min: 80, max: 100 }),
      }),
    ),
    totalProviders: 34,
    currentCriteria: "high-rated",
    pageSize: 8,
  },
};

export const PhysiciansOnly: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 6 }, () =>
      createMockProvider({
        title: faker.helpers.arrayElement([
          "Emergency Medicine Physician",
          "Internal Medicine Physician",
          "Cardiologist",
          "Neurologist",
          "Pediatrician",
          "Oncologist",
          "Orthopedic Surgeon",
          "Dermatologist",
          "Psychiatrist",
        ]),
        specialties: [
          {
            id: faker.string.uuid(),
            name: faker.helpers.arrayElement([
              "Emergency Medicine",
              "Internal Medicine",
              "Cardiology",
              "Neurology",
              "Pediatrics",
              "Oncology",
              "Orthopedics",
              "Dermatology",
              "Psychiatry",
            ]),
            description: faker.lorem.sentence(),
          },
        ],
      }),
    ),
    totalProviders: 89,
    currentRole: "Physician",
    pageSize: 8,
  },
};

export const RecentJoins: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 6 }, () =>
      createMockProvider({
        createdAt: faker.date.recent({ days: 30 }),
      }),
    ),
    totalProviders: 18,
    currentJoinDate: "30",
    pageSize: 8,
  },
};

export const SearchResults: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 3 }, () =>
      createMockProvider({
        person: {
          id: faker.string.uuid(),
          firstName: faker.helpers.arrayElement([
            "John",
            "Jane",
            "Jennifer",
            "James",
            "Jessica",
          ]),
          lastName: faker.helpers.arrayElement([
            "Johnson",
            "Jackson",
            "Jones",
            "Jenkins",
            "Joyce",
          ]),
          avatar: faker.image.avatar(),
        },
      }),
    ),
    totalProviders: 5,
    currentQuery: "J",
    pageSize: 8,
  },
};

export const LargeDataset: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 8 }, () => createMockProvider()),
    totalProviders: 1247,
    currentPage: 3,
    pageSize: 8,
  },
};

export const MixedStates: Story = {
  args: {
    ...Default.args,
    providers: [
      // Verified provider
      createMockProvider({
        verification: {
          id: faker.string.uuid(),
          status: VerificationStatus.APPROVED,
          verifiedAt: faker.date.recent(),
        },
        settings: {
          id: faker.string.uuid(),
          openToWork: true,
          openToOnCall: true,
        },
        score: 95,
      }),
      // Pending verification
      createMockProvider({
        verification: {
          id: faker.string.uuid(),
          status: VerificationStatus.PENDING,
          verifiedAt: null,
        },
        settings: {
          id: faker.string.uuid(),
          openToWork: false,
          openToOnCall: false,
        },
        score: 67,
      }),
      // Provider with no avatar
      createMockProvider({
        person: {
          id: faker.string.uuid(),
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          avatar: null,
        },
        specialties: [],
        score: 42,
      }),
      // High experience provider
      createMockProvider({
        experiences: Array.from({ length: 8 }, () => ({
          id: faker.string.uuid(),
          role: faker.person.jobTitle(),
          company: faker.company.name() + " Medical Center",
          startDate: faker.date.past({ years: 20 }),
          endDate:
            faker.helpers.maybe(() => faker.date.recent(), {
              probability: 0.3,
            }) ?? null,
        })),
        score: 88,
      }),
    ],
    totalProviders: 4,
    pageSize: 8,
  },
};

// New scenarios for testing less than 8 providers and empty states
export const FewProviders: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 3 }, () => createMockProvider()),
    totalProviders: 3,
    pageSize: 8,
  },
};

export const VeryFewProviders: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 1 }, () => createMockProvider()),
    totalProviders: 1,
    pageSize: 8,
  },
};

export const EmptyList: Story = {
  args: {
    ...Default.args,
    providers: [],
    totalProviders: 0,
    pageSize: 8,
  },
};

export const EmptySearchResults: Story = {
  args: {
    ...Default.args,
    providers: [],
    totalProviders: 0,
    currentQuery: "Dr. Nonexistent Provider",
    pageSize: 8,
  },
};

export const EmptyFilterResults: Story = {
  args: {
    ...Default.args,
    providers: [],
    totalProviders: 0,
    currentCriteria: "verified",
    currentRole: "Specialist",
    pageSize: 8,
  },
};

// New story to showcase the perfect 2-column grid
export const PerfectGrid: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 8 }, () => createMockProvider()),
    totalProviders: 64,
    pageSize: 8,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the optimal 8-provider layout in a 2-column grid (4 rows × 2 columns).",
      },
    },
  },
};

// New story to show multiple pages with 8-item pagination
export const MultiplePages: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 8 }, () => createMockProvider()),
    totalProviders: 72, // 9 pages × 8 items = perfect pagination
    currentPage: 2,
    pageSize: 8,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the pagination behavior with 8 items per page across multiple pages.",
      },
    },
  },
};

// Grid configuration demonstrations
export const TwoColumnLayout: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 8 }, () => createMockProvider()),
    totalProviders: 64,
    pageSize: 8,
    gridColumns: 2,
    className: getContainerClassName(2),
  },
  parameters: {
    docs: {
      description: {
        story:
          "2-column layout with responsive behavior: 1 column (mobile) → 2 columns (small screens+). Optimal for focused browsing with larger cards.",
      },
    },
  },
};

export const ThreeColumnLayout: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 9 }, () => createMockProvider()),
    totalProviders: 72,
    pageSize: 9,
    gridColumns: 3,
    className: getContainerClassName(3),
  },
  parameters: {
    docs: {
      description: {
        story:
          "3-column layout with progressive responsive behavior: 1 column (mobile) → 2 columns (small screens+) → 3 columns (large screens+). Balanced view for standard browsing.",
      },
    },
  },
};

export const FourColumnLayout: Story = {
  args: {
    ...Default.args,
    providers: Array.from({ length: 12 }, () => createMockProvider()),
    totalProviders: 96,
    pageSize: 12,
    gridColumns: 4,
    className: getContainerClassName(4),
  },
  parameters: {
    docs: {
      description: {
        story:
          "4-column layout with responsive behavior: 1 column (mobile) → 2 columns (small screens+) → 4 columns (extra large screens+). Dense view for power users on large screens.",
      },
    },
  },
};
