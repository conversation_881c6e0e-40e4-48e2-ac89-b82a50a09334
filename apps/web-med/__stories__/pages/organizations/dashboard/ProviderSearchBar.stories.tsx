/**
 * PROVIDER SEARCH BAR STORIES
 * ===========================
 *
 * Storybook stories for the ProviderSearchBar component.
 * Tests various search and filter states, loading states, and responsive layout.
 */

import type { Meta, StoryObj } from "@storybook/react";

import { SearchParams } from "@axa/ui/search";

import ProviderSearchBar from "@/www/organizations/dashboard/providers-panel/ProviderSearchBar";

const meta: Meta<typeof ProviderSearchBar> = {
  title: "Pages/Organizations/Dashboard/ProviderSearchBar",
  component: ProviderSearchBar,
  parameters: {
    layout: "padded",
    nextjs: {
      appDirectory: true,
    },
    docs: {
      description: {
        component: `
The ProviderSearchBar component provides the search interface for the providers panel.

**Responsive Design:**
- **Large screens**: All controls in one row (search takes 1/2 width, filters take 1/6 each)
- **Small screens**: Optimized layout with search and role full-width, join date and criteria sharing a row

**Small Screen Layout:**
\`\`\`
[     Search Input     ]
[     Role Filter      ]
[Join Date] [Criteria]
\`\`\`

**Large Screen Layout:**
\`\`\`
[    Search (50%)    ] [Role] [Join] [Criteria]
\`\`\`

**Features:**
- Text search input with URL state management
- Filter dropdowns for role, join date, and criteria
- Results summary display
- Beautiful responsive behavior with optimal space usage

This component handles all search and filter functionality for the providers panel.
        `,
      },
    },
  },
  decorators: [
    (Story) => (
      <SearchParams>
        <div className="w-full">
          <Story />
        </div>
      </SearchParams>
    ),
  ],
  argTypes: {
    resultsCount: {
      control: { type: "number", min: 0, max: 1000 },
      description: "Number of results currently displayed",
    },
    totalResults: {
      control: { type: "number", min: 0, max: 10000 },
      description: "Total number of results available",
    },
    loading: {
      control: { type: "boolean" },
      description: "Whether the search is currently loading",
    },
    searchNamespace: {
      control: { type: "text" },
      description: "Namespace for search URL parameters",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderSearchBar>;

// Filter options (same as used in the actual component)
const roleOptions = [
  { label: "All Positions", value: "" },
  { label: "Physician", value: "Physician" },
  { label: "Nurse", value: "Nurse" },
  { label: "Technician", value: "Technician" },
  { label: "Therapist", value: "Therapist" },
  { label: "Assistant", value: "Assistant" },
  { label: "Specialist", value: "Specialist" },
];

const joinDateOptions = [
  { label: "All Time", value: "" },
  { label: "Last 30 Days", value: "30" },
  { label: "Last 90 Days", value: "90" },
  { label: "Last 6 Months", value: "180" },
];

const criteriaOptions = [
  { label: "All Providers", value: "" },
  { label: "Verified Only", value: "verified" },
  { label: "Available for Work", value: "available" },
  { label: "High Rated", value: "high-rated" },
];

// Default args
const defaultArgs = {
  roleOptions,
  joinDateOptions,
  criteriaOptions,
  searchNamespace: "providers",
  loading: false,
};

/**
 * DEFAULT STATE
 * =============
 * Basic search bar with typical results display - shows responsive layout
 */
export const Default: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 12,
    totalResults: 847,
  },
};

/**
 * WITH MANY RESULTS
 * =================
 * Search bar showing large result set
 */
export const ManyResults: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 50,
    totalResults: 5247,
  },
};

/**
 * FEW RESULTS
 * ===========
 * Search bar with limited results
 */
export const FewResults: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 3,
    totalResults: 3,
  },
};

/**
 * SINGLE RESULT
 * =============
 * Search bar with only one result
 */
export const SingleResult: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 1,
    totalResults: 1,
  },
};

/**
 * NO RESULTS
 * ==========
 * Search bar showing no results (results summary hidden when results=0)
 */
export const NoResults: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 0,
    totalResults: 0,
  },
};

/**
 * LOADING STATE
 * =============
 * Search bar in loading state (results summary hidden)
 */
export const Loading: Story = {
  args: {
    ...defaultArgs,
    resultsCount: 0,
    totalResults: 0,
    loading: true,
  },
};

/**
 * WITH ACTIVE FILTERS
 * ====================
 * Search bar with active filters applied - shows all controls in use
 */
export const WithActiveFilters: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "cardiology",
    currentRole: "Physician",
    currentJoinDate: "90",
    currentCriteria: "verified",
    resultsCount: 8,
    totalResults: 23,
  },
};

/**
 * PHYSICIAN FILTER
 * ================
 * Search bar focused on physician results
 */
export const PhysicianFilter: Story = {
  args: {
    ...defaultArgs,
    currentRole: "Physician",
    resultsCount: 156,
    totalResults: 1834,
  },
};

/**
 * RECENT JOINERS
 * ==============
 * Search bar filtered for recent joiners
 */
export const RecentJoiners: Story = {
  args: {
    ...defaultArgs,
    currentJoinDate: "30",
    resultsCount: 42,
    totalResults: 42,
  },
};

/**
 * VERIFIED ONLY
 * =============
 * Search bar filtered for verified providers only
 */
export const VerifiedOnly: Story = {
  args: {
    ...defaultArgs,
    currentCriteria: "verified",
    resultsCount: 324,
    totalResults: 324,
  },
};

/**
 * SEARCH QUERY ACTIVE
 * ===================
 * Search bar with active text search
 */
export const SearchQueryActive: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "emergency medicine",
    resultsCount: 27,
    totalResults: 27,
  },
};

/**
 * COMPLEX FILTER COMBINATION
 * ==========================
 * Search bar with multiple filters creating specific results
 */
export const ComplexFilterCombination: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "cardiac",
    currentRole: "Specialist",
    currentJoinDate: "180",
    currentCriteria: "high-rated",
    resultsCount: 5,
    totalResults: 5,
  },
};

/**
 * DIFFERENT NAMESPACE
 * ===================
 * Search bar using a different namespace for URL parameters
 */
export const DifferentNamespace: Story = {
  args: {
    ...defaultArgs,
    searchNamespace: "team-search",
    resultsCount: 18,
    totalResults: 92,
  },
};

/**
 * MOBILE VIEW (NARROW)
 * ====================
 * Search bar in mobile/narrow viewport to test responsive stacking
 */
export const MobileView: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "nurse",
    currentRole: "Nurse",
    resultsCount: 25,
    totalResults: 185,
  },
  decorators: [
    (Story) => (
      <SearchParams>
        <div className="max-w-sm">
          <Story />
        </div>
      </SearchParams>
    ),
  ],
};

/**
 * TABLET VIEW (MEDIUM)
 * ====================
 * Search bar in tablet viewport to test responsive behavior
 */
export const TabletView: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "emergency",
    currentCriteria: "available",
    resultsCount: 67,
    totalResults: 423,
  },
  decorators: [
    (Story) => (
      <SearchParams>
        <div className="max-w-2xl">
          <Story />
        </div>
      </SearchParams>
    ),
  ],
};

/**
 * WIDE DESKTOP VIEW
 * =================
 * Search bar in wide desktop viewport to show full responsive layout
 */
export const WideDesktopView: Story = {
  args: {
    ...defaultArgs,
    currentQuery: "surgery",
    currentRole: "Specialist",
    currentJoinDate: "180",
    currentCriteria: "high-rated",
    resultsCount: 143,
    totalResults: 2156,
  },
  decorators: [
    (Story) => (
      <SearchParams>
        <div className="min-w-full">
          <Story />
        </div>
      </SearchParams>
    ),
  ],
};
