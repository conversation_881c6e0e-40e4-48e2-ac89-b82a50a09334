import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { RouterOutputs } from "@/api";

import Facilities from "@/www/organizations/facilities/Facilities";

const meta = {
  title: "Pages/Organizations/Facilities/page",
  component: Facilities,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/facilities",
      },
    },
  },
} satisfies Meta<typeof Facilities>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockFacilities: RouterOutputs["locations"]["getMany"] = {
  items: [
    {
      id: "loc_1",
      name: "City General Hospital",
      type: "HOSPITAL",
      description: "A major hospital in the city center",
      address: {
        formatted: "123 Medical Center Dr, New York, NY 10001",
        timeZone: "America/New_York",
        latitude: 40.7128,
        longitude: -74.006,
      },
      organization: {
        id: "org_1",
        name: "City Healthcare Network",
        avatar: "/placeholder.svg",
        path: "/organizations/org_1",
        type: "CLIENT",
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
        deletedAt: null,
      },
    },
    {
      id: "loc_2",
      name: "Westside Medical Center",
      type: "CLINIC",
      description: "Modern medical clinic with state-of-the-art facilities",
      address: {
        formatted: "456 Health Ave, New York, NY 10002",
        timeZone: "America/New_York",
        latitude: 40.7145,
        longitude: -74.008,
      },
      organization: {
        id: "org_2",
        name: "Westside Healthcare",
        avatar: "/placeholder.svg",
        path: "/organizations/org_2",
        type: "CLIENT",
        status: "ACTIVE",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
        deletedAt: null,
      },
    },
  ],
  total: 2,
};

export const Default: Story = {
  args: {
    facilities: mockFacilities,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    error: {
      message: "Failed to load facilities",
      data: {
        code: "INTERNAL_SERVER_ERROR",
        httpStatus: 500,
        path: "locations.getMany",
        zodError: null,
      },
      code: "INTERNAL_SERVER_ERROR",
      shape: {
        data: {
          code: "INTERNAL_SERVER_ERROR",
          httpStatus: 500,
          path: "locations.getMany",
          zodError: null,
        },
        message: "Failed to load facilities",
      },
    },
  },
};

export const Empty: Story = {
  args: {
    facilities: {
      items: [],
      total: 0,
    },
  },
};
