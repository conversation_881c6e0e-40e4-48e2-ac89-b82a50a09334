import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import type { DocumentQueryResult } from "@/www/organizations/document/Document";

import Document from "@/www/organizations/document/Document";

const meta = {
  title: "Pages/Organizations/Document/page",
  component: Document,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/app/documents/abc-123",
      },
    },
  },
} satisfies Meta<typeof Document>;

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for document
const mockDocument: DocumentQueryResult = {
  id: faker.string.uuid(),
  type: faker.system.mimeType(),
  name: faker.system.fileName(),
  url: faker.internet.url(),
  size: faker.number.int({ min: 1000, max: 10000000 }),
  organization: {
    id: faker.string.uuid(),
    name: faker.company.name(),
    avatar: faker.helpers.arrayElement([faker.image.avatar(), null]),
  },
};

export const Default: Story = {
  args: {
    document: mockDocument,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Error: Story = {
  args: {
    error: {
      message: "Failed to load document data",
    },
  },
};
