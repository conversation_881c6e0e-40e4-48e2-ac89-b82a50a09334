import type { Meta, StoryObj } from "@storybook/react";

import VerificationFeaturePage from "@/www/public/features/verification";

const meta = {
  title: "Pages/Public/Features/Verification",
  component: VerificationFeaturePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/features/verification",
      },
    },
  },
} satisfies Meta<typeof VerificationFeaturePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
