import type { <PERSON>a, StoryObj } from "@storybook/react";

import PublicLayout from "@/components/layouts/public/PublicLayout";
import CompanyPage from "@/www/public/company";

const meta: Meta<typeof CompanyPage> = {
  title: "Pages/Public/Company",
  component: CompanyPage,
  parameters: {
    layout: "fullscreen",
  },
  decorators: [
    (Story) => (
      <PublicLayout>
        <Story />
      </PublicLayout>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof CompanyPage>;

export const Default: Story = {
  args: {},
};
