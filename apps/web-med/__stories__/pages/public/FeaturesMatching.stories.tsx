import type { <PERSON>a, StoryObj } from "@storybook/react";

import MatchingFeaturePage from "@/www/public/features/matching";

const meta = {
  title: "Pages/Public/Features/Matching",
  component: MatchingFeaturePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/features/matching",
      },
    },
  },
} satisfies Meta<typeof MatchingFeaturePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
