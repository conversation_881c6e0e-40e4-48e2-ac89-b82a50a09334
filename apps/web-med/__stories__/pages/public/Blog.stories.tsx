import type { <PERSON>a, StoryObj } from "@storybook/react";

import BlogPage from "@/www/public/blog";

const meta = {
  title: "Pages/Public/Blog",
  component: BlogPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/blog",
      },
    },
  },
} satisfies Meta<typeof BlogPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
