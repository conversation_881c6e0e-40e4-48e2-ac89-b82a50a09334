import type { Meta, StoryObj } from "@storybook/react";

import MobileFeaturePage from "@/www/public/features/mobile";

const meta = {
  title: "Pages/Public/Features/Mobile",
  component: MobileFeaturePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/features/mobile",
      },
    },
  },
} satisfies Meta<typeof MobileFeaturePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
