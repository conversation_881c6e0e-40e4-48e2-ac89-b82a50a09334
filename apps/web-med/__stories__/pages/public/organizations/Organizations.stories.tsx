import type { <PERSON>a, StoryObj } from "@storybook/react";

import OrganizationsPage from "@/www/public/organizations/next";

const meta = {
  title: "Pages/Public/Organizations/page",
  tags: ["pages"],
  component: OrganizationsPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/organizations",
      },
    },
  },
} satisfies Meta<typeof OrganizationsPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
