import type { Meta, StoryObj } from "@storybook/react";

import TermsOfUsePage from "@/www/public/terms/TermsOfUse";

const meta = {
  title: "Pages/Public/TermsOfUse",
  component: TermsOfUsePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/terms-of-use",
      },
    },
  },
} satisfies Meta<typeof TermsOfUsePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
