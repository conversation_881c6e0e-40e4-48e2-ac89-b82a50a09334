import type { <PERSON>a, StoryObj } from "@storybook/react";

import FAQPage from "@/www/public/faq";

const meta = {
  title: "Pages/Public/FAQ",
  component: FAQPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/faq",
      },
    },
  },
} satisfies Meta<typeof FAQPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
