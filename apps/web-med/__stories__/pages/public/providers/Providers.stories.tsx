import type { <PERSON>a, StoryObj } from "@storybook/react";

import ProvidersPage from "@/www/public/providers";

const meta = {
  title: "Pages/Public/Providers/page",
  tags: ["pages"],
  component: ProvidersPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/providers",
      },
    },
  },
} satisfies Meta<typeof ProvidersPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
