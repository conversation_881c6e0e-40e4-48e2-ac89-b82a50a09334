import type { <PERSON>a, StoryObj } from "@storybook/react";

import HowItWorksPage from "@/www/public/how-it-works";

const meta = {
  title: "Pages/Public/HowItWorks",
  component: HowItWorksPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/how-it-works",
      },
    },
  },
} satisfies Meta<typeof HowItWorksPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
