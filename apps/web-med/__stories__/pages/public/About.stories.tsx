import type { <PERSON>a, StoryObj } from "@storybook/react";

import AboutPage from "@/www/public/about";

const meta = {
  title: "Pages/Public/About",
  component: AboutPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/about",
      },
    },
  },
} satisfies Meta<typeof AboutPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
