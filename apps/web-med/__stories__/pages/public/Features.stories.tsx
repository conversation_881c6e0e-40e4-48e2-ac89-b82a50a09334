import type { Meta, StoryObj } from "@storybook/react";

import FeaturesPage from "@/www/public/features";

const meta = {
  title: "Pages/Public/Features",
  component: FeaturesPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/features",
      },
    },
  },
} satisfies Meta<typeof FeaturesPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
