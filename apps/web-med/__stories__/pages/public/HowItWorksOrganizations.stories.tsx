import type { <PERSON>a, StoryObj } from "@storybook/react";

import OrganizationsHowItWorksPage from "@/www/public/how-it-works/organizations";

const meta = {
  title: "Pages/Public/HowItWorks/Organizations",
  component: OrganizationsHowItWorksPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/how-it-works/organizations",
      },
    },
  },
} satisfies Meta<typeof OrganizationsHowItWorksPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
