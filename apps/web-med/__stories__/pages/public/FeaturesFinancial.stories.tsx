import type { Meta, StoryObj } from "@storybook/react";

import FinancialFeaturePage from "@/www/public/features/financial";

const meta = {
  title: "Pages/Public/Features/Financial",
  component: FinancialFeaturePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/features/financial",
      },
    },
  },
} satisfies Meta<typeof FinancialFeaturePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
