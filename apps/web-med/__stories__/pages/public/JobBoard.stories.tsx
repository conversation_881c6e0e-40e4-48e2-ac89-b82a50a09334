import type { <PERSON>a, StoryObj } from "@storybook/react";

import JobBoardPage from "@/www/public/job-board";

const meta = {
  title: "Pages/Public/JobBoard",
  component: JobBoardPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/job-board",
      },
    },
  },
} satisfies Meta<typeof JobBoardPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
