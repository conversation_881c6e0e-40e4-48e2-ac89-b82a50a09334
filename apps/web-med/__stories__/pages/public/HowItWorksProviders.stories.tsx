import type { Meta, StoryObj } from "@storybook/react";

import ProvidersHowItWorksPage from "@/www/public/how-it-works/providers";

const meta = {
  title: "Pages/Public/HowItWorks/Providers",
  component: ProvidersHowItWorksPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/how-it-works/providers",
      },
    },
  },
} satisfies Meta<typeof ProvidersHowItWorksPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
