import type { Meta, StoryObj } from "@storybook/react";

import PrivacyPolicyPage from "@/www/public/privacy/PrivacyPolicy";

const meta = {
  title: "Pages/Public/PrivacyPolicy",
  component: PrivacyPolicyPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/privacy-policy",
      },
    },
  },
} satisfies Meta<typeof PrivacyPolicyPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
