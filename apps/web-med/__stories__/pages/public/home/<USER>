import type { <PERSON>a, StoryObj } from "@storybook/react";

import HomePage from "@/www/public/home";

const meta = {
  title: "Pages/Public/Home/page",
  tags: ["pages"],
  component: HomePage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/",
      },
    },
  },
} satisfies Meta<typeof HomePage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
