import type { <PERSON>a, StoryObj } from "@storybook/react";

import ContactUsPage from "@/www/public/contact/ContactUs";

const meta = {
  title: "Pages/Public/ContactUs",
  component: ContactUsPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/contact-us",
      },
    },
  },
} satisfies Meta<typeof ContactUsPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
