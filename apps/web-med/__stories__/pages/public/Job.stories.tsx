import type { <PERSON>a, StoryObj } from "@storybook/react";

import JobPage from "@/www/public/job";

const meta = {
  title: "Pages/Public/Job",
  component: JobPage,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
      router: {
        path: "/job",
      },
    },
  },
} satisfies Meta<typeof JobPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
