import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Contributing" />

# Contributing to AXA Professionals

This guide outlines how to contribute to the AXA Professionals project, with a focus on creating and maintaining Storybook stories.

## Getting Started

1. Clone the repository
2. Install dependencies with `npm install` or `yarn install`
3. Start the development server with `npm run dev` or `yarn dev`
4. Start Storybook with `npm run storybook` or `yarn storybook`

## Project Structure

The project follows a specific structure:

- `src/` - Source code
  - `api/` - API client and server adapter
  - `components/` - Reusable UI components
  - `www/` - Page components using Next.js App Router
- `__stories__/` - Storybook stories
  - `components/` - Stories for reusable components
  - `pages/` - Stories for page components

## Creating Storybook Stories

### When to Create Stories

Create Storybook stories for:

1. All reusable UI components
2. All page components
3. Complex UI patterns or flows

### Component Granularity

Break down complex components into smaller, testable pieces:

1. **Create separate components** for distinct UI elements (e.g., message bubbles, headers, forms)
2. **Create separate stories** for each component to test them in isolation
3. **Compose components** together in higher-level stories

For example, a chat widget might include:

- `MessageHeader.tsx` - Displays user avatar, name, and role
- `MessageBubble.tsx` - Displays a single message with options
- `TypingIndicator.tsx` - Shows when someone is typing
- `MessageForm.tsx` - Input form for new messages
- `ChatUI.tsx` - Combines all components into a complete UI
- `ChatWidget.tsx` - Container with data fetching logic

This approach makes components:

- Easier to test
- More reusable
- Simpler to maintain
- Better isolated for debugging

> **Note:** For more detailed guidance on component granularity, see our [Component Design Guidelines](/?path=/docs/component-design--docs).

### Data Organization for Stories

Use centralized data files for related stories:

1. Create a `data.ts` file in your story folder (e.g., `__stories__/components/widgets/chat/data.ts`)
2. Export mock data, sample props, and mock handlers
3. Import this data in your story files

Benefits:

- Ensures consistency across related stories
- Makes updates easier (change in one place)
- Reduces duplication
- Improves maintainability

> **Note:** For comprehensive guidance on data mocking strategies, see our [Data Mocking Guide](/?path=/docs/data-mocking--docs).

Example:

```tsx
// __stories__/components/widgets/chat/data.ts
export const users = {
  agent: {
    id: "agent",
    title: "Support Agent",
    firstName: "Jane",
    lastName: "Smith",
  },
  // more users...
};

export const messages = [
  {
    id: "1",
    content: "Hello! How can I help?",
    authorId: "agent",
    author: users.agent,
    // other properties...
  },
  // more messages...
];

export const mockHandlers = {
  onSubmit: async (values) => console.log("Submitted:", values),
  onEdit: (id) => console.log("Edit:", id),
};
```

Then in your stories:

```tsx
// MessageBubble.stories.tsx
import { messages, mockHandlers } from "./data";

export const Default = {
  args: {
    message: messages[0],
    onEdit: mockHandlers.onEdit,
  },
};
```

### Component Stories

For component stories:

1. Create a new file in `__stories__/components/[category]/[ComponentName].stories.tsx`
2. Follow the component story template in [Conventions](/?path=/docs/conventions-ui-components--docs)
3. Include stories for all component states (default, loading, error, empty)

Example:

```tsx
import type { Meta, StoryObj } from "@storybook/react";

import { ComponentName } from "@/components/path/to/ComponentName";

const meta: Meta<typeof ComponentName> = {
  title: "Components/Category/ComponentName",
  component: ComponentName,
  parameters: {
    layout: "padded",
  },
};

export default meta;
type Story = StoryObj<typeof ComponentName>;

export const Default: Story = {
  args: {
    // Component props
  },
};
```

### Page Stories

For page stories:

1. Create a new file in `__stories__/pages/[path]/[PageName].stories.tsx` that mirrors the structure in `src/www/`
2. Follow the page story template in [Conventions](/?path=/docs/conventions-ui-components--docs#page-conventions)
3. Include the `nextjs.appDirectory: true` parameter for App Router pages

Example:

```tsx
import type { Meta, StoryObj } from "@storybook/react";

import PageComponent from "@/www/path/to/PageComponent";

const meta: Meta<typeof PageComponent> = {
  title: "Pages/Category/PageName",
  component: PageComponent,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Page props if any
  },
};
```

## Best Practices

### Story Organization

- Group related stories together under the same category
- Use consistent naming conventions
- Mirror the application's folder structure in your stories

### Mock Data

- Use realistic mock data in your stories
- Consider using libraries like `@faker-js/faker` for generating mock data
- Keep mock data close to the stories that use it

### Interactive Stories

- Use the `render` function for interactive stories
- Implement realistic interactions
- Document any special interactions in story notes

## Troubleshooting

If you encounter issues while creating or maintaining Storybook stories, refer to our comprehensive [Troubleshooting Guide](/?path=/docs/troubleshooting--docs) for solutions to common problems, including:

- Type errors
- Import issues
- Rendering problems
- Data fetching challenges
- Storybook-specific issues

## Testing with Storybook

Storybook is a valuable tool for testing components:

1. Visual testing: Verify the component looks as expected
2. Interaction testing: Test user interactions
3. Accessibility testing: Check for accessibility issues
4. Responsive testing: Test on different viewport sizes

## Documentation

- Add documentation to your stories using the `docs` parameter
- Include usage examples and prop descriptions
- Document any special considerations or edge cases

## Review Checklist

Before submitting your stories, verify the following:

### Functionality

- [ ] All component states are covered (default, loading, error, empty)
- [ ] Interactive elements work as expected
- [ ] Edge cases are handled properly
- [ ] Data flows correctly between components

### Accessibility

- [ ] Proper semantic HTML is used
- [ ] ARIA attributes are correctly implemented
- [ ] Color contrast meets WCAG standards
- [ ] Keyboard navigation works properly
- [ ] Screen reader announcements are appropriate
- [ ] Focus states are visible and logical

### Responsive Design

- [ ] Component works on mobile devices
- [ ] Layout adjusts appropriately at different breakpoints
- [ ] Touch targets are large enough (min 44x44px)
- [ ] Text remains readable at all sizes
- [ ] No horizontal scrolling on mobile

### Performance

- [ ] Component renders efficiently
- [ ] No unnecessary re-renders
- [ ] Large lists are virtualized if needed
- [ ] Images are optimized
- [ ] Animations are smooth

### Code Quality

- [ ] Component follows project conventions
- [ ] Props are properly typed
- [ ] No console errors or warnings
- [ ] No unused code or props
- [ ] Tests are included where appropriate

### Documentation

- [ ] Props are documented
- [ ] Usage examples are provided
- [ ] Special considerations are noted
- [ ] Code is commented where necessary

## Review Process

When submitting new stories:

1. Ensure all states are covered (default, loading, error, empty)
2. Verify that the stories follow the project conventions
3. Test the stories in Storybook before submitting
4. Request review from the UI team

## Additional Resources

- [Data Mocking Guide](/?path=/docs/data-mocking--docs) - Comprehensive strategies for creating and organizing mock data
- [Troubleshooting Guide](/?path=/docs/troubleshooting--docs) - Solutions to common issues
- [Component Design Guidelines](/?path=/docs/component-design--docs) - Best practices for component architecture
- [Accessibility Guidelines](/?path=/docs/accessibility--docs) - Ensuring components are accessible

By following these guidelines, you'll help maintain a consistent and high-quality Storybook that serves as both documentation and a development tool for the team.
