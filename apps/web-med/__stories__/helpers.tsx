// Create a mock implementation for TRPC mutation functions with configurable options
// This can be used in Storybook stories to simulate API mutations

export interface MockMutationOptions {
  isPending?: boolean;
  isSuccess?: boolean;
  isError?: boolean;
  error?: Error | null;
  delay?: number;
  path?: string;
}

export function createMockMutation<
  TMethod = unknown,
  TInput = unknown,
  TOutput = unknown,
>(output?: TOutput, options: MockMutationOptions = {}) {
  const {
    isPending = false,
    isSuccess = false,
    isError = false,
    error = null,
    delay = 0,
    path = "mock.mutation.path",
  } = options;

  const mutate = (params?: TInput) => {
    console.log("Mock mutation called with params:", params);
    return output;
  };

  const mutateAsync = async (params?: TInput) => {
    console.log("Mock async mutation called with params:", params);
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
    if (isError && error) {
      throw error;
    }
    return output as TOutput;
  };

  return {
    mutate,
    mutateAsync,
    isPending,
    isSuccess,
    isError,
    error,
    reset: () => {},
    trpc: { path },
    context: {},
  } as TMethod;
}
