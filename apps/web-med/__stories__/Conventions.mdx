import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Conventions" />

# UI Component Conventions

This document outlines the standard patterns and conventions for UI components in the AXA Professionals project, particularly focusing on data-driven components with loading and error states.

## Component Structure

### Basic Component Template

```tsx
export interface ComponentProps {
  loading?: boolean;
  error?: string;
  data?: DataType[];
  onAction?: (data: DataType) => void | Promise<void>;
}

export default function Component({
  loading = false,
  error,
  data = [],
  onAction,
}: ComponentProps) {
  return (
    <section className="flex flex-col gap-4">
      <header className="flex items-center justify-between gap-2">
        <div className="flex grow flex-col gap-2">
          <h2 className="text-lg font-semibold">{i18n.en.title}</h2>
          <p className="text-sm text-muted-foreground">{i18n.en.subtitle}</p>
        </div>
      </header>

      {error ? (
        <ErrorFallback error={{ message: error } as Error} />
      ) : loading ? (
        <LoadingState />
      ) : data.length > 0 ? (
        <DataView data={data} onAction={onAction} />
      ) : (
        <EmptyState onAction={onAction} />
      )}
    </section>
  );
}
```

### State Management

Components should handle three main states:

1. Loading state
2. Error state
3. Data state (including empty state)

### Loading States

- Use the `Skeleton` component for loading states
- Match skeleton dimensions to actual content
- Maintain layout stability between states
- Disable interactive elements during loading

```tsx
{
  loading ? (
    <div className="flex flex-col gap-4">
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-32 w-full" />
    </div>
  ) : null;
}
```

### Error States

- Use the `ErrorFallback` component for error states
- Provide clear error messages
- Allow for recovery actions when appropriate

```tsx
{
  error ? <ErrorFallback error={{ message: error } as Error} /> : null;
}
```

### Empty States

- Provide clear call-to-action
- Use consistent empty state styling
- Include helpful guidance text

```tsx
{
  data.length === 0 ? (
    <Button
      variant="ghost"
      className="flex h-auto w-full flex-col items-center justify-center gap-2 rounded-lg border-4 border-dashed border-border p-4"
      onClick={onAction}
    >
      <span className="text-sm text-muted-foreground">{i18n.en.noData}</span>
      <span className="flex items-center gap-1">
        <PlusIcon className="size-4" />
        {i18n.en.addData}
      </span>
    </Button>
  ) : null;
}
```

## Story Structure

### Basic Story Template

```tsx
const meta: Meta<typeof Component> = {
  title: "Components/Category/Component",
  component: Component,
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Component>;

// Sample data
const sampleData = [...];

// Default story with interactive state
export const Default: Story = {
  args: {
    data: sampleData,
    onAction: async (data) => {
      console.log("Action:", data);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    },
  },
  render: function Render(args) {
    const [{ data }, updateArgs] = useArgs();

    const handleAction = async (newData: DataType) => {
      await args.onAction?.(newData);
      updateArgs({ data: [...(data ?? []), newData] });
    };

    return (
      <Component
        data={data ?? []}
        onAction={handleAction}
      />
    );
  },
};

// State variations
export const Empty: Story = {
  args: {
    data: [],
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const LoadingWithData: Story = {
  args: {
    loading: true,
    data: sampleData,
  },
};

export const Error: Story = {
  args: {
    error: "Failed to load data. Please try again later.",
  },
};

export const ErrorWithData: Story = {
  args: {
    error: "Failed to update data. Please try again later.",
    data: sampleData,
  },
};
```

## Form Conventions

### Form Component Structure

```tsx
export type FormValues = z.infer<typeof formSchema>;
export type FormProps = PropsWithChildren<{
  defaultValues?: Partial<FormValues>;
  onSubmit?: (values: FormValues) => void | Promise<void>;
}>;

export default function Form({
  children,
  defaultValues,
  onSubmit = () => void 0,
}: FormProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues ?? {},
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Form fields */}
        {children ?? (
          <div className="flex w-full justify-end">
            <FormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}
```

## Internationalization

Use an `i18n` object at the top of each component file:

```tsx
const i18n = {
  en: {
    title: "Component Title",
    subtitle: "Component description",
    noData: "No data available",
    actions: {
      add: "Add",
      edit: "Edit",
      delete: "Delete",
    },
    dialog: {
      add: {
        title: "Add Item",
        description: "Add a new item to your collection.",
      },
    },
  },
};
```

## Dialog Patterns

### Form Dialogs

Use `DialogForm` for forms in dialogs:

```tsx
<DialogForm
  useTrigger={false}
  title={i18n.en.dialog.add.title}
  description={i18n.en.dialog.add.description}
  Component={FormComponent}
  onSubmit={handleSubmit}
  open={dialogOpen}
  onOpenChange={setDialogOpen}
/>
```

### Confirmation Dialogs

Use `DialogConfirmation` for confirmations:

```tsx
<DialogConfirmation
  useTrigger={false}
  title={i18n.en.dialog.delete.title}
  description={i18n.en.dialog.delete.description}
  onClick={handleConfirmDelete}
  open={deleteDialogOpen}
  onOpenChange={setDeleteDialogOpen}
/>
```

These conventions ensure consistency across components and make the codebase more maintainable and predictable.

## Page Conventions

### Project Structure

The project follows a specific folder structure:

- `src/www/` - Contains the actual page components used in the Next.js App Router
- `__stories__/pages/` - Contains the Storybook stories for pages, mirroring the structure in `src/www/`

### Next.js App Directory

All pages in this project use the Next.js App Router (App Directory). When creating stories for these pages, you must include the `nextjs.appDirectory` parameter:

```tsx
const meta: Meta<typeof PageComponent> = {
  title: "Pages/Category/PageName",
  component: PageComponent,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};
```

### Page Story Template

```tsx
import type { Meta, StoryObj } from "@storybook/react";

import PageComponent from "@/www/path/to/PageComponent";

const meta: Meta<typeof PageComponent> = {
  title: "Pages/Category/PageName",
  component: PageComponent,
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;
type Story = StoryObj<typeof PageComponent>;

export const Default: Story = {
  args: {
    // Page props if any
  },
};

// Additional variations if the page accepts props
export const WithSpecificProp: Story = {
  args: {
    specificProp: "value",
  },
};
```

### Page Story Organization

Stories should be organized to mirror the application's routing structure:

- `__stories__/pages/onboarding/` - For onboarding pages in `src/www/onboarding/`
- `__stories__/pages/onboarding/provider/` - For provider onboarding pages in `src/www/onboarding/provider/`
- `__stories__/pages/onboarding/organization/` - For organization onboarding pages in `src/www/onboarding/organization/`

This makes it easier to find stories that correspond to specific routes in the application.
