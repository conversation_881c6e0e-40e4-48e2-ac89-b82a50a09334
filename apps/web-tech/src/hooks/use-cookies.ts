"use client";

import { useCallback, useEffect } from "react";
import Cookies from "js-cookie";
import { useTheme } from "next-themes";

// Cookie name constants
export const THEME_COOKIE_NAME = "theme";
export const ANALYTICS_CONSENT_COOKIE_NAME = "analytics-consent";

export interface CookieOptions {
  expires?: number;
  path?: string;
  sameSite?: "strict" | "lax" | "none";
  secure?: boolean;
}

const DEFAULT_COOKIE_OPTIONS: CookieOptions = {
  expires: 365, // 1 year
  path: "/",
  sameSite: "lax",
  secure: process.env.NODE_ENV === "production",
};

// Optimized cookie options for theme preferences
const THEME_COOKIE_OPTIONS: CookieOptions = {
  expires: 365, // 1 year - theme preferences should persist long-term
  path: "/", // Site-wide access for consistent theming
  sameSite: "lax", // Allows navigation from external sites while preventing CSRF
  secure: process.env.NODE_ENV === "production", // HTTPS only in production
};

// Optimized cookie options for analytics consent (GDPR compliance)
const ANALYTICS_COOKIE_OPTIONS: CookieOptions = {
  expires: 395, // 13 months - common GDPR compliance period
  path: "/", // Site-wide access for consent enforcement
  sameSite: "strict", // Maximum security - prevents all cross-site requests
  secure: process.env.NODE_ENV === "production", // HTTPS only in production
};

export function useCookies() {
  const setCookie = useCallback(
    (name: string, value: string, options?: CookieOptions) => {
      const mergedOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };
      Cookies.set(name, value, mergedOptions);
    },
    [],
  );

  const getCookie = useCallback((name: string): string | undefined => {
    return Cookies.get(name);
  }, []);

  const removeCookie = useCallback(
    (name: string, options?: Pick<CookieOptions, "path">) => {
      Cookies.remove(name, options);
    },
    [],
  );

  return { setCookie, getCookie, removeCookie };
}

// Theme cookie management with auto-sync option
export function useThemeCookie(options: { autoSet?: boolean } = {}) {
  const { theme } = useTheme();
  const { setCookie, getCookie } = useCookies();

  const setTheme = useCallback(
    (themeValue: "light" | "dark" | "system") => {
      setCookie(THEME_COOKIE_NAME, themeValue, THEME_COOKIE_OPTIONS);
    },
    [setCookie],
  );

  const getTheme = useCallback((): string | undefined => {
    return getCookie(THEME_COOKIE_NAME);
  }, [getCookie]);

  // Auto-sync theme changes to cookies when autoSet is enabled
  useEffect(() => {
    if (options.autoSet && theme) {
      setTheme(theme as "light" | "dark" | "system");
    }
  }, [theme, options.autoSet, setTheme]);

  return { setTheme, getTheme };
}

// Analytics consent hook
export function useAnalyticsCookie() {
  const { setCookie, getCookie } = useCookies();

  const setAnalyticsConsent = useCallback(
    (consent: boolean) => {
      setCookie(
        ANALYTICS_CONSENT_COOKIE_NAME,
        consent.toString(),
        ANALYTICS_COOKIE_OPTIONS,
      );
    },
    [setCookie],
  );

  const getAnalyticsConsent = useCallback((): boolean | undefined => {
    const consent = getCookie(ANALYTICS_CONSENT_COOKIE_NAME);
    return consent ? consent === "true" : undefined;
  }, [getCookie]);

  return { setAnalyticsConsent, getAnalyticsConsent };
}

// Export theme functions for direct use
export const setThemeCookie = (theme: "light" | "dark" | "system") => {
  Cookies.set(THEME_COOKIE_NAME, theme, DEFAULT_COOKIE_OPTIONS);
};

export const getThemeCookie = (): "light" | "dark" | "system" | undefined => {
  const theme = Cookies.get(THEME_COOKIE_NAME);
  if (theme === "light" || theme === "dark" || theme === "system") {
    return theme;
  }
  return undefined;
};
