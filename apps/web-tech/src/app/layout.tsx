import type { <PERSON>ada<PERSON>, Viewport } from "next";

import { DM_Serif_Display } from "next/font/google";
import { cookies } from "next/headers";
import { GoogleAnalytics } from "@next/third-parties/google";
import { Analytics } from "@vercel/analytics/react";
import { GeistM<PERSON> } from "geist/font/mono";
import { GeistSans } from "geist/font/sans";

import { cn } from "@axa/ui/lib";
import { ThemeProvider } from "@axa/ui/primitives/theme";
import { Toaster } from "@axa/ui/primitives/toast";

import "@axa/ui/styles";

import { env } from "@/env";
import {
  ANALYTICS_CONSENT_COOKIE_NAME,
  THEME_COOKIE_NAME,
} from "@/hooks/use-cookies";

const dm_serif_display = DM_Serif_Display({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dm_serif_display",
  weight: "400",
});

export const metadata: Metadata = {
  metadataBase: new URL(
    env.VERCEL_ENV === "production"
      ? "https://tech.axapro.com"
      : "http://localhost:3001",
  ),
  title: {
    absolute: "AXA Tech",
    template: "%s | AXA Tech",
  },
  description:
    "Connecting Business with Exceptional Talent | AXA Professionals is a leading staffing agency that intersects top-tier talent with forward-thinking companies. We specialize in providing skilled professionals across a wide range of industries.",
  openGraph: {
    title: {
      absolute: "AXA Tech",
      template: "%s | AXA Tech",
    },
    description:
      "Connecting Business with Exceptional Talent | AXA Professionals is a leading staffing agency that intersects top-tier talent with forward-thinking companies. We specialize in providing skilled professionals across a wide range of industries.",
    url: "https://axapro.com",
    siteName: "AXA Tech",
  },
  twitter: {
    card: "summary_large_image",
    site: "@axapros",
    creator: "@axapros",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const cookieStore = await cookies();
  const analyticsConsent = cookieStore.get(ANALYTICS_CONSENT_COOKIE_NAME);
  const theme = cookieStore.get(THEME_COOKIE_NAME);

  const themeName =
    theme?.value === "system"
      ? "system"
      : theme?.value === "dark"
        ? "dark"
        : "light";

  return (
    <html
      lang="en"
      suppressHydrationWarning={env.NODE_ENV === "production"}
      className={cn(themeName)}
      style={{ colorScheme: themeName }}
    >
      <body
        className={cn(
          "min-h-dvh bg-background font-sans text-foreground antialiased",
          dm_serif_display.variable,
          GeistSans.variable,
          GeistMono.variable,
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          forcedTheme={theme?.value}
          enableSystem
        >
          {props.children}
          <Toaster closeButton richColors expand position="bottom-center" />
        </ThemeProvider>
        <Analytics />
        {analyticsConsent?.value === "true" && (
          <GoogleAnalytics gaId="G-10YKHMCBT4" />
        )}
      </body>
    </html>
  );
}
