"use client";

import { use } from "react";

import type { StatementType } from "@axa/database-tech";
import {
  useSearchDateRangeValue,
  useSearchFilterValue,
  useSearchPaginationValue,
} from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListStatements from "@/components/ListStatements";
import { useUser } from "@/contexts/User";
import { AddStatement } from "@/widgets/actions/billing/statements";

export interface PreviewBillingStatementsProps {
  organizationId?: string;
  statements?: Promise<RouterOutputs["billing"]["getStatements"]>;
}

export interface PreviewStatementsProps {
  loading?: boolean;
  organizationId?: string;
  statements?: Promise<RouterOutputs["statements"]["getMany"]>;
}

export const groupName = "statements";
export default function PreviewStatements(props: PreviewStatementsProps) {
  const user = useUser();
  const pagination = useSearchPaginationValue(groupName);
  const type = useSearchFilterValue<StatementType>("type", groupName);
  const dates = useSearchDateRangeValue(groupName);

  const statements = api.statements.getMany.useQuery(
    {
      organizationId: props.organizationId ?? "",
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      type,
      dates,
    },
    {
      enabled: !!props.organizationId && !props.loading,
      initialData: props.statements ? use(props.statements) : undefined,
    },
  );

  return (
    <div>
      {statements.error && <ErrorFallback error={statements.error} />}
      <ListStatements
        loading={props.loading || statements.isLoading}
        statements={statements.data}
        defaultPageSize={10}
        defaultPageIndex={0}
      >
        {user.isBilling && <AddStatement />}
      </ListStatements>
    </div>
  );
}
