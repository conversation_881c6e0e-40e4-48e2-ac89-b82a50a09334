"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { XIcon } from "lucide-react";

import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import { toast } from "@axa/ui/primitives/toast";
import SelectOrganization from "@axa/ui/selectors/SelectOrganization";

import type { RouterOutputs } from "@/api";

import { api, getQueryClient } from "@/api/client";
import { useUser } from "@/contexts/User";

type Organization = Pick<
  RouterOutputs["organizations"]["getMany"]["organizations"][number],
  "id" | "name" | "avatar" | "type"
>;

export default function ClientSwitcher({
  size = "sm",
}: {
  size?: "sm" | "md" | "lg" | "xl";
}) {
  const { organization } = useUser();
  const [selection, setSelection] = useState<Organization | null>(
    organization as Organization,
  );
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useDebounceValue("", 500);
  const organizations = api.organizations.getMany.useQuery({
    query,
    pageNumber: 0,
    pageSize: 5,
  });

  const data = useMemo(() => {
    const orgs = organizations.data?.organizations ?? ([] as Organization[]);
    if (selection && !orgs.find((org) => org.id === selection.id)) {
      orgs.unshift(selection);
    }
    return orgs;
  }, [organizations.data, selection]);

  const switchOrganizationMutation = api.user.switchOrganization.useMutation({
    onSuccess: async () => {
      const queryClient = getQueryClient();
      await queryClient.invalidateQueries({
        refetchType: "all",
      });
      toast.success("Organization switched");
    },
    onError: (error) => {
      toast.error(`Failed to switch organization: ${error.message}`);
    },
  });

  const switchOrganization = useCallback(
    (organization: Organization) => {
      setSelection({
        id: organization.id,
        name: organization.name,
        avatar: organization.avatar,
        type: organization.type,
      });
      switchOrganizationMutation.mutate({
        organizationId: organization.id,
      });
    },
    [switchOrganizationMutation],
  );

  const clearSelection = useCallback(() => {
    setSelection({
      id: "AXA",
      name: "AXA Professionals",
      avatar: null,
      type: "INTERNAL",
    });
    switchOrganizationMutation.mutate({
      organizationId: null,
    });
  }, [switchOrganizationMutation]);

  const loading = organizations.isLoading;

  return (
    <SelectOrganization<Organization>
      data={data}
      loading={loading}
      open={open}
      onOpenChange={setOpen}
      variant="ghost"
      align="end"
      className="-mx-2"
      size={size}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={(org) => switchOrganization(org)}
      footer={
        selection && selection.id !== "AXA" ? (
          <div className="w-full p-2">
            <Button
              className="flex w-full items-center justify-center gap-2 text-muted-foreground"
              variant="ghost"
              onClick={clearSelection}
            >
              <XIcon className="size-4" />
              <span>Clear</span>
            </Button>
          </div>
        ) : null
      }
    />
  );
}
