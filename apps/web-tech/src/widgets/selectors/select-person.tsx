import { useCallback } from "react";
import Image from "next/image";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@axa/ui/selectors/Selector";
import ContactName from "@axa/ui/common/ContactName";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { Selector } from "@axa/ui/selectors/Selector";

import TechnicianLevel from "@/components/common/TechnicianLevel";

const i18n = {
  en: {
    addPerson: "Add Person",
    label: "Person",
    description: "The person to contact",
    placeholder: "Select a person to contact",
    searchPerson: "Search person...",
    avatarFor: "avatar image for ",
  },
};

export interface PersonPartial extends GenericNode {
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  avatar?: string | null;
  level?: number | null;
  userRole?: string | null;
  isUser?: boolean | null;
}

export interface SelectPersonProps<Person extends PersonPartial>
  extends SelectorProps<Person> {
  placeholder?: string;
}

export function SelectPerson<Person extends PersonPartial>({
  children,
  loading = false,
  placeholder = i18n.en.searchPerson,
  ...props
}: SelectPersonProps<Person>) {
  return (
    <Selector<Person>
      loading={loading}
      label={i18n.en.addPerson}
      placeholder={placeholder}
      renderLoading={() => (
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      )}
      renderValue={useCallback(
        (person: Person) => `${person.firstName} ${person.lastName}`,
        [],
      )}
      renderItem={useCallback(
        (person: Person) => {
          const { name, initials } = {
            name: `${person.firstName ?? ""} ${person.lastName ?? ""}`.trim(),
            initials:
              [(person.firstName ?? " ")[0], (person.lastName ?? " ")[0]]
                .join("")
                .trim() || "AA",
          };
          return (
            <div className="flex items-center gap-2">
              {loading ? (
                <Skeleton className="size-9" />
              ) : typeof person.level === "number" ? (
                <TechnicianLevel size="small" level={person.level} />
              ) : (
                <Avatar className="size-9 rounded-lg">
                  <AvatarImage asChild src={person.avatar ?? ""}>
                    <Image
                      src={person.avatar ?? ""}
                      alt={i18n.en.avatarFor + name}
                      width={32}
                      height={32}
                    />
                  </AvatarImage>
                  <AvatarFallback className="rounded-lg">
                    {initials}
                  </AvatarFallback>
                </Avatar>
              )}

              <dl className="grid flex-1 gap-1">
                {loading ? (
                  <Skeleton className="size-9" />
                ) : (
                  <ContactName
                    showCopyButton={false}
                    name={name}
                    className="font-semibold text-foreground"
                  />
                )}
              </dl>
            </div>
          );
        },
        [loading],
      )}
      {...props}
    >
      {children}
    </Selector>
  );
}

export interface SelectPersonFieldProps<DataType extends PersonPartial>
  extends SelectPersonProps<DataType> {
  name?: "person.id";
  label?: string;
  description?: string;
  placeholder?: string;
  onQueryChange?: (value: string) => void;
}

export function SelectPersonField<DataType extends PersonPartial>({
  loading = false,
  name = "person.id" as const,
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  onQueryChange,
  ...props
}: SelectPersonFieldProps<DataType>) {
  const form = useFormContext<{
    person?: DataType;
    "person.id"?: DataType["id"];
    personId?: DataType["id"];
  }>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selection = data?.find((person) => person.id === field.value);
        return (
          <FormItem>
            <FormLabel> {label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <SelectPerson
                {...props}
                loading={loading}
                placeholder={placeholder}
                data={data ?? []}
                selection={selection}
                useDialog={false}
                onValueChange={(value) => {
                  onQueryChange?.(value);
                }}
                onSelect={async (person) => {
                  await onSelect?.(person);
                  field.onChange(person.id);
                  form.setValue("personId", person.id, {
                    shouldDirty: true,
                  });
                  form.setValue(
                    "person",
                    {
                      id: person.id,
                      firstName: person.firstName,
                      lastName: person.lastName,
                      email: person.email ?? "",
                      phone: person.phone ?? "",
                      avatar: person.avatar ?? "",
                      isUser: person.isUser ?? false,
                    } as DataType,
                    {
                      shouldDirty: true,
                    },
                  );
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
