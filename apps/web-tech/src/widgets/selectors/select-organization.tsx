import { useCallback, useState } from "react";
import { ChevronDownIcon } from "lucide-react";

import type {
  SearchOrganizationProps as BaseSearchOrganizationProps,
  SelectOrganizationFieldProps as BaseSelectOrganizationFieldProps,
  SelectOrganizationProps as BaseSelectOrganizationProps,
} from "@axa/ui/selectors/SelectOrganization";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { cn } from "@axa/ui/lib";
import {
  SearchOrganization as SearchOrganizationBase,
  SelectOrganization as SelectOrganizationBase,
  SelectOrganizationField as SelectOrganizationFieldBase,
} from "@axa/ui/selectors/SelectOrganization";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { useUser } from "@/contexts/User";

export type Organization = Pick<
  RouterOutputs["organizations"]["getMany"]["organizations"][number],
  "id" | "name" | "avatar" | "type"
>;

export interface UseSelectOrganizationProps {
  omitInternal?: boolean;
  enabled?: boolean;
  pageNumber?: number;
  pageSize?: number;
  defaultOpen?: boolean;
  defaultQuery?: string;
  defaultSelection?: Organization;
  defaultDebounce?: number;
  onSelect?: (org: Organization) => void;
  onValueChange?: (value: string) => void;
}

export function useSelectOrganization({
  omitInternal = true,
  enabled,
  pageNumber = 0,
  pageSize = 5,
  defaultQuery = "",
  defaultSelection,
  defaultOpen = false,
  defaultDebounce = 500,
  onSelect,
  onValueChange,
}: UseSelectOrganizationProps = {}) {
  const { organization } = useUser();

  const [selection, setSelection] = useState<Organization | null>(() => {
    if (defaultSelection) {
      return defaultSelection;
    }
    if (organization) {
      if (!omitInternal || organization.type !== "INTERNAL") {
        return organization as Organization;
      }
    }
    return null;
  });
  const [open, setOpen] = useState(defaultOpen);
  const [query, setQuery] = useDebounceValue(defaultQuery, defaultDebounce);
  const organizations = api.organizations.getMany.useQuery(
    {
      query,
      pageNumber,
      pageSize,
      // ensure:
      //   selection?.type !== "INTERNAL" && selection?.id
      //     ? [selection.id]
      //     : undefined,
    },
    {
      enabled: enabled ?? open,
    },
  );

  const loading = organizations.isLoading;
  const data = organizations.data?.organizations;

  const handleSelect = useCallback(
    (org: Organization) => {
      if (omitInternal && org.type === "INTERNAL") {
        return;
      }
      setSelection(org);
      setOpen(false);
      setQuery("");
      onSelect?.(org);
      onValueChange?.("");
    },
    [omitInternal, setOpen, setQuery, onSelect, onValueChange],
  );

  return {
    data,
    loading,
    selection,
    open,
    setOpen,
    query,
    setQuery,
    setSelection: handleSelect,
  };
}

export type SelectOrganizationProps =
  BaseSelectOrganizationProps<Organization> &
    Omit<UseSelectOrganizationProps, "pageNumber" | "pageSize">;

export function SelectOrganization({
  omitInternal = true,
  enabled = true,
  defaultOpen = false,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  size = "md",
  variant = "ghost",
  onSelect,
  onValueChange,
  ...props
}: SelectOrganizationProps) {
  const {
    data,
    loading,
    selection,
    open,
    setOpen,
    query,
    setQuery,
    setSelection,
  } = useSelectOrganization({
    enabled,
    omitInternal,
    defaultOpen,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
    onValueChange,
  });

  return (
    <SelectOrganizationBase
      {...props}
      data={data}
      loading={loading}
      open={open}
      onOpenChange={setOpen}
      variant={variant}
      size={size}
      value={query}
      selection={selection ?? undefined}
      onValueChange={setQuery}
      onSelect={setSelection}
      knob={<ChevronIcon size={size} />}
    />
  );
}

export type SelectOrganizationFieldProps =
  BaseSelectOrganizationFieldProps<Organization> &
    Omit<UseSelectOrganizationProps, "pageNumber" | "pageSize">;

export function SelectOrganizationField({
  omitInternal = true,
  enabled = true,
  defaultOpen = false,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  size = "md",
  ...props
}: SelectOrganizationFieldProps) {
  const {
    data,
    loading,
    selection,
    open,
    setOpen,
    query,
    setQuery,
    setSelection,
  } = useSelectOrganization({
    enabled,
    omitInternal,
    defaultOpen,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
  });

  return (
    <SelectOrganizationFieldBase
      {...props}
      size={size}
      data={data}
      loading={loading}
      open={open}
      onOpenChange={setOpen}
      value={query}
      onValueChange={setQuery}
      onSelect={setSelection}
      selection={selection ?? undefined}
      knob={<ChevronIcon size={size} />}
    />
  );
}

export interface SearchOrganizationsProps
  extends BaseSearchOrganizationProps<Organization>,
    Omit<
      UseSelectOrganizationProps,
      "pageNumber" | "pageSize" | "onSelect" | "onValueChange"
    > {
  group?: string;
}

export function SearchOrganizations({
  omitInternal = true,
  enabled = true,
  group,
  defaultOpen = false,
  defaultQuery = "",
  defaultSelection,
  defaultDebounce = 500,
  size = "sm",
  className,
  ...props
}: SearchOrganizationsProps) {
  const {
    data,
    loading,
    selection,
    open,
    query,
    setQuery,
    setOpen,
    setSelection,
  } = useSelectOrganization({
    enabled,
    omitInternal,
    defaultOpen,
    defaultQuery,
    defaultSelection,
    defaultDebounce,
  });

  return (
    <SearchOrganizationBase
      {...props}
      group={group}
      size={size}
      data={data}
      loading={loading}
      open={open}
      onOpenChange={setOpen}
      defaultValue={defaultQuery}
      value={query}
      onValueChange={setQuery}
      onSelect={setSelection}
      selection={selection ?? undefined}
      className={className}
      clearable
    />
  );
}

export function ChevronIcon({
  size = "md",
}: {
  size?: "sm" | "md" | "lg" | "xl" | null;
}) {
  return (
    <ChevronDownIcon
      className={cn("ms-auto text-muted-foreground", {
        "size-4": size === "sm",
        "size-5": size === "md",
        "size-6": size === "lg",
        "size-8": size === "xl",
      })}
    />
  );
}
