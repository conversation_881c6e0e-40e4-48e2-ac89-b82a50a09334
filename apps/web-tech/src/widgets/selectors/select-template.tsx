import { useCallback } from "react";
import { useFormContext } from "react-hook-form";

import type { GenericNode, SelectorProps } from "@axa/ui/selectors/Selector";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Selector } from "@axa/ui/selectors/Selector";

const i18n = {
  en: {
    label: "Template",
    description: "The template",
    placeholder: "Select a template",
  },
};

export interface PartialTemplate extends GenericNode {
  name: string;
}

export interface SelectTemplateProps<DataType extends PartialTemplate>
  extends SelectorProps<DataType> {
  placeholder?: string;
}

export default function SelectTemplate<DataType extends PartialTemplate>({
  loading = false,
  variant = "outline",
  align = "center",
  className,
  disabled,
  useDialog,
  placeholder = i18n.en.placeholder,
  data,
  value,
  onSelect,
  ...props
}: SelectTemplateProps<DataType>) {
  return (
    <Selector<DataType>
      {...props}
      loading={loading}
      disabled={disabled}
      variant={variant}
      align={align}
      className={className}
      useDialog={useDialog ?? false}
      label={placeholder}
      data={data}
      selection={data?.find((template) => template.id === value)}
      onSelect={onSelect}
      renderValue={useCallback((template: DataType) => template.name, [])}
      renderItem={useCallback(
        (template: DataType) => (
          <div className="flex flex-col gap-1">
            <span className="font-semibold">{template.name}</span>
          </div>
        ),
        [],
      )}
    />
  );
}

export interface SelectTemplateFieldProps<DataType extends PartialTemplate>
  extends SelectTemplateProps<DataType> {
  name?: "templateId";
  label?: string;
  description?: string;
  placeholder?: string;
}

export function SelectTemplateField<DataType extends PartialTemplate>({
  loading = false,
  name = "templateId",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  ...props
}: SelectTemplateFieldProps<DataType>) {
  const form = useFormContext<{
    templateId?: DataType["id"];
  }>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selection = data?.find((template) => template.id === field.value);
        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <SelectTemplate
                {...props}
                loading={loading}
                placeholder={placeholder}
                data={data}
                selection={selection}
                onSelect={async (template: DataType): Promise<void> => {
                  if (onSelect) {
                    await onSelect(template);
                  }

                  field.onChange(template.id);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
