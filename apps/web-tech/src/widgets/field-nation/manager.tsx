"use client";

import Image from "next/image";
import { ExternalLinkIcon } from "lucide-react";

import type { WorkOrderStatus } from "@axa/database-tech";
import { Button } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";

import { api } from "@/api/client";
import { useUser } from "@/contexts/User";

export default function FieldNationManager({
  loading = false,
  order,
}: {
  loading?: boolean;
  order?: {
    id: string;
    status: WorkOrderStatus;
    fieldNationId?: number | null;
    fieldNationURL?: string | null;
  };
}) {
  const user = useUser();

  if (!user.isInternal) {
    return null;
  }

  const utils = api.useUtils();

  const createFieldNationOrderMutation =
    api.orders.fieldNation.createWorkOrder.useMutation({
      onSuccess: async () => {
        await utils.orders.get.invalidate({ id: order?.id });
        toast.success("Successfully synced with Field Nation");
      },
      onError: (error) => {
        toast.error("Failed to sync with Field Nation" + error.message);
      },
    });

  const PublishWorkOrderMutation =
    api.orders.fieldNation.publishWorkOrder.useMutation({
      onSuccess: async () => {
        await utils.orders.get.invalidate({ id: order?.id });
        toast.success("Successfully published to Field Nation");
      },
      onError: (error) => {
        toast.error("Failed to publish to Field Nation" + error.message);
      },
    });

  const approveWorkOrderMutation =
    api.orders.fieldNation.approveWorkOrder.useMutation({
      onSuccess: async () => {
        await utils.orders.get.invalidate({ id: order?.id });
        toast.success("Successfully approved in Field Nation");
      },
      onError: (error) => {
        toast.error("Failed to approve in Field Nation" + error.message);
      },
    });

  const syncFieldNationOrderMutation =
    api.orders.fieldNation.syncWorkOrder.useMutation({
      onSuccess: async () => {
        await utils.orders.get.invalidate({ id: order?.id });
        toast.success("Successfully synced with Field Nation");
      },
      onError: (error) => {
        toast.error("Failed to sync with Field Nation" + error.message);
      },
    });

  return (
    <section className="flex flex-col rounded-lg border">
      <header className="flex items-center justify-between gap-4 p-4">
        <Image
          className="aspect-auto h-12"
          src="/images/fn-logo.svg"
          alt="Field Nation"
          width={200}
          height={100}
        />
        {loading ? (
          <Skeleton className="h-4 w-[200px]" />
        ) : order?.status === "DRAFT" ? (
          <span className="text-muted-foreground">
            Please post the work order to connect
          </span>
        ) : order?.fieldNationURL ? (
          <a
            href={order.fieldNationURL}
            target="_blank"
            rel="noreferrer"
            className="flex flex-row flex-nowrap items-center gap-1 text-nowrap text-primary hover:underline"
          >
            <span>ID: {order.fieldNationId}</span>
            <ExternalLinkIcon size={14} />
          </a>
        ) : order?.fieldNationId ? (
          <div>
            <p>ID: {order.fieldNationId}</p>
          </div>
        ) : (
          <div>
            <Button
              disabled={createFieldNationOrderMutation.isPending}
              onClick={async () => {
                if (order) {
                  await createFieldNationOrderMutation.mutateAsync({
                    id: order.id,
                  });
                }
              }}
            >
              Connect
            </Button>
          </div>
        )}
      </header>

      {/* eslint-disable-next-line no-restricted-properties */}
      {process.env.NODE_ENV === "development" && (
        <footer className="flex flex-col gap-4 border-t p-4 sm:flex-row">
          {order?.fieldNationId ? (
            <div className="flex flex-wrap items-center gap-3">
              <Button
                onClick={async () => {
                  if (order) {
                    await PublishWorkOrderMutation.mutateAsync({
                      id: order.id,
                    });
                  }
                }}
              >
                Publish to Field Nation
              </Button>
              <Button
                onClick={async () => {
                  if (order) {
                    await approveWorkOrderMutation.mutateAsync({
                      id: order.id,
                    });
                  }
                }}
              >
                Approve in Field Nation
              </Button>
              <Button
                onClick={async () => {
                  if (order) {
                    await syncFieldNationOrderMutation.mutateAsync({
                      id: order.id,
                    });
                  }
                }}
              >
                Sync
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-3">
              <div className="flex-1"></div>
              <Button
                onClick={async () => {
                  if (order) {
                    await createFieldNationOrderMutation.mutateAsync({
                      id: order.id,
                    });
                  }
                }}
              >
                Connect
              </Button>
            </div>
          )}
        </footer>
      )}
    </section>
  );
}
