"use client";

import { use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import type { TimeSheetStatus } from "@axa/database-tech";
import {
  useSearchDateRangeValue,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchValueResult,
} from "@axa/ui/search";
import EmptyList from "@axa/ui/shared/EmptyList";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListTimeSheets from "@/components/ListTimeSheets";
import { useUser } from "@/contexts/User";
import { AddTimeSheet } from "@/widgets/actions/billing/time-sheet";

const i18n = {
  en: {
    title: "TimeSheets",
    description:
      "TimeSheets are the records for work order technicians. They contain the billing information and hours worked per shift.",
    noTimeSheets: "There are no time sheets",
  },
};

export interface PreviewTimeSheetsProps {
  billing?: boolean;
  loading?: boolean;
  invoice?: string;
  timeSheets?: Promise<RouterOutputs["timeSheets"]["getMany"]>;
}

const timesheetGroup = "timesheet";

export default function PreviewTimeSheets(props: PreviewTimeSheetsProps) {
  const user = useUser();

  const pagination = useSearchPaginationValue(timesheetGroup);
  const dates = useSearchDateRangeValue(timesheetGroup);
  const organization = useSearchValueResult(timesheetGroup, "organization");
  const status = useSearchFilterValue<TimeSheetStatus>(
    "status",
    timesheetGroup,
  );

  const timeSheets = api.timeSheets.getMany.useQuery(
    {
      invoiceId: props.invoice,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      status,
      organizationId: organization,
      dates,
    },
    {
      enabled:
        !props.loading &&
        !user.loading &&
        (props.billing ? !!props.invoice : true),
      initialData: props.timeSheets ? use(props.timeSheets) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || timeSheets.isLoading;

  return (
    <div className="space-y-6">
      {timeSheets.error && <ErrorFallback error={timeSheets.error} />}

      <ListTimeSheets
        loading={loading}
        timeSheets={timeSheets.data}
        billing={props.billing}
        group={timesheetGroup}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        {user.isBilling && <AddTimeSheet />}
      </ListTimeSheets>
    </div>
  );
}
