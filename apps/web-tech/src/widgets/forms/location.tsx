"use client";

import z from "zod";

import type { AddressFormProps } from "@axa/ui/forms/Address";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import AddressForm, {
  addressFormSchema,
  AddressFormSubmitButton,
} from "@axa/ui/forms/Address";

import { FieldNationIdField } from "@/components/forms/fields/field-nation-id";
import { useUser } from "@/contexts/User";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

export const locationFormSchema = addressFormSchema.extend({
  fieldNationId: z.string().optional(),
  organizationId: z.string(),
});

export type LocationFormValues = z.infer<typeof locationFormSchema>;

export type LocationFormProps = Omit<
  DialogFormProps<LocationFormValues, LocationFormValues>,
  "Component" | "onSubmit"
> & {
  onSubmit?: (values: LocationFormValues) => void | Promise<void>;
  locationId?: string;
  defaultValues?: Partial<LocationFormValues>;
  showOrganization?: boolean;
};

export function LocationForm({
  showOrganization = false,
  ...props
}: LocationFormProps) {
  const user = useUser();

  return (
    <AddressForm
      {...props}
      schema={locationFormSchema}
      onSubmit={props.onSubmit as AddressFormProps["onSubmit"]}
    >
      {showOrganization && (
        <SelectOrganizationField description="The organization that this location belongs to" />
      )}
      {user.isInternal && (
        <FieldNationIdField
          name="fieldNationId"
          label="FieldNation ID"
          description="The FieldNation ID associated with this location"
        />
      )}
      <div className="flex w-full justify-center">
        <AddressFormSubmitButton />
      </div>
    </AddressForm>
  );
}
