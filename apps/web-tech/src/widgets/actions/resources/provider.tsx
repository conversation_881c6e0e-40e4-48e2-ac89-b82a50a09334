"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, PlusCircleIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  ProviderFormProps,
  ProviderFormValues,
} from "@/components/forms/Provider";
import type { SelectPersonProps } from "@/widgets/selectors/select-person";

import { api } from "@/api/client";
import ProviderForm from "@/components/forms/Provider";
import { SelectPerson } from "@/widgets/selectors/select-person";

const i18n = {
  en: {
    titles: {
      add: "Add Provider",
      update: "Update Provider",
      delete: "Delete Provider",
    },
    descriptions: {
      delete: "Are you sure you want to delete this provider?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
      selectProvider: "Select Provider",
    },
    messages: {
      created: "Provider created successfully.",
      updated: "Provider updated successfully.",
      deleted: "Provider deleted successfully.",
      failedCreate: "Failed to create provider: ",
      failedUpdate: "Failed to update provider: ",
      failedDelete: "Failed to delete provider: ",
    },
  },
  links: {
    providers: "/app/providers",
  },
};

export function AddProvider(
  props: Omit<
    DialogFormProps<ProviderFormProps, ProviderFormValues>,
    "Component" | "onSubmit"
  >,
) {
  const utils = api.useUtils();
  const createProviderMutation = api.providers.create.useMutation({
    onSuccess: async () => {
      await utils.providers.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<ProviderFormProps, ProviderFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      Component={ProviderForm}
      onSubmit={useCallback<NonNullable<ProviderFormProps["onSubmit"]>>(
        async (values) => {
          await createProviderMutation.mutateAsync({
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            phone: values.phone,
            level: values.level ? parseInt(values.level, 10) : 1,
          });
        },
        [createProviderMutation],
      )}
    />
  );
}

export function UpdateProvider(
  props: Omit<
    DialogFormProps<ProviderFormProps, ProviderFormValues>,
    "Component" | "onSubmit"
  > &
    PropsWithChildren<{
      providerId: string;
      technicianId?: string;
      defaultValues?: ProviderFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateProviderMutation = api.providers.update.useMutation({
    onSuccess: async () => {
      if (props.technicianId) {
        await utils.technicians.get.invalidate({
          id: props.technicianId,
        });
      }
      await utils.providers.get.invalidate({
        id: props.providerId,
      });
      // TODO: granular invalidation
      await utils.providers.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<ProviderFormProps, ProviderFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={ProviderForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<ProviderFormProps["onSubmit"]>>(
        async (values) => {
          await updateProviderMutation.mutateAsync({
            id: props.providerId,
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            phone: values.phone,
            level: values.level ? parseInt(values.level, 10) : undefined,
          });
        },
        [updateProviderMutation, props.providerId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteProvider({
  providerId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ providerId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteProviderMutation = api.providers.delete.useMutation({
    onSuccess: async () => {
      await utils.providers.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.providers);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteProviderMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteProviderMutation.mutateAsync({ id: providerId });
      }, [deleteProviderMutation, providerId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function ProviderMenu({
  rerouteOnDelete,
  provider,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    provider?:
      | RouterOutputs["providers"]["get"]
      | RouterOutputs["providers"]["getMany"]["providers"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateProvider
            onOpenChange={handleDialogOpenChange}
            providerId={provider?.id ?? ""}
            defaultValues={{
              firstName: provider?.firstName,
              lastName: provider?.lastName,
              email: provider?.email,
              phone: provider?.phone,
              level: provider?.level.toString(),
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateProvider>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteProvider
            onOpenChange={handleDialogOpenChange}
            providerId={provider?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteProvider>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface SearchProvidersProps
  extends Pick<
    SelectPersonProps<
      RouterOutputs["providers"]["getMany"]["providers"][number]
    >,
    | "onSelect"
    | "defaultValue"
    | "children"
    | "variant"
    | "size"
    | "className"
    | "useDialog"
  > {
  useDialog?: boolean;
}

export function SearchProviders({
  useDialog = true,
  defaultValue,
  onSelect,
  variant = "ghost",
  size,
  className = "size-full",
  children,
}: SearchProvidersProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useDebounceValue(defaultValue ?? "", 500);
  const providers = api.providers.getMany.useQuery(
    {
      query,
      pageNumber: 0,
      pageSize: 10,
    },
    {
      enabled: open,
    },
  );

  return (
    <SelectPerson<RouterOutputs["providers"]["getMany"]["providers"][number]>
      useDialog={useDialog}
      data={providers.data?.providers ?? []}
      loading={providers.isLoading}
      defaultValue={defaultValue}
      open={open}
      onOpenChange={setOpen}
      onSelect={onSelect}
      onValueChange={setQuery}
      variant={variant}
      className={className}
      size={size}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectProvider}</span>
        </>
      )}
    </SelectPerson>
  );
}
