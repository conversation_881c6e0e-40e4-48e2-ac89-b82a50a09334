"use client";

import type { TRPCClientErrorLike } from "@trpc/client";
import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, PlusCircleIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { SelectLocationProps } from "@axa/ui/selectors/SelectLocation";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import SelectLocation from "@axa/ui/selectors/SelectLocation";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { AppRouter, RouterOutputs } from "@/api";
import type {
  LocationFormProps,
  LocationFormValues,
} from "@/widgets/forms/location";

import { api } from "@/api/client";
import { useUser } from "@/contexts/User";
import { LocationForm } from "@/widgets/forms/location";

const i18n = {
  en: {
    titles: {
      add: "Add Location",
      update: "Update Location",
      delete: "Delete Location",
    },
    descriptions: {
      delete: "Are you sure you want to delete this location?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
      selectLocation: "Select Location",
    },
    messages: {
      created: "Location created successfully.",
      updated: "Location updated successfully.",
      deleted: "Location deleted successfully.",
      failedCreate: "Failed to create location: ",
      failedUpdate: "Failed to update location: ",
      failedDelete: "Failed to delete location: ",
    },
  },
  links: {
    locations: "/app/locations",
  },
};

export function AddLocation(
  props: LocationFormProps & {
    onSuccess?: (
      location: RouterOutputs["locations"]["create"],
    ) => void | Promise<void>;
    onError?: (error: TRPCClientErrorLike<AppRouter>) => void | Promise<void>;
  },
) {
  const user = useUser();
  const utils = api.useUtils();
  const createLocationMutation = api.locations.create.useMutation({
    onSuccess: async (result) => {
      if (props.onSuccess) {
        await props.onSuccess(result);
      }
      await utils.locations.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: async (error) => {
      if (props.onError) {
        await props.onError(error);
      }
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<LocationFormProps, LocationFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      showOrganization
      Component={LocationForm}
      onSubmit={useCallback<NonNullable<LocationFormProps["onSubmit"]>>(
        async (values) => {
          if (!values.organizationId) {
            toast.error("Organization is required");
            return;
          }

          if (!values.address.latitude || !values.address.longitude) {
            toast.error(
              "Latitude and longitude are required; something went wrong with address search.",
            );
            return;
          }

          await createLocationMutation.mutateAsync({
            organizationId: values.organizationId,
            name: values.name,
            type: values.type ?? "location",
            description: values.description ?? "",
            fieldNationId: user.isInternal ? values.fieldNationId : undefined,
            address: {
              formatted: values.address.formatted,
              street: values.address.street,
              city: values.address.city,
              state: values.address.state,
              postal: values.address.postal,
              country: values.address.country,

              latitude: values.address.latitude,

              longitude: values.address.longitude,
            },
          });
        },
        [createLocationMutation, user.isInternal],
      )}
    />
  );
}

export function UpdateLocation(
  props: LocationFormProps &
    PropsWithChildren<{
      locationId: string;
    }>,
) {
  const user = useUser();
  const utils = api.useUtils();
  const updateLocationMutation = api.locations.update.useMutation({
    onSuccess: async () => {
      await utils.locations.get.invalidate({
        id: props.locationId,
      });
      // TODO: granular invalidation
      await utils.locations.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<LocationFormProps, LocationFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={LocationForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<LocationFormProps["onSubmit"]>>(
        async (values) => {
          if (!values.organizationId) {
            toast.error("Organization is required");
            return;
          }

          if (!values.address.latitude || !values.address.longitude) {
            toast.error(
              "Latitude and longitude are required; something went wrong with address search.",
            );
            return;
          }

          await updateLocationMutation.mutateAsync({
            organizationId: values.organizationId,
            id: props.locationId,
            name: values.name,
            type: "location",
            fieldNationId: user.isInternal ? values.fieldNationId : undefined,
            description: values.description ?? "",
            address: {
              formatted: values.address.formatted,
              street: values.address.street,
              city: values.address.city,
              state: values.address.state,
              postal: values.address.postal,
              country: values.address.country,
              latitude: values.address.latitude,
              longitude: values.address.longitude,
            },
          });
        },
        [updateLocationMutation, props.locationId, user.isInternal],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteLocation({
  locationId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ locationId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteLocationMutation = api.locations.delete.useMutation({
    onSuccess: async () => {
      await utils.locations.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.locations);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteLocationMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteLocationMutation.mutateAsync({ id: locationId });
      }, [deleteLocationMutation, locationId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function LocationMenu({
  loading = false,
  rerouteOnDelete,
  location,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    rerouteOnDelete?: boolean;
    location?:
      | RouterOutputs["locations"]["get"]
      | RouterOutputs["locations"]["getMany"]["locations"][number];
  } & ButtonProps
>) {
  const user = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateLocation
            showOrganization
            onOpenChange={handleDialogOpenChange}
            locationId={location?.id ?? ""}
            defaultValues={{
              organizationId: location?.organization.id,
              name: location?.name,
              description: location?.description ?? "",
              fieldNationId: user.isInternal
                ? location?.fieldNationId?.toString()
                : undefined,
              address: {
                formatted: location?.address.formatted ?? "",
                street: location?.address.street ?? "",
                city: location?.address.city ?? "",
                state: location?.address.state ?? "",
                postal: location?.address.postal ?? "",
                country: location?.address.country ?? "",
                latitude: location?.address.latitude ?? 0,
                longitude: location?.address.longitude ?? 0,
              },
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateLocation>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteLocation
            onOpenChange={handleDialogOpenChange}
            locationId={location?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteLocation>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface SearchLocationProps
  extends Pick<
    SelectLocationProps<
      RouterOutputs["locations"]["getMany"]["locations"][number]
    >,
    | "onSelect"
    | "defaultValue"
    | "children"
    | "variant"
    | "className"
    | "loading"
  > {
  useDialog?: boolean;
}

export function SearchLocation({
  loading = false,
  useDialog = true,
  defaultValue,
  onSelect,
  variant = "ghost",
  className = "size-full",
  children,
}: SearchLocationProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useDebounceValue(defaultValue ?? "", 500);
  const locations = api.locations.getMany.useQuery(
    {
      query,
      pageNumber: 0,
      pageSize: 5,
    },
    {
      enabled: open,
    },
  );

  return (
    <SelectLocation
      loading={loading || locations.isLoading}
      useDialog={useDialog}
      data={locations.data?.locations ?? []}
      open={open}
      onOpenChange={setOpen}
      onSelect={onSelect}
      value={query}
      onValueChange={setQuery}
      variant={variant}
      className={className}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectLocation}</span>
        </>
      )}
    </SelectLocation>
  );
}
