"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  InvoiceFormProps,
  InvoiceFormValues,
} from "@/components/forms/Invoice";

import { api } from "@/api/client";
import ExportRecords from "@/components/common/ExportRecords";
import InvoiceForm, {
  InvoiceFormSubmitButton,
} from "@/components/forms/Invoice";
import { useUser } from "@/contexts/User";
import { useExportInvoices } from "@/hooks/useExportInvoices";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    titles: {
      add: "Add Invoice",
      update: "Update Invoice",
      delete: "Delete Invoice",
    },
    descriptions: {
      delete: "Are you sure you want to delete this invoice?",
      organization: "The organization that the invoice belongs to",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
      cancel: "Cancel",
      duplicate: "Duplicate",
      export: "Export",
    },
    messages: {
      created: "Invoice created successfully.",
      updated: "Invoice updated successfully.",
      deleted: "Invoice deleted successfully.",
      failedCreate: "Failed to create invoice: ",
      failedUpdate: "Failed to update invoice: ",
      failedDelete: "Failed to delete invoice: ",
    },
  },
  links: {
    invoices: "/app/finances/invoices",
  },
};

export type OrganizationInvoiceFormProps = Omit<
  DialogFormProps<InvoiceFormProps, InvoiceFormValues>,
  "Component" | "onSubmit"
>;

export function OrganizationInvoiceForm(props: OrganizationInvoiceFormProps) {
  return (
    <InvoiceForm {...props}>
      <SelectOrganizationField
        description={i18n.en.descriptions.organization}
      />
      <div className="flex w-full justify-center">
        <InvoiceFormSubmitButton />
      </div>
    </InvoiceForm>
  );
}

export function AddInvoice(
  props: OrganizationInvoiceFormProps & {
    defaultValues?: InvoiceFormProps["defaultValues"];
  },
) {
  const utils = api.useUtils();
  const createInvoiceMutation = api.invoices.create.useMutation({
    onSuccess: async () => {
      await utils.invoices.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<InvoiceFormProps, InvoiceFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationInvoiceForm}
      onSubmit={useCallback<NonNullable<InvoiceFormProps["onSubmit"]>>(
        async (values) => {
          await createInvoiceMutation.mutateAsync({
            organizationId: values.organizationId,
            timePeriodStart: values.startDate,
            timePeriodEnd: values.endDate,
            dueDate: values.dueDate,
            name: values.name,
            status: "DRAFT",
          });
        },
        [createInvoiceMutation],
      )}
    />
  );
}

export function UpdateInvoice(
  props: OrganizationInvoiceFormProps &
    PropsWithChildren<{
      invoiceId: string;
      defaultValues?: InvoiceFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateInvoiceMutation = api.invoices.update.useMutation({
    onSuccess: async () => {
      await utils.invoices.get.invalidate({
        id: props.invoiceId,
      });
      // TODO: granular invalidation
      await utils.invoices.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<InvoiceFormProps, InvoiceFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationInvoiceForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<InvoiceFormProps["onSubmit"]>>(
        async (values) => {
          await updateInvoiceMutation.mutateAsync({
            id: props.invoiceId,
            timePeriodStart: values.startDate,
            timePeriodEnd: values.endDate,
            dueDate: values.dueDate,
            name: values.name,
          });
        },
        [updateInvoiceMutation, props.invoiceId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteInvoice({
  invoiceId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ invoiceId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteInvoiceMutation = api.invoices.delete.useMutation({
    onSuccess: async () => {
      await utils.invoices.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.invoices);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteInvoiceMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteInvoiceMutation.mutateAsync({ id: invoiceId });
      }, [deleteInvoiceMutation, invoiceId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function InvoiceMenu({
  rerouteOnDelete,
  invoice,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    invoice?:
      | RouterOutputs["invoices"]["get"]
      | RouterOutputs["invoices"]["getMany"]["invoices"][number];
  } & ButtonProps
>) {
  const user = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const { exportInvoices, isPending: isExportInvoicesPending } =
    useExportInvoices({
      invoices: [invoice?.id ?? ""],
    });

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onSelect={handleItemSelect}
            onClick={exportInvoices}
            disabled={isExportInvoicesPending}
          >
            {i18n.en.actions.export}
          </DropdownMenuItem>
          {user.isAdmin && (
            <UpdateInvoice
              onOpenChange={handleDialogOpenChange}
              invoiceId={invoice?.id ?? ""}
              defaultValues={{
                organizationId: invoice?.organization.id,
                startDate: invoice?.timePeriodStart,
                endDate: invoice?.timePeriodEnd,
                dueDate: invoice?.dueDate,
                name: invoice?.name ?? "",
              }}
            >
              <DropdownMenuItem onSelect={handleItemSelect}>
                {i18n.en.actions.update}
              </DropdownMenuItem>
            </UpdateInvoice>
          )}
        </DropdownMenuGroup>
        {user.isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DeleteInvoice
                onOpenChange={handleDialogOpenChange}
                invoiceId={invoice?.id ?? ""}
                reroute={rerouteOnDelete}
              >
                <DropdownMenuItem onSelect={handleItemSelect}>
                  {i18n.en.actions.delete}
                </DropdownMenuItem>
              </DeleteInvoice>
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface ExportInvoicesProps extends PropsWithChildren, ButtonProps {
  invoices: string[];
}

export function ExportInvoices({
  invoices,
  children,
  ...props
}: ExportInvoicesProps) {
  const { exportInvoices, isPending } = useExportInvoices({ invoices });

  return (
    <ExportRecords {...props} disabled={isPending} onClick={exportInvoices}>
      {children}
    </ExportRecords>
  );
}
