"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type {
  MemberFormProps,
  MemberFormValues,
} from "@axa/ui/forms/organizations/Member";
import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
// import { SelectOrganizationField } from "@axa/ui/selectors/SelectOrganization";
import MemberForm, {
  MemberFormSubmitButton,
} from "@axa/ui/forms/organizations/Member";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { SelectPersonField } from "@/widgets/selectors/select-person";

const i18n = {
  en: {
    titles: {
      add: "Add Member",
      update: "Update Member",
      delete: "Delete Member",
    },
    descriptions: {
      delete: "Are you sure you want to delete this member?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      created: "Member created successfully.",
      updated: "Member updated successfully.",
      deleted: "Member deleted successfully.",
      failedCreate: "Failed to create member: ",
      failedUpdate: "Failed to update member: ",
      failedDelete: "Failed to delete member: ",
    },
  },
  links: {
    organizations: "/app/organizations",
  },
};

export type OrganizationMemberFormProps = Omit<
  DialogFormProps<MemberFormProps, MemberFormValues>,
  "Component" | "onSubmit"
> & {
  showPersonField?: boolean;
  defaultValues?: MemberFormProps["defaultValues"];
};

export function OrganizationMemberForm(props: OrganizationMemberFormProps) {
  const [personQuery, setPersonQuery] = useDebounceValue<string>("", 300);
  const people = api.people.getMany.useQuery(
    {
      query: personQuery,
      pageNumber: 0,
      pageSize: 5,
      onlyUsers: true,
    },
    {
      enabled: props.open,
    },
  );

  const loading = people.isLoading;

  return (
    <MemberForm {...props}>
      {props.showPersonField && (
        <SelectPersonField
          loading={loading}
          data={people.data?.people ?? []}
          onQueryChange={(value) => {
            setPersonQuery(value);
          }}
        />
      )}
      <div className="flex w-full justify-center">
        <MemberFormSubmitButton />
      </div>
    </MemberForm>
  );
}

export function AddMember(
  props: OrganizationMemberFormProps & { organizationId: string },
) {
  const utils = api.useUtils();
  const createMemberMutation = api.members.create.useMutation({
    onSuccess: async () => {
      await utils.members.getAll.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<OrganizationMemberFormProps, MemberFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      defaultValues={{
        organizationId: props.organizationId,
      }}
      variant="outline"
      size="sm"
      showPersonField
      Component={OrganizationMemberForm}
      onSubmit={useCallback<NonNullable<MemberFormProps["onSubmit"]>>(
        async (values) => {
          await createMemberMutation.mutateAsync({
            organizationId: values.organizationId ?? "",
            userId: values.personId ?? "",
            role: values.role,
          });
        },
        [createMemberMutation],
      )}
    />
  );
}

export function UpdateMember(
  props: OrganizationMemberFormProps &
    PropsWithChildren<{
      memberId: string;
      organizationId: string;
      defaultValues?: MemberFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateMemberMutation = api.members.update.useMutation({
    onSuccess: async () => {
      await utils.members.getAll.invalidate({
        organizationId: props.organizationId,
      });
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<OrganizationMemberFormProps, MemberFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      showPersonField={false}
      Component={OrganizationMemberForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<MemberFormProps["onSubmit"]>>(
        async (values) => {
          await updateMemberMutation.mutateAsync({
            organizationId: values.organizationId ?? "",
            userId: values.personId ?? "",
            role: values.role,
          });
        },
        [updateMemberMutation],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteMember({
  personId,
  organizationId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{
  personId: string;
  organizationId: string;
  reroute?: boolean;
}> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteMemberMutation = api.members.delete.useMutation({
    onSuccess: async () => {
      await utils.members.getAll.invalidate({
        organizationId,
      });
      if (reroute) {
        router.replace(i18n.links.organizations);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteMemberMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteMemberMutation.mutateAsync({
          userId: personId,
          organizationId,
        });
      }, [deleteMemberMutation, personId, organizationId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function MemberMenu({
  rerouteOnDelete,
  member,
  organizationId,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    organizationId: string;
    member?: RouterOutputs["members"]["getAll"]["data"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateMember
            onOpenChange={handleDialogOpenChange}
            memberId={member?.id ?? ""}
            organizationId={organizationId}
            defaultValues={{
              organizationId,
              personId: member?.user.id,
              role: member?.role,
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateMember>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteMember
            onOpenChange={handleDialogOpenChange}
            personId={member?.user.id ?? ""}
            organizationId={organizationId}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteMember>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
