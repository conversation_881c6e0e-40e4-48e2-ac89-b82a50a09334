"use client";

import { useMemo } from "react";

import type { ValueStoreType } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { useSearchParamsContext, useSearchValueResult } from "@axa/ui/search";
import { SearchValue } from "@axa/ui/selectors/SelectValue";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

export const SEARCH_ORDER_TYPE_NAME = "type";
export const SEARCH_ORDER_CATEGORY_NAME = "category";

export type OrderType = RouterOutputs["values"]["getMany"]["values"][number];

export interface OrderTypeSelection {
  value: string;
  id: string;
  subtypes?: { id: string; text: string }[];
}

export interface OrderCategorySelection {
  value: string;
  id: string;
}

export interface UseSearchOrderTypeProps {
  enabled?: boolean;
  group?: string;
  typeName?: string;
  categoryName?: string;
  valueStoreType?: ValueStoreType;
}

export function useSearchOrderTypeValue(name = SEARCH_ORDER_TYPE_NAME) {
  const { searchParams } = useSearchParamsContext();
  return searchParams[name] as string | undefined;
}

export interface SearchOrderTypeProps extends UseSearchOrderTypeProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function SearchOrderType({
  enabled = true,
  group = "orders",
  typeName = SEARCH_ORDER_TYPE_NAME,
  categoryName = SEARCH_ORDER_CATEGORY_NAME,
  valueStoreType = "ORDER_TYPE" as ValueStoreType,
  className,
  size = "sm",
}: SearchOrderTypeProps) {
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const typeParam = useSearchValueResult(group, typeName);
  const categoryParam = useSearchValueResult(group, categoryName);

  const types = api.values.getMany.useQuery(
    {
      type: valueStoreType,
    },
    {
      enabled,
    },
  );

  const categoryData = useMemo(() => {
    const type = types.data?.values?.find((t) => t.value === typeParam);

    const subtypes = type?.subtypes as { id: string; text: string }[];

    return (
      subtypes?.map(({ id, text }: { id: string; text: string }) => ({
        value: text,
        id,
      })) ?? []
    );

    return type;
  }, [typeParam, types.data?.values]);

  return (
    <div className={cn("flex flex-row gap-2", className)}>
      {/* Type Selector */}
      <SearchValue
        group={group}
        name="type"
        size={size}
        variant="outline"
        data={types.data?.values ?? []}
        loading={types.isLoading}
        placeholder="Select type"
        defaultValue={typeParam}
        clearable
      />

      {/* Category Selector */}
      <SearchValue
        group={group}
        name="category"
        size={size}
        variant="outline"
        disabled={!typeParam}
        placeholder="Select category"
        data={categoryData}
        loading={types.isLoading}
        defaultValue={categoryParam}
        clearable
      />
    </div>
  );
}
