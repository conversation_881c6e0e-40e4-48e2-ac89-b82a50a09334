"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  ProjectFormProps,
  ProjectFormValues,
} from "@/components/forms/Project";

import { api } from "@/api/client";
import ProjectForm, {
  ProjectFormSubmitButton,
} from "@/components/forms/Project";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    titles: {
      add: "Add Project",
      update: "Update Project",
      delete: "Delete Project",
    },
    descriptions: {
      delete: "Are you sure you want to delete this project?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      created: "Project created successfully.",
      updated: "Project updated successfully.",
      deleted: "Project deleted successfully.",
      failedCreate: "Failed to create project: ",
      failedUpdate: "Failed to update project: ",
      failedDelete: "Failed to delete project: ",
    },
  },
  links: {
    projects: "/app/projects",
  },
};

export type OrganizationProjectFormProps = Omit<
  DialogFormProps<ProjectFormProps, ProjectFormValues>,
  "Component" | "onSubmit"
>;

export function OrganizationProjectForm(props: OrganizationProjectFormProps) {
  return (
    <ProjectForm {...props}>
      <SelectOrganizationField description="The organization that the project belongs to" />
      <div className="flex w-full justify-center">
        <ProjectFormSubmitButton />
      </div>
    </ProjectForm>
  );
}

export function AddProject(props: OrganizationProjectFormProps) {
  const utils = api.useUtils();
  const createProjectMutation = api.projects.create.useMutation({
    onSuccess: async () => {
      await utils.projects.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<ProjectFormProps, ProjectFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      Component={OrganizationProjectForm}
      onSubmit={useCallback<NonNullable<ProjectFormProps["onSubmit"]>>(
        async (values) => {
          await createProjectMutation.mutateAsync({
            ...values,
          });
        },
        [createProjectMutation],
      )}
    />
  );
}

export function UpdateProject(
  props: OrganizationProjectFormProps &
    PropsWithChildren<{
      projectId: string;
      defaultValues?: ProjectFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateProjectMutation = api.projects.update.useMutation({
    onSuccess: async () => {
      await utils.projects.get.invalidate({
        id: props.projectId,
      });
      // TODO: granular invalidation
      await utils.projects.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<ProjectFormProps, ProjectFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationProjectForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<ProjectFormProps["onSubmit"]>>(
        async (values) => {
          await updateProjectMutation.mutateAsync({
            id: props.projectId,
            ...values,
          });
        },
        [updateProjectMutation, props.projectId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteProject({
  projectId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ projectId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteProjectMutation = api.projects.delete.useMutation({
    onSuccess: async () => {
      await utils.projects.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.projects);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteProjectMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteProjectMutation.mutateAsync({ id: projectId });
      }, [deleteProjectMutation, projectId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function ProjectMenu({
  rerouteOnDelete,
  project,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    project?:
      | RouterOutputs["projects"]["get"]
      | RouterOutputs["projects"]["getMany"]["projects"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateProject
            onOpenChange={handleDialogOpenChange}
            projectId={project?.id ?? ""}
            defaultValues={{
              organizationId: project?.organization.id,
              name: project?.name,
              description: project?.description ?? undefined,
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateProject>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteProject
            onOpenChange={handleDialogOpenChange}
            projectId={project?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteProject>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
