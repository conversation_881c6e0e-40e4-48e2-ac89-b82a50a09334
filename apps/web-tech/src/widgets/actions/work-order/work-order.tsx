"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { CopyIcon, MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  WorkOrderFormProps,
  WorkOrderFormValues,
} from "@/components/forms/WorkOrder";

import { api } from "@/api/client";
import WorkOrderForm, {
  WorkOrderFormSubmitButton,
} from "@/components/forms/WorkOrder";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    titles: {
      add: "Add Work Order",
      update: "Update Work Order",
      delete: "Delete Work Order",
      cancel: "Cancel Work Order",
      duplicate: "Duplicate Work Order",
    },
    descriptions: {
      cancel:
        "Are you sure you want to cancel this work order? There will be fees if canceled within 24 hours of the scheduled date.",
      delete: "Are you sure you want to delete this work order?",
      duplicate: "Are you sure you want to duplicate this work order?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
      duplicate: "Duplicate",
      cancel: "Cancel",
    },
    messages: {
      created: "Work order created successfully.",
      updated: "Work order updated successfully.",
      deleted: "Work order deleted successfully.",
      canceled: "Work order canceled successfully.",
      duplicated: "Work order duplicated successfully.",
      failedCreate: "Failed to create work order: ",
      failedUpdate: "Failed to update work order: ",
      failedDelete: "Failed to delete work order: ",
      failedCancel:
        "Failed to cancel work order. Please contact an admin immediately. Reason: ",
      failedDuplicate: "Failed to duplicate work order: ",
    },
  },
  links: {
    orders: "/app/orders",
    order: "/app/orders/[id]",
  },
};

export type OrganizationWorkOrderFormProps = Omit<
  DialogFormProps<WorkOrderFormProps, WorkOrderFormValues>,
  "Component" | "onSubmit"
>;

export function OrganizationWorkOrderForm(
  props: OrganizationWorkOrderFormProps,
) {
  return (
    <WorkOrderForm {...props}>
      <SelectOrganizationField description="The organization that the work order belongs to" />
      <div className="flex w-full justify-center">
        <WorkOrderFormSubmitButton />
      </div>
    </WorkOrderForm>
  );
}

export function AddWorkOrder(props: OrganizationWorkOrderFormProps) {
  const utils = api.useUtils();
  const createWorkOrderMutation = api.orders.create.useMutation({
    onSuccess: async () => {
      await utils.orders.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<WorkOrderFormProps, WorkOrderFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      Component={OrganizationWorkOrderForm}
      onSubmit={useCallback<NonNullable<WorkOrderFormProps["onSubmit"]>>(
        async (values) => {
          await createWorkOrderMutation.mutateAsync({
            ...values,
            status: "DRAFT",
            priority: "MEDIUM",
          });
        },
        [createWorkOrderMutation],
      )}
    />
  );
}

export function UpdateWorkOrder(
  props: OrganizationWorkOrderFormProps &
    PropsWithChildren<{
      orderId: string;
      defaultValues?: WorkOrderFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateWorkOrderMutation = api.orders.update.useMutation({
    onSuccess: async () => {
      await utils.orders.get.invalidate({
        id: props.orderId,
      });
      // TODO: granular invalidation
      await utils.orders.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<WorkOrderFormProps, WorkOrderFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationWorkOrderForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<WorkOrderFormProps["onSubmit"]>>(
        async (values) => {
          await updateWorkOrderMutation.mutateAsync({
            id: props.orderId,
            summary: values.summary,
            scope: values.summary,
            type: values.type,
            category: values.category,
          });
        },
        [updateWorkOrderMutation, props.orderId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteWorkOrder({
  orderId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ orderId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteWorkOrderMutation = api.orders.delete.useMutation({
    onSuccess: async () => {
      await utils.orders.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.orders);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={deleteWorkOrderMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteWorkOrderMutation.mutateAsync({ id: orderId });
      }, [deleteWorkOrderMutation, orderId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function PublishWorkOrder() {
  return null;
}

export function ApproveWorkOrder() {
  return null;
}

export function CancelWorkOrder({
  orderId,
  reroute,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ orderId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const cancelWorkOrderMutation = api.orders.cancel.useMutation({
    onSuccess: async () => {
      await utils.orders.get.invalidate({
        id: orderId,
      });
      await utils.orders.getMany.invalidate();
      if (reroute) {
        router.replace(i18n.links.orders);
      }
      toast.success(i18n.en.messages.canceled);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCancel + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.cancel}
      description={i18n.en.descriptions.cancel}
      onOpenChange={onOpenChange}
      open={open}
      {...props}
      disabled={cancelWorkOrderMutation.isPending}
      action={i18n.en.actions.cancel}
      variant="destructive"
      onClick={useCallback(async () => {
        await cancelWorkOrderMutation.mutateAsync({ id: orderId });
      }, [cancelWorkOrderMutation, orderId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function DuplicateWorkOrder({
  orderId,
  reroute = true,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ orderId: string; reroute?: boolean }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const duplicateWorkOrderMutation = api.orders.duplicate.useMutation({
    onSuccess: async (nextOrder) => {
      await utils.orders.getMany.invalidate();
      if (reroute) {
        router.push(i18n.links.order.replace("[id]", nextOrder.id));
      }
      toast.success(i18n.en.messages.duplicated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDuplicate + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.duplicate}
      description={i18n.en.descriptions.duplicate}
      action={i18n.en.actions.duplicate}
      onOpenChange={onOpenChange}
      variant="primary"
      open={open}
      {...props}
      disabled={duplicateWorkOrderMutation.isPending}
      onClick={useCallback(async () => {
        await duplicateWorkOrderMutation.mutateAsync({ id: orderId });
      }, [duplicateWorkOrderMutation, orderId])}
    >
      {children ?? (
        <Button size="icon">
          <CopyIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function WorkOrderMenu({
  rerouteOnDelete,
  order,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    order?:
      | RouterOutputs["orders"]["get"]
      | RouterOutputs["orders"]["getMany"]["orders"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DuplicateWorkOrder
            onOpenChange={handleDialogOpenChange}
            orderId={order?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.duplicate}
            </DropdownMenuItem>
          </DuplicateWorkOrder>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <CancelWorkOrder
            onOpenChange={handleDialogOpenChange}
            orderId={order?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem
              onSelect={handleItemSelect}
              disabled={["CANCELLED", "APPROVED", "COMPLETED"].includes(
                order?.status ?? "",
              )}
            >
              {i18n.en.actions.cancel}
            </DropdownMenuItem>
          </CancelWorkOrder>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function AssignProvider() {
  return null;
}
