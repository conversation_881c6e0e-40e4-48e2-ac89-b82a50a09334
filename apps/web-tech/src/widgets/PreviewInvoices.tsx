"use client";

import { use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import type { InvoiceStatus } from "@axa/database-tech";
import {
  useSearchDateRangeValue,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListInvoices from "@/components/ListInvoices";
import { useUser } from "@/contexts/User";
import { AddInvoice } from "@/widgets/actions/billing/invoice";

export interface PreviewInvoicesProps {
  loading?: boolean;
  billing?: boolean;
  organization?: string;
  organizations?: string[];
  invoices?: Promise<RouterOutputs["invoices"]["getMany"]>;
}

const invoiceGroup = "invoice";

export default function PreviewInvoices(props: PreviewInvoicesProps) {
  const user = useUser();

  const query = useSearchTextValue(invoiceGroup);
  const organization = useSearchValueResult(invoiceGroup, "organization");
  const status = useSearchFilterValue<InvoiceStatus>("status", invoiceGroup);
  const dates = useSearchDateRangeValue(invoiceGroup);
  const pagination = useSearchPaginationValue(invoiceGroup);

  const invoices = api.invoices.getMany.useQuery(
    {
      organizationId: organization || props.organization,
      organizations: organization ? [organization] : props.organizations,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      status,
      query,
      dates,
    },
    {
      enabled:
        !props.loading &&
        !user.loading &&
        (props.billing
          ? !!props.organization || (props.organizations?.length ?? 0) > 0
          : true),
      initialData: props.invoices ? use(props.invoices) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || invoices.isLoading;

  return (
    <div className="space-y-6">
      {invoices.error && <ErrorFallback error={invoices.error} />}

      <ListInvoices
        loading={loading}
        invoices={invoices.data}
        billing={props.billing}
        group={invoiceGroup}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        {user.isBilling && (
          <AddInvoice
            defaultValues={{
              organizationId: props.organization,
            }}
          />
        )}
      </ListInvoices>
    </div>
  );
}
