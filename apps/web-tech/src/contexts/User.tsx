"use client";

import type { QueryObserver, QueryObserverResult } from "@tanstack/react-query";
import type { PropsWithChildren } from "react";

import { createContext, use, useMemo } from "react";

import type { OrganizationType } from "@axa/database-tech";

import type { RouterError, RouterOutputs } from "@/api";

import { api } from "@/api/client";

type UserType = RouterOutputs["user"]["me"];

export interface UserContextType
  extends Partial<Pick<QueryObserver<UserType>, "refetch">> {
  initialized: boolean;
  loading: boolean;
  error: RouterError | null;
  isInternal?: boolean;
  isBilling?: boolean;
  isAdmin?: boolean;
  isClient?: boolean;
  id?: string;
  role: UserType["role"];
  firstName?: string;
  lastName?: string;
  nickName?: string;
  avatar?: string;
  email: {
    emailAddress?: string;
  };
  phone: {
    phoneNumber?: string;
  };
  preferences?: UserType["preferences"];
  organization: {
    id?: string;
    name?: string;
    avatar?: string | null;
    type?: OrganizationType;
  } | null;
}

export const UserContext = createContext<UserContextType>({
  // refetch: () => Promise.resolve(undefined as unknown),
  initialized: false,
  loading: false,
  error: null,
  isInternal: false,
  isBilling: false,
  isAdmin: false,
  isClient: false,
  role: "CLIENT",
  firstName: "",
  lastName: "",
  nickName: "",
  avatar: "",
  email: {
    emailAddress: "",
  },
  phone: {
    phoneNumber: "",
  },
  organization: {
    id: "",
    name: "",
    avatar: "",
    type: "CLIENT",
  },
});

export function useUser() {
  const ctx = use(UserContext);

  if (!ctx.initialized) {
    throw new Error("User context not used within provider");
  }

  return ctx;
}

export function UserProvider(
  props: PropsWithChildren<{
    user?: Promise<UserType>;
  }>,
) {
  const user = api.user.me.useQuery(undefined, {
    initialData: props.user ? use(props.user) : undefined,
  });

  const role = user.data?.role ?? "CLIENT";

  return (
    <UserContext.Provider
      value={useMemo(
        () => ({
          initialized: true,
          refetch: user.refetch as () => Promise<QueryObserverResult<UserType>>,
          error: user.error,
          role,
          loading: user.isLoading,
          isInternal: ["INTERNAL", "BILLING", "ADMIN"].includes(role),
          isBilling: ["BILLING", "ADMIN"].includes(role),
          isAdmin: role === "ADMIN",
          isClient: role === "CLIENT",
          id: user.data?.id,
          firstName: user.data?.firstName ?? "",
          lastName: user.data?.lastName ?? "",
          nickName: user.data?.nickname ?? "",
          avatar: user.data?.avatarUrl ?? "",
          email: user.data?.email
            ? { emailAddress: user.data.email.emailAddress }
            : { emailAddress: "" },
          phone: user.data?.phone
            ? { phoneNumber: user.data.phone.phoneNumber }
            : { phoneNumber: "" },
          preferences: user.data?.preferences,
          organization: {
            id: user.data?.organization.id,
            name: user.data?.organization.name,
            avatar: user.data?.organization.avatar,
            type: user.data?.organization.type ?? undefined,
          },
        }),
        [user, role],
      )}
    >
      {props.children}
    </UserContext.Provider>
  );
}
