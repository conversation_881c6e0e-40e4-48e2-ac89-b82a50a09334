/* eslint-disable no-restricted-properties */
import { createEnv } from "@t3-oss/env-nextjs";
import { vercel } from "@t3-oss/env-nextjs/presets-zod";
import { z } from "zod";

import { env as apiEnv } from "@axa/api-tech/env";
import { env as uiEnv } from "@axa/ui/env";

export const env = createEnv({
  extends: [vercel(), uiEnv, apiEnv],
  shared: {
    DEBUG: z.boolean().default(false),
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
  },
  server: {
    SENTRY_AUTH_TOKEN: z.string(),
  },
  client: {},
  experimental__runtimeEnv: {
    DEBUG: process.env.DEBUG,
    NODE_ENV: process.env.NODE_ENV,
  },
  skipValidation:
    !!process.env.CI ||
    !!process.env.SKIP_ENV_VALIDATION ||
    process.env.npm_lifecycle_event === "lint",
});
