"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import { UploadDropzone } from "@/components/forms/OrgDocument";
import ListDocuments from "@/components/ListDocuments";
import { AddDocument } from "@/widgets/actions/resources/document";

const i18n = {
  en: {
    title: "Documents",
    description:
      "Documents are files that can be attached to work orders, locations and document. They can be used to provide additional information or instructions to the workforce.",
    actions: {
      add: "Add Document",
    },
  },
  links: {
    document: "/app/documents",
  },
};

export type DocumentsData = RouterOutputs["documents"]["getMany"]["documents"];

export interface DocumentsViewProps {
  loading?: boolean;
  documents?: Promise<RouterOutputs["documents"]["getMany"]>;
}

export const groupName = "document";

export function DocumentsView(props: DocumentsViewProps) {
  const query = useSearchTextValue(groupName);
  const organizations = useSearchValueResult(groupName, "organization");
  const pagination = useSearchPaginationValue(groupName);

  const documents = api.documents.getMany.useQuery(
    {
      query,
      organizations: organizations ? [organizations] : undefined,
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageIndex,
    },
    {
      initialData: props.documents ? use(props.documents) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || documents.isLoading;

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <UploadDropzone className="h-[300px] w-full" disabled={loading} />
      </div>

      {documents.error && <ErrorFallback error={documents.error} />}

      <ListDocuments
        group={groupName}
        documents={documents.data}
        loading={loading}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        <AddDocument title={i18n.en.actions.add} label={i18n.en.actions.add} />
      </ListDocuments>
    </div>
  );
}

export default function DocumentsPage(props: DocumentsViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddDocument
          size="sm"
          title={i18n.en.actions.add}
          label={i18n.en.actions.add}
        />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<DocumentsView loading />}>
          <SearchParams>
            <DocumentsView documents={props.documents} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
