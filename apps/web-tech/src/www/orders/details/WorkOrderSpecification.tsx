import { use<PERSON><PERSON>back, useLayout<PERSON>ffect, useRef, useState } from "react";
import { EditIcon } from "lucide-react";

import type { WorkOrderPriority } from "@axa/database-tech";
import { ValueStoreType } from "@axa/database-tech";
import { Badge } from "@axa/ui/primitives/badge";
import { <PERSON><PERSON> } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from "@axa/ui/primitives/dialog";
import { ScrollArea } from "@axa/ui/primitives/scroll-area";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import CopyButton from "@axa/ui/shared/CopyButton";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { OrderSpecificationFormProps } from "@/components/forms/OrderSpecification";

import { api } from "@/api/client";
import OrderSpecificationForm from "@/components/forms/OrderSpecification";
import OrderPriority from "@/components/symbols/OrderPriority";

const i18n = {
  en: {
    titles: {
      scope: "Scope of work",
    },
    actions: {
      update: "Update",
      copyScope: "Copy Scope",
      copySummary: "Copy Summary",
    },
    messages: {
      updateSuccess: "Order specification updated successfully",
      updateFail: "Failed to update order specification: ",
    },
  },
};

export function UpdateWorkOrderSpecification({
  loading = false,
  order,
}: {
  loading?: boolean;
  order?: {
    id: string;
    summary: string;
    scope: string;
    type: string;
    category: string;
  };
}) {
  const [open, setOpen] = useState(false);
  const utils = api.useUtils();
  const updateSpecMutation = api.orders.update.useMutation({
    onSuccess: async () => {
      await utils.orders.get.invalidate({
        id: order?.id,
      });
      await utils.orders.transactions.get.invalidate({
        orderId: order?.id,
      });
      toast.success(i18n.en.messages.updateSuccess);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.updateFail + error.message);
    },
  });

  const categories = api.values.getMany.useQuery(
    {
      type: ValueStoreType.ORDER_CATEGORY,
    },
    {
      enabled: !loading && open,
    },
  );

  const types = api.values.getMany.useQuery(
    {
      type: ValueStoreType.ORDER_TYPE,
    },
    {
      enabled: !loading && open,
    },
  );

  const dataLoading = loading || categories.isLoading || types.isLoading;

  return (
    <DialogForm
      loading={dataLoading}
      categories={categories.data?.values ?? []}
      types={types.data?.values ?? []}
      open={open}
      onOpenChange={setOpen}
      title="Update Order Specification"
      Component={OrderSpecificationForm}
      onSubmit={useCallback<
        NonNullable<OrderSpecificationFormProps["onSubmit"]>
      >(
        async (values) => {
          await updateSpecMutation.mutateAsync({
            id: order?.id!,
            summary: values.summary,
            scope: values.scope,
            type: values.type,
            category: values.category,
          });
          setOpen(false);
        },
        [order?.id, updateSpecMutation, setOpen],
      )}
      defaultValues={{
        summary: order?.summary,
        scope: order?.scope,
        type: order?.type,
        category: order?.category,
      }}
    >
      <Button
        className="size-full space-x-1 text-muted-foreground"
        variant="outline"
      >
        <EditIcon size={20} color="currentColor" />
        <span>{i18n.en.actions.update}</span>
      </Button>
    </DialogForm>
  );
}

export default function WorkOrderSpecification({
  loading = false,
  order,
}: {
  loading?: boolean;
  order?: {
    id: string;
    summary: string;
    scope: string;
    type: string;
    category: string;
    priority: WorkOrderPriority;
  };
}) {
  const [showDialog, setShowDialog] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const scopeRef = useRef<HTMLParagraphElement | null>(null);

  useLayoutEffect(() => {
    const node = scopeRef.current;
    if (!node) return;

    // Use a frame to ensure styles are applied
    const frame = requestAnimationFrame(() => {
      setIsOverflowing(node.scrollHeight > node.clientHeight + 2);
    });

    // Cleanup to avoid memory leaks
    return () => {
      cancelAnimationFrame(frame);
    };
  }, [order?.scope]);

  return (
    <Card className="flex h-full flex-col justify-between" shadow="lift-heavy">
      <CardHeader>
        {loading ? (
          <Skeleton className="h-6 w-2/3" />
        ) : (
          <div className="group grid w-full grid-cols-[1fr_auto] gap-2 overflow-hidden truncate">
            <CardTitle className="line-clamp-2 w-full truncate text-wrap text-2xl">
              {order?.summary}
            </CardTitle>
            <CopyButton
              text={order?.summary}
              label={i18n.en.actions.copySummary}
            />
          </div>
        )}

        <div className="flex items-center justify-between">
          {loading ? (
            <Skeleton className="h-5 w-1/2" />
          ) : (
            <Badge
              className="w-fit -translate-x-2.5 bg-primary/10"
              variant="muted"
            >
              <CardDescription className="w-fit space-x-1">
                <span>{order?.type}</span>
                <span>|</span>
                <span>{order?.category}</span>
              </CardDescription>
            </Badge>
          )}
          {loading ? (
            <Skeleton className="h-6 w-1/3" />
          ) : (
            <OrderPriority priority={order?.priority ?? "MEDIUM"} />
          )}
        </div>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="flex h-full flex-col gap-2">
          <div className="group flex items-center gap-2">
            <h3 className="font-semibold">{i18n.en.titles.scope}</h3>
            <CopyButton text={order?.scope} label={i18n.en.actions.copyScope} />
          </div>
          {loading ? (
            <div className="space-y-1">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-2/3" />
              <Skeleton className="h-6 w-1/2" />
            </div>
          ) : (
            <>
              <div className="relative">
                <p
                  ref={scopeRef}
                  className="line-clamp-6 overflow-hidden truncate text-wrap pr-8 leading-relaxed text-muted-foreground sm:line-clamp-2"
                  title={order?.scope}
                >
                  {order?.scope}
                </p>
                {isOverflowing && (
                  <div
                    className="pointer-events-none absolute inset-x-0 bottom-0 h-6 bg-gradient-to-t from-background to-transparent"
                    style={{ borderRadius: 4 }}
                  />
                )}
              </div>
              {isOverflowing && order?.scope && (
                <div className="mt-2">
                  <Dialog open={showDialog} onOpenChange={setShowDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        className="w-full px-2 py-1 text-center text-primary underline"
                        onClick={() => setShowDialog(true)}
                      >
                        View More
                      </Button>
                    </DialogTrigger>
                    <DialogContent
                      className="max-h-[80vh] w-full max-w-2xl overflow-y-auto"
                      style={{ maxHeight: "80vh" }}
                    >
                      <DialogTitle>Full Scope of Work</DialogTitle>
                      <ScrollArea className="mt-4 max-h-[60vh] p-2">
                        <p className="whitespace-pre-line text-muted-foreground">
                          {order.scope}
                        </p>
                      </ScrollArea>
                      <DialogFooter>
                        <DialogClose asChild>
                          <Button variant="outline">Close</Button>
                        </DialogClose>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <UpdateWorkOrderSpecification loading={loading} order={order} />
      </CardFooter>
    </Card>
  );
}
