import { use<PERSON><PERSON>back, useMemo } from "react";
import Link from "next/link";
import {
  EditIcon,
  PlusCircleIcon,
  TimerIcon,
  UserPlus2Icon,
  UserRoundSearchIcon,
  UserRoundXIcon,
  XIcon,
} from "lucide-react";

import type { Technician, WorkOrderStatus } from "@axa/database-tech";
import PreviewContact from "@axa/ui/common/PreviewContact";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";

import type { RouterOutputs } from "@/api";
import type {
  AddTechnicianProps,
  DeleteTechnicianProps,
  UpdateTechnicianProps,
} from "@/widgets/actions/work-order/technician";

import { api } from "@/api/client";
import BillingAmount from "@/components/common/BillingAmount";
import TechnicianLevel from "@/components/common/TechnicianLevel";
import { useUser } from "@/contexts/User";
import { SearchProviders } from "@/widgets/actions/resources/provider";
import {
  AddTechnician,
  DeleteTechnician,
  UpdateTechnician,
} from "@/widgets/actions/work-order/technician";

const i18n = {
  en: {
    pendingAssign: "Pending Assignment",
    pendingReview: "Pending Review",
    removeProvider: "Remove Provider",
    removeProviderConfirmation:
      "Are you sure you want to remove the provider from this assignment?",
    actions: {
      setTechnician: "Set Technician Level and Payment",
      assignProvider: "Assign Provider",
      editTechnician: "Edit Technician",
      addTechnician: "Add Technician",
      removeTechnician: "Remove Technician",
      paymentNotSet: "payment options not set",
      removeProvider: "Remove Provider",
      editProvider: "Update Provider",
      reassignProvider: "Reassign Provider",
    },
  },
  links: {
    provider: "/app/providers/[id]",
    timeSheets: "/app/billing/time-sheets/[id]",
  },
};

export function WorkOrderProvider({
  loading = false,
  orderId,
  orderStatus,
  technicianId,
  provider,
  timeSheets = [],
}: {
  loading?: boolean;
  orderId?: string;
  orderStatus?: WorkOrderStatus;
  technicianId?: string;
  provider?: RouterOutputs["technicians"]["get"]["provider"];
  timeSheets?: RouterOutputs["technicians"]["get"]["timeSheets"];
}) {
  const user = useUser();
  const utils = api.useUtils();
  const assignProviderMutation = api.orders.assign.useMutation({
    onSuccess: async () => {
      await utils.orders.get.invalidate({ id: orderId ?? "" });
      await utils.technicians.get.invalidate({ id: technicianId ?? "" });
      toast.success("Provider assigned successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const assignProvider = useCallback(
    async (providerId: string) => {
      await assignProviderMutation.mutateAsync({
        id: orderId ?? "",
        technicianId: technicianId ?? "",
        providerId: providerId,
      });
    },
    [orderId, technicianId, assignProviderMutation],
  );

  const unAssignProviderMutation = api.orders.unAssign.useMutation({
    onSuccess: async () => {
      await utils.orders.get.invalidate({ id: orderId ?? "" });
      await utils.technicians.get.invalidate({ id: technicianId ?? "" });
      toast.success("Provider unassigned successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const unAssignProvider = useCallback(async () => {
    await unAssignProviderMutation.mutateAsync({
      id: orderId ?? "",
      technicianId: technicianId ?? "",
    });
  }, [orderId, technicianId, unAssignProviderMutation]);

  const dataLoading = loading || user.loading;

  return dataLoading || !provider ? (
    <div className="flex size-full items-center justify-center rounded-lg border border-dashed p-0.5">
      {dataLoading ? (
        <Skeleton className="size-full" />
      ) : user.isInternal ? (
        ["DRAFT", "PENDING"].includes(orderStatus ?? "") ? (
          <div className="flex size-full items-center justify-center rounded-lg p-4 font-medium text-muted-foreground">
            {i18n.en.pendingReview}
          </div>
        ) : (
          <div className="size-full rounded-lg">
            <SearchProviders
              className="size-full min-h-full space-x-1 text-base text-muted-foreground"
              variant="ghost"
              onSelect={async (pro) => {
                await assignProvider(pro.id);
              }}
            >
              <UserPlus2Icon size={16} color="currentColor" />
              <span>{i18n.en.actions.assignProvider}</span>
            </SearchProviders>
          </div>
        )
      ) : (
        <div className="flex size-full items-center justify-center rounded-lg p-4 font-medium text-muted-foreground">
          {i18n.en.pendingAssign}
        </div>
      )}
    </div>
  ) : (
    <div className="flex size-full flex-col rounded-lg border">
      <div className="grid grid-cols-[1fr_auto] p-4">
        <PreviewContact
          loading={dataLoading}
          link={
            user.isInternal
              ? i18n.links.provider.replace("[id]", provider.id)
              : undefined
          }
          firstName={provider.firstName}
          lastName={provider.lastName}
          email={provider.email}
          phone={provider.phone}
          avatar={provider.avatar}
        >
          {/* eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing */}
          {(user.isBilling || user.isClient) && (
            <div className="flex size-full items-center justify-center">
              <Button
                asChild
                variant="ghost"
                size="icon"
                className="text-muted-foreground"
              >
                <Link
                  href={i18n.links.timeSheets.replace(
                    "[id]",
                    timeSheets[0]?.id ?? "",
                  )}
                >
                  <TimerIcon size={20} color="currentColor" />
                  <span className="sr-only">
                    {i18n.en.actions.editProvider}
                  </span>
                </Link>
              </Button>
            </div>
          )}
        </PreviewContact>
        {user.isInternal && (
          <div className="grid grid-rows-2 gap-2">
            <SearchProviders
              variant="ghost"
              size="md"
              className="size-fit text-muted-foreground"
              onSelect={async (pro) => {
                await assignProvider(pro.id);
              }}
            >
              <div className="flex items-center justify-center">
                <UserRoundSearchIcon size={20} color="currentColor" />
                <span className="sr-only">
                  {i18n.en.actions.reassignProvider}
                </span>
              </div>
            </SearchProviders>

            <DialogConfirmation
              title={i18n.en.actions.removeProvider}
              description={i18n.en.removeProviderConfirmation}
              onClick={unAssignProvider}
            >
              <Button
                variant="ghost"
                size="icon"
                className="text-muted-foreground"
              >
                <UserRoundXIcon size={20} color="currentColor" />
                <span className="sr-only">
                  {i18n.en.actions.removeProvider}
                </span>
              </Button>
            </DialogConfirmation>
          </div>
        )}
      </div>
    </div>
  );
}

export type TechnicianBare = Pick<Technician, "id">;
export type TechnicianBase = Pick<
  Technician,
  "id" | "level" | "paymentRate" | "paymentType" | "billingRate" | "billingType"
>;

export interface WorkOrderTechnicianProps {
  index?: number;
  local?: boolean;
  loading?: boolean;
  orderId?: string;
  orderStatus?: WorkOrderStatus;
  templateId?: string;
  technicianId?: string | null;
  technician?: TechnicianBare | TechnicianBase | null;
  onCreate?: AddTechnicianProps["onCreate"];
  onUpdate?: UpdateTechnicianProps["onUpdate"];
  onDelete?: DeleteTechnicianProps["onDelete"];
}

export function WorkOrderTechnician({
  index = 0,
  local = false,
  loading = false,
  orderId,
  orderStatus,
  templateId,
  technicianId,
  technician: _tech,
  onCreate,
  onUpdate,
  onDelete,
}: WorkOrderTechnicianProps) {
  const technician = api.technicians.get.useQuery(
    {
      id: technicianId ?? "",
      include: {
        provider: true,
      },
    },
    {
      enabled: !!technicianId && !loading && !local,
    },
  );

  if (loading || technician.isLoading) {
    return (
      <div className="flex h-28 flex-col rounded-lg border-2 border-dashed p-0.5">
        <Skeleton className="size-full" />
      </div>
    );
  }

  const tech = local ? (_tech as TechnicianBase) : technician.data;

  if (technicianId) {
    const level = tech?.level ?? "";
    const billingRate = tech?.billingRate ?? 0;
    const billingType = tech?.billingType ?? null;
    // @ts-expect-error provider is not in the type in local mode
    const provider = (local ? null : tech?.provider) ?? null;
    // @ts-expect-error provider is not in the type in local mode
    const timeSheets = (local ? null : tech?.timeSheets) ?? null;

    return (
      <div className="flex min-h-28 flex-col rounded-lg border-2">
        <div className="flex h-full items-center gap-2 p-4">
          <TechnicianLevel level={`${level}`} />
          <div
            className={cn("grid h-full flex-1 grid-cols-1 gap-6", {
              "xl:grid-cols-3": !!orderId,
            })}
          >
            {billingType ? (
              <div className="mx-auto flex h-full flex-col items-center justify-center">
                <BillingAmount amount={billingRate} type={billingType} />
              </div>
            ) : (
              <div className="mx-auto flex h-full items-center justify-center rounded-lg border-2 border-dashed">
                <p className="text-sm text-muted-foreground">
                  {i18n.en.actions.paymentNotSet}
                </p>
              </div>
            )}
            {orderId && (
              <div className="col-span-2 me-auto hidden size-full xl:grid">
                <WorkOrderProvider
                  loading={loading}
                  orderId={orderId}
                  orderStatus={orderStatus}
                  technicianId={technicianId}
                  provider={provider}
                  timeSheets={timeSheets}
                />
              </div>
            )}
          </div>
          <div className="grid h-full grid-rows-2 gap-1">
            <UpdateTechnician
              orderId={orderId}
              templateId={templateId}
              technicianId={technicianId}
              onUpdate={onUpdate}
              defaultValues={{
                level: tech?.level.toString() ?? undefined,
                billingRate: tech?.billingRate.toString() ?? undefined,
                billingType: tech?.billingType ?? undefined,
                paymentRate:
                  // fixes bug where paymentRate is not a value
                  // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                  (tech?.paymentRate as number | undefined)?.toString() ??
                  undefined,
                paymentType: tech?.paymentType ?? undefined,
              }}
            >
              <Button
                type="button"
                variant="ghost"
                className="size-9 p-0 text-muted-foreground"
              >
                <EditIcon
                  size={20}
                  color="currentColor"
                  aria-label={i18n.en.actions.editTechnician}
                />
              </Button>
            </UpdateTechnician>
            <DeleteTechnician
              orderId={orderId}
              templateId={templateId}
              technicianId={technicianId}
              onDelete={onDelete}
            >
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="text-muted-foreground"
                disabled={orderId !== undefined}
              >
                <XIcon
                  size={20}
                  color="currentColor"
                  aria-label={i18n.en.actions.removeTechnician}
                />
              </Button>
            </DeleteTechnician>
          </div>
        </div>

        {orderId && (
          <div className="border-t p-2 xl:hidden">
            <WorkOrderProvider
              loading={loading}
              orderId={orderId}
              technicianId={technicianId}
              provider={provider}
              timeSheets={timeSheets}
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="h-full min-h-28 rounded-lg border-2 border-dashed p-0.5">
      <AddTechnician
        templateId={templateId}
        orderId={orderId}
        onCreate={onCreate}
      >
        <Button
          type="button"
          className="size-full space-x-1 text-muted-foreground"
          variant="ghost"
        >
          <PlusCircleIcon size={20} color="currentColor" />
          <span>
            {index === 0
              ? i18n.en.actions.setTechnician
              : i18n.en.actions.addTechnician}
          </span>
        </Button>
      </AddTechnician>
    </div>
  );
}

export interface WorkOrderTechniciansProps {
  slots?: number;
  expand?: boolean;
  local?: boolean;
  loading?: boolean;
  className?: string;
  orderId?: string;
  orderStatus?: WorkOrderStatus;
  templateId?: string;
  technicians?: (TechnicianBare | TechnicianBase)[];
  onCreate?: WorkOrderTechnicianProps["onCreate"];
  onUpdate?: WorkOrderTechnicianProps["onUpdate"];
  onDelete?: WorkOrderTechnicianProps["onDelete"];
}

export default function WorkOrderTechnicians({
  slots = 1,
  expand = false,
  local = false,
  loading = false,
  className,
  orderId,
  orderStatus,
  templateId,
  technicians = [],
  onCreate,
  onUpdate,
  onDelete,
}: WorkOrderTechniciansProps) {
  return (
    <ul className={cn("grid w-full grid-cols-1 gap-2", className)}>
      {useMemo(() => {
        let totalSlots = Math.max(slots, technicians.length);

        if (orderId && totalSlots <= technicians.length && expand) {
          totalSlots += 1;
        }

        const list = new Array(totalSlots).fill(null);

        return list
          .map((_, index) => {
            if (technicians[index]) {
              return technicians[index];
            }

            return null;
          })
          .map((technician, key) => (
            <li key={key}>
              <WorkOrderTechnician
                index={key}
                local={local}
                loading={loading}
                orderId={orderId}
                orderStatus={orderStatus}
                templateId={templateId}
                technicianId={technician?.id}
                technician={technician}
                onCreate={onCreate}
                onUpdate={onUpdate}
                onDelete={onDelete}
              />
            </li>
          ));
      }, [
        orderStatus,
        technicians,
        expand,
        slots,
        orderId,
        templateId,
        loading,
        local,
        onCreate,
        onUpdate,
        onDelete,
      ])}
    </ul>
  );
}
