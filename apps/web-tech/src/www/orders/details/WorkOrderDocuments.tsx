import { useCallback, useMemo } from "react";
import { EditIcon, XIcon } from "lucide-react";

import type { Document } from "@axa/database-tech";
import PreviewDocument from "@axa/ui/common/PreviewDocument";
import { Button } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";

import type { SearchDocumentsProps } from "@/widgets/actions/resources/document";

import { api } from "@/api/client";
import {
  SearchDocuments,
  UpdateDocument,
} from "@/widgets/actions/resources/document";

const i18n = {
  en: {
    noDescription: "No description",
    actions: {
      addDocument: "Add Document",
      editDocument: "Edit Document",
      removeDocument: "Remove Document",
    },
    removeDocument: {
      title: "Remove Document",
      description: "Are you sure you want to remove this document?",
    },
    messages: {
      linkSuccess: "Document linked successfully.",
      linkError: "Failed to link document. ",
      unlinkSuccess: "Document unlinked successfully.",
      unlinkError: "Failed to unlink document. ",
    },
  },
};

type PartialDocument = Pick<
  Document,
  "id" | "name" | "url" | "type" | "description" | "size" | "organizationId"
>;

export interface WorkOrderDocumentProps {
  loading?: boolean;
  orderId?: string;
  templateId?: string;
  document?: PartialDocument;
  onLink?: SearchDocumentsProps["onSelect"];
  onUnlink?: (id: string) => void | Promise<void>;
}

export function WorkOrderDocument({
  loading = false,
  orderId,
  templateId,
  document,
  onLink,
  onUnlink,
}: WorkOrderDocumentProps) {
  const utils = api.useUtils();

  const linkDocumentMutation = api.documents.link.useMutation({
    onSuccess: async () => {
      if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      }
      toast.success(i18n.en.messages.linkSuccess);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.linkError + error.message);
    },
  });

  const unlinkDocumentMutation = api.documents.unlink.useMutation({
    onSuccess: async () => {
      if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      }
      toast.success(i18n.en.messages.unlinkSuccess);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.unlinkError + error.message);
    },
  });

  const linkDocument = useCallback<
    NonNullable<SearchDocumentsProps["onSelect"]>
  >(
    async (_document) => {
      if (onLink) {
        await onLink(_document);
      }
      if (orderId || templateId) {
        await linkDocumentMutation.mutateAsync({
          id: _document.id,
          orderId,
          templateId,
        });
      }
    },
    [linkDocumentMutation, onLink, orderId, templateId],
  );

  const unlinkDocument = useCallback(async () => {
    if (document?.id) {
      if (onUnlink) {
        await onUnlink(document.id);
      }
      if (orderId || templateId) {
        await unlinkDocumentMutation.mutateAsync({
          id: document.id,
          orderId,
          templateId,
        });
      }
    }
  }, [unlinkDocumentMutation, onUnlink, document?.id, orderId, templateId]);

  if (loading) {
    return (
      <div className="flex size-full flex-col gap-4 rounded-lg border-2 border-dashed p-0.5">
        <Skeleton className="size-full min-h-28" />
      </div>
    );
  }

  if (document) {
    return (
      <div className="flex h-28 flex-col rounded-lg border-2">
        <div className="flex items-center justify-between gap-2 p-4">
          <div className="flex h-full flex-1 flex-col gap-2">
            <PreviewDocument loading={loading} document={document} />
            <p className="flex-1 text-sm text-muted-foreground">
              {document.description ?? i18n.en.noDescription}
            </p>
          </div>

          <div className="flex flex-col gap-1">
            <UpdateDocument
              documentId={document.id}
              templateId={templateId}
              orderId={orderId}
              defaultValues={{
                organizationId: document.organizationId ?? undefined,
                name: document.name,
                description: document.description ?? undefined,
              }}
            >
              <Button
                type="button"
                variant="ghost"
                className="size-9 p-0 text-muted-foreground"
              >
                <EditIcon
                  size={20}
                  color="currentColor"
                  aria-label={i18n.en.actions.editDocument}
                />
              </Button>
            </UpdateDocument>
            <DialogConfirmation
              title={i18n.en.removeDocument.title}
              description={i18n.en.removeDocument.description}
              action={i18n.en.actions.removeDocument}
              onClick={unlinkDocument}
            >
              <Button type="button" variant="ghost" size="icon">
                <XIcon size={20} color="currentColor" />
              </Button>
            </DialogConfirmation>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex size-full min-h-28 flex-col rounded-lg border-2 border-dashed p-0.5">
      <SearchDocuments onSelect={linkDocument} className="min-h-full" />
    </div>
  );
}

export default function WorkOrderDocuments({
  loading = false,
  orderId,
  templateId,
  documents,
  onLink,
  onUnlink,
}: {
  loading?: boolean;
  orderId?: string;
  templateId?: string;
  documents?: PartialDocument[];
} & Pick<WorkOrderDocumentProps, "onLink" | "onUnlink">) {
  return (
    <ul className="grid grid-cols-1 gap-2 lg:grid-cols-1 xl:grid-cols-3">
      {useMemo(() => {
        const slots =
          (documents?.length ?? 0) <= 3 ? 3 : (documents?.length ?? 0);
        const length =
          (documents?.length ?? 0) > slots ? documents?.length : slots;
        return new Array(length).fill(null).map((_, index) => {
          const document = documents?.[index];
          return (
            <li key={document?.id ?? index}>
              <WorkOrderDocument
                loading={loading}
                orderId={orderId}
                templateId={templateId}
                document={document}
                onLink={onLink}
                onUnlink={onUnlink}
              />
            </li>
          );
        });
      }, [templateId, orderId, documents, loading, onLink, onUnlink])}
    </ul>
  );
}
