"use client";

import { useCallback } from "react";
import { AdvancedMarker } from "@vis.gl/react-google-maps";
import { EditIcon, XIcon } from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import type { WorkOrderStatus } from "@axa/database-tech";
import PreviewLocation from "@axa/ui/common/PreviewLocation";
import { GoogleMap } from "@axa/ui/maps";
import { Button } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";

import type { SearchLocationProps } from "@/widgets/actions/resources/location";

import { api } from "@/api/client";
import {
  SearchLocation,
  UpdateLocation,
} from "@/widgets/actions/resources/location";

const i18n = {
  en: {
    actions: {
      update: "Update",
      remove: "Remove",
      removeLocation: "Remove Location",
    },
    removeLocation: {
      title: "Remove Location",
      description: "Are you sure you want to remove this location?",
    },
    messages: {
      linkSuccess: "Location linked successfully.",
      linkError: "Failed to link location. ",
      unlinkSuccess: "Location unlinked successfully.",
      unlinkError: "Failed to unlink location. ",
    },
  },
};

export type LocationUnion =
  | RouterOutputs["orders"]["get"]["location"]
  | RouterOutputs["templates"]["get"]["location"]
  | RouterOutputs["locations"]["get"]
  | RouterOutputs["locations"]["getMany"]["locations"][number];

export interface WorkOrderLocationProps {
  loading?: boolean;
  orderId?: string;
  templateId?: string;
  status?: WorkOrderStatus;
  location?: LocationUnion;
  onLink?: (location?: LocationUnion) => void | Promise<void>;
  onUnlink?: (id: string) => void | Promise<void>;
}

export default function WorkOrderLocation({
  loading = false,
  orderId,
  templateId,
  status,
  location,
  onLink,
  onUnlink,
}: WorkOrderLocationProps) {
  const utils = api.useUtils();
  const linkLocationMutation = api.locations.link.useMutation({
    onSuccess: async () => {
      if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      }
      toast.success(i18n.en.messages.linkSuccess);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.linkError + error.message);
    },
  });

  const unlinkLocationMutation = api.locations.unlink.useMutation({
    onSuccess: async () => {
      if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      }
      toast.success(i18n.en.messages.unlinkSuccess);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.unlinkError + error.message);
    },
  });

  const linkLocation = useCallback<
    NonNullable<SearchLocationProps["onSelect"]>
  >(
    async (_location) => {
      if (onLink) {
        await onLink(_location);
      }

      if (orderId || templateId) {
        await linkLocationMutation.mutateAsync({
          id: _location.id,
          orderId,
          templateId,
        });
      }
    },
    [linkLocationMutation, onLink, orderId, templateId],
  );

  const unlinkLocation = useCallback(async () => {
    if (location) {
      if (onUnlink) {
        await onUnlink(location.id);
      }

      if (orderId || templateId) {
        await unlinkLocationMutation.mutateAsync({
          id: location.id,
          orderId,
          templateId,
        });
      }
    }
  }, [unlinkLocationMutation, onUnlink, location, orderId, templateId]);

  const onSelect = useCallback<NonNullable<SearchLocationProps["onSelect"]>>(
    async (_location) => {
      await linkLocation(_location);
    },
    [linkLocation],
  );

  if (loading) {
    return (
      <div className="flex size-full flex-col gap-4 rounded-lg border-2 border-dashed p-0.5">
        <Skeleton className="size-full min-h-60" />
      </div>
    );
  }

  if (location) {
    const isLocked = orderId
      ? ["APPROVED", "COMPLETED"].includes(status ?? "")
      : false;
    // @ts-expect-error location overlapping types, due to conditional selector
    const address = location.address;
    const activeLocation = {
      // @ts-ignore organization comes from two different types of sources
      organizationId: location.organization?.id || location.organizationId,
      fieldNationId: location.fieldNationId?.toString(),
      id: location.id,
      name: location.name,
      description: location.description,
      address: {
        formatted: address.formatted ?? "",
        street: address.street ?? "",
        city: address.city ?? "",
        state: address.state ?? "",
        postal: address.postal ?? "",
        country: address.country ?? "",
        latitude: address.latitude ?? 0,
        longitude: address.longitude ?? 0,
      },
    };

    return (
      <div className="flex size-full flex-col gap-4 overflow-hidden rounded-lg border-2 sm:aspect-square">
        <div className="w-full flex-1 overflow-hidden border-b-2">
          <GoogleMap
            className="h-full min-h-[200px]"
            defaultZoom={15}
            defaultCenter={{
              lat: activeLocation.address.latitude,
              lng: activeLocation.address.longitude,
            }}
          >
            <AdvancedMarker
              position={{
                lat: activeLocation.address.latitude,
                lng: activeLocation.address.longitude,
              }}
            />
          </GoogleMap>
        </div>

        <div className="grid gap-4 p-4">
          <div className="grid w-full grid-cols-1 gap-2">
            <PreviewLocation
              link
              loading={loading}
              className="text-wrap"
              location={{
                id: activeLocation.id,
                name: activeLocation.name,
                address: {
                  formatted: activeLocation.address.formatted,
                },
              }}
            />
            <div className="mt-2">
              <p className="text-sm text-muted-foreground">
                {activeLocation.description ?? "No description"}
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <UpdateLocation
              locationId={activeLocation.id}
              defaultValues={activeLocation}
            >
              <Button
                disabled={isLocked}
                type="button"
                variant="outline"
                className="flex-1 gap-1 text-muted-foreground"
              >
                <EditIcon size="20" color="currentColor" />
                <span>{i18n.en.actions.update}</span>
              </Button>
            </UpdateLocation>
            <DialogConfirmation
              disabled={unlinkLocationMutation.isPending}
              title={i18n.en.removeLocation.title}
              description={i18n.en.removeLocation.description}
              action={i18n.en.actions.removeLocation}
              onClick={unlinkLocation}
            >
              <Button
                disabled={isLocked}
                type="button"
                variant="outline"
                className="flex-1 gap-1 text-muted-foreground"
              >
                <XIcon size="20" color="currentColor" />
                <span>{i18n.en.actions.remove}</span>
              </Button>
            </DialogConfirmation>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex aspect-square size-full flex-col rounded-lg border-2 border-dashed p-0.5">
      <div className="grid size-full">
        <SearchLocation onSelect={onSelect} className="min-h-full" />
      </div>
    </div>
  );
}
