"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListTemplates from "@/components/ListTemplates";
import { AddTemplate } from "@/widgets/actions/work-order/template";

const i18n = {
  en: {
    title: "Templates",
    description:
      "Templates are a collection of preset fields for work orders. They can be used to populate new work orders.",
    actions: {
      add: "Add Template",
    },
  },
};

export type TemplatesData = RouterOutputs["templates"]["getMany"]["templates"];

export interface TemplatesViewProps {
  loading?: boolean;
  templates?: Promise<RouterOutputs["templates"]["getMany"]>;
}

export const groupName = "templates";

export function TemplatesView(props: TemplatesViewProps) {
  const query = useSearchTextValue(groupName);
  const organizations = useSearchValueResult(groupName, "organization");
  const pagination = useSearchPaginationValue(groupName);

  const templates = api.templates.getMany.useQuery(
    {
      query,
      organizations: organizations ? [organizations] : undefined,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
    },
    {
      enabled: !props.loading,
      initialData: props.templates ? use(props.templates) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || templates.isLoading;

  return (
    <div className="space-y-6">
      {templates.error && <ErrorFallback error={templates.error} />}

      <ListTemplates
        loading={loading}
        templates={templates.data}
        group={groupName}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      />
    </div>
  );
}

export default function TemplatesPage({ templates }: TemplatesViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddTemplate size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<TemplatesView loading />}>
          <SearchParams>
            <TemplatesView templates={templates} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
