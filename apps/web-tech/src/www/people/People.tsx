"use client";

import { Suspense, use, useState } from "react";

import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListPeople from "@/components/ListPeople";
import { AddPerson } from "@/widgets/actions/resources/people";

const i18n = {
  en: {
    title: "People",
    description: "People are the individuals that you work with.",
    noPeople: "There are no people yet",
    actions: {
      add: "Add People",
      search: "Search people...",
    },
    headers: {
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
  },
  links: {
    people: "/app/people/[id]",
  },
};

export type PeopleQueryResult = RouterOutputs["people"]["getMany"];
export type PeopleData = PeopleQueryResult["people"];

export interface PeopleViewProps {
  loading?: boolean;
  people?: Promise<PeopleQueryResult>;
}

export const groupName = "people";

export const defaultPeople: PeopleData = [];

export function PeopleView(props: PeopleViewProps) {
  const query = useSearchTextValue(groupName);
  const organizations = useSearchValueResult(groupName, "organization");
  const pagination = useSearchPaginationValue(groupName);

  const people = api.people.getMany.useQuery(
    {
      query,
      organizations: organizations ? [organizations] : undefined,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
    },
    {
      enabled: !props.loading,
      initialData: props.people ? use(props.people) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || people.isLoading;

  return (
    <div className="space-y-6">
      {people.error && <ErrorFallback error={people.error} />}

      <ListPeople
        people={people.data}
        loading={loading}
        group={groupName}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        <AddPerson />
      </ListPeople>
    </div>
  );
}

export default function PeoplePage({ people }: PeopleViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddPerson size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<PeopleView loading />}>
          <SearchParams>
            <PeopleView people={people} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
