"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { Suspense, use, useMemo } from "react";
import { PlusIcon } from "lucide-react";

import type { FilterGroup, TableConfig } from "@axa/ui/tables/table";
import { ValueStoreType } from "@axa/database-tech";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import Table, { DataTableColumnHeader } from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { toast } from "@/ui/primitives/toast";
import {
  SearchParams,
  SearchText,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@/ui/search";
import { AddValueMap, ValueMapMenu } from "@/widgets/actions/values";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    actions: {
      search: "Search values...",
    },
  },
};

export type ValueType = NonNullable<
  RouterOutputs["values"]["getMany"]["values"][number]
>;

const VALUE_TYPES = {
  ALL: "All",
  [ValueStoreType.EXPENSE]: "Expense Types",
  [ValueStoreType.CONTACT]: "Contact Roles",
  [ValueStoreType.ORDER_TYPE]: "Order Types",
  [ValueStoreType.ORDER_CATEGORY]: "Order Categories",
  [ValueStoreType.LOCATION_TYPE]: "Location Types",
} satisfies Record<ValueStoreType | "ALL", string>;

const filterGroups: FilterGroup[] = [
  {
    id: "type",
    label: "Value Type",
    options: [
      { value: null, label: VALUE_TYPES.ALL },
      {
        value: ValueStoreType.EXPENSE,
        label: VALUE_TYPES[ValueStoreType.EXPENSE],
      },
      {
        value: ValueStoreType.CONTACT,
        label: VALUE_TYPES[ValueStoreType.CONTACT],
      },
      {
        value: ValueStoreType.ORDER_TYPE,
        label: VALUE_TYPES[ValueStoreType.ORDER_TYPE],
      },
      {
        value: ValueStoreType.ORDER_CATEGORY,
        label: VALUE_TYPES[ValueStoreType.ORDER_CATEGORY],
      },
      {
        value: ValueStoreType.LOCATION_TYPE,
        label: VALUE_TYPES[ValueStoreType.LOCATION_TYPE],
      },
    ],
  },
];

const tableConfig: TableConfig = {
  groupName: "values",
  enableSelection: true,
  i18n: {
    noData: "No values found",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search values...",
    },
  },
};

interface ValuesTablePresentationProps {
  group?: string;
  data: ValueType[];
  loading: boolean;
  onBulkDelete: (values: ValueType[]) => void;
  onBulkExport: (values: ValueType[]) => void;
  total?: number;
}

function ValuesTablePresentation({
  group = groupName,
  data,
  loading,
  onBulkDelete,
  onBulkExport,
  total,
}: ValuesTablePresentationProps) {
  return (
    <section className="flex flex-col gap-4">
      <header className="flex justify-between">
        <h2 className="text-xl font-semibold">System Types</h2>
        <AddValueMap>
          <Button variant="primary" size="sm" className="gap-2">
            <PlusIcon className="size-4" />
            <span>Add Value</span>
          </Button>
        </AddValueMap>
      </header>

      <Table<ValueType>
        loading={loading}
        config={tableConfig}
        data={useMemo(
          () => ({
            items: data,
            total: total ?? 0,
          }),
          [data, total],
        )}
        filters={filterGroups}
        header={
          <>
            <SearchText
              group={group}
              loading={loading}
              placeholder={i18n.en.actions.search}
            />
            <div>
              <SearchOrganizations
                group={group}
                loading={loading}
                size="sm"
                variant="outline"
                className="w-fit min-w-48"
                clearable
              />
            </div>
          </>
        }
        actions={useMemo(
          () => [
            // Selection Actions (formerly bulk actions)
            {
              type: "selection" as const,
              label: "Delete Selected",
              variant: "destructive" as const,
              disabled: (context) =>
                context.type === "selection" &&
                context.selectedRows.length === 0,
              onClick: (context) => {
                if (context.type === "selection") {
                  onBulkDelete(context.selectedRows);
                }
              },
            },
            {
              type: "selection" as const,
              label: "Export Selected",
              disabled: (context) =>
                context.type === "selection" &&
                context.selectedRows.length === 0,
              onClick: (context) => {
                if (context.type === "selection") {
                  onBulkExport(context.selectedRows);
                }
              },
            },
            // Row Actions (using render escape hatch for ValueMapMenu)
            {
              type: "row" as const,
              label: "Row Actions",
              render: (context) => {
                if (context.type === "row") {
                  return (
                    <ValueMapMenu
                      variant="ghost"
                      size="icon"
                      value={context.row}
                    />
                  );
                }
                return null;
              },
            },
          ],
          [onBulkDelete, onBulkExport],
        )}
        columns={useMemo<ColumnDef<ValueType>[]>(
          () => [
            {
              id: "key",
              accessorKey: "key",
              header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Key" />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const key = row.getValue("key") as ValueType["key"];
                return (
                  <div className="font-mono text-sm">
                    {key ?? <span className="text-muted-foreground">—</span>}
                  </div>
                );
              },
              enableHiding: false,
            },
            {
              id: "value",
              accessorKey: "value",
              header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Value" />
              ),
              cell: ({ row }) => (
                <div className="max-w-[200px] truncate font-medium">
                  {row.getValue("value")}
                </div>
              ),
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Type" />
              ),
              cell: ({ row }) => {
                const type = row.getValue("type");
                return (
                  <Badge variant="secondary" className="text-xs">
                    {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
                    {VALUE_TYPES[type as keyof typeof VALUE_TYPES] ?? type}
                  </Badge>
                );
              },
              filterFn: (row, id, value: string[]) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const rowType = row.getValue("type") as ValueType["type"];
                return value.includes(rowType);
              },
            },
            {
              id: "organization",
              accessorKey: "organization",
              header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Organization" />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const org = row.getValue(
                  "organization",
                ) as ValueType["organization"];
                return org ? (
                  <div className="text-sm">{org.name}</div>
                ) : (
                  <div className="text-sm text-muted-foreground">Global</div>
                );
              },
            },
          ],
          [],
        )}
      />
    </section>
  );
}

const groupName = "values";
export function ValuesTable(props: {
  values?: Promise<RouterOutputs["values"]["getMany"]>;
}) {
  const valuesQuery = useSearchTextValue(groupName);
  const valueOrganization = useSearchValueResult(groupName, "organization");
  const valuesPagination = useSearchPaginationValue(groupName);
  const valueStatus = useSearchFilterValue<ValueType["type"]>(
    "type",
    groupName,
  );

  const values = api.values.getMany.useQuery(
    {
      query: valuesQuery,
      type: valueStatus,
      organization: valueOrganization,
      pageSize: valuesPagination.pageSize,
      pageNumber: valuesPagination.pageIndex,
    },
    {
      initialData: props.values ? use(props.values) : undefined,
    },
  );

  const handleBulkDelete = (selectedValues: ValueType[]) => {
    console.log("Deleting values:", selectedValues);
    // TODO: Implement bulk delete logic
    toast.warning("Not implemented");
  };

  const handleBulkExport = (selectedValues: ValueType[]) => {
    console.log("Exporting values:", selectedValues);
    // TODO: Implement export logic
    toast.warning("Not implemented");
  };

  return (
    <ValuesTablePresentation
      data={values.data?.values ?? []}
      loading={values.isLoading}
      onBulkDelete={handleBulkDelete}
      onBulkExport={handleBulkExport}
      total={values.data?.total}
    />
  );
}

// Container component that handles API calls and data fetching
export default function ValuesTableView() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SearchParams>
        <ValuesTable />
      </SearchParams>
    </Suspense>
  );
}
