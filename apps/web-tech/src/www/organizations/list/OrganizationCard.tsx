"use client";

import { useMemo } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { OrganizationTypeBadge } from "@/components/symbols/OrganizationType";
import { OrganizationMenu } from "@/widgets/actions/organizations/organization";
import MemberAvatarList from "@/www/organizations/list/MemberAvatarList";

export default function OrganizationCard({
  loading = false,
  organization,
}: {
  loading?: boolean;
  organization?: RouterOutputs["organizations"]["getMany"]["organizations"][number];
}) {
  return (
    <section
      key={organization?.id}
      className="flex flex-col items-center gap-4 rounded-md p-4 shadow-md dark:ring-2 dark:ring-muted"
    >
      <header
        key={organization?.id}
        className="flex w-full items-center gap-4 p-2"
      >
        <PreviewOrganization
          loading={loading}
          organization={organization}
          link
        />

        <div className="ml-auto flex gap-2">
          <OrganizationTypeBadge
            loading={loading}
            type={organization?.type ?? "CLIENT"}
          />
          <div className="flex items-center gap-2">
            {loading ? (
              <Skeleton className="aspect-square size-10" />
            ) : (
              <OrganizationMenu organization={organization} />
            )}
          </div>
        </div>
      </header>

      <footer className="flex w-full flex-row gap-2 p-2">
        <MemberAvatarList
          loading={loading}
          compress
          members={useMemo(() => {
            return organization?.members?.map((member) => ({
              id: member.id,
              name: [member.user.firstName ?? "", member.user.lastName ?? ""]
                .join(" ")
                .trim(),
              initials: [
                (member.user.firstName ?? "")[0],
                (member.user.lastName ?? "")[0],
              ]
                .join("")
                .trim(),
              avatar: member.user.avatar ?? "",
            }));
          }, [organization?.members])}
        />
      </footer>
    </section>
  );
}
