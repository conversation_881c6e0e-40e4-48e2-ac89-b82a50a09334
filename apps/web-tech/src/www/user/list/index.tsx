"use client";

import { Suspense, use } from "react";

import type { PersonRole } from "@axa/database-tech";
import { toast } from "@axa/ui/primitives/toast";
import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

import Users from "./Users";

export type UsersQueryResult = NonNullable<RouterOutputs["user"]["getMany"]>;
export type InvitationsQueryResult = NonNullable<
  RouterOutputs["user"]["invitations"]["getMany"]
>;

export type UsersData = UsersQueryResult["users"];
export type InvitationData = InvitationsQueryResult["invites"];
export type InvitationStatus = NonNullable<InvitationData[number]["status"]>;

export interface UsersViewProps {
  loading?: boolean;
  users?: Promise<UsersQueryResult>;
  invitations?: Promise<InvitationsQueryResult>;
}
const userGroup = "user";
const invitationGroup = "user-invitations";
export function UsersView(props: UsersViewProps) {
  const userPagination = useSearchPaginationValue(userGroup);
  const userOrganizations = useSearchValueResult<string>(
    userGroup,
    "organization",
  );
  const userRole = useSearchFilterValue<PersonRole>("role", userGroup);
  const userQuery = useSearchTextValue(userGroup);

  const users = api.user.getMany.useQuery(
    {
      query: userQuery,
      organizations: userOrganizations ? [userOrganizations] : undefined,
      pageNumber: userPagination.pageIndex,
      pageSize: userPagination.pageSize,
      roles: userRole ? [userRole] : undefined,
    },
    {
      enabled: !props.loading,
      initialData: props.users ? use(props.users) : undefined,
    },
  );

  const invitationPagination = useSearchPaginationValue(invitationGroup);
  const invitationStatus = useSearchFilterValue<InvitationStatus>(
    "status",
    invitationGroup,
  );
  const invitationQuery = useSearchTextValue(invitationGroup);
  const invitations = api.user.invitations.getMany.useQuery(
    {
      query: invitationQuery,
      status: invitationStatus,
      limit: invitationPagination.pageSize,
      offset: invitationPagination.pageIndex * invitationPagination.pageSize,
    },
    {
      enabled: !props.loading,
      initialData: props.invitations ? use(props.invitations) : undefined,
    },
  );

  const sendUserInvitation = api.user.invitations.send.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Invitation sent successfully!");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const revokeUserInvitation = api.user.invitations.revoke.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Invitation revoked successfully!");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const syncPeopleMutation = api.admin.clerk.syncClerkUsers.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Users synced");
    },
  });

  const syncOrganizationsMutation =
    api.admin.clerk.syncClerkOrganizations.useMutation({
      onSuccess: async () => {
        await users.refetch();
        await invitations.refetch();
        toast.success("Organizations synced");
      },
    });

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || users.isLoading;

  return (
    <Users
      group={userGroup}
      loading={loading}
      users={users}
      invitations={invitations}
      sendUserInvitation={sendUserInvitation}
      revokeUserInvitation={revokeUserInvitation}
      syncPeopleMutation={syncPeopleMutation}
      syncOrganizationsMutation={syncOrganizationsMutation}
    />
  );
}

export default function UsersPage({ users, invitations }: UsersViewProps) {
  return (
    <Suspense fallback={<UsersView loading />}>
      <SearchParams>
        <UsersView users={users} invitations={invitations} />
      </SearchParams>
    </Suspense>
  );
}
