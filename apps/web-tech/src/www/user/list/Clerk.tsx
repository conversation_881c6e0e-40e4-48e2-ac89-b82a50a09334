import { useState } from "react";
import {
  AlertTriangleIcon,
  BuildingIcon,
  FolderSyncIcon,
  RefreshCwIcon,
  ShieldCheckIcon,
  UsersIcon,
  XIcon,
} from "lucide-react";

import { cn } from "@axa/ui/lib";
import { Badge } from "@axa/ui/primitives/badge";
import { But<PERSON> } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

const i18n = {
  en: {
    title: "Clerk Management",
    description:
      "Sync users and organizations, detect violations, and manage data integrity",
    sync: {
      title: "Data Synchronization",
      description: "Keep Clerk and database in sync",
      users: "Sync Users",
      organizations: "Sync Organizations",
      success: {
        users: "Users synced successfully",
        organizations: "Organizations synced successfully",
      },
    },
    violations: {
      title: "Data Violations",
      description: "Review and fix data integrity issues",
      refresh: "Check Violations",
      noViolations: "No violations found",
      types: {
        duplicate: "Duplicate Organizations",
        "no-manager": "Missing Manager",
        "no-clerk-organization": "Missing in Clerk",
      },
      deduplicate: "Merge Organizations",
      confirmDeduplicate:
        "Are you sure you want to merge these duplicate organizations? This action cannot be undone.",
    },
  },
};

type ViolationsQueryResult = NonNullable<
  RouterOutputs["admin"]["clerk"]["getViolations"]
>;

interface ClerkSyncButtonProps {
  type: "users" | "organizations";
  loading?: boolean;
  onSync: () => void | Promise<void>;
}

function ClerkSyncButton({
  type,
  loading = false,
  onSync,
}: ClerkSyncButtonProps) {
  const isUsers = type === "users";
  const Icon = isUsers ? UsersIcon : BuildingIcon;
  const label = isUsers ? i18n.en.sync.users : i18n.en.sync.organizations;

  return (
    <Button className="space-x-2" disabled={loading} onClick={onSync}>
      {loading ? (
        <RefreshCwIcon className="size-4 animate-spin" />
      ) : (
        <Icon className="size-4" />
      )}
      <span>{label}</span>
    </Button>
  );
}

interface ViolationItemProps {
  violation: ViolationsQueryResult["violations"][0];
  onDeduplicate?: (organizationId: string) => void;
  deduplicating?: boolean;
}

function ViolationItem({
  violation,
  onDeduplicate,
  deduplicating = false,
}: ViolationItemProps) {
  const { type, organization, duplicates } = violation;

  const getViolationColor = (type: string) => {
    switch (type) {
      case "duplicate":
        return "destructive";
      case "no-manager":
        return "secondary";
      case "no-clerk-organization":
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <div className="flex items-center justify-between rounded-lg border p-4">
      <div className="flex items-center gap-3">
        <AlertTriangleIcon className="size-5 text-destructive" />
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-medium">{organization.name}</span>
            <Badge variant={getViolationColor(type)}>
              {i18n.en.violations.types[type]}
            </Badge>
          </div>
          {duplicates && duplicates.length > 0 && (
            <p className="text-sm text-muted-foreground">
              {duplicates.length} duplicate{duplicates.length > 1 ? "s" : ""}{" "}
              found
            </p>
          )}
        </div>
      </div>

      {type === "duplicate" && onDeduplicate && (
        <DialogConfirmation
          title="Merge Duplicate Organizations"
          description={i18n.en.violations.confirmDeduplicate}
          onClick={() => onDeduplicate(organization.id)}
        >
          <Button
            variant="outline"
            size="sm"
            disabled={deduplicating}
            className="space-x-1"
          >
            {deduplicating ? (
              <RefreshCwIcon className="size-4 animate-spin" />
            ) : (
              <ShieldCheckIcon className="size-4" />
            )}
            <span>{i18n.en.violations.deduplicate}</span>
          </Button>
        </DialogConfirmation>
      )}
    </div>
  );
}

interface ClerkManagementProps {
  onSyncUsers?: () => void | Promise<void>;
  onSyncOrganizations?: () => void | Promise<void>;
  syncUsersLoading?: boolean;
  syncOrganizationsLoading?: boolean;
}

export default function ClerkManagement({
  onSyncUsers,
  onSyncOrganizations,
  syncUsersLoading = false,
  syncOrganizationsLoading = false,
}: ClerkManagementProps) {
  const [violationsRefreshing, setViolationsRefreshing] = useState(false);

  // Violations query
  const violationsQuery = api.admin.clerk.getViolations.useQuery();

  // Deduplicate mutation
  const deduplicateMutation =
    api.admin.clerk.deduplicateOrganizations.useMutation({
      onSuccess: (data) => {
        toast.success(
          `Successfully merged ${data.mergedCount} duplicate organization${data.mergedCount > 1 ? "s" : ""} into ${data.targetOrganization.name}`,
        );
        violationsQuery.refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const handleRefreshViolations = async () => {
    setViolationsRefreshing(true);
    try {
      await violationsQuery.refetch();
      toast.success("Violations refreshed");
    } catch (error) {
      toast.error("Failed to refresh violations");
    } finally {
      setViolationsRefreshing(false);
    }
  };

  const handleDeduplicate = (organizationId: string) => {
    deduplicateMutation.mutate({ organizationId });
  };

  const handleSyncUsers = async () => {
    if (onSyncUsers) {
      await onSyncUsers();
      toast.success(i18n.en.sync.success.users);
    }
  };

  const handleSyncOrganizations = async () => {
    if (onSyncOrganizations) {
      await onSyncOrganizations();
      toast.success(i18n.en.sync.success.organizations);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      {/* Sync Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderSyncIcon className="size-5" />
            {i18n.en.sync.title}
          </CardTitle>
          <CardDescription>{i18n.en.sync.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <ClerkSyncButton
              type="users"
              loading={syncUsersLoading}
              onSync={handleSyncUsers}
            />
            <ClerkSyncButton
              type="organizations"
              loading={syncOrganizationsLoading}
              onSync={handleSyncOrganizations}
            />
          </div>
        </CardContent>
      </Card>

      {/* Violations Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangleIcon className="size-5" />
                {i18n.en.violations.title}
              </CardTitle>
              <CardDescription>
                {i18n.en.violations.description}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshViolations}
              disabled={violationsRefreshing || violationsQuery.isLoading}
              className="space-x-1"
            >
              <RefreshCwIcon
                className={cn("size-4", {
                  "animate-spin":
                    violationsRefreshing || violationsQuery.isLoading,
                })}
              />
              <span>{i18n.en.violations.refresh}</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {violationsQuery.isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : violationsQuery.error ? (
            <div className="flex items-center justify-center rounded-lg border border-destructive/20 bg-destructive/10 p-4">
              <p className="text-sm text-destructive">
                Failed to load violations: {violationsQuery.error.message}
              </p>
            </div>
          ) : !violationsQuery.data?.violations.length ? (
            <div className="flex items-center justify-center rounded-lg border border-dashed p-8">
              <div className="text-center">
                <ShieldCheckIcon className="mx-auto size-8 text-muted-foreground" />
                <p className="mt-2 text-sm font-medium">
                  {i18n.en.violations.noViolations}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {violationsQuery.data.violations.map((violation, index) => (
                <ViolationItem
                  key={`${violation.organization.id}-${index}`}
                  violation={violation}
                  onDeduplicate={handleDeduplicate}
                  deduplicating={deduplicateMutation.isPending}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
