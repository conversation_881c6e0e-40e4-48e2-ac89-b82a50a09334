"use client";

import { useMemo, useState } from "react";
import { TrashIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { GenericInvitation } from "@axa/ui/blocks";
import type { UserInvitationFormProps } from "@axa/ui/forms/users/UserInvitation";
// import InvitationList from "./Invitations";
import { createInvitationMenu, InvitationsList } from "@axa/ui/blocks";
import InvitationForm, {
  UserInvitationFormSubmitButton,
} from "@axa/ui/forms/users/UserInvitation";
import AppView from "@axa/ui/layouts/AppView";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@axa/ui/primitives/dialog";
import { useSearchPagination } from "@axa/ui/search";

import type { RouterError, RouterOutputs } from "@/api";
import type { api } from "@/api/client";

import { ErrorFallback } from "@/components/common/Error";
import ListUsers from "@/components/ListUsers";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

import ClerkManagement from "./Clerk";

const i18n = {
  en: {
    title: "Users",
    description: "Users are the individuals that you work with.",
  },
};

export function OrgSelector() {
  const form = useFormContext();

  const role = form.watch("role") as string | undefined;
  const enabled = role === "CLIENT";

  return enabled ? (
    <SelectOrganizationField description="The organization that the user belongs to" />
  ) : null;
}

export function UserInvitationForm(props: UserInvitationFormProps) {
  return (
    <InvitationForm {...props}>
      <OrgSelector />
      <UserInvitationFormSubmitButton />
    </InvitationForm>
  );
}

export type UsersQueryResult = NonNullable<RouterOutputs["user"]["getMany"]>;
export type InvitationsQueryResult = NonNullable<
  RouterOutputs["user"]["invitations"]["getMany"]
>;

export interface UsersViewProps {
  group: string;
  loading?: boolean;
  users: {
    data?: UsersQueryResult;
    error?: RouterError;
    isLoading: boolean;
  };
  invitations: {
    data?: InvitationsQueryResult;
    error?: RouterError;
    isLoading: boolean;
  };
  syncPeopleMutation: ReturnType<
    typeof api.admin.clerk.syncClerkUsers.useMutation
  >;
  syncOrganizationsMutation: ReturnType<
    typeof api.admin.clerk.syncClerkOrganizations.useMutation
  >;
  revokeUserInvitation: ReturnType<
    typeof api.user.invitations.revoke.useMutation
  >;
  sendUserInvitation: ReturnType<typeof api.user.invitations.send.useMutation>;
}

export default function Users({
  group,
  users,
  invitations,
  syncPeopleMutation,
  syncOrganizationsMutation,
  revokeUserInvitation,
  sendUserInvitation,
}: UsersViewProps) {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Use search pagination for the invitations
  const { pagination, setPagination } = useSearchPagination({
    group: "user-invitations",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });

  // Transform the API data to match GenericInvitation interface
  const transformedInvitations: GenericInvitation[] = useMemo(() => {
    return (
      invitations.data?.invites.map((invitation) => ({
        id: invitation.id,
        emailAddress: invitation.email,
        role: invitation.role,
        status: invitation.status,
        createdAt: new Date(invitation.createdAt), // Convert timestamp to Date
        metadata: {
          organizationId: invitation.organizationId,
          revoked: invitation.revoked,
        },
      })) || []
    );
  }, [invitations.data?.invites]);

  // Define filter groups for status and role
  const filterGroups = useMemo(
    () => [
      {
        id: "status",
        label: "Status",
        options: [
          { label: "All Statuses", value: null },
          { label: "Pending", value: "pending" },
          { label: "Accepted", value: "accepted" },
          { label: "Revoked", value: "revoked" },
          { label: "Expired", value: "expired" },
        ],
      },
      {
        id: "role",
        label: "Role",
        options: [
          { label: "All Roles", value: null },
          { label: "Admin", value: "admin" },
          { label: "Member", value: "member" },
          { label: "Billing", value: "billing" },
          { label: "Internal", value: "internal" },
          { label: "Client", value: "client" },
          { label: "Provider", value: "provider" },
        ],
      },
    ],
    [],
  );

  // Create the invitation menu using the utility function
  const renderInvitationMenu = useMemo(
    () =>
      createInvitationMenu<GenericInvitation>([
        {
          label: "Revoke Invitation",
          icon: TrashIcon,
          onClick: (invitation) => {
            revokeUserInvitation.mutate({ id: invitation.id });
          },
          variant: "destructive",
          confirmMessage: (invitation) =>
            `Are you sure you want to revoke the invitation for ${invitation.emailAddress}?`,
          confirmTitle: "Revoke Invitation",
          confirmLabel: "Revoke",
        },
      ]),
    [revokeUserInvitation],
  );

  const handleInviteSuccess = () => {
    setIsInviteModalOpen(false);
  };

  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2"></div>

      <div className="flex h-full flex-col gap-12">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <div className="space-y-6">
          {users.error && <ErrorFallback error={users.error} />}

          <ListUsers
            group={group}
            users={users.data}
            loading={users.isLoading}
          />
        </div>

        <section className="space-y-6">
          <InvitationsList
            invitations={transformedInvitations}
            total={invitations.data?.count || 0}
            loading={invitations.isLoading}
            error={
              invitations.error
                ? new Error(invitations.error.message)
                : undefined
            }
            title="User Invitations"
            description="Manage pending user invitations and access requests"
            emptyMessage="No pending invitations"
            filters={filterGroups}
            searchPlaceholder="Search invitations by email..."
            searchNamespace="user-invitations"
            pagination={pagination}
            onPaginationChange={setPagination}
            renderInvitationMenu={renderInvitationMenu}
            onInviteClick={() => setIsInviteModalOpen(true)}
            inviteButtonLabel="Invite User"
            gridCols={2}
          />
        </section>

        <ClerkManagement
          onSyncUsers={() => syncPeopleMutation.mutate()}
          onSyncOrganizations={async () => {
            await syncOrganizationsMutation.mutateAsync();
          }}
          syncUsersLoading={syncPeopleMutation.isPending}
          syncOrganizationsLoading={syncOrganizationsMutation.isPending}
        />

        {/* Invite User Modal */}
        <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Invite User</DialogTitle>
              <DialogDescription>
                Send an invitation to a new user to join your organization.
              </DialogDescription>
            </DialogHeader>
            <UserInvitationForm
              onSubmit={(data) => {
                sendUserInvitation.mutate(
                  {
                    email: data.email,
                    role: data.role as any,
                    organizationId: data.organizationId,
                  },
                  {
                    onSuccess: handleInviteSuccess,
                  },
                );
              }}
            />
          </DialogContent>
        </Dialog>
      </div>
    </AppView>
  );
}
