"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListProjects from "@/components/ListProjects";
import { AddProject } from "@/widgets/actions/work-order/project";

const i18n = {
  en: {
    title: "Projects",
    description:
      "Project are a collection of work orders over time. They can be used to group work orders together for a specific purpose.",
  },
};

export type ProjectsData = RouterOutputs["projects"]["getMany"]["projects"];

export interface ProjectsViewProps {
  loading?: boolean;
  projects?: Promise<RouterOutputs["projects"]["getMany"]>;
}

export const groupName = "projects";

export function ProjectsView(props: ProjectsViewProps) {
  const query = useSearchTextValue(groupName);
  const organizations = useSearchValueResult(groupName, "organization");
  const pagination = useSearchPaginationValue(groupName);

  const projects = api.projects.getMany.useQuery(
    {
      query,
      organizations: organizations ? [organizations] : undefined,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
    },
    {
      enabled: !props.loading,
      initialData: props.projects ? use(props.projects) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || projects.isLoading;

  return (
    <div className="space-y-6">
      {projects.error && <ErrorFallback error={projects.error} />}

      <ListProjects
        loading={loading}
        projects={projects.data}
        group={groupName}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        <AddProject />
      </ListProjects>
    </div>
  );
}

export default function ProjectsPage({ projects }: ProjectsViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddProject size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<ProjectsView loading />}>
          <SearchParams>
            <ProjectsView projects={projects} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
