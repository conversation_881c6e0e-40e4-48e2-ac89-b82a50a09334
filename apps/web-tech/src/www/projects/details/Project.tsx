"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@axa/ui/primitives/tabs";
import {
  SearchParams,
  useSearchDateRangeValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListLocations from "@/components/ListLocations";
import ListOrders from "@/components/ListOrders";
import { SearchLocation } from "@/widgets/actions/resources/location";
import { ProjectMenu } from "@/widgets/actions/work-order/project";

// import ProjectSchedule from "@/www/projects/details/ProjectSchedule";

const i18n = {
  en: {
    title: "Project",
    actions: {
      add: "Add Project",
    },
    tabs: {
      orders: "Orders",
      locations: "Locations",
    },
  },
  links: {
    projects: "/app/projects",
  },
};

export function ProjectActionBar(props: {
  loading?: boolean;
  project?: RouterOutputs["projects"]["get"];
  status?: string;
}) {
  return <div>{props.project?.status}</div>;
}

export const ordersGroupName = "project-orders";

export function ProjectOrders(props: {
  loading?: boolean;
  project?: RouterOutputs["projects"]["get"];
}) {
  const query = useSearchTextValue(ordersGroupName);
  const { startDate, endDate } = useSearchDateRangeValue();
  const pagination = useSearchPaginationValue(ordersGroupName);

  const orders = api.orders.getMany.useQuery(
    {
      projectId: props.project?.id,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      query,
      startDate,
      endDate,
      include: {
        organization: true,
        schedule: true,
        location: true,
      },
    },
    {
      enabled: !props.loading && !!props.project,
    },
  );
  return (
    <ListOrders
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      loading={props.loading || orders.isLoading}
      orders={orders.data}
      group={ordersGroupName}
      defaultPageSize={pagination.pageSize}
      defaultPageIndex={pagination.pageIndex}
    />
  );
}

export const locationsGroupName = "project-locations";

export function ProjectLocations(props: {
  loading?: boolean;
  project?: RouterOutputs["projects"]["get"];
}) {
  const pagination = useSearchPaginationValue(locationsGroupName);
  const locations = api.locations.getMany.useQuery(
    {
      projectId: props.project?.id,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
    },
    {
      enabled: !props.loading && !!props.project,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || locations.isLoading;

  return (
    <ListLocations
      loading={loading}
      locations={locations.data}
      group={locationsGroupName}
      defaultPageSize={pagination.pageSize}
      defaultPageIndex={pagination.pageIndex}
    >
      <SearchLocation loading={loading} />
    </ListLocations>
  );
}

export interface ProjectViewProps {
  loading?: boolean;
  projectId: string;
  project?: Promise<RouterOutputs["projects"]["get"]>;
}

export function ProjectView(props: ProjectViewProps) {
  const project = api.projects.get.useQuery(
    { id: props.projectId },
    {
      enabled: !props.loading,
      initialData: props.project ? use(props.project) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || project.isLoading;

  return (
    <AppView
      title={
        loading ? (
          <Skeleton className="h-9 w-[200px]" />
        ) : (
          (project.data?.name ?? i18n.en.title)
        )
      }
      goBackUrl={i18n.links.projects}
    >
      <div className="flex justify-end">
        <ProjectMenu project={project.data} rerouteOnDelete />
      </div>

      <div className="flex flex-col gap-4">
        {project.error && <ErrorFallback error={project.error} />}

        {loading ? (
          <Skeleton className="h-6 w-[480px] max-w-full" />
        ) : (
          <div>
            <p className="text-muted-foreground">{project.data?.description}</p>
          </div>
        )}

        <section className="flex flex-row items-center gap-2 rounded-lg border">
          <header className="flex flex-1 items-center gap-2 p-4">
            <div>
              <PreviewOrganization
                loading={loading}
                organization={project.data?.organization}
              />
            </div>
          </header>

          <footer className="border-s p-4">
            <ProjectActionBar loading={loading} project={project.data} />
          </footer>
        </section>

        {/* <section className="grid grid-cols-1 gap-6 xl:grid-cols-2">
          <div className="h-[400px] rounded-lg bg-primary/10"></div>
          <ProjectSchedule
            loading={loading}
            projectId={props.projectId}
            schedule={project.data?.schedule}
          />
        </section> */}

        <Suspense fallback={null}>
          <SearchParams>
            <Tabs defaultValue="orders">
              <TabsList>
                <TabsTrigger value="orders">{i18n.en.tabs.orders}</TabsTrigger>
                <TabsTrigger value="locations">
                  {i18n.en.tabs.locations}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="orders" className="pt-6">
                <ProjectOrders project={project.data} loading={loading} />
              </TabsContent>
              <TabsContent value="locations" className="pt-6">
                <ProjectLocations project={project.data} loading={loading} />
              </TabsContent>
            </Tabs>
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}

export default function ProjectPage(props: ProjectViewProps) {
  return (
    <Suspense
      fallback={
        <ProjectView
          key={props.projectId}
          projectId={props.projectId}
          loading
        />
      }
    >
      <ProjectView
        key={props.projectId}
        projectId={props.projectId}
        project={props.project}
      />
    </Suspense>
  );
}
