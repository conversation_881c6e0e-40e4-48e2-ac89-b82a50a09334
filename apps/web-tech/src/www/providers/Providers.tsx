"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListProviders from "@/components/ListProviders";
import { AddProvider } from "@/widgets/actions/resources/provider";

const i18n = {
  en: {
    title: "Providers",
    description:
      "Providers are people you can assign to work orders to fulfill them.",
  },
};

export type ProvidersData = RouterOutputs["providers"]["getMany"]["providers"];

export interface ProvidersViewProps {
  loading?: boolean;
  providers?: Promise<RouterOutputs["providers"]["getMany"]>;
}

export const groupName = "provider";

export function ProvidersView(props: ProvidersViewProps) {
  const query = useSearchTextValue(groupName);
  const level = useSearchFilterValue<string>("level", groupName);
  const pagination = useSearchPaginationValue(groupName);

  const providers = api.providers.getMany.useQuery(
    {
      query,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      level,
    },
    {
      initialData: props.providers ? use(props.providers) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || providers.isLoading;

  return (
    <div className="space-y-6">
      {providers.error && <ErrorFallback error={providers.error} />}

      <ListProviders
        providers={providers.data}
        loading={loading}
        group={groupName}
        defaultPageSize={pagination.pageSize}
        defaultPageIndex={pagination.pageIndex}
      >
        <AddProvider />
      </ListProviders>
    </div>
  );
}

export default function ProvidersPage({ providers }: ProvidersViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddProvider size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<ProvidersView loading />}>
          <SearchParams>
            <ProvidersView providers={providers} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
