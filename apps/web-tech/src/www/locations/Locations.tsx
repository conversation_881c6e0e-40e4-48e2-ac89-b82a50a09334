"use client";

import { Suspense, use } from "react";

import type { RouterOutputs } from "@axa/api-tech";
import type { UseDataTableProps } from "@axa/ui/tables";
import AppView from "@axa/ui/layouts/AppView";
import {
  SearchParams,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListLocations from "@/components/ListLocations";
import { AddLocation } from "@/widgets/actions/resources/location";
import MapLocations from "@/www/locations/MapLocations";

const i18n = {
  en: {
    title: "Locations",
    description: "Locations are the work order sites to designate talent.",
  },
  links: {
    location: "/app/locations/[id]",
  },
};

export type LocationsQueryResult = RouterOutputs["locations"]["getMany"];
export type LocationsType = LocationsQueryResult["locations"];
export type LocationType = LocationsType[number];
export type TableProps = UseDataTableProps<LocationType, LocationsType>;

export interface LocationsViewProps {
  locations?: Promise<LocationsQueryResult>;
  loading?: boolean;
  organizationId?: string;
  organizations?: string[];
  address?: string;
  pageSize?: number;
  pageIndex?: number;
}

export const groupName = "location";

export function LocationsView(props: LocationsViewProps) {
  const query = useSearchTextValue(groupName);
  const organizations = useSearchValueResult(groupName, "organization");
  const pagination = useSearchPaginationValue(groupName);

  const locations = api.locations.getMany.useQuery(
    {
      query,
      organizations: organizations ? [organizations] : undefined,
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageIndex,
    },
    {
      enabled: !props.loading,
      initialData: props.locations ? use(props.locations) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || locations.isLoading;

  return (
    <div className="flex h-full flex-col gap-8 md:gap-12">
      <div>
        <MapLocations
          loading={loading}
          locations={locations.data?.locations ?? []}
        />
      </div>

      {locations.error && <ErrorFallback error={locations.error} />}

      <div className="flex-1">
        <ListLocations
          loading={loading}
          locations={locations.data}
          group={groupName}
          defaultPageSize={pagination.pageSize}
          defaultPageIndex={pagination.pageIndex}
        >
          <AddLocation />
        </ListLocations>
      </div>
    </div>
  );
}

export default function LocationsPage({ locations }: LocationsViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddLocation size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<LocationsView loading />}>
          <SearchParams>
            <LocationsView locations={locations} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
