"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";
import { format } from "date-fns";

import type { RouterOutputs } from "@axa/api-tech";
import type { Address, Location, WorkOrderPriority } from "@axa/database-tech";
import type { ActionContext } from "@axa/ui/tables/table";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import LocationAddress from "@axa/ui/common/LocationAddress";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { displayDateForTimeZone } from "@axa/ui/lib/dates";
import { SearchDateRange, SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { OrderPriorityIcon } from "@/components/symbols/OrderPriority";
import OrderStatus from "@/components/symbols/OrderStatus";
import { useUser } from "@/contexts/User";
import { SearchOrderType } from "@/widgets/actions/search";
import { WorkOrderMenu } from "@/widgets/actions/work-order/work-order";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no work orders",
    selection: "Selection",
    pendingAssign: "Pending Assignment",
    pendingSchedule: "Pending Schedule",
    pendingLocation: "Pending Location",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search orders...",
    },
    headers: {
      id: "ID",
      summary: "Summary",
      type: "Type",
      category: "Category",
      location: "Location",
      provider: "Technician",
      schedule: "Service Date",
      created: "Created At",
      priority: "Priority",
      status: "Status",
      organization: "Organization",
    },
    filters: {
      priority: "Priority",
      options: {
        ALL: "All Priorities",
        LOW: "Low",
        MEDIUM: "Normal",
        HIGH: "Urgent",
      },
    },
  },
  links: {
    order: "/app/orders/[id]",
    locations: "/app/locations/[id]",
  },
};

export const groupName = "orders";

export type WorkOrdersQueryResult = RouterOutputs["orders"]["getMany"];
export type WorkOrdersType = WorkOrdersQueryResult["orders"];
export type WorkOrderType = WorkOrdersType[number];

interface ListOrdersProps extends PropsWithChildren {
  loading?: boolean;
  orders?: WorkOrdersQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

export default function ListOrders({
  group = groupName,
  loading: loadingProp = false,
  orders,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListOrdersProps) {
  const user = useUser();

  const loading = user.loading || loadingProp;

  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!orders) return undefined;
    return {
      items: orders.orders,
      total: orders.total,
    };
  }, [orders]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={useMemo(
        () => ({
          groupName: group,
          enableSelection: true,
          i18n: i18n.en,
        }),
        [group],
      )}
      filters={useMemo(
        () => [
          {
            id: "priority",
            label: i18n.en.filters.priority,
            options: [
              { value: null, label: i18n.en.filters.options.ALL },
              { value: "LOW", label: i18n.en.filters.options.LOW },
              { value: "MEDIUM", label: i18n.en.filters.options.MEDIUM },
              { value: "HIGH", label: i18n.en.filters.options.HIGH },
            ] satisfies { value: WorkOrderPriority | null; label: string }[],
          },
        ],
        [],
      )}
      header={
        <>
          <SearchText group={group} placeholder={i18n.en.actions.search} />
          <SearchOrganizations
            group={group}
            loading={loading}
            size="sm"
            variant="outline"
            className="w-fit min-w-48"
            clearable
          />
          <SearchDateRange
            size="sm"
            group={group}
            loading={loading}
            className="w-fit"
          />
          <SearchOrderType />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<WorkOrderType>(
            [
              "id",
              "number",
              "customId",
              "summary",
              "scope",
              "type",
              "category",
              "status",
              "priority",
              "createdAt",
              "organization",
              "location",
              "providers",
            ],
            {
              filename: "work_orders_export.csv",
              label: "Export Selected Orders",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name ?? "";
                  }
                  return "";
                },
                location: (location) => {
                  if (
                    location &&
                    typeof location === "object" &&
                    "name" in location
                  ) {
                    return location.name ?? "";
                  }
                  return "";
                },
                providers: (providers) => {
                  if (Array.isArray(providers) && providers.length > 0) {
                    return providers
                      .map((p) => {
                        if ("firstName" in p && "lastName" in p) {
                          return `${p.firstName} ${p.lastName}`;
                        }
                        return p.id;
                      })
                      .join(", ");
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Order Actions",
            render: (context: ActionContext<WorkOrderType>) => {
              if (context.type === "row") {
                return <WorkOrderMenu order={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => {
                return (
                  <div className="flex w-[100px] items-center">
                    <OrderStatus status={row.getValue("status")} />
                  </div>
                );
              },
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              accessorKey: "priority",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.priority}
                />
              ),
              cell: ({ row }) => {
                return (
                  <div className="flex w-full items-center ps-4">
                    <OrderPriorityIcon priority={row.getValue("priority")} />
                  </div>
                );
              },
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization
                  size="sm"
                  organization={{
                    id: row.original.organization.id,
                    name: row.original.organization.name,
                    avatar: row.original.organization.avatar,
                  }}
                />
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              accessorKey: "id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.id}
                />
              ),
              cell: ({ row }) => (
                <div className="max-w-[100px] truncate text-nowrap">
                  {row.original.customId ? (
                    row.original.customUrl ? (
                      <a
                        rel="noopener noreferrer"
                        target="_blank"
                        href={row.original.customUrl}
                        className="font-semibold hover:text-primary"
                      >
                        {row.original.customId}
                      </a>
                    ) : (
                      row.original.customId
                    )
                  ) : (
                    `#${row.original.number}`
                  )}
                </div>
              ),
              enableSorting: false,
              enableHiding: false,
            },
            {
              accessorKey: "summary",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.summary}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full min-w-[300px] max-w-[400px] flex-col truncate">
                  <p className="w-full max-w-[400px] truncate">
                    <Link
                      href={i18n.links.order.replace("[id]", row.original.id)}
                      className="font-semibold hover:text-primary"
                    >
                      <span className="w-full max-w-[400px] truncate">
                        {row.getValue("summary")}
                      </span>
                    </Link>
                  </p>

                  <p className="w-full max-w-[400px] truncate text-sm text-muted-foreground">
                    <span className="w-full max-w-[400px] truncate">
                      {row.original.scope}
                    </span>
                  </p>
                </div>
              ),
            },
            {
              id: "provider",
              accessorKey: "providers",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => {
                const [provider] = row.original.providers;

                return (
                  <div className="flex w-fit flex-col">
                    <div className="w-full font-semibold hover:text-primary">
                      {provider &&
                      "firstName" in provider &&
                      "lastName" in provider ? (
                        <div>
                          {user.isInternal ? (
                            <Link
                              href={`/app/providers/${provider.id}`}
                              className="font-semibold transition-colors hover:text-primary"
                            >
                              <ContactName
                                showCopyButton={false}
                                name={[
                                  provider.firstName,
                                  provider.lastName,
                                ].join(" ")}
                              />
                            </Link>
                          ) : (
                            <ContactName
                              showCopyButton={false}
                              name={[
                                provider.firstName,
                                provider.lastName,
                              ].join(" ")}
                            />
                          )}

                          <ContactPhone
                            phone={provider.phone}
                            className="text-sm text-muted-foreground"
                          />
                        </div>
                      ) : (
                        <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                          {i18n.en.pendingAssign}
                        </div>
                      )}
                    </div>
                  </div>
                );
              },
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "location",
              accessorKey: "location.address.formatted",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.location}
                />
              ),
              cell: ({ row }) => {
                const location = row.original.location as
                  | (Location & {
                      address: Address;
                    })
                  | null;

                return (
                  <div className="flex w-fit flex-col">
                    <div className="w-full font-semibold hover:text-primary">
                      {location ? (
                        <div className="w-fit">
                          <Link
                            href={i18n.links.locations.replace(
                              "[id]",
                              location.id,
                            )}
                            className="text-nowrap font-semibold transition-colors hover:text-primary"
                          >
                            {location.name}
                          </Link>
                          <LocationAddress
                            truncate
                            address={location.address.formatted}
                            className="max-w-[500px] truncate text-sm text-muted-foreground"
                          />
                        </div>
                      ) : (
                        <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                          {i18n.en.pendingLocation}
                        </div>
                      )}
                    </div>
                  </div>
                );
              },
            },
            {
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <span className="text-nowrap">{row.getValue("type")}</span>
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              accessorKey: "category",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.category}
                />
              ),
              cell: ({ row }) => (
                <span className="text-nowrap">{row.getValue("category")}</span>
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "schedule",
              accessorKey: "schedule.shifts",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.schedule}
                />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const [shift] = row.getValue("schedule") as
                  | {
                      date?: Date;
                      startDate?: Date;
                      endDate?: Date;
                      type: "SERVICE_WINDOW" | "SERVICE_DATE";
                      timeZone?: string;
                    }[]
                  | [];

                const endDate =
                  shift?.type === "SERVICE_WINDOW" ? shift.endDate : null;
                const startDate =
                  shift?.type === "SERVICE_WINDOW"
                    ? shift.startDate
                    : shift?.date;

                return shift ? (
                  <div className="flex w-fit flex-col items-start">
                    <span className="text-nowrap">
                      {format(
                        displayDateForTimeZone(
                          startDate ?? new Date(),
                          shift.timeZone ?? "local",
                        ),
                        "PPP",
                      )}
                    </span>
                    {endDate && (
                      <>
                        <span className="w-full text-center text-muted-foreground">
                          to
                        </span>
                        <span className="text-nowrap">
                          {format(
                            displayDateForTimeZone(
                              endDate,
                              shift.timeZone ?? "local",
                            ),
                            "PPP",
                          )}
                        </span>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                    {i18n.en.pendingSchedule}
                  </div>
                );
              },
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              accessorKey: "createdAt",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.created}
                />
              ),
              cell: ({ row }) => (
                <span className="text-nowrap">
                  {format(row.getValue("createdAt"), "PPP ' at ' hh:mm a")}
                </span>
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
          ] as ColumnDef<WorkOrderType, WorkOrderType[]>[],
        [user.isInternal],
      )}
    >
      {children}
    </Table>
  );
}
