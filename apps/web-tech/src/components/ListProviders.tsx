"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { ProviderMenu } from "@/widgets/actions/resources/provider";

const i18n = {
  en: {
    noData: "There are no providers yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search providers...",
    },
    headers: {
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      level: "Level",
    },
    filters: {
      level: "Level",
      options: {
        ALL: "All Levels",
        LEVEL_1: "Level 1",
        LEVEL_2: "Level 2",
        LEVEL_3: "Level 3",
      },
    },
  },
  links: {
    providers: "/app/providers/[id]",
  },
};

export const groupName = "provider";

export type ProvidersQueryResult = RouterOutputs["providers"]["getMany"];
export type ProvidersType = ProvidersQueryResult["providers"];
export type ProviderType = ProvidersType[number];

interface ListProvidersProps extends PropsWithChildren {
  loading?: boolean;
  providers?: ProvidersQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

// Filters configuration
const filters = [
  {
    id: "level",
    label: i18n.en.filters.level,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "1", label: i18n.en.filters.options.LEVEL_1 },
      { value: "2", label: i18n.en.filters.options.LEVEL_2 },
      { value: "3", label: i18n.en.filters.options.LEVEL_3 },
    ] satisfies { value: string | null; label: string }[],
  },
];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListProviders({
  group = groupName,
  loading = false,
  providers,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListProvidersProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!providers) return undefined;
    return {
      items: providers.providers,
      total: providers.total,
    };
  }, [providers]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ProviderType>(
            ["id", "firstName", "lastName", "email", "phone", "level"],
            {
              filename: "providers_export.csv",
              label: "Export Selected Providers",
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Provider Actions",
            render: (context: ActionContext<ProviderType>) => {
              if (context.type === "row") {
                return <ProviderMenu provider={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      src={row.original.avatar ?? ""}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.providers.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
            },
            {
              id: "level",
              accessorKey: "level",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.level}
                />
              ),
              cell: ({ row }) => (
                <Badge className="font-semibold">{row.getValue("level")}</Badge>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.getValue(id));
              },
            },
          ] as ColumnDef<ProviderType, ProviderType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
