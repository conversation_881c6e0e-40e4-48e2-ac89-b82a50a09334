"use client";

import type { PropsWith<PERSON>hildren, ReactNode } from "react";

import { useMemo } from "react";
import { usePathname } from "next/navigation";
import {
  Clock,
  CogIcon,
  FilesIcon,
  FolderKanbanIcon,
  GaugeIcon,
  Globe,
  HardHatIcon,
  Landmark,
  LayoutDashboard,
  LineChart,
  MapPinned,
  NotebookTabsIcon,
  ScrollText,
  StampIcon,
  UserCircle2Icon,
  UsersRoundIcon,
  WaypointsIcon,
} from "lucide-react";

import Logo, { Tech } from "@axa/ui/brand/Logo";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import ApplicationLayout from "@axa/ui/layouts/AppLayout";
import { Badge } from "@axa/ui/primitives/badge";
import { ThemeToggle } from "@axa/ui/primitives/theme";

import { useNotifications } from "@/contexts/Notifications";
import { UserContext, useUser } from "@/contexts/User";
import ClientSwitcher from "@/widgets/ClientSwitcher";
import MembershipSwitcher from "@/widgets/MembershipSwitcher";
import UserManager from "@/widgets/UserManager";

const i18n = {
  en: {
    company: "AXA Tech",
    preview: "Preview",
    links: {
      dashboard: "Dashboard",
      orders: "Work Orders",
      people: "People",
      locations: "Locations",
      projects: "Projects",
      templates: "Templates",
      organizations: "Organizations",
      billing: "Billing",
      invoices: "Invoices",
      analytics: "Analytics",
      settings: "Settings",
      profile: "Profile",
      finances: "Finances",
      providers: "Providers",
      timeSheets: "Time Sheets",
      documents: "Documents",
      system: "System",
      users: "Users",
      operations: "Operations",
    },
  },
  links: {
    dashboard: "/app",
    profile: "/app/profile",
    settings: "/app/settings",
    organizations: "/app/organizations",
    billing: "/app/billing",
    orders: "/app/orders",
    templates: "/app/templates",
    projects: "/app/projects",
    people: "/app/people",
    locations: "/app/locations",
    documents: "/app/documents",
    providers: "/app/providers",
    finances: "/app/finances",
    invoices: "/app/finances/invoices",
    timeSheets: "/app/finances/time-sheets",
    users: "/app/admin/users",
    system: "/app/admin/system",
    operations: "/app/admin/operations",
    analytics: "/app/admin/analytics",
  },
};

const links = {
  dashboard: {
    id: "dashboard",
    tags: ["ALERT"],
    href: i18n.links.dashboard,
    label: i18n.en.links.dashboard,
    icon: LayoutDashboard,
    badge: null,
  },
  organization: {
    id: "organization",
    tags: [],
    href: i18n.links.organizations,
    label: i18n.en.links.organizations,
    icon: Globe,
    badge: null,
  },
  billing: {
    // client only
    id: "billing",
    tags: [],
    href: i18n.links.billing,
    label: i18n.en.links.billing,
    icon: Landmark,
    badge: null,
  },
  order: {
    id: "order",
    tags: ["ORDER", "MESSAGE"],
    href: i18n.links.orders,
    label: i18n.en.links.orders,
    icon: ScrollText,
    badge: null,
  },
  template: {
    id: "template",
    tags: [],
    href: i18n.links.templates,
    label: i18n.en.links.templates,
    icon: StampIcon,
    badge: null,
  },
  project: {
    id: "project",
    tags: [],
    href: i18n.links.projects,
    label: i18n.en.links.projects,
    icon: FolderKanbanIcon,
    badge: null,
  },
  location: {
    id: "location",
    tags: [],
    href: i18n.links.locations,
    label: i18n.en.links.locations,
    icon: MapPinned,
    badge: null,
  },
  people: {
    id: "people",
    tags: [],
    href: i18n.links.people,
    label: i18n.en.links.people,
    icon: UsersRoundIcon,
    badge: null,
  },
  document: {
    id: "document",
    tags: [],
    href: i18n.links.documents,
    label: i18n.en.links.documents,
    icon: FilesIcon,
    badge: null,
  },
  // internal
  provider: {
    id: "provider",
    tags: [],
    href: i18n.links.providers,
    label: i18n.en.links.providers,
    icon: HardHatIcon,
    badge: null,
  },
  finances: {
    id: "finances",
    tags: [],
    href: i18n.links.finances,
    label: i18n.en.links.finances,
    icon: LineChart,
    badge: null,
  },
  timeSheet: {
    id: "timeSheet",
    tags: [],
    href: i18n.links.timeSheets,
    label: i18n.en.links.timeSheets,
    icon: Clock,
    badge: null,
  },
  invoice: {
    id: "invoice",
    tags: [],
    href: i18n.links.invoices,
    label: i18n.en.links.invoices,
    icon: NotebookTabsIcon,
    badge: null,
  },
  operations: {
    id: "operations",
    tags: [],
    href: i18n.links.operations,
    label: i18n.en.links.operations,
    icon: WaypointsIcon,
    badge: (
      <Badge className="ml-auto flex shrink-0 items-center justify-center">
        {i18n.en.preview}
      </Badge>
    ),
  },
  analytics: {
    id: "analytics",
    tags: [],
    href: i18n.links.analytics,
    label: i18n.en.links.analytics,
    icon: GaugeIcon,
    badge: (
      <Badge className="ml-auto flex shrink-0 items-center justify-center">
        {i18n.en.preview}
      </Badge>
    ),
  },
  system: {
    id: "system",
    tags: [],
    href: i18n.links.system,
    label: i18n.en.links.system,
    icon: CogIcon,
    badge: null,
  },
  users: {
    id: "users",
    tags: [],
    href: i18n.links.users,
    label: i18n.en.links.users,
    icon: UserCircle2Icon,
    badge: null,
  },
};

export function useLayoutData() {
  const {
    loading,
    organization,
    isInternal,
    isBilling,
    isAdmin,
    avatar,
    firstName,
    lastName,
    email,
  } = useUser();
  const { notifications } = useNotifications();
  const pathname: string | null = usePathname();

  return useMemo(() => {
    const navLinks = [
      [
        links.dashboard,
        links.organization,
        isInternal ? links.provider : links.billing,
      ],
      [
        links.order,
        links.template,
        links.location,
        links.people,
        links.document,
      ],
      isBilling ? [links.billing, links.invoice, links.timeSheet] : [],
      isAdmin ? [links.users, links.system] : [],
    ].map((linkGroup) =>
      linkGroup.map((link) => {
        const isActive =
          link.id === "dashboard"
            ? pathname === link.href
            : (pathname ?? "").startsWith(link.href);
        const linkNotifications = notifications.filter((notification) =>
          link.tags.includes(notification.type),
        );
        return {
          ...link,
          isActive,
          badge:
            linkNotifications.length > 0 ? (
              <Badge className="ml-auto flex size-6 shrink-0 items-center justify-center rounded-full">
                {linkNotifications.length}
              </Badge>
            ) : (
              link.badge
            ),
        };
      }),
    );

    return {
      navLinks,
      loading: loading,
      organization: {
        id: organization?.id ?? "",
        name: organization?.name ?? "",
        avatar: organization?.avatar,
      },
      user: {
        avatar: avatar ?? "",
        firstName: firstName ?? "",
        lastName: lastName ?? "",
        emailAddress: email.emailAddress ?? "",
      },
    };
  }, [
    pathname,
    loading,
    organization,
    isAdmin,
    isBilling,
    isInternal,
    avatar,
    firstName,
    lastName,
    email,
    notifications,
  ]);
}

export function AppShell({
  children,
  manager,
}: PropsWithChildren<{
  manager?: ReactNode;
}>) {
  const { loading, navLinks, user, organization } = useLayoutData();

  return (
    <ApplicationLayout
      navLinks={navLinks}
      loading={loading}
      brand={
        <div className="me-10 items-center justify-center first-letter:flex">
          <Logo
            className="aspect-auto h-10 w-fit overflow-visible"
            textColor="currentColor"
          >
            <Tech textColor="currentColor" />
          </Logo>
        </div>
      }
    >
      <div className="flex w-full justify-stretch gap-2">
        {manager ?? (
          <PreviewOrganization
            size="sm"
            shadow={false}
            link={organization.id !== "AXA"}
            loading={loading}
            organization={organization}
          />
        )}
      </div>
      <div className="flex w-full items-center gap-4">
        <div className="w-full flex-1">{/* <ClientSwitcher /> */}</div>

        <ThemeToggle variant="ghost" />
        <UserManager loading={loading} user={user} />
      </div>
      {children}
    </ApplicationLayout>
  );
}

export default function AppLayout({ children }: PropsWithChildren) {
  const user = useUser();

  const nextContext = useMemo(() => {
    if (user.isClient || (user.isInternal && user.organization?.id === "AXA")) {
      return user;
    }

    return {
      ...user,
      role: "CLIENT",
      isInternal: false,
      isBilling: false,
      isAdmin: false,
      isClient: true,
    };
  }, [user]);

  if (!user.isInternal) {
    return <AppShell manager={<MembershipSwitcher />}>{children}</AppShell>;
  }

  return (
    <UserContext.Provider value={nextContext}>
      {/* <AppShell manager={<MembershipSwitcher />}>{children}</AppShell> */}
      <AppShell manager={<ClientSwitcher />}>{children}</AppShell>
    </UserContext.Provider>
  );
}
