"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import DocumentViewer from "@axa/ui/common/DocumentViewer";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { DocumentMenu } from "@/widgets/actions/resources/document";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no documents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search documents...",
    },
    headers: {
      name: "Name",
      type: "Type",
      phone: "Phone Number",
      organization: "Organization",
    },
  },
  links: {
    documents: "/app/documents/[id]",
  },
};

export const groupName = "document";

export type DocumentsQueryResult = RouterOutputs["documents"]["getMany"];
export type DocumentsType = DocumentsQueryResult["documents"];
export type DocumentType = DocumentsType[number];

interface ListDocumentsProps extends PropsWithChildren {
  loading?: boolean;
  documents?: DocumentsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListDocuments({
  group = groupName,
  loading = false,
  documents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListDocumentsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!documents) return undefined;
    return {
      items: documents.documents,
      total: documents.total,
    };
  }, [documents]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<DocumentType>(
            ["id", "name", "type", "organization"],
            {
              filename: "documents_export.csv",
              label: "Export Selected Documents",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Document Actions",
            render: (context: ActionContext<DocumentType>) => {
              if (context.type === "row") {
                return <DocumentMenu document={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "preview",
              accessorKey: "preview",
              header: () => null,
              cell: ({ row }) => <DocumentViewer document={row.original} />,
            },
            {
              id: "name",
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <Link
                  href={`/app/documents/${row.original.id}`}
                  className="h-fit"
                >
                  <p className="font-semibold">{row.getValue("name")}</p>
                </Link>
              ),
              enableHiding: false,
            },
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => <p>{row.getValue("type")}</p>,
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization
                  size="sm"
                  organization={row.original.organization}
                />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<DocumentType, DocumentType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
