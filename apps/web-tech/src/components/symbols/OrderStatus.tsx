import { WorkOrderStatus } from "@axa/database-tech";
import { cn } from "@axa/ui/lib";
import { Badge } from "@axa/ui/primitives/badge";

const i18n = {
  en: {
    status: {
      [WorkOrderStatus.PENDING]: "Pending",
      [WorkOrderStatus.PUBLISHED]: "Reviewed",
      [WorkOrderStatus.SCHEDULED]: "Scheduled",
      [WorkOrderStatus.ASSIGNED]: "Assigned",
      [WorkOrderStatus.ACTIVE]: "In Progress",
      [WorkOrderStatus.COMPLETED]: "Completed",
      [WorkOrderStatus.APPROVED]: "Invoiced",
      [WorkOrderStatus.CANCELLED]: "Canceled",
      [WorkOrderStatus.DRAFT]: "Draft",
    } satisfies Record<WorkOrderStatus, string>,
  },
};

export interface OrderStatusProps {
  status: WorkOrderStatus;
  className?: string;
}

export default function OrderStatusBadge({
  status = WorkOrderStatus.ACTIVE,
  className = "",
}: OrderStatusProps) {
  return (
    <Badge
      variant="outline"
      className={cn(className, "text-muted-foreground", {
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300":
          status === WorkOrderStatus.PENDING,
        "bg-fuchsia-100 text-fuchsia-800 dark:bg-fuchsia-900 dark:text-fuchsia-300":
          status === WorkOrderStatus.PUBLISHED,
        "bg-blue-100 text-blue-500 dark:bg-blue-900 dark:text-blue-400":
          status === WorkOrderStatus.ASSIGNED,
        "bg-cyan-100 text-cyan-500 dark:bg-cyan-900 dark:text-cyan-400":
          status === WorkOrderStatus.SCHEDULED,
        "bg-teal-100 text-teal-500 dark:bg-teal-900 dark:text-teal-400":
          status === WorkOrderStatus.ACTIVE,
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-400":
          status === WorkOrderStatus.COMPLETED,
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-400":
          status === WorkOrderStatus.APPROVED,
        "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300":
          status === WorkOrderStatus.CANCELLED,
      })}
    >
      {i18n.en.status[status]}
    </Badge>
  );
}
