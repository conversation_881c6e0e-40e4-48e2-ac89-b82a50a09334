import { Badge } from "@axa/ui/primitives/badge";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { OrganizationsType } from "@/components/ListOrganizations";

export type OrganizationType = OrganizationsType[number];

export function OrganizationTypeBadge({
  loading,
  type,
}: {
  loading?: boolean;
  type: OrganizationType["type"];
}) {
  if (loading) {
    return <Skeleton className="h-6 w-16" />;
  }

  return (
    <Badge variant="outline">
      {
        {
          CLIENT: "Client",
          ACCOUNT: "Account",
          INTERNAL: "Internal",
        }[type]
      }
    </Badge>
  );
}
