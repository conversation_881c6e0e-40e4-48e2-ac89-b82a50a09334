"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import type { RouterOutputs } from "@/api";

import { OrganizationTypeBadge } from "@/components/symbols/OrganizationType";
import { OrganizationMenu } from "@/widgets/actions/organizations/organization";

const i18n = {
  en: {
    noData: "There are no organizations yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search organizations...",
      add: "Add Organization",
    },
    headers: {
      name: "Name",
      type: "Type",
      status: "Status",
    },
    filters: {
      type: "Type",
      options: {
        ALL: "All Types",
        CLIENT: "Client",
        ACCOUNT: "Account",
        INTERNAL: "Internal",
      },
    },
  },
  links: {
    organizations: "/app/organizations/[id]",
  },
};

export const groupName = "organizations";

export type OrganizationsQueryResult =
  RouterOutputs["organizations"]["getMany"];
export type OrganizationsType = OrganizationsQueryResult["organizations"];
export type OrganizationType = OrganizationsType[number];

interface ListOrganizationsProps extends PropsWithChildren {
  loading?: boolean;
  organizations?: OrganizationsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

// Filters configuration
const filters = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "CLIENT", label: i18n.en.filters.options.CLIENT },
      { value: "ACCOUNT", label: i18n.en.filters.options.ACCOUNT },
      { value: "INTERNAL", label: i18n.en.filters.options.INTERNAL },
    ] satisfies { value: string | null; label: string }[],
  },
];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListOrganizations({
  group = groupName,
  loading = false,
  organizations,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListOrganizationsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!organizations) return undefined;
    return {
      items: organizations.organizations,
      total: organizations.total,
    };
  }, [organizations]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<OrganizationType>(["id", "name", "type"], {
            filename: "organizations_export.csv",
            label: "Export Selected Organizations",
          }),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Organization Actions",
            render: (context: ActionContext<OrganizationType>) => {
              if (context.type === "row") {
                return (
                  <OrganizationMenu
                    organization={context.row}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={i18n.links.organizations.replace(
                      "[id]",
                      row.original.id,
                    )}
                    className="w-full text-nowrap font-semibold hover:text-primary"
                  >
                    <PreviewOrganization organization={row.original} />
                  </Link>
                </div>
              ),
              enableHiding: false,
            },
            {
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <OrganizationTypeBadge type={row.original.type} />
                </div>
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.getValue(id));
              },
            },
          ] as ColumnDef<OrganizationType, OrganizationType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
