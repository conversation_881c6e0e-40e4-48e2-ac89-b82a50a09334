"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { format } from "date-fns";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import StatementTypeBadge from "@/components/symbols/StatementType";

const i18n = {
  en: {
    noData: "There are no statements yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search statements...",
      add: "Add Statement",
    },
    noStatements: "There are no statements",
    noDescription: "No description",
    headers: {
      actor: "Created By",
      date: "Date",
      type: "Type",
      createdBy: "Created By",
      amount: "Amount",
      note: "Notes",
    },
    filters: {
      type: "Type",
      options: {
        ALL: "All",
        PAYMENT: "Payment",
        FUNDING: "Funding",
        FEE: "Fee",
      },
    },
  },
  links: {
    statements: "/app/statements/[id]",
  },
};

export const groupName = "statements";

export type StatementsQueryResult = RouterOutputs["statements"]["getMany"];
export type StatementsType = StatementsQueryResult["statements"];
export type StatementType = StatementsType[number];

interface ListStatementsProps extends PropsWithChildren {
  loading?: boolean;
  statements?: StatementsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

// Filters configuration
const filters = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "PAYMENT", label: i18n.en.filters.options.PAYMENT },
      { value: "FUNDING", label: i18n.en.filters.options.FUNDING },
      { value: "FEE", label: i18n.en.filters.options.FEE },
    ] satisfies { value: string | null; label: string }[],
  },
];

export default function ListStatements({
  group = groupName,
  loading = false,
  statements,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListStatementsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!statements) return undefined;
    return {
      items: statements.statements,
      total: statements.total,
    };
  }, [statements]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={useMemo(
        () => ({
          groupName: group,
          enableSelection: true,
          i18n: i18n.en,
        }),
        [group],
      )}
      filters={filters}
      header={null}
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<StatementType>(
            ["type", "balance", "notes", "period", "actor"],
            {
              filename: "statements_export.csv",
              label: "Export Selected Statements",
              resolvers: {
                actor: (actor) => {
                  if (actor && typeof actor === "object") {
                    const a = actor as {
                      firstName?: string;
                      lastName?: string;
                    };
                    return (
                      `${a.firstName || ""} ${a.lastName || ""}`.trim() ||
                      "SYSTEM"
                    );
                  }
                  return "SYSTEM";
                },
                period: (period) => {
                  if (period instanceof Date) {
                    return format(period, "LLLL dd, yyyy");
                  }
                  return "";
                },
              },
            },
          ),
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "type",
              accessorKey: "type",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <StatementTypeBadge type={row.getValue("type")} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.getValue(id));
              },
            },
            {
              id: "amount",
              accessorKey: "balance",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.amount}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("amount")} />,
            },
            {
              id: "note",
              accessorKey: "notes",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.note}
                />
              ),
              cell: ({ row }) => <p children={row.getValue("note")} />,
            },
            {
              id: "date",
              accessorKey: "period",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.date}
                />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const period = row.getValue("date") as Date;
                const date = new Date(
                  period.getTime() + period.getTimezoneOffset() * 60000,
                );

                return (
                  <p className="text-nowrap">{format(date, "LLLL dd, yyyy")}</p>
                );
              },
            },
            {
              id: "actor",
              accessorKey: "actor",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.actor}
                />
              ),
              cell: ({ row }) => {
                const actor = row.original.actor ?? {
                  firstName: "SYSTEM",
                  lastName: "",
                };

                return <p>{`${actor.firstName} ${actor.lastName}`.trim()}</p>;
              },
            },
          ] as ColumnDef<StatementType, StatementType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
