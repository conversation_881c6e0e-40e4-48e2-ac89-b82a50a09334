"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import LocationAddress from "@axa/ui/common/LocationAddress";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import TimeZone from "@axa/ui/shared/TimeZone";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { LocationMenu } from "@/widgets/actions/resources/location";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no locations yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search locations...",
    },
    headers: {
      address: "Address",
      timeZone: "Time Zone",
      organization: "Organization",
    },
  },
  links: {
    locations: "/app/locations/[id]",
  },
};

export const groupName = "location";

export type LocationsQueryResult = RouterOutputs["locations"]["getMany"];
export type LocationsType = LocationsQueryResult["locations"];
export type LocationType = LocationsType[number];

interface ListLocationsProps extends PropsWithChildren {
  loading?: boolean;
  locations?: LocationsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListLocations({
  group = groupName,
  loading = false,
  locations,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListLocationsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!locations) return undefined;
    return {
      items: locations.locations,
      total: locations.total,
    };
  }, [locations]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<LocationType>(
            ["id", "name", "address", "timeZone", "organization"],
            {
              filename: "locations_export.csv",
              label: "Export Selected Locations",
              resolvers: {
                address: (address) => {
                  if (
                    address &&
                    typeof address === "object" &&
                    "formatted" in address
                  ) {
                    return address.formatted || "";
                  }
                  return "";
                },
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Location Actions",
            render: (context: ActionContext<LocationType>) => {
              if (context.type === "row") {
                return <LocationMenu location={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "address",
              accessorKey: "address.formatted",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.address}
                />
              ),
              cell: ({ row }) => (
                <div className="w-fit">
                  <Link
                    href={i18n.links.locations.replace("[id]", row.original.id)}
                    className="font-semibold transition-colors hover:text-primary"
                  >
                    {row.original.name}
                  </Link>
                  <LocationAddress
                    address={row.getValue("address")}
                    className="text-sm text-muted-foreground"
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "timeZone",
              accessorKey: "timeZone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.timeZone}
                />
              ),
              cell: ({ row }) => (
                <TimeZone
                  timeZone={
                    /* eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion, @typescript-eslint/no-unnecessary-condition */
                    ((row.getValue("timeZone") ?? "") as string)
                      .replace("_", " ")
                      .replace("/", " | ")
                  }
                />
              ),
              filterFn: (row, id, value: string) =>
                value.includes(row.getValue(id)),
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization
                  organization={row.original.organization}
                  className="text-sm text-muted-foreground"
                />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization.id);
              },
            },
          ] as ColumnDef<LocationType, LocationType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
