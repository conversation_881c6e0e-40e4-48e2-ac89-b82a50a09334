"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { PersonMenu } from "@/widgets/actions/resources/people";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no people yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search people...",
    },
    headers: {
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
  },
  links: {
    people: "/app/people/[id]",
  },
};

export const groupName = "person";

export type PeopleQueryResult = RouterOutputs["people"]["getMany"];
export type PeopleType = PeopleQueryResult["people"];
export type PersonType = PeopleType[number];

interface ListPeopleProps extends PropsWithChildren {
  loading?: boolean;
  people?: PeopleQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListPeople({
  group = groupName,
  loading = false,
  people,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListPeopleProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!people) return undefined;
    return {
      items: people.people,
      total: people.total,
    };
  }, [people]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<PersonType>(
            ["id", "firstName", "lastName", "email", "phone", "organization"],
            {
              filename: "people_export.csv",
              label: "Export Selected People",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Person Actions",
            render: (context: ActionContext<PersonType>) => {
              if (context.type === "row") {
                return (
                  <PersonMenu
                    person={context.row}
                    disabled={context.row.isUser}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      // @ts-expect-error - Next.js doesn't support null values in types yet recommended by the team
                      src={row.original.avatar ?? null}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.people.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization
                  size="sm"
                  organization={row.original.organization}
                  className="text-sm text-muted-foreground"
                />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization.id);
              },
            },
          ] as ColumnDef<PersonType, PersonType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
