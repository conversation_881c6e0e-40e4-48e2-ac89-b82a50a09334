"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { TemplateMenu } from "@/widgets/actions/work-order/template";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no templates yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search templates...",
      add: "Add Template",
    },
    noTemplates: "There are no templates",
    noDescription: "No description",
    headers: {
      name: "Name",
      organization: "Organization",
    },
  },
  links: {
    templates: "/app/templates/[id]",
  },
};

export const groupName = "template";

export type TemplatesQueryResult = RouterOutputs["templates"]["getMany"];
export type TemplatesType = TemplatesQueryResult["templates"];
export type TemplateType = TemplatesType[number];

interface ListTemplatesProps extends PropsWithChildren {
  loading?: boolean;
  templates?: TemplatesQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListTemplates({
  group = groupName,
  loading = false,
  templates,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListTemplatesProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!templates) return undefined;
    return {
      items: templates.templates,
      total: templates.total,
    };
  }, [templates]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<TemplateType>(
            ["id", "name", "description", "organization"],
            {
              filename: "templates_export.csv",
              label: "Export Selected Templates",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Template Actions",
            render: (context: ActionContext<TemplateType>) => {
              if (context.type === "row") {
                return <TemplateMenu template={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={i18n.links.templates.replace("[id]", row.original.id)}
                    className="w-full text-nowrap font-semibold hover:text-primary"
                  >
                    {row.getValue("name")}
                  </Link>
                  <p className="line-clamp-3 text-wrap text-xs text-muted-foreground">
                    {row.original.description ?? i18n.en.noDescription}
                  </p>
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization organization={row.original.organization} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization.id);
              },
            },
          ] as ColumnDef<TemplateType, TemplateType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
