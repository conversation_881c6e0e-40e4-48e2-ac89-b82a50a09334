"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { PhoneNumberField } from "@axa/ui/fields/numeric/PhoneNumber";
import { EmailField } from "@axa/ui/fields/text/Email";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { TechnicianLevelField } from "@/components/forms/fields/TechnicianLevel";

const i18n = {
  en: {
    firstName: {
      label: "First Name",
      description: "The given name of the provider",
      placeholder: "Enter the first name of the provider",
    },
    lastName: {
      label: "Last Name",
      description: "The family name of the provider",
      placeholder: "Enter the last name of the provider",
    },
    email: {
      label: "Email Address",
      description: "The email address of the provider",
      placeholder: "Enter the email address of the provider",
    },
    phone: {
      label: "Phone Number",
      description: "The phone number of the provider",
      placeholder: "Enter the phone number of the provider",
    },
    level: {
      label: "Technical Level",
      description: "The technical level of the provider",
      placeholder: "Enter the level of the provider",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      email: "Please enter a valid email address.",
      phone: "Please enter a valid phone number.",
    },
  },
};

const providerFormSchema = z.object({
  level: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email({
    message: i18n.en.messages.email,
  }),
  phone: z.string().min(10, {
    message: i18n.en.messages.phone,
  }),
});

export type ProviderFormValues = z.infer<typeof providerFormSchema>;
export type ProviderFormProps = PropsWithChildren<
  Parameters<typeof useForm<ProviderFormValues>>[0] & {
    onSubmit?: (values: ProviderFormValues) => void | Promise<void>;
  }
>;

export default function ProviderForm({
  children,
  onSubmit = () => void 0,
  ...props
}: ProviderFormProps) {
  const form = useForm<ProviderFormValues>({
    ...props,
    defaultValues: {
      level: "1",
      ...props.defaultValues,
    },
    resolver: zodResolver(providerFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="firstName"
          label={i18n.en.firstName.label}
          description={i18n.en.firstName.description}
          placeholder={i18n.en.firstName.placeholder}
        />

        <TextField
          name="lastName"
          label={i18n.en.lastName.label}
          description={i18n.en.lastName.description}
          placeholder={i18n.en.lastName.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <PhoneNumberField
          name="phone"
          label={i18n.en.phone.label}
          description={i18n.en.phone.description}
          placeholder={i18n.en.phone.placeholder}
        />

        <TechnicianLevelField
          name="level"
          label={i18n.en.level.label}
          description={i18n.en.level.description}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <ProviderFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ProviderFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ProviderFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
