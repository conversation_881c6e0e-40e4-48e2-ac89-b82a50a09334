"use client";

import type { DialogFormProps } from "@axa/ui/shared/DialogForm";

import type {
  TemplateFormProps,
  TemplateFormValues,
} from "@/components/forms/Template";

import TemplateForm, {
  TemplateFormSubmitButton,
} from "@/components/forms/Template";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

export type {
  TemplateFormProps,
  TemplateFormValues,
} from "@/components/forms/Template";
export type OrganizationTemplateFormProps = Omit<
  DialogFormProps<TemplateFormProps, TemplateFormValues>,
  "Component" | "onSubmit"
> & {
  templateId?: string;
  defaultValues?: Partial<TemplateFormValues>;
};

export function OrganizationTemplateForm(props: OrganizationTemplateFormProps) {
  return (
    <TemplateForm {...props}>
      <SelectOrganizationField description="The organization that the template belongs to" />
      <div className="flex w-full justify-center">
        <TemplateFormSubmitButton />
      </div>
    </TemplateForm>
  );
}
