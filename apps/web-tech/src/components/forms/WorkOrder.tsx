"use client";

import type { <PERSON>psWithChildren } from "react";
import type { TimeValue } from "react-aria";
import type { DateRange } from "react-day-picker";

import { useCallback, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon, EllipsisVerticalIcon, InfoIcon } from "lucide-react";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { contactFormSchema } from "@axa/ui/forms/Contact";
import { documentFormSchema } from "@axa/ui/forms/Document";
import AppView from "@axa/ui/layouts/AppView";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Calendar } from "@axa/ui/primitives/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Checkbox } from "@axa/ui/primitives/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@axa/ui/primitives/popover";
import { Switch } from "@axa/ui/primitives/switch";
import { Textarea } from "@axa/ui/primitives/textarea";
import { TimePicker } from "@axa/ui/primitives/time-picker";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@axa/ui/primitives/tooltip";
import { SelectValue } from "@axa/ui/selectors/SelectValue";

import OrderPriorityControl from "@/components/forms/fields/OrderPriority";
import { shiftFormSchema } from "@/components/forms/Shift";
import { technicianFormSchema } from "@/components/forms/Technician";
import { locationFormSchema } from "@/widgets/forms/location";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";
import SelectTemplate from "@/widgets/selectors/select-template";

const i18n = {
  en: {
    title: "New Work Order",
    summary: {
      label: "Summary",
      description: "A brief summary of the work order",
      placeholder: "Enter a summary outlining the task",
    },
    scope: {
      label: "Scope of Work",
      description: "The scope of the task",
      placeholder: "Enter a detailed scope of the task",
    },
    type: {
      label: "Type",
      description: "The type of the task",
      placeholder: "Select the task type",
    },
    category: {
      label: "Category",
      description: "The category of the task",
      placeholder: "Select the task category",
    },
    priority: {
      label: "Priority",
      description: "The priority of the task",
      placeholder: "Select the task priority",
    },
    customId: {
      label: "External ID",
      description: "The external ID of the work order",
      placeholder: "Enter a external ID",
    },
    customUrl: {
      label: "External URL",
      description: "The external URL of the work order",
      placeholder: "Enter a external URL",
    },
    PO: {
      label: "PO #",
      description: "The PO number of the work order",
      placeholder: "Enter a PO",
    },
    actions: {
      goBack: "Back to Work Orders",
      discard: "Discard",
      save: "Save",
      submit: "Submit",
      reset: "Reset",
    },
    sections: {
      specification: {
        title: "Order Specification",
        description: "Details on the type and category of the work order",
      },
      details: {
        title: "Order Details",
        description: "Details on the priority and scheduling of the work order",
      },
    },
    messages: {
      organizationShort: "The work order organization has to be set.",
      summaryShort: "The work order summary has to be at least 2 characters.",
      scopeShort: "The work order scope has to be at least 5 characters.",
      categoryShort: "The work order category has to be set.",
      typeShort: "The work order type has to be set.",
    },
  },
  links: {
    workOrders: "/app/orders",
  },
};

export const workOrderFormSchema = z.object({
  emergency: z.boolean().optional().default(false),
  organizationId: z.string().min(1, {
    message: i18n.en.messages.organizationShort,
  }),
  templateId: z.string().optional(),
  location: locationFormSchema
    .extend({
      id: z.string().optional(),
      organizationId: z.string().optional(),
    })
    .optional()
    .nullable(),
  documents: z
    .array(
      documentFormSchema.extend({
        id: z.string().optional(),
        description: z.string().nullable().optional(),
        file: z.instanceof(File).optional(),
      }),
    )
    .optional(),
  contacts: z
    .array(
      contactFormSchema.extend({
        id: z.string().optional(),
      }),
    )
    .optional(),
  technicians: z
    .array(
      technicianFormSchema.extend({
        id: z.string().optional(),
      }),
    )
    .optional(),
  shift: shiftFormSchema,
  customId: z.string().optional(),
  customUrl: z.string().optional(),
  PO: z.string().optional(),
  summary: z.string().min(2, {
    message: i18n.en.messages.summaryShort,
  }),
  scope: z.string().min(5, {
    message: i18n.en.messages.scopeShort,
  }),
  category: z.string().min(1, {
    message: i18n.en.messages.categoryShort,
  }),
  type: z.string().min(1, {
    message: i18n.en.messages.typeShort,
  }),
  priority: z.string(),
});

export type WorkOrderFormValues = z.infer<typeof workOrderFormSchema>;
export type WorkOrderFormProps = PropsWithChildren<
  Parameters<typeof useForm<WorkOrderFormValues>>[0] & {
    onSubmit?: (values: WorkOrderFormValues) => void | Promise<void>;
    loading?: boolean;
    categories?: WorkOrderFormSpecificationProps["categories"];
    types?: WorkOrderFormSpecificationProps["types"];
  }
>;

export default function WorkOrderForm({
  children,
  loading,
  types,
  categories,
  organizations,
  templates,
  onSubmit = () => void 0,
  ...props
}: WorkOrderFormProps) {
  const form = useForm<WorkOrderFormValues>({
    ...props,
    resolver: zodResolver(workOrderFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <AppView title={i18n.en.title} goBackUrl={i18n.links.workOrders}>
          <header>
            <div className="hidden items-center gap-2 md:ml-auto md:flex">
              <WorkOrderFormResetButton />
              <WorkOrderFormSubmitButton />
            </div>
          </header>

          <div className="flex flex-col gap-6">
            <TooltipProvider>
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <WorkOrderOrganization
                  loading={loading}
                  organizations={organizations}
                />
                <WorkOrderTemplate loading={loading} templates={templates} />
                <WorkOrderFormSpecification
                  loading={loading}
                  types={types}
                  categories={categories}
                />
                <WorkOrderFormDetails loading={loading} />
              </div>

              {children}
            </TooltipProvider>

            <footer className="flex items-center justify-center gap-2 md:hidden">
              <WorkOrderFormResetButton />
              <WorkOrderFormSubmitButton />
            </footer>
          </div>
        </AppView>
      </form>
    </Form>
  );
}

export function WorkOrderFormResetButton({
  children = i18n.en.actions.reset,
  ...props
}: ButtonProps) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Button
      {...props}
      size="sm"
      variant="outline"
      className={cn("min-w-20", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="button"
      onClick={useCallback(() => {
        form.reset();
      }, [form])}
    >
      {children}
    </Button>
  );
}

export function WorkOrderFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Button
      {...props}
      size="sm"
      className={cn("min-w-20", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}

export function OrderLabel({
  label,
  description,
  children,
}: PropsWithChildren<{
  label: string;
  description: string;
}>) {
  return (
    <div className="flex w-full items-center gap-2">
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex items-center gap-2">
          <FormLabel>{label}</FormLabel>
          <Tooltip>
            <TooltipTrigger asChild>
              <InfoIcon className="size-4" />
            </TooltipTrigger>
            <TooltipContent>{description}</TooltipContent>
          </Tooltip>
        </div>
        {children}
      </div>
      <FormDescription className="sr-only">{description}</FormDescription>
    </div>
  );
}

export function WorkOrderOrganization(props: {
  loading?: boolean;
  organizations: {
    id: string;
    name: string;
    avatar: string | null;
  }[];
  templates: {
    id: string;
    name: string;
    organization: {
      id: string;
      name: string;
      avatar: string | null;
    };
    summary?: string;
    scope?: string;
    type?: string;
    category?: string;
    location?: {
      id: string;
      name: string;
      address: {
        formatted: string;
        latitude?: number;
        longitude?: number;
        street?: string;
        city?: string;
        state?: string;
        postal?: string;
        country?: string;
      };
      timeZone?: string;
      description?: string;
      type?: string;
    } | null;
    documents?: {
      id: string;
      name: string;
      description: string;
      url: string;
    }[];
    technicians?: {
      id: string;
      level?: string;
      billingRate?: string;
      billingType?: string;
      paymentRate?: string;
      paymentType?: string;
    }[];
    contacts?: {
      id: string;
      role: string;
      person: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        avatar: string | null;
        isUser: boolean;
      };
    }[];
  }[];
  onOrganizationValueChange: (value: string) => void;
}) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Card>
      <CardHeader className="flex-col items-center gap-4 border-b">
        <div className="flex w-full items-center gap-2">
          <CardTitle>Order Metadata</CardTitle>
        </div>

        <FormField
          control={form.control}
          name="templateId"
          render={({ field }) => (
            <FormItem className="w-full">
              <OrderLabel
                label="Template"
                description="The template that the work order is associated with"
              />
              <FormControl>
                <SelectTemplate
                  placeholder="Select a template"
                  loading={props.loading}
                  data={props.templates ?? []}
                  value={field.value}
                  onSelect={(value) => {
                    form.setValue("summary", value.summary ?? "");
                    form.setValue("scope", value.scope ?? "");

                    if (value.organization) {
                      form.setValue("organizationId", value.organization.id);
                    }

                    if (value.location) {
                      form.setValue("location", value.location);
                    }

                    if (value.type) {
                      form.setValue("type", value.type);
                      form.setValue("category", value.category ?? "");
                    }

                    if (value.documents) {
                      form.setValue("documents", value.documents ?? []);
                    }
                    if (value.technicians) {
                      form.setValue(
                        "technicians",
                        value.technicians.map((tech) => ({
                          id: tech.id,
                          level: `${tech.level ?? "1"}`,
                          billingRate: `${tech.billingRate ?? "20"}`,
                          billingType: tech.billingType ?? "HOURLY",
                          paymentRate: `${tech.paymentRate ?? "20"}`,
                          paymentType: tech.paymentType ?? "HOURLY",
                        })),
                      );
                    }

                    if (value.contacts) {
                      form.setValue(
                        "contacts",
                        value.contacts.map((contact) => ({
                          id: contact.id,
                          role: contact.role,
                          personId: contact.person.id,
                          person: {
                            id: contact.person.id,
                            firstName: contact.person.firstName,
                            lastName: contact.person.lastName,
                            email: contact.person.email ?? "",
                            phone: contact.person.phone ?? "",
                            avatar: contact.person.avatar ?? "",
                            isUser: contact.person.isUser,
                          },
                        })),
                      );
                    }
                    field.onChange(value.id);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardHeader>
      <CardContent className="space-y-6 pt-4">
        <FormField
          control={form.control}
          name="organizationId"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label="Organization"
                description="The organization that the work order is associated with"
              />
              <FormControl>
                <SelectOrganizationField
                  description="The organization that the work order is associated with"
                  loading={props.loading}
                  onSelect={(value) => field.onChange(value.id)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="customId"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label={i18n.en.customId.label}
                description={i18n.en.customId.description}
              />
              <FormControl>
                <Input
                  className="truncate"
                  placeholder={i18n.en.customId.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="customUrl"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label={i18n.en.customUrl.label}
                description={i18n.en.customUrl.description}
              />
              <FormControl>
                <Input
                  className="truncate"
                  placeholder={i18n.en.customUrl.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="PO"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label={i18n.en.PO.label}
                description={i18n.en.PO.description}
              />
              <FormControl>
                <Input
                  className="truncate"
                  placeholder={i18n.en.PO.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}

export function WorkOrderTemplate(props: {
  loading?: boolean;
  templates: {
    id: string;
    name: string;
  }[];
}) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CardTitle>Template</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <FormField
          control={form.control}
          name="templateId"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label="Template"
                description="The template that the work order is associated with"
              />
              <FormControl>
                <SelectTemplate
                  placeholder="Select a template"
                  loading={props.loading}
                  data={props.templates}
                  value={field.value}
                  onSelect={(value) => {
                    field.onChange(value.id);
                    form.setValue("summary", value.summary ?? "");
                    form.setValue("scope", value.scope ?? "");
                    form.setValue("type", value.type ?? "");
                    form.setValue("category", value.category ?? "");
                    form.setValue("locationId", value.locationId ?? "");
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}

export interface WorkOrderFormSpecificationProps {
  loading?: boolean;
  categories?: { id: string; value: string }[];
  types?: {
    id: string;
    value: string;
    subtypes: {
      id: number;
      text: string;
    }[];
  }[];
}

export function WorkOrderFormSpecification({
  loading,
  categories,
  types,
}: WorkOrderFormSpecificationProps) {
  const [type, setType] = useState<NonNullable<typeof types>[number] | null>(
    null,
  );

  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Card className="flex h-full flex-col">
      <CardHeader>
        <div className="flex items-center gap-2">
          <CardTitle>{i18n.en.sections.specification.title}</CardTitle>
          <Tooltip>
            <TooltipTrigger asChild>
              <InfoIcon className="size-4" />
            </TooltipTrigger>
            <TooltipContent>
              {i18n.en.sections.specification.description}
            </TooltipContent>
          </Tooltip>
          <CardDescription className="sr-only">
            {i18n.en.sections.specification.description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="h-full flex-1">
        <div className="grid h-full grid-rows-[auto_1fr_auto_auto] gap-6">
          <FormField
            control={form.control}
            name="summary"
            render={({ field }) => (
              <FormItem>
                <OrderLabel
                  label={i18n.en.summary.label}
                  description={i18n.en.summary.description}
                />
                <FormControl>
                  <Input
                    className="truncate"
                    placeholder={i18n.en.summary.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="scope"
            render={({ field }) => (
              <FormItem className="flex h-full flex-col">
                <OrderLabel
                  label={i18n.en.scope.label}
                  description={i18n.en.scope.description}
                />
                <FormControl>
                  <Textarea
                    className="flex-1"
                    placeholder={i18n.en.scope.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 sm:gap-2">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="flex w-full flex-col gap-2">
                  <OrderLabel
                    label={i18n.en.type.label}
                    description={i18n.en.type.description}
                  />
                  <FormControl>
                    <SelectValue
                      placeholder={i18n.en.type.placeholder}
                      loading={loading}
                      data={types ?? []}
                      onSelect={(value) => {
                        field.onChange(value.value);
                        setType(value);
                        form.setValue("category", "", { shouldTouch: true });
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem className="flex w-full flex-col gap-2">
                  <OrderLabel
                    label={i18n.en.category.label}
                    description={i18n.en.category.description}
                  />
                  <FormControl>
                    <SelectValue
                      placeholder={i18n.en.category.placeholder}
                      loading={loading}
                      data={
                        type?.subtypes.map((sub) => ({
                          id: sub.id.toString(),
                          value: sub.text,
                        })) ??
                        categories ??
                        []
                      }
                      onSelect={(value) => field.onChange(value.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between gap-6 sm:gap-2">
                  <OrderLabel
                    label={i18n.en.priority.label}
                    description={i18n.en.priority.description}
                  />
                  <FormField
                    control={form.control}
                    name="emergency"
                    render={({ field }) => (
                      <FormItem className="ml-auto flex items-center justify-center gap-2">
                        <FormLabel className="text-nowrap font-bold leading-none text-red-600">
                          Emergency Service
                        </FormLabel>
                        <FormControl>
                          <Switch
                            className="mt-0"
                            checked={field.value}
                            onCheckedChange={(value) => {
                              field.onChange(value);

                              if (value === true) {
                                const now = new Date(Date.now());
                                form.setValue("priority", "HIGH");
                                form.setValue("shift.type", "SERVICE_DATE");
                                form.setValue("shift.date", new Date(now));
                                form.setValue("shift.hours", 4);
                                form.setValue("shift.startTime", {
                                  hour: now.getHours() + 4,
                                  minute: now.getMinutes(),
                                });
                                form.setValue("shift.endTime", {
                                  hour: now.getHours() + 8,
                                  minute: now.getMinutes(),
                                });
                              }
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <OrderPriorityControl
                  {...field}
                  disabled={loading}
                  onValueChange={field.onChange}
                  value={field.value}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}

export function WorkOrderFormDetails({ loading }: { loading?: boolean }) {
  const form = useFormContext<WorkOrderFormValues>();

  const four = form.watch("state.dateFourHours");
  const dateType = form.watch("state.dateType");

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <CardTitle>{i18n.en.sections.details.title}</CardTitle>
          <Tooltip>
            <TooltipTrigger asChild>
              <InfoIcon className="size-4" />
            </TooltipTrigger>
            <TooltipContent>
              {i18n.en.sections.details.description}
            </TooltipContent>
          </Tooltip>
          <CardDescription className="sr-only">
            {i18n.en.sections.details.description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        <FormField
          control={form.control}
          name="priority"
          render={({ field }) => (
            <FormItem>
              <OrderLabel
                label={i18n.en.priority.label}
                description={i18n.en.priority.description}
              />
              <OrderPriorityControl
                {...field}
                disabled={four === true || loading}
                onValueChange={field.onChange}
                value={field.value}
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="serviceDate"
          render={({ field }) => (
            <FormItem className="flex flex-col gap-1">
              <OrderLabel
                label="Service Date"
                description="The date on which the work order will be scheduled."
              >
                <FormField
                  control={form.control}
                  name="state.dateFourHours"
                  render={({ field }) => (
                    <FormItem className="ml-auto flex items-center justify-center gap-2">
                      <div className="leading-none">
                        <FormLabel>Within Four Hours</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={(value) => {
                            field.onChange(value);

                            if (value === true) {
                              const now = new Date(Date.now());
                              form.setValue("priority", "HIGH");
                              form.setValue("serviceDate", now);
                              form.setValue("startTime", {
                                hour: now.getHours() + 4,
                                minute: 0,
                              });
                            }
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </OrderLabel>

              <Popover>
                <div className="flex w-full items-center justify-between gap-2">
                  <PopoverTrigger asChild className="flex-1">
                    <FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",

                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {dateType === "range" ? (
                          `${format(field.value, "PPP")} - ${format(form.getValues("endDate") ?? new Date(), "PPP")}`
                        ) : field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto size-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <EllipsisVerticalIcon className="size-4" />
                        <span className="sr-only">Advanced Date Options</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <div className="flex flex-col gap-2">
                        <FormField
                          control={form.control}
                          name="state.dateType"
                          render={({ field }) => (
                            <FormItem className="flex max-w-[300px] flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value === "range"}
                                  onCheckedChange={(checked) => {
                                    form.setValue(
                                      "state.dateType",
                                      checked ? "range" : "single",
                                    );
                                  }}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Range of Dates</FormLabel>
                                <FormDescription>
                                  Select multiple dates for the work order
                                </FormDescription>
                              </div>

                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="state.omitWeekdays"
                          render={({ field }) => (
                            <FormItem className="flex max-w-[300px] flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Omit Weekends</FormLabel>
                                <FormDescription>
                                  When using a date range, ask the system to
                                  skip weekends in the range of dates.
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="state.omitHolidays"
                          render={({ field }) => (
                            <FormItem className="flex max-w-[300px] flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Omit Holidays</FormLabel>
                                <FormDescription>
                                  When using a date range, ask the system to
                                  skip bank holidays in the range of dates.
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode={dateType ?? "single"}
                    selected={
                      dateType === "range"
                        ? {
                            from: field.value,
                            to: form.getValues("endDate"),
                          }
                        : field.value
                    }
                    onSelect={(value: Date | DateRange) => {
                      if (dateType === "range") {
                        field.onChange((value as DateRange).from);
                        form.setValue("endDate", (value as DateRange).to);
                      } else {
                        field.onChange(value);
                      }
                    }}
                    disabled={(date) => date < new Date()}
                    initialFocus
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex items-end justify-between gap-2">
          <div className="mt-2 grid flex-1 grid-cols-2 gap-4">
            <div>
              <FormField
                control={form.control}
                name="startTime"
                render={({ field }) => (
                  <FormItem className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      <FormLabel>Start Time</FormLabel>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <InfoIcon className="size-4" />
                        </TooltipTrigger>
                        <TooltipContent>
                          The time the work order will start
                        </TooltipContent>
                      </Tooltip>
                      <FormDescription className="sr-only">
                        The time the work order will start
                      </FormDescription>
                    </div>

                    <FormControl>
                      <TimePicker
                        {...field}
                        value={field.value as TimeValue}
                        onChange={(value) => {
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="endTime"
                render={({ field }) => (
                  <FormItem className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      <FormLabel>End Time</FormLabel>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <InfoIcon className="size-4" />
                        </TooltipTrigger>
                        <TooltipContent>
                          The time the work order will end
                        </TooltipContent>
                      </Tooltip>
                      <FormDescription className="sr-only">
                        The time the work order will end
                      </FormDescription>
                    </div>

                    <FormControl>
                      <TimePicker
                        {...field}
                        value={field.value as TimeValue}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <EllipsisVerticalIcon className="size-4" />
                <span className="sr-only">Time Presets</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuGroup>
                <DropdownMenuLabel>Daytime Hours</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 8,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 16,
                      minute: 0,
                    });
                  }}
                >
                  8:00 AM - 4:00 PM
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 9,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 17,
                      minute: 0,
                    });
                  }}
                >
                  9:00 AM - 5:00 PM
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 10,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 18,
                      minute: 0,
                    });
                  }}
                >
                  10:00 AM - 6:00 PM
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuLabel>Nighttime Hours</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 18,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 2,
                      minute: 0,
                    });
                  }}
                >
                  6:00 PM - 2:00 AM
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 19,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 3,
                      minute: 0,
                    });
                  }}
                >
                  7:00 PM - 3:00 AM
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    form.setValue("startTime", {
                      hour: 20,
                      minute: 0,
                    });
                    form.setValue("endTime", {
                      hour: 4,
                      minute: 0,
                    });
                  }}
                >
                  8:00 PM - 4:00 AM
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
}
