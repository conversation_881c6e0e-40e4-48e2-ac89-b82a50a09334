"use client";

import type { PropsWithChildren } from "react";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";
import { SelectValueField } from "@axa/ui/selectors/SelectValue";

const i18n = {
  en: {
    summary: {
      label: "Summary",
      description: "A brief summary of the task",
      placeholder: "Enter a brief summary of the task",
    },
    scope: {
      label: "Scope",
      description: "The scope of the task",
      placeholder: "Enter a detailed scope of the task",
    },
    type: {
      label: "Type",
      description: "The type of the task",
      placeholder: "Select the task type",
    },
    category: {
      label: "Category",
      description: "The category of the task",
      placeholder: "Select the task category",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      summaryShort: "The summary has to be at least 5 characters.",
      scopeShort: "The scope has to be at least 5 characters.",
    },
  },
};

const orderSpecificationFormSchema = z.object({
  organizationId: z.string().optional(),
  summary: z.string().optional(),
  scope: z.string().optional(),
  category: z.string().optional(),
  type: z.string().optional(),
  priority: z.string().optional(),
});

export type OrderSpecificationFormValues = z.infer<
  typeof orderSpecificationFormSchema
>;
export type OrderSpecificationFormProps = PropsWithChildren<
  Parameters<typeof useForm<OrderSpecificationFormValues>>[0] & {
    onSubmit?: (values: OrderSpecificationFormValues) => void | Promise<void>;
    loading?: boolean;
    categories?: { id: string; value: string }[];
    types?: {
      id: string;
      value: string;
      subtypes: {
        id: number;
        text: string;
      }[];
    }[];
  }
>;

export default function OrderSpecificationForm({
  loading,
  children,
  categories,
  types,
  onSubmit = () => void 0,
  ...props
}: OrderSpecificationFormProps) {
  const [type, setType] = useState<NonNullable<typeof types>[number] | null>(
    null,
  );

  const form = useForm<OrderSpecificationFormValues>({
    ...props,
    resolver: zodResolver(orderSpecificationFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <TextField
          name="summary"
          label={i18n.en.summary.label}
          description={i18n.en.summary.description}
          placeholder={i18n.en.summary.placeholder}
        />

        <DescriptionField
          name="scope"
          label={i18n.en.scope.label}
          description={i18n.en.scope.description}
          placeholder={i18n.en.scope.placeholder}
        />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <SelectValueField
            loading={loading}
            name="type"
            label={i18n.en.type.label}
            description={i18n.en.type.description}
            placeholder={i18n.en.type.placeholder}
            onSelect={(value) => {
              setType(value);
            }}
            data={types ?? []}
          />
          <SelectValueField
            loading={loading}
            name="category"
            label={i18n.en.category.label}
            description={i18n.en.category.description}
            placeholder={i18n.en.category.placeholder}
            data={
              type?.subtypes.map((sub) => ({
                id: sub.id.toString(),
                value: sub.text,
              })) ??
              categories ??
              []
            }
          />
        </div>

        {children ?? (
          <div className="flex w-full justify-center">
            <OrderSpecificationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function OrderSpecificationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrderSpecificationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
