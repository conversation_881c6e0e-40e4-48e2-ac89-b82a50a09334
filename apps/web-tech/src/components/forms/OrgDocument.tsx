"use client";

import { useCallback, useState } from "react";

import type {
  DocumentFormProps,
  DocumentFormValues,
} from "@axa/ui/forms/Document";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import DocumentType from "@axa/ui/common/DocumentType";
import DocumentForm, { DocumentFormSubmitButton } from "@axa/ui/forms/Document";
import { Button } from "@axa/ui/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@axa/ui/primitives/dialog";
import { Progress } from "@axa/ui/primitives/progress";
import { toast } from "@axa/ui/primitives/toast";

import type { DropzoneProps } from "@/components/forms/fields/Dropzone";

import { api } from "@/api/client";
import Dropzone from "@/components/forms/fields/Dropzone";
import { useDocumentUploader } from "@/hooks/useDocumentUploader";
import {
  SelectOrganization,
  SelectOrganizationField,
} from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    messages: {
      created: "Document created successfully.",
      failedCreate: "Failed to create document: ",
      failedUpload: "Failed to upload document: ",
    },
  },
};

export type {
  DocumentFormProps,
  DocumentFormValues,
} from "@axa/ui/forms/Document";

export function useUploader(organizationId: string) {
  const utils = api.useUtils();
  return useDocumentUploader({
    organizationId,
    onSuccess: async () => {
      await utils.documents.getMany.invalidate();
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });
}

export function UploadDropzone(props: Omit<DropzoneProps, "onDrop">) {
  const [orgId, setOrgId] = useState("");
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const { uploading, progress, uploadFiles } = useUploader(orgId);

  const upload = useCallback(
    () =>
      uploadFiles(
        files.map((file) => ({
          name: file.name,
          size: file.size,
          type: file.type,
          file,
        })),
      )
        .then(() => {
          setFiles([]);
          setOpen(false);
        })
        .catch((error: Error) => {
          toast.error(i18n.en.messages.failedUpload + error.message);
        }),
    [files, uploadFiles],
  );

  console.log("orgId", orgId);
  console.log("files", files);

  return (
    <>
      <Dropzone
        {...props}
        // accept="application/pdf"
        multiple
        maxFiles={4}
        maxSize={10000000}
        onDrop={(files) => {
          setFiles(files);
          setOpen(true);
        }}
      />
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Add {files.length} file{files.length > 1 && "s"}
            </DialogTitle>
          </DialogHeader>

          {files.map((file) => (
            <div
              key={file.name}
              className="mt-4 flex items-center justify-between rounded-md border p-2"
            >
              <DocumentType type={file.type} size="40" />

              <span className="grid w-full flex-1 truncate px-2">
                <span className="truncate text-wrap font-semibold">
                  {file.name}
                </span>
                <span>{file.type}</span>
                <span>{(file.size / 1000 ** 2).toFixed(2)} megabytes</span>
              </span>
            </div>
          ))}

          <SelectOrganization
            size="sm"
            onSelect={useCallback(
              (org: { id: string }) => setOrgId(org.id),
              [],
            )}
          />

          <DialogFooter>
            <div className="flex w-full flex-col gap-2">
              <div className="h-6 w-full">
                {uploading && <Progress value={progress} />}
              </div>
              {files.length > 0 && (
                <Button onClick={upload} disabled={uploading || !orgId}>
                  Upload
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export type OrganizationDocumentFormProps = Omit<
  DialogFormProps<DocumentFormProps, DocumentFormValues>,
  "Component" | "onSubmit"
> &
  Omit<DropzoneProps, "onDrop"> & {
    onDrop?: DropzoneProps["onDrop"];
    showDropzone?: boolean;
  } & {
    templateId?: string;
    orderId?: string;
    onSubmit?: DocumentFormProps["onSubmit"];
    defaultValues?: Partial<DocumentFormValues>;
  };

export function OrganizationDocumentForm({
  onSubmit,
  open,
  maxFiles,
  maxSize,
  minSize,
  accept,
  onDrop,
  showDropzone,
  defaultValues,
  ...props
}: OrganizationDocumentFormProps) {
  const [orgId, setOrgId] = useState("");
  const { uploading, progress, uploadFiles } = useUploader(orgId);

  return (
    <DocumentForm
      {...props}
      defaultValues={defaultValues}
      // accept={fileTypes.join(",")}
      onSubmit={useCallback<NonNullable<DocumentFormProps["onSubmit"]>>(
        async (values) => {
          console.log("values", values);
          if (showDropzone && values.file) {
            await uploadFiles([
              {
                name: values.name,
                size: values.size,
                type: values.type,
                file: values.file,
              },
            ]);
          }

          if (onSubmit) {
            await onSubmit(values);
          }
        },
        [onSubmit, uploadFiles, showDropzone],
      )}
      dropzone={(form) =>
        showDropzone && (
          <Dropzone
            showFiles
            maxFiles={maxFiles}
            maxSize={maxSize}
            minSize={minSize}
            accept={accept}
            onDrop={(files) => {
              if (onDrop) onDrop(files);

              const [file] = files;
              const { name, size, type } = file as {
                name: string;
                size: number;
                type: string;
              };

              form.setValue("file", file as File);
              form.setValue("name", name);
              form.setValue("size", size);
              form.setValue("type", type);
            }}
          />
        )
      }
    >
      <SelectOrganizationField
        description="The organization that this document belongs to"
        onSelect={useCallback((org: { id: string }) => setOrgId(org.id), [])}
      />

      {uploading && <Progress value={progress} />}
      <div className="flex w-full justify-center">
        <DocumentFormSubmitButton />
      </div>
    </DocumentForm>
  );
}
