import type { ComponentProps } from "react";

import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { RadioGroup, RadioGroupItem } from "@axa/ui/primitives/radio-group";

import { OrderPriorityIcon } from "@/components/symbols/OrderPriority";

const priorityOptions = [
  { value: "HIGH", key: "high" },
  { value: "MEDIUM", key: "medium" },
  { value: "LOW", key: "low" },
] as const;

const i18n = {
  en: {
    priority: {
      label: "Priority",
      description: "The priority of the order",
      options: {
        high: "High",
        medium: "Medium",
        low: "Low",
      },
    },
  },
};

export default function OrderPriorityControl(
  props: ComponentProps<typeof RadioGroup>,
) {
  return (
    <RadioGroup {...props} className="grid grid-cols-3 gap-3">
      {priorityOptions.map(({ value, key }) => (
        <div key={value} className="relative">
          <label
            htmlFor={`priority-${key}`}
            className="flex cursor-pointer items-center justify-between rounded-lg border border-border bg-background p-4 text-sm font-medium transition-colors hover:bg-muted has-[:checked]:border-primary has-[:checked]:bg-primary/5"
          >
            <div className="flex items-center gap-2">
              <OrderPriorityIcon priority={value} />
              <span className="sr-only sm:not-sr-only lg:sr-only xl:not-sr-only">
                {i18n.en.priority.options[key]}
              </span>
            </div>
            <RadioGroupItem value={value} id={`priority-${key}`} />
          </label>
        </div>
      ))}
    </RadioGroup>
  );
}

export function OrderPriorityField(props: ComponentProps<typeof RadioGroup>) {
  const form = useFormContext<{
    priority: string;
  }>();

  return (
    <FormField
      control={form.control}
      name="priority"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{i18n.en.priority.label}</FormLabel>
          <FormDescription>{i18n.en.priority.description}</FormDescription>
          <FormControl>
            <OrderPriorityControl {...field} {...props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
