import { useFormContext } from "react-hook-form";

import type { InputProps } from "@axa/ui/primitives/input";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";

const i18n = {
  en: {
    label: "FieldNation ID",
    description: "The FieldNation ID associated with this organization",
    placeholder: "Add a FieldNation ID",
  },
};

export interface FieldNationIdProps extends InputProps {
  placeholder?: string;
}

export default function FieldNationId({
  value,
  onChange,
  placeholder = i18n.en.placeholder,
  ...props
}: FieldNationIdProps) {
  return <Input value={value} onChange={onChange} {...props} />;
}

export interface FieldNationIdFieldProps extends FieldNationIdProps {
  label?: string;
  description?: string;
}

export function FieldNationIdField({
  name = "fieldNationId",
  label = i18n.en.label,
  description = i18n.en.description,
  ...props
}: FieldNationIdFieldProps) {
  const form = useFormContext<{
    fieldNationId?: string;
  }>();

  return (
    <FormField
      control={form.control}
      // @ts-expect-error `name` is not assignable to type `never`
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <FieldNationId {...props} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
