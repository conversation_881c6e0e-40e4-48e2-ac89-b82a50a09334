"use client";

import type { PersonFormProps, PersonFormValues } from "@axa/ui/forms/Person";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import PersonForm, { PersonFormSubmitButton } from "@axa/ui/forms/Person";

import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

export type { PersonFormProps, PersonFormValues } from "@axa/ui/forms/Person";
export type OrganizationPersonFormProps = Omit<
  DialogFormProps<PersonFormProps, PersonFormValues>,
  "Component" | "onSubmit"
> & {
  personId?: string;
  showOrganization?: boolean;
  defaultValues?: Partial<PersonFormValues>;
};

export function OrganizationPersonForm(props: OrganizationPersonFormProps) {
  return (
    <PersonForm {...props}>
      {props.showOrganization && (
        <SelectOrganizationField description="The organization that the person belongs to" />
      )}

      <div className="flex w-full justify-center">
        <PersonFormSubmitButton />
      </div>
    </PersonForm>
  );
}
