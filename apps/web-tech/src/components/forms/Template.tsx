"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "The template name",
      placeholder: "Enter the template name",
    },
    description: {
      label: "Description",
      description: "The template description",
      placeholder: "Enter the template description",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameShort: "The template name has to be at least 2 characters.",
    },
  },
};

const templateFormSchema = z.object({
  organizationId: z.string(),
  name: z.string().min(2, {
    message: i18n.en.messages.nameShort,
  }),
  description: z.string().optional(),
});

export type TemplateFormValues = z.infer<typeof templateFormSchema>;
export type TemplateFormProps = PropsWithChildren<
  Parameters<typeof useForm<TemplateFormValues>>[0] & {
    onSubmit?: (values: TemplateFormValues) => void | Promise<void>;
  }
>;

export default function TemplateForm({
  children,
  onSubmit = () => void 0,
  ...props
}: TemplateFormProps) {
  const form = useForm<TemplateFormValues>({
    ...props,
    resolver: zodResolver(templateFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <TemplateFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function TemplateFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<TemplateFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
