"use client";

import { useState } from "react";

import type {
  OrganizationFormProps,
  OrganizationFormValues,
} from "@axa/ui/forms/organizations/Organization";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import OrganizationForm, {
  OrganizationFormSubmitButton,
} from "@axa/ui/forms/organizations/Organization";
import { SelectOrganizationField } from "@axa/ui/selectors/SelectOrganization";

import { api } from "@/api/client";
import { FieldNationIdField } from "@/components/forms/fields/field-nation-id";
import { useUser } from "@/contexts/User";

export type {
  OrganizationFormProps,
  OrganizationFormValues,
} from "@axa/ui/forms/organizations/Organization";

export interface CompositeOrganizationFormProps
  extends Omit<
    DialogFormProps<OrganizationFormProps, OrganizationFormValues>,
    "Component" | "onSubmit"
  > {
  organizationId?: string;
  parentId?: string;
  defaultValues?: Partial<OrganizationFormValues>;
}

export function CompositeOrganizationForm(
  props: CompositeOrganizationFormProps,
) {
  const [query, setQuery] = useState("");
  const user = useUser();
  const orgId = props.organizationId ?? "";
  const organization = api.organizations.get.useQuery(
    {
      id: orgId,
      include: {
        parent: true,
      },
    },
    {
      enabled: !!orgId && props.open,
      refetchOnMount: true,
    },
  );

  const organizations = api.organizations.getMany.useQuery(
    {
      query,
      pageSize: 5,
      pageNumber: 0,
      include: {
        parent: true,
      },
    },
    {
      enabled: props.open,
    },
  );

  const loading = organization.isLoading || organizations.isLoading;

  return (
    <OrganizationForm
      {...props}
      loading={loading}
      defaultValues={
        organization.isLoading
          ? undefined
          : {
              name: organization.data?.organization.name ?? "",
              organizationId: organization.data?.organization.id,
              parentId:
                props.parentId ??
                organization.data?.organization.parent?.id ??
                (user.isInternal ? "null" : ""),
              fieldNationId:
                organization.data?.organization.fieldNationId?.toString(),
            }
      }
    >
      <SelectOrganizationField
        loading={loading}
        onValueChange={(value) => {
          setQuery(value);
        }}
        name="parentId"
        label="Parent Organization"
        description="The parent organization"
        data={
          organizations.data?.organizations
            ? [].concat(
                // @ts-expect-error TODO: fix this
                user.isInternal ? [{ id: "null", name: "None" }] : [],
                organization.data?.organization.parent
                  ? [organization.data.organization.parent]
                  : [],
                [...organizations.data.organizations],
              )
            : []
        }
      />
      {user.isInternal && <FieldNationIdField />}
      <div className="flex w-full justify-center">
        <OrganizationFormSubmitButton />
      </div>
    </OrganizationForm>
  );
}
