"use client";

import type { PropsWithChildren } from "react";
import type { FieldError } from "react-hook-form";
import type { z } from "zod";

import { useCallback, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";

import type { WorkOrderPriority } from "@axa/database-tech";
import type { ButtonProps } from "@axa/ui/primitives/button";
import { ValueStoreType } from "@axa/database-tech";
import AppView from "@axa/ui/layouts/AppView";
import { cn } from "@axa/ui/lib";
import { Alert, AlertDescription, AlertTitle } from "@axa/ui/primitives/alert";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";
import { toast } from "@axa/ui/primitives/toast";
import { TooltipProvider } from "@axa/ui/primitives/tooltip";

import type { WorkOrderContactsProps } from "@/www/orders/details/WorkOrderContacts";
import type { WorkOrderDocumentProps } from "@/www/orders/details/WorkOrderDocuments";
import type { WorkOrderLocationProps } from "@/www/orders/details/WorkOrderLocation";
import type { WorkOrderShiftProps } from "@/www/orders/details/WorkOrderSchedule";
import type { WorkOrderTechniciansProps } from "@/www/orders/details/WorkOrderTechnicians";

import { api } from "@/api/client";
import {
  workOrderFormSchema,
  WorkOrderFormSpecification,
  WorkOrderOrganization,
} from "@/components/forms/WorkOrder";
import { useUser } from "@/contexts/User";
import WorkOrderContacts from "@/www/orders/details/WorkOrderContacts";
import WorkOrderDocuments from "@/www/orders/details/WorkOrderDocuments";
import WorkOrderLocation from "@/www/orders/details/WorkOrderLocation";
import { WorkOrderShift } from "@/www/orders/details/WorkOrderSchedule";
import WorkOrderTechnicians from "@/www/orders/details/WorkOrderTechnicians";

const i18n = {
  en: {
    title: "New Work Order",
    headings: {
      specification: "Specification",
      schedule: "Schedule",
      technicians: "Technicians",
      location: "Location",
      contacts: "Contacts",
      documents: "Documents",
    },
    actions: {
      goBack: "Back to Work Orders",
      discard: "Discard",
      save: "Save",
      submit: "Submit",
      reset: "Reset",
    },
    messages: {
      summaryShort: "The work order summary has to be at least 2 characters.",
      scopeShort: "The work order scope has to be at least 5 characters.",
      success: "Work Order created successfully.",
      error: "Failed to create work order: ",
    },
  },
  links: {
    workOrders: "/app/orders",
    orders: "/app/orders/[id]",
  },
};

export type WorkOrderFormValues = z.infer<typeof workOrderFormSchema>;
export type WorkOrderFormProps = PropsWithChildren<
  Parameters<typeof useForm<WorkOrderFormValues>>[0] & {
    onSubmit?: (values: WorkOrderFormValues) => void | Promise<void>;
    loading?: boolean;
  }
>;

export default function WorkOrderForm({
  children,
  defaultValues,
  ...props
}: WorkOrderFormProps) {
  const user = useUser();
  const form = useForm<WorkOrderFormValues>({
    ...props,
    defaultValues: useMemo(() => {
      return {
        priority: "MEDIUM",
        shift: {
          date: new Date(new Date().setDate(new Date().getDate() + 1)),
          type: "SERVICE_DATE",
          startTime: {
            hour: new Date().getHours(),
            minute: 0,
          },
          hours: 2,
        },
        ...(defaultValues ?? {}),
      };
    }, [defaultValues]),
    resolver: zodResolver(workOrderFormSchema),
  });

  const router = useRouter();
  const [query, setQuery] = useState("");
  const organizations = api.organizations.getMany.useQuery({
    query,
    pageSize: 5,
    pageNumber: 0,
  });
  const templates = api.templates.getMany.useQuery({
    include: {
      location: true,
      contacts: true,
      documents: true,
      technicians: true,
    },
  });
  const categories = api.values.getMany.useQuery({
    type: ValueStoreType.ORDER_CATEGORY,
  });
  const types = api.values.getMany.useQuery({
    type: ValueStoreType.ORDER_TYPE,
  });
  const loading =
    categories.isLoading ||
    types.isLoading ||
    organizations.isLoading ||
    templates.isLoading;

  const createWorkOrderMutation = api.orders.create.useMutation(
    useMemo(
      () => ({
        onSuccess: (data) => {
          router.push(i18n.links.orders.replace("[id]", data.order.id));

          if (user.isInternal) {
            if (data.warnings.length > 0) {
              toast.warning(
                data.warnings.map((warning) => warning.message).join("\n"),
              );
            }
          }

          toast.success(i18n.en.messages.success);
        },
        onError: (error) => {
          toast.error(i18n.en.messages.error + error.message);
        },
      }),
      [user.isInternal, router],
    ),
  );

  const errors = useMemo(() => {
    const formErrors = Object.entries(form.formState.errors).reduce(
      (acc: [string, FieldError][], [key, error]) => {
        if (error.message) {
          acc.push([key, error as FieldError]);
          return acc;
        }

        return acc.concat(
          Object.entries(error).map(([k, e]) => [
            [key, k].join("-"),
            e as FieldError,
          ]),
        );
      },
      [],
    );
    // const formErrors = Object.entries(form.formState.errors).flatMap(
    //   ([key, error]) =>
    //     error.message
    //       ? [key, error]
    //       : Object.entries(error).map(([k, e]) => [[key, k].join("-"), e]),
    // );

    // eslint-disable-next-line no-restricted-properties
    if (process.env.NODE_ENV === "development") {
      console.log({
        errors: form.formState.errors,
        formatted: Object.entries(form.formState.errors).map(([key, error]) =>
          error.message
            ? [key, error]
            : Object.entries(error).map(([k, e]) => [[key, k].join("-"), e]),
        ),
      });
    }

    if (formErrors.length > 0) {
      return (
        <Alert variant="destructive">
          <AlertTitle>Errors</AlertTitle>
          <AlertDescription>
            {formErrors.map(([key, error]) => (
              <p key={key}>
                {key}: {error.message}
              </p>
            ))}
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  }, [form.formState.errors]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(
          useCallback(
            async (values: WorkOrderFormValues) => {
              let status: "DRAFT" | "PENDING" = "DRAFT";

              const hasContacts = (values.contacts?.length ?? 0) > 0;
              const hasTechnicians = (values.technicians?.length ?? 0) > 0;
              const hasLocation = values.location?.id !== undefined;
              const hasDate =
                values.shift.date !== undefined ||
                (values.shift.startDate && values.shift.endDate);

              if (values.emergency) {
                status = "PENDING";
              } else if (
                hasDate &&
                hasContacts &&
                hasTechnicians &&
                hasLocation
              ) {
                status = "PENDING";
              }

              const payload = {
                status,
                emergency: values.emergency,
                organizationId: values.organizationId,
                templateId: values.templateId,
                summary: values.summary,
                scope: values.scope,
                type: values.type,
                category: values.category,
                customId: values.customId,
                customUrl: values.customUrl,
                PO: values.PO,
                priority: values.priority as WorkOrderPriority,
                shift: values.shift,
                locationId: values.location?.id,
                contacts: values.contacts?.map((contact) => ({
                  personId: contact.personId,
                  role: contact.role,
                })),
                technicians: values.technicians?.map((tech) => ({
                  level: parseInt(tech.level, 10),
                  billingRate: tech.billingRate
                    ? parseFloat(tech.billingRate)
                    : 0,
                  billingType: tech.billingType,
                  paymentRate: tech.paymentRate
                    ? parseFloat(tech.paymentRate)
                    : 0,
                  paymentType: tech.paymentType,
                })),
                documents: (values.documents ?? [])
                  .map((doc) => doc.id)
                  .filter(Boolean) as string[],
              };

              await createWorkOrderMutation.mutateAsync(payload);
            },
            [createWorkOrderMutation],
          ),
        )}
        className="space-y-8"
      >
        <AppView title={i18n.en.title} goBackUrl={i18n.links.workOrders}>
          <header>
            <div className="hidden items-center gap-2 md:ml-auto md:flex">
              <WorkOrderFormResetButton disabled={loading} />
              <WorkOrderFormSubmitButton disabled={loading} />
            </div>
          </header>

          <div className="flex flex-col gap-6">
            <TooltipProvider>
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <WorkOrderOrganization
                  loading={loading}
                  organizations={organizations.data?.organizations ?? []}
                  templates={templates.data?.templates ?? []}
                  onOrganizationValueChange={useCallback((value: string) => {
                    setQuery(value);
                  }, [])}
                />
                <WorkOrderFormSpecification
                  loading={loading}
                  categories={categories.data?.values ?? []}
                  types={types.data?.values ?? []}
                />
              </div>

              {errors}
              {children}

              <section className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="grid size-full grid-cols-1 gap-6 lg:col-span-2 xl:grid-cols-2">
                  <div className="flex size-full flex-col items-stretch justify-stretch gap-2">
                    <h2 className="text-lg font-semibold">
                      {i18n.en.headings.schedule}
                    </h2>
                    <div className="flex-1">
                      <WorkOrderShift
                        loading={loading}
                        timeZone={form.watch("location")?.timeZone}
                        shift={form.watch("shift")}
                        onCreate={useCallback<
                          NonNullable<WorkOrderShiftProps["onCreate"]>
                        >(
                          (shift) => {
                            form.setValue("shift", {
                              ...shift,
                              type: shift.type ?? "SERVICE_DATE",
                              date: shift.date ?? shift.startDate ?? new Date(),
                            });
                          },
                          [form],
                        )}
                        onUpdate={useCallback<
                          NonNullable<WorkOrderShiftProps["onUpdate"]>
                        >(
                          (shift) => {
                            form.setValue("shift", {
                              ...shift,
                              type: shift.type ?? "SERVICE_DATE",
                              date: shift.date ?? shift.startDate ?? new Date(),
                            });
                          },
                          [form],
                        )}
                      />
                    </div>
                  </div>
                  <div className="flex size-full flex-col items-stretch justify-stretch gap-2">
                    <h2 className="text-lg font-semibold">
                      {i18n.en.headings.technicians}
                    </h2>
                    <WorkOrderTechnicians
                      className="h-full"
                      local
                      loading={loading}
                      technicians={form.watch("technicians")}
                      onCreate={useCallback<
                        NonNullable<WorkOrderTechniciansProps["onCreate"]>
                      >(
                        (technician) => {
                          const technicians =
                            form.getValues("technicians") ?? [];
                          form.setValue("technicians", [
                            ...technicians,
                            {
                              id: `${Math.random() * 1000}`,
                              level: technician.level,
                              billingRate: technician.billingRate,
                              billingType: technician.billingType,
                              paymentRate: technician.paymentRate,
                              paymentType: technician.paymentType,
                            },
                          ]);
                        },
                        [form],
                      )}
                      onUpdate={useCallback<
                        NonNullable<WorkOrderTechniciansProps["onUpdate"]>
                      >(
                        (technician) => {
                          const technicians =
                            form.getValues("technicians") ?? [];
                          form.setValue("technicians", [
                            ...technicians.map((tech) =>
                              tech.id === technician.id ? technician : tech,
                            ),
                          ]);
                        },
                        [form],
                      )}
                      onDelete={useCallback<
                        NonNullable<WorkOrderTechniciansProps["onDelete"]>
                      >(
                        (id) => {
                          const technicians =
                            form.getValues("technicians") ?? [];
                          form.setValue(
                            "technicians",
                            technicians.filter((tech) => tech.id !== id),
                          );
                        },
                        [form],
                      )}
                    />
                  </div>

                  <div className="flex size-full flex-col items-stretch justify-stretch gap-2">
                    <h2 className="text-lg font-semibold">
                      {i18n.en.headings.location}
                    </h2>
                    <WorkOrderLocation
                      loading={loading}
                      location={form.watch("location")}
                      onLink={useCallback<
                        NonNullable<WorkOrderLocationProps["onLink"]>
                      >(
                        (location) => {
                          if (!location) {
                            return;
                          }

                          form.setValue(
                            "location",
                            {
                              ...location,
                              fieldNationId: location.fieldNationId
                                ? location.fieldNationId.toString()
                                : undefined,
                            },
                            {
                              shouldValidate: true,
                            },
                          );

                          form.setValue("shift", {
                            ...form.getValues("shift"),
                            timeZone: location.timeZone,
                          });
                        },
                        [form],
                      )}
                      onUnlink={useCallback<
                        NonNullable<WorkOrderLocationProps["onUnlink"]>
                      >(() => {
                        form.setValue("location", null, {
                          shouldValidate: true,
                        });
                      }, [form])}
                    />
                  </div>
                  <div className="flex size-full flex-col items-stretch justify-stretch gap-2">
                    <h2 className="text-lg font-semibold">
                      {i18n.en.headings.contacts}
                    </h2>
                    <WorkOrderContacts
                      local
                      loading={loading}
                      contacts={form.watch("contacts")}
                      onCreate={useCallback<
                        NonNullable<WorkOrderContactsProps["onCreate"]>
                      >(
                        (contact) => {
                          const contacts = form.getValues("contacts") ?? [];
                          form.setValue("contacts", [
                            ...contacts,
                            {
                              id: `${contacts.length + 1}-${Math.random() * 1000}`,
                              ...contact,
                            },
                          ]);
                        },
                        [form],
                      )}
                      onUpdate={useCallback<
                        NonNullable<WorkOrderContactsProps["onUpdate"]>
                      >(
                        (contact) => {
                          const contacts = form.getValues("contacts") ?? [];
                          form.setValue("contacts", [
                            ...contacts.map((_contact) =>
                              _contact.id === contact.id ? contact : _contact,
                            ),
                          ]);
                        },
                        [form],
                      )}
                      onDelete={useCallback<
                        NonNullable<WorkOrderContactsProps["onDelete"]>
                      >(
                        (id) => {
                          const contacts = form.getValues("contacts") ?? [];
                          form.setValue(
                            "contacts",
                            contacts.filter((contact) => contact.id !== id),
                          );
                        },
                        [form],
                      )}
                    />
                  </div>
                </div>
                <div className="flex size-full flex-col items-stretch justify-stretch gap-2 lg:col-span-2">
                  <h2 className="text-lg font-semibold">
                    {i18n.en.headings.documents}
                  </h2>
                  <WorkOrderDocuments
                    loading={loading}
                    documents={form.watch("documents")}
                    onLink={useCallback<
                      NonNullable<WorkOrderDocumentProps["onLink"]>
                    >(
                      (document) => {
                        const documents = form.getValues("documents") ?? [];
                        form.setValue("documents", [
                          ...documents,
                          {
                            id: document.id,
                            name: document.name,
                            description: document.description,
                            type: document.type,
                            size: document.size,
                            url: document.url,
                            organizationId: document.organization?.id,
                          },
                        ]);
                      },
                      [form],
                    )}
                    onUnlink={useCallback<
                      NonNullable<WorkOrderDocumentProps["onUnlink"]>
                    >(
                      (id) => {
                        const documents = form.getValues("documents") ?? [];
                        form.setValue(
                          "documents",
                          documents.filter((doc) => doc.id !== id),
                        );
                      },
                      [form],
                    )}
                  />
                </div>
              </section>
            </TooltipProvider>

            <footer className="flex items-center justify-center gap-2 md:hidden">
              <WorkOrderFormResetButton disabled={loading} />
              <WorkOrderFormSubmitButton disabled={loading} />
            </footer>
          </div>
        </AppView>
      </form>
    </Form>
  );
}

export function WorkOrderFormResetButton({
  children = i18n.en.actions.reset,
  ...props
}: ButtonProps) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Button
      {...props}
      size="sm"
      variant="outline"
      className={cn("min-w-20", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="reset"
      onClick={useCallback(() => {
        form.reset();
      }, [form])}
    >
      {children}
    </Button>
  );
}

export function WorkOrderFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<WorkOrderFormValues>();

  return (
    <Button
      {...props}
      size="sm"
      className={cn("min-w-20", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
