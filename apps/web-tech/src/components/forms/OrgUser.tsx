"use client";

import type { PropsWithChildren } from "react";

import { useState } from "react";

import type {
  UserFormProps,
  UserFormValues,
  UserRole,
} from "@axa/ui/forms/users/User";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import UserForm, { UserFormSubmitButton } from "@axa/ui/forms/users/User";

import { SelectOrganizationField } from "@/widgets/selectors/select-organization";

export type { UserFormProps, UserFormValues, UserRole };

export type OrganizationUserProps = Omit<
  DialogFormProps<UserFormProps, UserFormValues>,
  "Component" | "onSubmit"
> &
  PropsWithChildren<{
    showOrganization?: boolean;
    userId: string;
    defaultValues?: Partial<UserFormValues>;
    onUpdate?: (user: UserFormValues & { id: string }) => void | Promise<void>;
  }>;

export function OrganizationUserForm(props: OrganizationUserProps) {
  const [currentRole, setCurrentRole] = useState<UserRole | undefined>(
    props.defaultValues?.role as UserRole | undefined,
  );

  const showOrg = props.showOrganization && currentRole === "CLIENT";

  return (
    <UserForm
      {...props}
      onRoleChange={(role) => {
        setCurrentRole(role);
      }}
    >
      {showOrg && (
        <SelectOrganizationField
          enabled={props.open}
          description="The organization that the user belongs to"
        />
      )}
      <div className="flex w-full justify-center">
        <UserFormSubmitButton />
      </div>
    </UserForm>
  );
}
