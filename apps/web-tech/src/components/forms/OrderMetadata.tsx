"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { IDField } from "@axa/ui/fields/text/ID";
import { TextField } from "@axa/ui/fields/text/Text";
import { URLField } from "@axa/ui/fields/text/URL";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    customId: {
      label: "External ID",
      description: "The external ID for the order",
      placeholder: "Enter the external ID for the order",
    },
    customUrl: {
      label: "External URL",
      description: "The external URL for the order",
      placeholder: "Enter the external URL for the order",
    },
    PO: {
      label: "Purchase Order",
      description: "The PO number for the order",
      placeholder: "Enter the PO number for the order",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const orderMetadataFormSchema = z.object({
  customId: z.string().optional(),
  customUrl: z.string().url().optional(),
  PO: z.string().optional(),
});

export type OrderMetadataFormValues = z.infer<typeof orderMetadataFormSchema>;
export type OrderMetadataFormProps = PropsWithChildren<
  Parameters<typeof useForm<OrderMetadataFormValues>>[0] & {
    onSubmit?: (values: OrderMetadataFormValues) => void | Promise<void>;
  }
>;

export default function OrderMetadataForm({
  children,
  onSubmit = () => void 0,
  ...props
}: OrderMetadataFormProps) {
  const form = useForm<OrderMetadataFormValues>({
    ...props,
    resolver: zodResolver(orderMetadataFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <IDField
          name="customId"
          label={i18n.en.customId.label}
          description={i18n.en.customId.description}
          placeholder={i18n.en.customId.placeholder}
        />

        <URLField
          name="customUrl"
          label={i18n.en.customUrl.label}
          description={i18n.en.customUrl.description}
          placeholder={i18n.en.customUrl.placeholder}
        />

        <TextField
          name="PO"
          label={i18n.en.PO.label}
          description={i18n.en.PO.description}
          placeholder={i18n.en.PO.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <OrderMetadataFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function OrderMetadataFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrderMetadataFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
