"use client";

import type {
  ContactFormProps,
  ContactFormValues,
} from "@axa/ui/forms/Contact";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { ValueStoreType } from "@axa/database-tech";
import ContactForm, { ContactFormSubmitButton } from "@axa/ui/forms/Contact";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";

import { api } from "@/api/client";
import { SelectOrganizationField } from "@/widgets/selectors/select-organization";
import { SelectPersonField } from "@/widgets/selectors/select-person";

export type {
  ContactFormProps,
  ContactFormValues,
} from "@axa/ui/forms/Contact";

export type OrganizationContactFormProps = Omit<
  DialogFormProps<ContactFormProps, ContactFormValues>,
  "Component" | "onSubmit"
> & {
  showOrganization?: boolean;
  templateId?: string;
  orderId?: string;
  personId?: string;
  defaultValues?: Partial<ContactFormValues>;
};

export function OrganizationContactForm(props: OrganizationContactFormProps) {
  const [personQuery, setPersonQuery] = useDebounceValue<string>("", 300);
  const roles = api.values.getMany.useQuery(
    {
      type: ValueStoreType.CONTACT,
    },
    {
      enabled: props.open,
    },
  );

  const people = api.people.getMany.useQuery(
    {
      query: personQuery,
      pageNumber: 0,
      pageSize: 5,
    },
    {
      enabled: props.open,
    },
  );

  const loading = roles.isLoading || people.isLoading;

  return (
    <ContactForm {...props} roles={roles.data?.values ?? []} loading={loading}>
      <SelectPersonField
        loading={loading}
        data={people.data?.people ?? []}
        onQueryChange={(value) => {
          setPersonQuery(value);
        }}
      />
      {props.showOrganization && (
        <SelectOrganizationField
          loading={loading}
          description="The organization that the contact belongs to"
        />
      )}
      <div className="flex w-full justify-center">
        <ContactFormSubmitButton />
      </div>
    </ContactForm>
  );
}
