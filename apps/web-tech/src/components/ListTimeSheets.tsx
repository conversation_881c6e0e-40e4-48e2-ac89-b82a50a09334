"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { RouterOutputs } from "@axa/api-tech";
import type { TimeSheetStatus as TimeSheetStatusType } from "@axa/database-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import ContactName from "@axa/ui/common/ContactName";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchDateRange, SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import TimeSheetStatus from "@/components/symbols/TimeSheetStatus";
import { useUser } from "@/contexts/User";
import { TimeSheetMenu } from "@/widgets/actions/billing/time-sheet";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no time sheets yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search time sheets...",
      export: "Export Time Sheets",
    },
    headers: {
      name: "Name",
      provider: "Provider",
      total: "Total Amount",
      rate: "Billing Rate",
      type: "Billing Type",
      hours: "Hours",
      paymentRate: "Payment Rate",
      paymentType: "Payment Type",
      status: "Status",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All Statuses",
        PENDING: "Pending",
        APPROVED: "Approved",
        REJECTED: "Rejected",
        ASSIGNED: "Assigned",
      },
    },
    pendingAssign: "Pending Assignment",
  },
  links: {
    billingTimeSheet: "/app/billing/time-sheets/[id]",
    timeSheet: "/app/finances/time-sheets/[id]",
  },
};

export const groupName = "timesheet";

export type TimeSheetsQueryResult = RouterOutputs["timeSheets"]["getMany"];
export type TimeSheetsType = TimeSheetsQueryResult["timeSheets"];
export type TimeSheetType = TimeSheetsType[number];

interface ListTimeSheetsProps extends PropsWithChildren {
  loading?: boolean;
  timeSheets?: TimeSheetsQueryResult;
  billing?: boolean;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

// Filters configuration
const filters = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "PENDING", label: i18n.en.filters.options.PENDING },
      { value: "APPROVED", label: i18n.en.filters.options.APPROVED },
      { value: "REJECTED", label: i18n.en.filters.options.REJECTED },
      { value: "ASSIGNED", label: i18n.en.filters.options.ASSIGNED },
    ] satisfies { value: TimeSheetStatusType | null; label: string }[],
  },
];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListTimeSheets({
  group = groupName,
  loading = false,
  timeSheets,
  billing = false,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListTimeSheetsProps) {
  const user = useUser();

  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!timeSheets) return undefined;
    return {
      items: timeSheets.timeSheets,
      total: timeSheets.total,
    };
  }, [timeSheets]);

  const timeSheetLink = billing
    ? i18n.links.billingTimeSheet
    : i18n.links.timeSheet;

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
          <SearchDateRange
            size="sm"
            group={group}
            loading={loading}
            className="w-fit"
          />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<TimeSheetType>(
            [
              "id",
              "status",
              "total",
              "billingType",
              "billingRate",
              "hours",
              ...(user.isBilling
                ? (["paymentType", "paymentRate"] as const)
                : []),
              "provider",
            ],
            {
              filename: "timesheets_export.csv",
              label: i18n.en.actions.export,
              resolvers: {
                provider: (provider) => {
                  if (provider && typeof provider === "object") {
                    const p = provider as {
                      firstName?: string;
                      lastName?: string;
                    };
                    return (
                      `${p.firstName || ""} ${p.lastName || ""}`.trim() ||
                      "Unassigned"
                    );
                  }
                  return "Unassigned";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Time Sheet Actions",
            render: (context: ActionContext<TimeSheetType>) => {
              if (context.type === "row" && user.isBilling) {
                return (
                  <TimeSheetMenu
                    billing={billing}
                    timeSheet={context.row}
                    variant="ghost"
                  />
                );
              }
              return null;
            },
          },
        ],
        [billing, user.isBilling],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <TimeSheetStatus status={row.getValue("status")} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.getValue(id));
              },
            },
            {
              accessorKey: "id",
              enableHiding: false,
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.provider}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={timeSheetLink.replace("[id]", row.original.id)}
                    className="w-full font-semibold hover:text-primary"
                  >
                    {row.original.provider ? (
                      <div>
                        <ContactName
                          showCopyButton={false}
                          name={[
                            row.original.provider.firstName,
                            row.original.provider.lastName,
                          ].join(" ")}
                        />
                      </div>
                    ) : (
                      <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                        {i18n.en.pendingAssign}
                      </div>
                    )}
                  </Link>
                </div>
              ),
            },
            {
              id: "total",
              accessorKey: "total",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.total}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("total")} />,
            },
            {
              id: "type",
              accessorKey: "billingType",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.type}
                />
              ),
              cell: ({ row }) => (
                <Badge variant="outline">{row.getValue("type")}</Badge>
              ),
            },
            {
              id: "rate",
              accessorKey: "billingRate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.rate}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("rate")} />,
            },
            {
              id: "hours",
              accessorKey: "hours",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.hours}
                />
              ),
              cell: ({ row }) => {
                return <p>{row.getValue("hours")}</p>;
              },
            },
            ...(user.isBilling
              ? ([
                  {
                    id: "paymentType",
                    accessorKey: "paymentType",
                    header: ({ column }) => (
                      <DataTableColumnHeader
                        column={column}
                        title={i18n.en.headers.paymentType}
                      />
                    ),
                    cell: ({ row }) => (
                      <Badge variant="outline">
                        {row.getValue("paymentType")}
                      </Badge>
                    ),
                  },
                  {
                    id: "paymentRate",
                    accessorKey: "paymentRate",
                    header: ({ column }) => (
                      <DataTableColumnHeader
                        column={column}
                        title={i18n.en.headers.paymentRate}
                      />
                    ),
                    cell: ({ row }) => (
                      <Currency amount={row.getValue("paymentRate")} />
                    ),
                  },
                ] as ColumnDef<TimeSheetType, TimeSheetType[]>[])
              : []),
          ] as ColumnDef<TimeSheetType, TimeSheetType[]>[],
        [user.isBilling, timeSheetLink],
      )}
    >
      {children}
    </Table>
  );
}
