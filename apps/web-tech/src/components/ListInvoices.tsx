"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";
import { format } from "date-fns";

import type { RouterOutputs } from "@axa/api-tech";
import type { InvoiceStatus } from "@axa/database-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchDateRange, SearchText } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import InvoiceStatusBadge from "@/components/symbols/InvoiceStatus";
import { InvoiceMenu } from "@/widgets/actions/billing/invoice";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no invoices yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search invoices...",
      export: "Export Invoices",
    },
    headers: {
      name: "Name",
      pending: "Pending",
      balance: "Balance",
      period: "Period",
      due: "Due Date",
      status: "Status",
      organization: "Organization",
    },
    filters: {
      status: "Status",
      options: {
        ALL: "All Statuses",
        OPEN: "Open",
        PAID: "Paid",
        DUE: "Due",
        VOID: "Void",
      },
    },
  },
  links: {
    billing: "/app/billing/invoices/[id]",
    invoice: "/app/finances/invoices/[id]",
  },
};

export const groupName = "invoice";

export type InvoicesQueryResult = RouterOutputs["invoices"]["getMany"];
export type InvoicesType = InvoicesQueryResult["invoices"];
export type InvoiceType = InvoicesType[number];

interface ListInvoicesProps extends PropsWithChildren {
  loading?: boolean;
  invoices?: InvoicesQueryResult;
  billing?: boolean;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

// Filters configuration
const filters = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "OPEN", label: i18n.en.filters.options.OPEN },
      { value: "PAID", label: i18n.en.filters.options.PAID },
      { value: "DUE", label: i18n.en.filters.options.DUE },
      { value: "VOID", label: i18n.en.filters.options.VOID },
    ] satisfies { value: InvoiceStatus | null; label: string }[],
  },
];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListInvoices({
  group = groupName,
  loading = false,
  invoices,
  billing = false,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListInvoicesProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!invoices) return undefined;
    return {
      items: invoices.invoices,
      total: invoices.total,
    };
  }, [invoices]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
          <SearchDateRange
            size="sm"
            group={group}
            loading={loading}
            className="w-fit"
          />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<InvoiceType>(
            [
              "id",
              "name",
              "status",
              "balance",
              "pending",
              "timePeriodStart",
              "dueDate",
              "organization",
            ],
            {
              filename: "invoices_export.csv",
              label: i18n.en.actions.export,
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
                timePeriodStart: (date) => {
                  if (date instanceof Date) {
                    return format(date, "LLLL yyyy");
                  }
                  return "";
                },
                dueDate: (date) => {
                  if (date instanceof Date) {
                    return format(date, "PPP");
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Invoice Actions",
            render: (context: ActionContext<InvoiceType>) => {
              if (context.type === "row") {
                return <InvoiceMenu invoice={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <InvoiceStatusBadge status={row.getValue("status")} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.getValue(id));
              },
            },
            {
              id: "id",
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => {
                const link = billing ? i18n.links.billing : i18n.links.invoice;

                return (
                  <div className="flex w-fit flex-col">
                    <Link
                      href={link.replace("[id]", row.original.id)}
                      className="w-full font-semibold hover:text-primary"
                    >
                      #{row.getValue("id") ?? "000"}
                    </Link>
                  </div>
                );
              },
              enableHiding: false,
            },
            {
              id: "balance",
              accessorKey: "balance",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.balance}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("balance")} />,
            },
            {
              id: "pending",
              accessorKey: "pending",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.pending}
                />
              ),
              cell: ({ row }) => <Currency amount={row.getValue("pending")} />,
            },
            {
              id: "period",
              accessorKey: "timePeriodStart",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.period}
                />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const period = row.getValue("period") as Date;
                const date = new Date(
                  period.getTime() + period.getTimezoneOffset() * 60000,
                );

                return (
                  <p className="text-nowrap">{format(date, "LLLL yyyy")}</p>
                );
              },
            },
            {
              id: "due",
              accessorKey: "dueDate",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.due}
                />
              ),
              cell: ({ row }) => {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                const due = row.getValue("due") as Date;
                const date = new Date(
                  due.getTime() + due.getTimezoneOffset() * 60000,
                );

                return <p className="text-nowrap">{format(date, "PPP")}</p>;
              },
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <Badge variant="outline" className="text-xs">
                  {row.getValue("organization")}
                </Badge>
              ),
            },
          ] as ColumnDef<InvoiceType, InvoiceType[]>[],
        [billing],
      )}
    >
      {children}
    </Table>
  );
}
