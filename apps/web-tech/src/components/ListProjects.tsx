"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Link from "next/link";

import type { RouterOutputs } from "@axa/api-tech";
import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";

import { ProjectMenu } from "@/widgets/actions/work-order/project";
import { SearchOrganizations } from "@/widgets/selectors/select-organization";

const i18n = {
  en: {
    noData: "There are no projects yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search projects...",
      add: "Add Project",
    },
    noProjects: "There are no projects",
    noDescription: "No description",
    headers: {
      name: "Name",
      organization: "Organization",
    },
  },
  links: {
    projects: "/app/projects/[id]",
  },
};

export const groupName = "project";

export type ProjectsQueryResult = RouterOutputs["projects"]["getMany"];
export type ProjectsType = ProjectsQueryResult["projects"];
export type ProjectType = ProjectsType[number];

interface ListProjectsProps extends PropsWithChildren {
  loading?: boolean;
  projects?: ProjectsQueryResult;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListProjects({
  group = groupName,
  loading = false,
  projects,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListProjectsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!projects) return undefined;
    return {
      items: projects.projects,
      total: projects.total,
    };
  }, [projects]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div>
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ProjectType>(
            ["id", "name", "description", "organization"],
            {
              filename: "projects_export.csv",
              label: "Export Selected Projects",
              resolvers: {
                organization: (org) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return org.name || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Project Actions",
            render: (context: ActionContext<ProjectType>) => {
              if (context.type === "row") {
                return <ProjectMenu project={context.row} variant="ghost" />;
              }
              return null;
            },
          },
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <div className="flex w-fit flex-col">
                  <Link
                    href={i18n.links.projects.replace("[id]", row.original.id)}
                    className="w-full font-semibold hover:text-primary"
                  >
                    {row.getValue("name")}
                  </Link>
                  <p className="text-xs text-muted-foreground">
                    {row.original.description ?? i18n.en.noDescription}
                  </p>
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization organization={row.original.organization} />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization.id);
              },
            },
          ] as ColumnDef<ProjectType, ProjectType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
