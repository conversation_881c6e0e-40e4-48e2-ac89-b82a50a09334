# TODO

List of items need doing (make into GH Issues)

- [ ] update org account, invitation and members components
- [ ] refactor all forms and move to widgets to consolidate data usage components
- [ ] tables, move to widgets and combine data with ready-made list table components
- [ ] tables, enhance fields for some tables
- [ ] tables/symbols - move all badges (status, type, etc) to dedicated components to be reused and enhance
- [ ] timesheet table - add ability to search by provider
- [ ] refactor work-orders and break into granular pieces
- [ ] work order - reuse messages, actions and anything else from core ui lib
- [ ] work order - type and category field need to be sequential
- [ ] storybook (add stories for testing)
- [ ] finish cookie implementation
- [ ] Analytics - capture more details on usage and user behavior

ui

- [ ] enhance selector loading in dialog mode
- [ ] add clear all button to selectors
- [ ] fix location preview with link
- [ ]

fixes

- [ ] document form
- [ ] service date form (work order) invalid date
- [ ] order forms for editing resources not submitting and no error message (eg, contact person, document)
