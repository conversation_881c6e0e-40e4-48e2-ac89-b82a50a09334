{"name": "@axa/native", "version": "0.1.0", "private": true, "repository": {"type": "git", "url": "https://github.com/axa-professionals/axa-professionals.git", "directory": "apps/native"}, "main": "expo-router/entry", "scripts": {"clean": "git clean -xdf .expo .turbo node_modules", "disabled:dev": "expo start", "dev:android": "expo start --android", "dev:ios": "expo start --ios", "android": "expo run:android", "ios": "expo run:ios", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit", "storybook": "storybook dev -p 6011", "storybook:ci": "storybook dev -p 6011 --no-open --ci", "build-storybook": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@bacons/text-decoder": "^0.0.0", "@clerk/clerk-expo": "^2.12.0", "@expo/metro-config": "^0.20.14", "@sentry/react-native": "^6.14.0", "@shopify/flash-list": "^1.8.2", "@tanstack/react-query": "^5.79.0", "@trpc/client": "^11.1.4", "@trpc/react-query": "^11.1.4", "@trpc/server": "^11.1.4", "expo": "^53.0.9", "expo-constants": "^17.1.6", "expo-dev-client": "^5.1.8", "expo-linking": "^7.1.5", "expo-router": "^5.0.7", "expo-secure-store": "^14.2.3", "expo-splash-screen": "^0.30.8", "expo-status-bar": "^2.2.3", "nativewind": "^4.1.23", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "^0.79.2", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "superjson": "^2.2.2"}, "devDependencies": {"@axa/api-medical": "workspace:*", "@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.4", "@chromatic-com/storybook": "^4.0.0-0", "@storybook/addon-a11y": "^9.0.1", "@storybook/addon-docs": "^9.0.1", "@storybook/addon-onboarding": "^9.0.1", "@storybook/addon-vitest": "^9.0.1", "@storybook/react-native-web-vite": "^9.0.1", "@types/babel__core": "^7.20.5", "@types/react": "^19.1.6", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "eslint": "^9.28.0", "eslint-plugin-storybook": "^9.0.1", "playwright": "^1.52.0", "prettier": "^3.5.3", "react-native-web": "^0.20.0", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}