import { dirname, join } from "path";
import type { StorybookConfig } from "@storybook/react-native-web-vite";

function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, "package.json")));
}

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    // getAbsolutePath("@storybook/addon-onboarding"),
    // getAbsolutePath("@chromatic-com/storybook"),
    getAbsolutePath("@storybook/addon-docs"),
    getAbsolutePath("@storybook/addon-a11y"),
    getAbsolutePath("@storybook/addon-vitest"),
  ],
  framework: {
    name: getAbsolutePath("@storybook/react-native-web-vite"),
    options: {
      pluginReactOptions: {
        jsxImportSource: "nativewind",
      },
    },
  },
};
export default config;
