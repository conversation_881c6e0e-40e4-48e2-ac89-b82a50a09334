import "@bacons/text-decoder/install";

import Constants from "expo-constants";
import { Stack } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { StatusBar } from "expo-status-bar";
import { Clerk<PERSON>rovider } from "@clerk/clerk-expo";
import * as Sentry from "@sentry/react-native";
import { useColorScheme } from "nativewind";

import { TRPCProvider } from "@/api/client";

import "../styles.css";

Sentry.init({
  dsn: "https://<EMAIL>/4507425480572928",
  tracesSampleRate: 1.0,
  _experiments: {
    profilesSampleRate: 1.0,
  },
});

const tokenCache = {
  async getToken(key: string) {
    try {
      return SecureStore.getItemAsync(key);
    } catch (err) {
      console.log("Error getting token", err);
      return null;
    }
  },
  async saveToken(key: string, value: string) {
    try {
      return SecureStore.setItemAsync(key, value);
    } catch (err) {
      console.log("Error saving token", err);
      return;
    }
  },
};

// This is the main layout of the app
// It wraps your pages with the providers they need
export default function RootLayout() {
  const { colorScheme } = useColorScheme();
  return (
    <TRPCProvider>
      <ClerkProvider
        tokenCache={tokenCache}
        publishableKey={
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          Constants.expoConfig!.extra!.clerkPublishableKey as string
        }
      >
        {/*
          The Stack component displays the current page.
          It also allows you to configure your screens 
        */}
        <Stack
          screenOptions={{
            headerStyle: {
              backgroundColor: "#f472b6",
            },
            contentStyle: {
              backgroundColor: colorScheme == "dark" ? "#09090B" : "#FFFFFF",
            },
          }}
        />

        <StatusBar />
      </ClerkProvider>
    </TRPCProvider>
  );
}
