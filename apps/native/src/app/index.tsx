import { Button, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { SignedIn, SignedOut, useUser } from "@clerk/clerk-expo";
import * as Sentry from "@sentry/react-native";

// import SignUpScreen from "@/components/SignUpScreen";
import SignInScreen from "@/components/SignInScreen";

// import SignInWithOAuth from '@/components/SignInWithOAuth';

// import { FlashList } from "@shopify/flash-list";

// import { api } from "@/utils/api";

function UseUserExample() {
  const { isLoaded, isSignedIn, user } = useUser();

  if (!isLoaded || !isSignedIn) {
    return null;
  }

  return <Text>Hello, {user.firstName} welcome to Hedge</Text>;
}

export default function Index() {
  return (
    <SafeAreaView className="bg-background">
      {/* Changes page title visible on the header */}
      <Stack.Screen options={{ title: "Home Page" }} />
      <View className="h-full w-full bg-background p-4">
        <Button
          title="Try!"
          onPress={() => {
            Sentry.captureException(new Error("First error"));
          }}
        />
        <SignedIn>
          <UseUserExample />
        </SignedIn>
        <SignedOut>
          {/* <SignUpScreen /> */}
          <SignInScreen />
          {/* <SignInWithOAuth /> */}
        </SignedOut>
      </View>
    </SafeAreaView>
  );
}
