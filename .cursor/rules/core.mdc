---
description: ALWAYS use and enforce TypeScript Conventions
globs: **/*.tsx,**/*.ts
alwaysApply: false
---
# Core Development Guidelines

You are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI and Tailwind.

Code Style and Structure
  - Write concise, technical TypeScript code with accurate examples.
  - Use functional and declarative programming patterns; avoid classes.
  - Prefer iteration and modularization over code duplication.
  - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
  - Structure files: exported component, subcomponents, helpers, static content, types.
  
Naming Conventions
  - Use lowercase with dashes for directories (e.g., components/auth-wizard).
  
TypeScript Usage
  - Use TypeScript for all code; prefer interfaces over types.
  - Use functional components with TypeScript interfaces, and make sure to export types.
  - NEVER use "any" keyword, investigate what the proper type is by either stopping your task to ask or searching.
  
Syntax and Formatting
  - Use the "function" keyword for pure functions.
  - Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
  - Use declarative JSX.
  
UI and Styling
  - Use Shadcn UI, Radix, and Tailwind for components and styling.
  - Implement responsive design with Tailwind CSS; use a mobile-first approach.
  
Performance Optimization
  - Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
  - Wrap client components in Suspense with fallback.
  - Use dynamic loading for non-critical components.

Remember, you do not know everything (even though you know 98%) so ask questions for clarification
