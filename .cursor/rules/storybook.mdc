---
description: 
globs: **/__stories__/**
alwaysApply: false
---
# API Workflows

This document outlines the key workflows and steps to follow when working with our API layer and building components.

## Working with API Types in Components

When working with components that interact with our API, follow these steps:

1. **Understand the Data Model**

   - Check the database schema in the relevant `@axa/db-[domain]` package
   - Identify the primary entity and its relationships
   - Note required fields, defaults, and constraints

2. **Explore the API Layer**

   - Locate the relevant router in `@axa/api-[domain]/src/router/`
   - Review available procedures (get, getMany, create, update, etc.)
   - Understand input validation schemas and requirements
   - DO NOT PROCEED WITHOUT VERIFYING THE route(s) in question

3. **Analyze Selection Patterns**

   - Check what relations can be included via the `include` parameter
   - Determine which includes are needed for your component
   - Note any nested includes required for deeper relationships

4. **Plan Your Component Structure**

   - Decide on separation between data and presentation layers
   - Define prop types based on API return types with specific includes
   - Plan loading, error, and empty states

5. **Implement Data Layer**

   - Create data fetching logic using appropriate hooks
   - Set up proper error handling
   - Implement caching strategy if needed

6. **Implement Presentation Layer**

   - Build UI components that receive data as props
   - Ensure components are typed correctly based on API selection
   - Create variations based on different data shapes if needed

7. **Test Your Implementation**
   - Test with different include combinations
   - Verify error handling works correctly
   - Test loading states and transitions

## Type Conventions and Imports

We have specific conventions for importing and using types from our API layer:

### Importing Types from API

```tsx
// Import types for API responses, requests, and errors
import type { RouterError, RouterInputs, RouterOutputs } from "@/api";
// Import enums directly from the API (these come from Prisma) - can also be used as types
import { ContractType, PaymentMethod, UserStatus } from "@/api";
```

### Using API Types in Components

> **Note:** Explicit type definitions are best suited for presentational components. For data fetching components, prefer to use direct type inference from procedure calls.

```tsx
import type { RouterInputs, RouterOutputs } from "@/api";
import { UserStatus } from "@/api";
import { api } from "@/api/client";

// Get the type of a specific API response
type User = RouterOutputs["users"]["get"];

// Get the type of a specific API input
type CreateUserInput = RouterInputs["users"]["create"];

// Define color mapping outside the component function
const statusColorMap = {
  [UserStatus.ACTIVE]: "green",
  [UserStatus.PENDING]: "yellow",
};

function getStatusColor(userStatus: UserStatus) {
  return statusColorMap[userStatus] || "gray";
}
// Use enums in your component with named function
function UserStatusBadge({ status }: { status: UserStatus }) {
  return <Badge color={getStatusColor(status)}>{status}</Badge>;
}

// Example of type inference in data fetching component
function UserProfile({ userId }: { userId: string }) {
  // Type is automatically inferred from the procedure
  const { data } = api.users.get.useQuery({
    id: userId,
    include: { profile: true },
  });

  if (!data) return null;

  // Pass data to presentational component
  return <UserCard user={data} />;
}
```

## Data Layer vs. Presentation Layer

### Data Layer Responsibilities

- **Data Fetching**

  - Make API calls using appropriate hooks
  - Handle pagination and filtering
  - Manage loading states

- **Data Transformation**

  - Format data for presentation if needed
  - Combine data from multiple sources if required
  - Compute derived values

- **State Management**

  - Manage cache invalidation
  - Handle optimistic updates
  - Track loading and error states

- **Error Handling**
  - Catch and process API errors
  - Provide meaningful error messages
  - Implement retry logic if appropriate

### Presentation Layer Responsibilities

- **Rendering UI**

  - Display data in appropriate format
  - Implement responsive layouts
  - Apply styling and animations

- **User Interaction**

  - Handle user inputs
  - Trigger callbacks provided by data layer
  - Manage local UI state (e.g., expanded/collapsed)

- **Accessibility**

  - Ensure proper semantic markup
  - Implement keyboard navigation
  - Support screen readers

- **Variations**
  - Handle different data shapes
  - Implement conditional rendering
  - Support different visual states

## API Selection Workflow

1. **Identify Required Data**

   - Determine what data your component needs to display
   - List all fields and relationships required

2. **Check Available Includes**

   - Review the API documentation or code for available includes
   - Note any limitations or performance considerations

3. **Define Selection Pattern**

   - Specify includes based on your requirements
   - Consider nested includes for deeper relationships
   - Be mindful of over-fetching

4. **Type Your Component**

   - Use TypeScript to derive types from your selection pattern
   - Ensure type safety throughout your component
   - Document required includes in component props

5. **Implement Data Fetching**
   - Use the selection pattern in your API calls
   - Handle loading and error states appropriately
   - Consider caching and refetching strategies

## Client-Side API Consumption Examples

### Basic Query with Includes

```typescript
const { data, isLoading } = api.users.get.useQuery({
  id: userId,
  include: {
    profile: true,
    organization: true,
  },
});
```

### Mutation with Optimistic Updates

```typescript
const updateUser = api.users.update.useMutation({
  onMutate: async (variables) => {
    // Optimistic update logic
  },
  onError: (error) => {
    // Error handling
  },
});
```

### Suspense Query

```typescript
const userData = api.users.get.useSuspenseQuery({
  id: userId,
  include: { profile: true },
});
```

## Server-Side API Consumption Examples

### Direct API Call

```typescript
const userData = await api.users.get({
  id: userId,
  include: { profile: true },
});
```

### Error Handling

```typescript
try {
  const data = await api.users.getMany({
    pageSize: 10,
    include: { profile: true },
  });
} catch (error) {
  // Handle error
}
```

## Type Safety Best Practices

1. **Derive Types from API Selection**

   - Use `RouterOutputs` and `RouterInputs` to get exact types
   - Let TypeScript infer the shape based on includes

2. **Document Required Includes**

   - Note required includes in component documentation
   - Use JSDoc comments to explain data requirements

3. **Use Discriminated Unions**

   - Create union types for different data shapes
   - Use type narrowing for conditional rendering

4. **Avoid Type Assertions**
   - Don't use `as` to force types
   - Let the type system validate your data access
