---
description: CRITICAL API router conventions, please adhere
globs: packages/api-*/**/*.ts
alwaysApply: false
---
# @axa/api-[domain] Conventions

## Package Structure

```
api-[domain]/
├── src/
│ ├── router/ # tRPC route definitions
│ ├── functions/ # Server-side functions (equivalent to route.ts)
│ ├── jobs/ # Server job implementations (follows route.ts structure with helpers and some exceptions)
│ ├── hooks/ # Webhook implementations (follows route.ts structure)
│ └── lib/ # Shared utilities and helpers
├── __tests__/ # Test files following router structure
├── __mocks__/ # Mock implementations for testing
└── env.ts # Environment configuration
```

## Environment Configuration

Required environment variables are strictly typed using `@t3-oss/env-nextjs`. The environemnt variables are inherited from various sources (`@axa/lib`, etc). The files are located at:

- For AXA Med, it is [env.ts](mdc:packages/db-medical/env.ts)
- For AXA Tech, it is [env.ts](mdc:packages/api-tech/env.ts) 

## Coding Practices

### Router Pattern

- Each domain entity has its own router (e.g., organizations, users, contracts)
- Standard CRUD operations follow consistent patterns:
  - `create`: Create new entity
  - `getMany`: List entities with pagination
  - `get`: Get single entity by ID
  - `update`: Update entity
  - `delete`: Delete entity

> There are uncommon exceptions to this pattern.

### Input Validation

- Use Zod for input validation in tRPC procedures
- Standard input structure for list operations:

```typescript
interface ListOptions {
  query?: string; // Search query
  pageSize?: number; // Items per page
  pageNumber?: number; // Page number
  include?: {
    // Relations to include and other options (eg, deleted items, metadata, etc)
    [key: string]: boolean;
  };
}
```

### Error Handling

Use standard tRPC error codes:

```typescript
throw new TRPCError({
  code: "NOT_FOUND",
  message: "Entity not found",
});
```

### Common codes:

- `NOT_FOUND`: Resource doesn't exist
- `FORBIDDEN`: User lacks permission
- `CONFLICT`: Resource already exists
- `PRECONDITION_FAILED`: Operation prerequisites not met

### Testing

- Mock external dependencies (Prisma, Clerk) in `__mocks__/`
- Test files mirror router structure
- Standard test pattern for each router:
  - Create entity
  - List entities
  - Get single entity
  - Update entity
  - Delete entity

## Business Logic

1. **Data Access Layer**

   - Prisma for database operations
   - Strict typing for all database models
   - Use includes pattern for relation loading

2. **Authentication & Authorization**

   - Clerk for user authentication
   - Role-based access control
   - Organization-level permissions

3. **Integration Points**

   - Stripe for payment processing
   - Documenso for document signing
   - Google Maps for location services
   - OpenAI/Perplexity for AI features

4. **Event Handling**

   - Webhook implementations in `hooks/`
   - Event aggregation for notifications
   - Contract/offer expiration monitoring

5. **Background Jobs**
   - Implemented in `jobs/` directory
   - Handle routine tasks like expiration checks
   - Process notifications and alerts

## Related Packages

- `@axa/db-[domain]`: Database schema and Prisma client
  - For `@axa/api-medical`, [package.json](mdc:packages/db-medical/package.json)
  - For `@axa/api-tech`, [package.json](mdc:packages/db-tech/package.json)
- `@axa/lib`: Shared utilities and integrations [package.json](mdc:packages/lib/package.json)
- `@axa/emails`: Email templates and rendering [package.json](mdc:packages/emails/package.json)

## Best Practices

- Keep procedures focused and modular
- Use server actions for form submissions
- Implement proper error handling and logging
- Follow project naming conventions
- Keep Stripe-related logic in dedicated modules
