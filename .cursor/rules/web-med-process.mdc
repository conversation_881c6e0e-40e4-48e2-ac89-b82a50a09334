---
description: 
globs: apps/web-med/**/*.ts
alwaysApply: false
---
# Component Development Guide for Web-Med

This guide outlines the step-by-step process for developing components in the `apps/web-med/src/www` folder.

## Development Process

### 1. Examine the Database Schema (CRITICAL FIRST STEP)

Always start by examining the Prisma schema to understand the data model:
- Location: `packages/db-medical/prisma/schema.prisma` [schema.prisma](mdc:packages/db-medical/prisma/schema.prisma)
- Understand the entity structure, relationships, and available fields
- Note the field types, enums, and relationships between models
- Pay special attention to optional fields and their default values

### 2. Understand the API Structure (CRITICAL SECOND STEP)

After understanding the database schema, examine the API router:
- Location: `packages/api-medical/src/router/[entity-type]/[entity-name].ts`
- Look for the `selection` object to understand what fields are included in the API response
- Check the input validation schemas to understand what parameters are required
- Understand the available procedures (get, getMany, create, update, delete, etc.)

### 3. Set Up the Data Layer (index.tsx)

- Create the data fetching component with proper includes based on the API structure
- Use the correct tRPC query with all necessary includes
- Ensure proper typing for props and return values
- Handle mutations and their states
- For list views, use search params hooks to get filter values from URL

### 4. Check Shared Libraries and Components

Before implementing your component, check the shared libraries and components that are available:

#### Shared UI Library (`packages/ui`)

- Contains Shadcn UI components and other common components
- Used across the entire codebase for consistency
- Check for existing components that match your needs

```tsx
// Example: Using components from the shared UI library
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";
import { Tabs } from "@axa/ui/primitives/tabs";
```

#### Search Components (`@axa/ui/search`)

- Components for URL-driven filtering, sorting, and pagination
- Hooks to get values from URL in data layer
- Components to render inputs in presentation layer

```tsx
// Hooks for data layer
// Components for presentation layer
import {
  SearchFilter,
  SearchPagination,
  SearchText,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";
```

#### App-Specific Components (`apps/web-med/src/components`)

- **Common Components** (`components/common`): Contains badges and displays for database enums

  ```tsx
  // Example: Using a status badge for an enum
  import ContractStatusBadge from "@/components/common/ContractStatus";

  // In your component
  <ContractStatusBadge status={contract.status} />;
  ```

- **Shared Components** (`components/shared`): General-purpose components specific to the app

  ```tsx
  // Example: Using a shared component
  import { ErrorFallback } from "@/components/shared/Error";

  // In your component
  {
    error && <ErrorFallback error={error} />;
  }
  ```

- **Action Components** (`components/actions`): Pre-built action buttons and forms

  ```tsx
  // Example: Using an action component
  import { CreateContractButton } from "@/components/actions/contract";

  // In your component - still pass your mutation for local state updates
  <CreateContractButton onSuccess={refetch} mutation={createMutation} />;
  ```

> **Important**: When using action components, still pass your mutations from the data layer to ensure proper state updates specific to your page.

### 5. Create/Update Stories

- Create a base mock object that matches the API type structure
- Use Faker.js to generate realistic mock data
- Create variations for different states (loading, error, different statuses)
- Test edge cases (empty arrays, missing optional fields)
- Use the correct enum values from the API

```tsx
// Example: Creating mock data with Faker
import { faker } from "@faker-js/faker";

const mockData = {
  id: faker.string.uuid(),
  title: faker.commerce.productName(),
  createdAt: faker.date.recent(),
  status: ContractStatus.PENDING,
  // Nested objects
  provider: {
    id: faker.string.uuid(),
    person: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
    },
  },
  // Arrays
  agreements: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    status: AgreementStatus.PENDING,
    // ...other fields
  })),
} satisfies RouterOutputs["entityName"]["get"];
```

### 6. Implement the Component

- Start with skeleton and loading states
- Implement error handling
- Build the main component with proper type safety
- Use conditional rendering for optional fields
- Implement responsive design and dark mode support
- Use common components for consistency
- For list views, use search components for filtering and pagination

```tsx
// Example: Component implementation with proper typing
interface EntityProps {
  loading?: boolean;
  error?: RouterError;
  data?: RouterOutputs["entityName"]["get"];
  create?: ReturnType<typeof api.entity.create.useMutation>;
  update?: ReturnType<typeof api.entity.update.useMutation>;
}

export default function Entity({
  loading,
  error,
  data,
  create,
  update,
}: EntityProps) {
  // Implementation using shared components
  if (loading) return <Skeleton />;
  if (error) return <ErrorFallback error={error} />;
  if (!data) return <EmptyState />;

  return (
    <div>
      <h1>{data.title}</h1>
      <ContractStatusBadge status={data.status} />
      {/* Rest of the component */}
    </div>
  );
}
```

## Common Pitfalls to Avoid

- **Not examining the database schema and API structure before implementation**
- Creating custom types instead of using the generated types from tRPC
- Not checking for optional fields before accessing them
- Hardcoding values that should come from the API
- Not handling loading and error states
- Ignoring responsive design and dark mode
- Mixing data fetching and presentation concerns
- Modifying the API without prior approval
- Reinventing components that already exist in shared libraries
- Implementing custom state management for lists when search params are available
- Hardcoding user-facing text instead of using i18n objects

## Best Practices

- **Type Safety**: Always use the generated types from tRPC
- **API Consistency**: Reference the API selection object to ensure you're using all available fields
- **Error Handling**: Implement proper error handling for all API calls
- **Responsive Design**: Ensure components work on all screen sizes
- **Dark Mode**: Support both light and dark modes
- **Accessibility**: Follow accessibility best practices
- **Testing**: Create comprehensive stories for all states
- **Component Reuse**: Check for existing components before creating new ones
- **URL-Driven State**: Use search params for list views and tables
- **Internationalization**: Use i18n objects for all user-facing text

## Form Components

For form components, follow these conventions:

- Place form components in `components/forms/`
- Place field components for specific types in `components/forms/fields/`
- Use React Hook Form for form state management
- Use Zod for validation schemas

```tsx
// Example: Form field for an enum type
export function EntityTypeField({ control, name }: FieldProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>Entity Type</FormLabel>
          <Select value={field.value} onValueChange={field.onChange}>
            {Object.values(EntityType).map((type) => (
              <SelectItem key={type} value={type}>
                {typeLabels[type]}
              </SelectItem>
            ))}
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
```

## Internationalization

We use a simple object-based approach for internationalization. Each component that needs text should define its own `i18n` constant with an `en` property containing all text strings:

```tsx
// Define i18n object at the top of your component file
const i18n = {
  en: {
    title: "Contract Details",
    status: {
      pending: "Waiting for signatures",
      signed: "All parties have signed",
      rejected: "Contract was rejected",
      expired: "Contract has expired",
    },
    actions: {
      view: "View Document",
      sign: "Sign Document",
      reject: "Reject",
    },
    empty: "No contracts found",
    error: "Failed to load contract details",
  },
};

// Use in your component
export default function Contract({ loading, error, contract }: ContractProps) {
  if (loading) return <Skeleton />;
  if (error) return <ErrorFallback message={i18n.en.error} />;
  if (!contract) return <EmptyState message={i18n.en.empty} />;

  return (
    <div>
      <h1>{i18n.en.title}</h1>
      <p>{i18n.en.status[contract.status.toLowerCase()]}</p>
      {/* Rest of component */}
    </div>
  );
}
```

## Quick Reference

1. **Examine Prisma Schema**: Understand the database structure and available fields
2. **Scan API Types**: Understand the data structure from the API
3. **Set Up Data Layer**: Create the data fetching component with proper includes
4. **Check Shared Libraries**: Look for existing components in shared libraries and app components
5. **Update Stories**: Create mock data that matches the API types (using Faker)
6. **Implement Component**: Build the component with proper type safety

Following this process ensures consistency, type safety, and maintainability across all components in the application.
