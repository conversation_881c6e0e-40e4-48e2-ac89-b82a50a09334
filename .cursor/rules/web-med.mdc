---
description: ALWAYS USE FOR FRONT END
globs: apps/web-med/**/*.tsx
alwaysApply: false
---
# Component Development Guide for Web-Med

This guide outlines the step-by-step process for developing components in the `apps/web-med/src/www` folder.

Important files we will use all the time:
- [schema.prisma](mdc:packages/db-medical/prisma/schema.prisma) (medical database schema)
- [index.ts](mdc:packages/api-medical/src/router/index.ts) (router entry)


## Architecture Pattern

We follow a strict separation of concerns between data and presentation layers:

- **Data Layer (`index.tsx`)**: Handles data fetching, mutations, and business logic
- **Presentation Layer (`EntityName.tsx`)**: Pure presentational components that receive data via props

```tsx
// Data Layer (index.tsx)
export function EntityView() {
  // Data fetching, mutations, and business logic
  const data = api.entity.get.useQuery();

  // Pass data to presentation component
  return (
    <Entity data={data.data} loading={data.isLoading} error={data.error} />
  );
}

// Presentation Layer (Entity.tsx)
export default function Entity({ data, loading, error }) {
  // Pure presentation with no direct API calls
}
```

The only exception is the user context, which can be accessed directly in any component as it's globally available and easily mockable.

## URL-Driven State with Search Params

For list views, searching and tables, we use URL search parameters to manage filtering, sorting, and pagination state. This approach has several benefits:

- State persists across page refreshes
- URLs can be shared with the exact same view
- Browser history works naturally with state changes
- Decouples state management from components

We use the search components from `@axa/ui/search` to implement this pattern:

### Data Layer Setup

1. Wrap your component with `SearchParams` provider
2. Use the search hooks to get values from URL
3. Pass these values to your API queries

```tsx
// Data Layer (index.tsx)
import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

export default function EntityListPage() {
  return (
    <Suspense fallback={<EntityListSkeleton />}>
      <SearchParams>
        <EntityListView />
      </SearchParams>
    </Suspense>
  );
}

export function EntityListView() {
  // Get search values from URL
  const query = useSearchTextValue("entity");
  const status = useSearchFilterValue("status", "entity");
  const pagination = useSearchPaginationValue("entity");

  // Use these values in your API query
  const entities = api.entities.getMany.useQuery({
    query,
    status,
    pageSize: pagination.pageSize,
    pageNumber: pagination.pageIndex,
  });

  return (
    <EntityList
      entities={entities.data}
      loading={entities.isLoading}
      error={entities.error}
    />
  );
}
```

### Presentation Layer Implementation

In your presentation component, use the search components to render inputs that update the URL:

```tsx
// Presentation Layer (EntityList.tsx)
import { SearchFilter, SearchPagination, SearchText } from "@axa/ui/search";

export default function EntityList({ entities, loading, error }) {
  return (
    <div>
      <div className="flex gap-4">
        <SearchText placeholder="Search entities..." namespace="entity" />
        <SearchFilter
          options={[
            { label: "All", value: "" },
            { label: "Active", value: "ACTIVE" },
            { label: "Inactive", value: "INACTIVE" },
          ]}
          name="status"
          namespace="entity"
        />
      </div>

      {/* Table or list of entities */}
      <table>{/* ... */}</table>

      <SearchPagination totalItems={entities?.total || 0} namespace="entity" />
    </div>
  );
}
```

This pattern ensures that your list views and tables have consistent filtering, sorting, and pagination behavior across the application.

## Development Process

### 1. Examine the Prisma Schema

- Start by examining the Prisma schema in `packages/db-medical/prisma/schema.prisma`
- Understand the entity structure, relationships, and available fields
- Note the field types, enums, and relationships between models
- This gives you a complete picture of what data is available in the database

```prisma
// Example: Prisma schema for Contract model
model Contract {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  status    ContractStatus @default(DRAFT)
  type      ContractType
  title     String

  // Relationships
  provider       Provider?     @relation(fields: [providerId], references: [id])
  providerId     String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?

  // Other fields and relationships
}

// Enums
enum ContractStatus {
  DRAFT
  PENDING
  SIGNED
  REJECTED
  EXPIRED
}
```

### 2. Understand the API Types

- Locate the relevant tRPC router in `packages/api-medical/src/router/`
- Examine the `selection` object to understand the exact data structure
- Note the available fields and their types
- Check for nested selections and includes
- Identify enums and other special types

```tsx
// Example: Understanding contract selections in the API
const selection = {
  contract: {
    id: true,
    title: true,
    type: true,
    status: true,
    // ...other fields
  },
  // Related entities
  agreement: {
    /* fields */
  },
  signature: {
    /* fields */
  },
  // ...other entities
};
```

### 3. Set Up the Data Layer (index.tsx)

- Create the data fetching component with proper includes
- Use the correct tRPC query with all necessary includes
- Ensure proper typing for props and return values
- Handle mutations and their states
- For list views, use search params hooks to get filter values from URL

```tsx
// Example: Setting up data layer with includes and mutations
export function EntityView() {
  // For list views, get search params from URL
  const query = useSearchTextValue("entity");
  const status = useSearchFilterValue("status", "entity");

  // Data fetching with includes
  const data = api.entity.getMany.useQuery({
    query,
    status,
    include: {
      relatedEntity1: true,
      relatedEntity2: true,
    },
  });

  // Mutations
  const createMutation = api.entity.create.useMutation();
  const updateMutation = api.entity.update.useMutation();

  // Pass everything to presentation layer
  return (
    <Entity
      data={data.data}
      loading={data.isLoading}
      error={data.error}
      create={createMutation}
      update={updateMutation}
    />
  );
}

// Wrap with SearchParams for list views
export default function EntityPage() {
  return (
    <Suspense fallback={<EntitySkeleton />}>
      <SearchParams>
        <EntityView />
      </SearchParams>
    </Suspense>
  );
}
```

### 4. Check Shared Libraries and Components

Before implementing your component, check the shared libraries and components that are available:

#### Shared UI Library (`packages/ui`)

- Contains Shadcn UI components and other common components
- Used across the entire codebase for consistency
- Check for existing components that match your needs

```tsx
// Example: Using components from the shared UI library
import { Button } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";
import { Tabs } from "@axa/ui/primitives/tabs";
```

#### Search Components (`@axa/ui/search`)

- Components for URL-driven filtering, sorting, and pagination
- Hooks to get values from URL in data layer
- Components to render inputs in presentation layer

```tsx
// Hooks for data layer
// Components for presentation layer
import {
  SearchFilter,
  SearchPagination,
  SearchText,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";
```

#### App-Specific Components (`apps/web-med/src/components`)

- **Common Components** (`components/common`): Contains badges and displays for database enums

  ```tsx
  // Example: Using a status badge for an enum
  import ContractStatusBadge from "@/components/common/ContractStatus";

  // In your component
  <ContractStatusBadge status={contract.status} />;
  ```

- **Shared Components** (`components/shared`): General-purpose components specific to the app

  ```tsx
  // Example: Using a shared component
  import { ErrorFallback } from "@/components/shared/Error";

  // In your component
  {
    error && <ErrorFallback error={error} />;
  }
  ```

- **Action Components** (`components/actions`): Pre-built action buttons and forms

  ```tsx
  // Example: Using an action component
  import { CreateContractButton } from "@/components/actions/contract";

  // In your component - still pass your mutation for local state updates
  <CreateContractButton onSuccess={refetch} mutation={createMutation} />;
  ```

> **Important**: When using action components, still pass your mutations from the data layer to ensure proper state updates specific to your page.

### 5. Create/Update Stories

- Create a base mock object that matches the API type structure
- Use Faker.js to generate realistic mock data
- Create variations for different states (loading, error, different statuses)
- Test edge cases (empty arrays, missing optional fields)
- Use the correct enum values from the API

```tsx
// Example: Creating mock data with Faker
import { faker } from "@faker-js/faker";

const mockData = {
  id: faker.string.uuid(),
  title: faker.commerce.productName(),
  createdAt: faker.date.recent(),
  status: ContractStatus.PENDING,
  // Nested objects
  provider: {
    id: faker.string.uuid(),
    person: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
    },
  },
  // Arrays
  agreements: Array.from({ length: 3 }, () => ({
    id: faker.string.uuid(),
    status: AgreementStatus.PENDING,
    // ...other fields
  })),
} satisfies RouterOutputs["entityName"]["get"];
```

### 6. Implement the Component

- Start with skeleton and loading states
- Implement error handling
- Build the main component with proper type safety
- Use conditional rendering for optional fields
- Implement responsive design and dark mode support
- Use common components for consistency
- For list views, use search components for filtering and pagination

```tsx
// Example: Component implementation with proper typing
interface EntityProps {
  loading?: boolean;
  error?: RouterError;
  data?: RouterOutputs["entityName"]["get"];
  create?: ReturnType<typeof api.entity.create.useMutation>;
  update?: ReturnType<typeof api.entity.update.useMutation>;
}

export default function Entity({
  loading,
  error,
  data,
  create,
  update,
}: EntityProps) {
  // Implementation using shared components
  if (loading) return <Skeleton />;
  if (error) return <ErrorFallback error={error} />;
  if (!data) return <EmptyState />;

  return (
    <div>
      <h1>{data.title}</h1>
      <ContractStatusBadge status={data.status} />
      {/* Rest of the component */}
    </div>
  );
}
```

## API Modification Protocol

In some cases, you may need to modify the API to support new features or fix issues. Follow these guidelines:

1. **Get Explicit Approval**: Always discuss and get approval before modifying the API
2. **Follow API Conventions**:

   - Maintain the same structure and naming conventions
   - Use the existing selection patterns
   - Follow the error handling patterns
   - Use the same validation approach with Zod

3. **Update Documentation**: Document any changes you make to the API
4. **Test Thoroughly**: Ensure your changes don't break existing functionality

```tsx
// Example: Adding a new field to an API selection
const selection = {
  contract: {
    id: true,
    title: true,
    type: true,
    status: true,
    // New field - follow the existing pattern
    description: true,
  },
  // ...other selections
};
```

## Form Components

For form components, follow these conventions:

- Place form components in `components/forms/`
- Place field components for specific types in `components/forms/fields/`
- Use React Hook Form for form state management
- Use Zod for validation schemas

```tsx
// Example: Form field for an enum type
export function EntityTypeField({ control, name }: FieldProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>Entity Type</FormLabel>
          <Select value={field.value} onValueChange={field.onChange}>
            {Object.values(EntityType).map((type) => (
              <SelectItem key={type} value={type}>
                {typeLabels[type]}
              </SelectItem>
            ))}
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
```

## Internationalization

We use a simple object-based approach for internationalization. Each component that needs text should define its own `i18n` constant with an `en` property containing all text strings:

```tsx
// Define i18n object at the top of your component file
const i18n = {
  en: {
    title: "Contract Details",
    status: {
      pending: "Waiting for signatures",
      signed: "All parties have signed",
      rejected: "Contract was rejected",
      expired: "Contract has expired",
    },
    actions: {
      view: "View Document",
      sign: "Sign Document",
      reject: "Reject",
    },
    empty: "No contracts found",
    error: "Failed to load contract details",
  },
};

// Use in your component
export default function Contract({ loading, error, contract }: ContractProps) {
  if (loading) return <Skeleton />;
  if (error) return <ErrorFallback message={i18n.en.error} />;
  if (!contract) return <EmptyState message={i18n.en.empty} />;

  return (
    <div>
      <h1>{i18n.en.title}</h1>
      <p>{i18n.en.status[contract.status.toLowerCase()]}</p>
      {/* Rest of component */}
    </div>
  );
}
```

This pattern:

- Keeps all text in one place for easy updates
- Makes it clear which strings are user-facing
- Prepares the component for future multi-language support
- Maintains a clean separation between code and content

For enum labels and other reusable text, consider creating shared i18n objects in a separate file:

```tsx
// shared/i18n/contract.ts
export const contractTypeLabels = {
  en: {
    EMPLOYMENT: "Employment Contract",
    NON_COMPETE: "Non-Compete Agreement",
    NON_DISCLOSURE: "Non-Disclosure Agreement",
    SERVICE_RATE: "Service Rate Agreement",
    SERVICE_AGREEMENT: "Service Agreement",
    OTHER: "Other Contract",
  },
};

// In your component
import { contractTypeLabels } from "@/shared/i18n/contract";

function getContractTypeLabel(type: ContractType): string {
  return contractTypeLabels.en[type];
}
```

## Best Practices

- **Type Safety**: Always use the generated types from tRPC
- **API Consistency**: Reference the API selection object to ensure you're using all available fields
- **Error Handling**: Implement proper error handling for all API calls
- **Responsive Design**: Ensure components work on all screen sizes
- **Dark Mode**: Support both light and dark modes
- **Accessibility**: Follow accessibility best practices
- **Testing**: Create comprehensive stories for all states
- **Component Reuse**: Check for existing components before creating new ones
- **URL-Driven State**: Use search params for list views and tables
- **Internationalization**: Use i18n objects for all user-facing text

### Type Safety

- Use `RouterOutputs`, `RouterInputs`, and `RouterError` types from API for proper typing `"@/api"`
- Use exact types from schema for enums and status values - `"@/api"` exports all of our database enums
- Pass complete mutation objects to allow access to all mutation states

## Common Pitfalls to Avoid

- Creating custom types instead of using the generated types from tRPC
- Not checking for optional fields before accessing them
- Hardcoding values that should come from the API
- Not handling loading and error states
- Ignoring responsive design and dark mode
- Mixing data fetching and presentation concerns
- Modifying the API without prior approval
- Reinventing components that already exist in shared libraries
- Implementing custom state management for lists when search params are available
- Hardcoding user-facing text instead of using i18n objects

## Quick Reference

1. **Examine Prisma Schema**: Understand the database structure and available fields
2. **Scan API Types**: Understand the data structure from the API
3. **Set Up Data Layer**: Create the data fetching component with proper includes
4. **Check Shared Libraries**: Look for existing components in shared libraries and app components
5. **Update Stories**: Create mock data that matches the API types (using Faker)
6. **Implement Component**: Build the component with proper type safety

Following this process ensures consistency, type safety, and maintainability across all components in the application.
