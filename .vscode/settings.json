{
  "editor.codeActionsOnSave": {
    "source.removeUnusedImports": "never",
    "source.organizeLinkDefinitions": "never",
    "source.fixAll.eslint": "explicit",
    // we use eslint to fix linting errors
    "source.organizeImports": "never",
    // we use prettier to sort imports
    "source.sortImports": "never"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "eslint.rules.customizations": [{ "rule": "*", "severity": "warn" }],
  "eslint.useFlatConfig": true,
  "eslint.workingDirectories": [
    { "pattern": "apps/*/" },
    { "pattern": "packages/*/" },
    { "pattern": "tooling/*/" }
  ],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "tailwindCSS.experimental.configFile": "./tooling/tailwind/web.ts",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.preferences.autoImportFileExcludePatterns": [
    "next/router.d.ts",
    "next/dist/client/router.d.ts"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
  "cSpell.words": ["lucide"]
}
